@props([
    'type' => 'primary',
    'style' => 'solid', // solid, outline, label
    'shape' => 'default', // default, pill, circle
    'size' => 'default', // default, sm, lg
    'href' => null,
    'target' => null
])

@php
    $baseClasses = 'badge';
    
    // Type classes for different styles
    $solidClasses = [
        'primary' => 'text-bg-primary',
        'secondary' => 'text-bg-secondary',
        'success' => 'text-bg-success',
        'danger' => 'text-bg-danger',
        'warning' => 'text-bg-warning',
        'info' => 'text-bg-info',
        'light' => 'text-bg-light',
        'dark' => 'text-bg-dark',
    ];
    
    $outlineClasses = [
        'primary' => 'border border-primary text-primary',
        'secondary' => 'border border-secondary text-secondary',
        'success' => 'border border-success text-success',
        'danger' => 'border border-danger text-danger',
        'warning' => 'border border-warning text-warning',
        'info' => 'border border-info text-info',
        'light' => 'border border-light text-light',
        'dark' => 'border border-dark text-dark',
    ];
    
    $labelClasses = [
        'primary' => 'bg-label-primary',
        'secondary' => 'bg-label-secondary',
        'success' => 'bg-label-success',
        'danger' => 'bg-label-danger',
        'warning' => 'bg-label-warning',
        'info' => 'bg-label-info',
        'light' => 'bg-label-light',
        'dark' => 'bg-label-dark',
    ];
    
    // Choose style classes
    $styleClasses = match($style) {
        'solid' => $solidClasses,
        'outline' => $outlineClasses,
        'label' => $labelClasses,
        default => $solidClasses,
    };
    
    $classes = $baseClasses . ' ' . $styleClasses[$type];
    
    // Shape classes
    if ($shape === 'pill') {
        $classes .= ' rounded-pill';
    } elseif ($shape === 'circle') {
        $classes .= ' badge-center rounded-pill';
    }
    
    // Size classes
    if ($size === 'sm') {
        $classes .= ' fs-tiny';
    } elseif ($size === 'lg') {
        $classes .= ' fs-6';
    }
@endphp

@if($href)
    <a href="{{ $href }}" 
       @if($target) target="{{ $target }}" @endif
       class="{{ $classes }}"
       {{ $attributes }}>
        {{ $slot }}
    </a>
@else
    <span class="{{ $classes }}" {{ $attributes }}>
        {{ $slot }}
    </span>
@endif
