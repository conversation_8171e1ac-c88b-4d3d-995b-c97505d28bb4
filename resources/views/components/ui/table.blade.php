@props([
    'striped' => false,
    'bordered' => false,
    'hover' => false,
    'responsive' => false,
    'size' => 'default', // sm, default, lg
    'variant' => 'default', // default, primary, secondary, success, danger, warning, info, light, dark
    'caption' => null
])

@php
    $baseClasses = 'table';
    
    // Size classes
    $sizeClasses = [
        'sm' => 'table-sm',
        'default' => '',
        'lg' => 'table-lg',
    ];
    
    $classes = $baseClasses . ' ' . $sizeClasses[$size];
    
    // Variant classes
    $variantClasses = [
        'default' => '',
        'primary' => 'table-primary',
        'secondary' => 'table-secondary',
        'success' => 'table-success',
        'danger' => 'table-danger',
        'warning' => 'table-warning',
        'info' => 'table-info',
        'light' => 'table-light',
        'dark' => 'table-dark',
    ];
    
    $classes .= ' ' . $variantClasses[$variant];
    
    if ($striped) {
        $classes .= ' table-striped';
    }
    
    if ($bordered) {
        $classes .= ' table-bordered';
    }
    
    if ($hover) {
        $classes .= ' table-hover';
    }
@endphp

@if($responsive)
    <div class="table-responsive">
@endif

<table class="{{ $classes }}" {{ $attributes }}>
    @if($caption)
        <caption>{{ $caption }}</caption>
    @endif
    
    {{ $slot }}
</table>

@if($responsive)
    </div>
@endif
