@props([
    'value' => 0,
    'min' => 0,
    'max' => 100,
    'type' => 'default', // default, striped, animated
    'size' => 'default', // sm, default, lg
    'variant' => 'primary', // primary, secondary, success, danger, warning, info, light, dark
    'label' => null,
    'showValue' => false
])

@php
    $baseClasses = 'progress';
    
    // Size classes
    $sizeClasses = [
        'sm' => 'progress-sm',
        'default' => '',
        'lg' => 'progress-lg',
    ];
    
    $classes = $baseClasses . ' ' . $sizeClasses[$size];
    
    $barClasses = 'progress-bar';
    
    // Variant classes
    $variantClasses = [
        'primary' => 'bg-primary',
        'secondary' => 'bg-secondary',
        'success' => 'bg-success',
        'danger' => 'bg-danger',
        'warning' => 'bg-warning',
        'info' => 'bg-info',
        'light' => 'bg-light',
        'dark' => 'bg-dark',
    ];
    
    $barClasses .= ' ' . $variantClasses[$variant];
    
    if ($type === 'striped') {
        $barClasses .= ' progress-bar-striped';
    } elseif ($type === 'animated') {
        $barClasses .= ' progress-bar-striped progress-bar-animated';
    }
    
    $percentage = min(100, max(0, ($value - $min) / ($max - $min) * 100));
@endphp

<div class="{{ $classes }}" {{ $attributes }}>
    <div class="{{ $barClasses }}" 
         role="progressbar" 
         style="width: {{ $percentage }}%"
         aria-valuenow="{{ $value }}" 
         aria-valuemin="{{ $min }}" 
         aria-valuemax="{{ $max }}">
        @if($label || $showValue)
            {{ $label ?? ($showValue ? $value . '%' : '') }}
        @endif
    </div>
</div>
