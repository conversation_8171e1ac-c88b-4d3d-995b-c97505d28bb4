@props([
    'id',
    'title' => null,
    'subtitle' => null,
    'type' => 'default', // default, success, danger, warning, info
    'autohide' => true,
    'delay' => 5000,
    'animation' => true,
    'closeButton' => true,
    'icon' => null
])

@php
    $baseClasses = 'toast';
    
    if ($animation) {
        $baseClasses .= ' fade';
    }
    
    // Default icons for each type
    $defaultIcons = [
        'default' => 'bx bx-info-circle',
        'success' => 'bx bx-check-circle',
        'danger' => 'bx bx-error-circle',
        'warning' => 'bx bx-error',
        'info' => 'bx bx-info-circle',
    ];
    
    $iconClass = $icon ?? $defaultIcons[$type];
    
    // Type classes for header
    $typeClasses = [
        'default' => '',
        'success' => 'bg-success text-white',
        'danger' => 'bg-danger text-white',
        'warning' => 'bg-warning text-white',
        'info' => 'bg-info text-white',
    ];
@endphp

<div class="{{ $baseClasses }}" 
     id="{{ $id }}" 
     role="alert" 
     aria-live="assertive" 
     aria-atomic="true"
     @if($autohide) data-bs-autohide="{{ $autohide }}" @endif
     @if($delay) data-bs-delay="{{ $delay }}" @endif
     {{ $attributes }}>
    <div class="toast-header {{ $typeClasses[$type] }}">
        @if($iconClass)
            <i class="{{ $iconClass }} me-2"></i>
        @endif
        
        @if($title)
            <strong class="me-auto">{{ $title }}</strong>
        @endif
        
        @if($subtitle)
            <small>{{ $subtitle }}</small>
        @endif
        
        @if($closeButton)
            <button type="button" 
                    class="btn-close" 
                    data-bs-dismiss="toast" 
                    aria-label="Close"></button>
        @endif
    </div>
    
    <div class="toast-body">
        {{ $slot }}
    </div>
</div>
