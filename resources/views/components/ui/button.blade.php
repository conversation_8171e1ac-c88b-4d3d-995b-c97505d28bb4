@props([
    'type' => 'button',
    'variant' => 'primary',
    'size' => 'md',
    'disabled' => false,
    'href' => null,
    'target' => null,
    'icon' => null,
    'iconPosition' => 'left'
])

@php
    $baseClasses = 'btn';
    
    // Variant classes
    $variantClasses = [
        'primary' => 'btn-primary',
        'secondary' => 'btn-secondary',
        'success' => 'btn-success',
        'danger' => 'btn-danger',
        'warning' => 'btn-warning',
        'info' => 'btn-info',
        'light' => 'btn-light',
        'dark' => 'btn-dark',
        'outline-primary' => 'btn-outline-primary',
        'outline-secondary' => 'btn-outline-secondary',
        'outline-success' => 'btn-outline-success',
        'outline-danger' => 'btn-outline-danger',
        'outline-warning' => 'btn-outline-warning',
        'outline-info' => 'btn-outline-info',
        'outline-light' => 'btn-outline-light',
        'outline-dark' => 'btn-outline-dark',
    ];
    
    // Size classes
    $sizeClasses = [
        'sm' => 'btn-sm',
        'md' => '',
        'lg' => 'btn-lg',
    ];
    
    $classes = $baseClasses . ' ' . $variantClasses[$variant] . ' ' . $sizeClasses[$size];
    
    if ($disabled) {
        $classes .= ' disabled';
    }
@endphp

@if($href)
    <a href="{{ $href }}" 
       @if($target) target="{{ $target }}" @endif
       class="{{ $classes }}"
       @if($disabled) aria-disabled="true" @endif
       {{ $attributes }}>
        @if($icon && $iconPosition === 'left')
            <i class="{{ $icon }} me-1"></i>
        @endif
        {{ $slot }}
        @if($icon && $iconPosition === 'right')
            <i class="{{ $icon }} ms-1"></i>
        @endif
    </a>
@else
    <button type="{{ $type }}" 
            class="{{ $classes }}"
            @if($disabled) disabled @endif
            {{ $attributes }}>
        @if($icon && $iconPosition === 'left')
            <i class="{{ $icon }} me-1"></i>
        @endif
        {{ $slot }}
        @if($icon && $iconPosition === 'right')
            <i class="{{ $icon }} ms-1"></i>
        @endif
    </button>
@endif
