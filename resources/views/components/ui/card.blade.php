@props([
    'title' => null,
    'subtitle' => null,
    'header' => null,
    'footer' => null,
    'variant' => 'default',
    'border' => true,
    'shadow' => true,
    'padding' => true
])

@php
    $baseClasses = 'card';
    
    // Variant classes
    $variantClasses = [
        'default' => '',
        'primary' => 'border-primary',
        'secondary' => 'border-secondary',
        'success' => 'border-success',
        'danger' => 'border-danger',
        'warning' => 'border-warning',
        'info' => 'border-info',
        'light' => 'border-light',
        'dark' => 'border-dark',
    ];
    
    $classes = $baseClasses;
    
    if ($border) {
        $classes .= ' ' . $variantClasses[$variant];
    }
    
    if ($shadow) {
        $classes .= ' shadow-sm';
    }
    
    $bodyClasses = $padding ? 'card-body' : '';
@endphp

<div class="{{ $classes }}" {{ $attributes }}>
    @if($header)
        <div class="card-header">
            {{ $header }}
        </div>
    @elseif($title || $subtitle)
        <div class="card-header">
            @if($title)
                <h5 class="card-title mb-0">{{ $title }}</h5>
            @endif
            @if($subtitle)
                <small class="text-muted">{{ $subtitle }}</small>
            @endif
        </div>
    @endif
    
    <div class="{{ $bodyClasses }}">
        {{ $slot }}
    </div>
    
    @if($footer)
        <div class="card-footer">
            {{ $footer }}
        </div>
    @endif
</div>
