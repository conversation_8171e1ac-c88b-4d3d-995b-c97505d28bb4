@props([
    'id',
    'title' => null,
    'size' => 'default', // default, sm, lg, xl
    'centered' => false,
    'scrollable' => false,
    'staticBackdrop' => false,
    'footer' => null,
    'closeButton' => true,
    'closeButtonText' => 'Close',
    'saveButton' => false,
    'saveButtonText' => 'Save changes'
])

@php
    $baseClasses = 'modal fade';
    
    if ($staticBackdrop) {
        $baseClasses .= ' modal-static';
    }
    
    $dialogClasses = 'modal-dialog';
    
    // Size classes
    $sizeClasses = [
        'sm' => 'modal-sm',
        'default' => '',
        'lg' => 'modal-lg',
        'xl' => 'modal-xl',
    ];
    
    $dialogClasses .= ' ' . $sizeClasses[$size];
    
    if ($centered) {
        $dialogClasses .= ' modal-dialog-centered';
    }
    
    if ($scrollable) {
        $dialogClasses .= ' modal-dialog-scrollable';
    }
@endphp

<div class="{{ $baseClasses }}" 
     id="{{ $id }}" 
     tabindex="-1" 
     aria-hidden="true"
     {{ $attributes }}>
    <div class="{{ $dialogClasses }}" role="document">
        <div class="modal-content">
            @if($title || $closeButton)
                <div class="modal-header">
                    @if($title)
                        <h5 class="modal-title">{{ $title }}</h5>
                    @endif
                    @if($closeButton)
                        <button type="button" 
                                class="btn-close" 
                                data-bs-dismiss="modal" 
                                aria-label="Close"></button>
                    @endif
                </div>
            @endif
            
            <div class="modal-body">
                {{ $slot }}
            </div>
            
            @if($footer || $saveButton)
                <div class="modal-footer">
                    @if($footer)
                        {{ $footer }}
                    @else
                        @if($closeButton)
                            <button type="button" 
                                    class="btn btn-label-secondary" 
                                    data-bs-dismiss="modal">
                                {{ $closeButtonText }}
                            </button>
                        @endif
                        @if($saveButton)
                            <button type="button" class="btn btn-primary">
                                {{ $saveButtonText }}
                            </button>
                        @endif
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>
