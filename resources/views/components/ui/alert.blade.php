@props([
    'type' => 'primary',
    'dismissible' => false,
    'icon' => null,
    'title' => null
])

@php
    $baseClasses = 'alert';
    
    // Type classes
    $typeClasses = [
        'primary' => 'alert-primary',
        'secondary' => 'alert-secondary',
        'success' => 'alert-success',
        'danger' => 'alert-danger',
        'warning' => 'alert-warning',
        'info' => 'alert-info',
        'light' => 'alert-light',
        'dark' => 'alert-dark',
    ];
    
    $classes = $baseClasses . ' ' . $typeClasses[$type];
    
    if ($dismissible) {
        $classes .= ' alert-dismissible';
    }
    
    // Default icons for each type
    $defaultIcons = [
        'primary' => 'bx bx-info-circle',
        'secondary' => 'bx bx-info-circle',
        'success' => 'bx bx-check-circle',
        'danger' => 'bx bx-error-circle',
        'warning' => 'bx bx-error',
        'info' => 'bx bx-info-circle',
        'light' => 'bx bx-info-circle',
        'dark' => 'bx bx-info-circle',
    ];
    
    $iconClass = $icon ?? $defaultIcons[$type];
@endphp

<div class="{{ $classes }}" role="alert" {{ $attributes }}>
    @if($iconClass)
        <i class="{{ $iconClass }} me-2"></i>
    @endif
    
    @if($title)
        <h6 class="alert-heading">{{ $title }}</h6>
    @endif
    
    {{ $slot }}
    
    @if($dismissible)
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    @endif
</div>
