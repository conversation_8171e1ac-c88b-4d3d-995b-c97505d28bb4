@props([
    'type' => 'border', // border, grow
    'size' => 'default', // sm, default, lg
    'variant' => 'primary', // primary, secondary, success, danger, warning, info, light, dark
    'text' => null,
    'role' => 'status'
])

@php
    $baseClasses = 'spinner-' . $type;
    
    // Size classes
    $sizeClasses = [
        'sm' => 'spinner-' . $type . '-sm',
        'default' => '',
        'lg' => 'spinner-' . $type . '-lg',
    ];
    
    $classes = $baseClasses . ' ' . $sizeClasses[$size];
    
    // Variant classes
    $variantClasses = [
        'primary' => 'text-primary',
        'secondary' => 'text-secondary',
        'success' => 'text-success',
        'danger' => 'text-danger',
        'warning' => 'text-warning',
        'info' => 'text-info',
        'light' => 'text-light',
        'dark' => 'text-dark',
    ];
    
    $classes .= ' ' . $variantClasses[$variant];
@endphp

<div class="{{ $classes }}" role="{{ $role }}" {{ $attributes }}>
    @if($text)
        <span class="visually-hidden">{{ $text }}</span>
    @endif
</div>
