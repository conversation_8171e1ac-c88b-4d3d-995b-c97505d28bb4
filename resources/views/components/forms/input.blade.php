@props([
    'type' => 'text',
    'name',
    'label' => null,
    'placeholder' => null,
    'value' => null,
    'required' => false,
    'disabled' => false,
    'readonly' => false,
    'help' => null,
    'error' => null,
    'size' => 'md',
    'prepend' => null,
    'append' => null
])

@php
    $baseClasses = 'form-control';
    
    // Size classes
    $sizeClasses = [
        'sm' => 'form-control-sm',
        'md' => '',
        'lg' => 'form-control-lg',
    ];
    
    $classes = $baseClasses . ' ' . $sizeClasses[$size];
    
    if ($error) {
        $classes .= ' is-invalid';
    }
    
    $inputId = $name . '_' . uniqid();
@endphp

<div class="mb-3">
    @if($label)
        <label for="{{ $inputId }}" class="form-label">
            {{ $label }}
            @if($required)
                <span class="text-danger">*</span>
            @endif
        </label>
    @endif
    
    @if($prepend || $append)
        <div class="input-group">
            @if($prepend)
                <span class="input-group-text">{{ $prepend }}</span>
            @endif
            
            <input type="{{ $type }}"
                   id="{{ $inputId }}"
                   name="{{ $name }}"
                   value="{{ old($name, $value) }}"
                   @if($placeholder) placeholder="{{ $placeholder }}" @endif
                   @if($required) required @endif
                   @if($disabled) disabled @endif
                   @if($readonly) readonly @endif
                   class="{{ $classes }}"
                   {{ $attributes }}>
            
            @if($append)
                <span class="input-group-text">{{ $append }}</span>
            @endif
        </div>
    @else
        <input type="{{ $type }}"
               id="{{ $inputId }}"
               name="{{ $name }}"
               value="{{ old($name, $value) }}"
               @if($placeholder) placeholder="{{ $placeholder }}" @endif
               @if($required) required @endif
               @if($disabled) disabled @endif
               @if($readonly) readonly @endif
               class="{{ $classes }}"
               {{ $attributes }}>
    @endif
    
    @if($help)
        <div class="form-text">{{ $help }}</div>
    @endif
    
    @if($error)
        <div class="invalid-feedback">
            {{ $error }}
        </div>
    @endif
    
    @error($name)
        <div class="invalid-feedback d-block">
            {{ $message }}
        </div>
    @enderror
</div>
