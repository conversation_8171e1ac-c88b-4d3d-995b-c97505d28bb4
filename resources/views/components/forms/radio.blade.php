@props([
    'name',
    'value',
    'label' => null,
    'checked' => false,
    'required' => false,
    'disabled' => false,
    'help' => null,
    'error' => null,
    'inline' => false
])

@php
    $baseClasses = 'form-check-input';
    
    if ($error) {
        $baseClasses .= ' is-invalid';
    }
    
    $inputId = $name . '_' . $value . '_' . uniqid();
    
    $currentValue = old($name, $checked);
@endphp

<div class="mb-3 {{ $inline ? 'form-check form-check-inline' : 'form-check' }}">
    <input type="radio"
           id="{{ $inputId }}"
           name="{{ $name }}"
           value="{{ $value }}"
           @if($currentValue) checked @endif
           @if($required) required @endif
           @if($disabled) disabled @endif
           class="{{ $baseClasses }}"
           {{ $attributes }}>
    
    @if($label)
        <label class="form-check-label" for="{{ $inputId }}">
            {{ $label }}
            @if($required)
                <span class="text-danger">*</span>
            @endif
        </label>
    @endif
    
    @if($help)
        <div class="form-text">{{ $help }}</div>
    @endif
    
    @if($error)
        <div class="invalid-feedback">
            {{ $error }}
        </div>
    @endif
    
    @error($name)
        <div class="invalid-feedback d-block">
            {{ $message }}
        </div>
    @enderror
</div>
