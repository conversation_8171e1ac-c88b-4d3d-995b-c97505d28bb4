@props([
    'name',
    'label' => null,
    'placeholder' => null,
    'options' => [],
    'selected' => null,
    'required' => false,
    'disabled' => false,
    'multiple' => false,
    'size' => 'md',
    'help' => null,
    'error' => null
])

@php
    $baseClasses = 'form-select';
    
    // Size classes
    $sizeClasses = [
        'sm' => 'form-select-sm',
        'md' => '',
        'lg' => 'form-select-lg',
    ];
    
    $classes = $baseClasses . ' ' . $sizeClasses[$size];
    
    if ($error) {
        $classes .= ' is-invalid';
    }
    
    $inputId = $name . '_' . uniqid();
    
    $currentValue = old($name, $selected);
@endphp

<div class="mb-3">
    @if($label)
        <label for="{{ $inputId }}" class="form-label">
            {{ $label }}
            @if($required)
                <span class="text-danger">*</span>
            @endif
        </label>
    @endif
    
    <select id="{{ $inputId }}"
            name="{{ $name }}"
            @if($required) required @endif
            @if($disabled) disabled @endif
            @if($multiple) multiple @endif
            class="{{ $classes }}"
            {{ $attributes }}>
        
        @if($placeholder && !$multiple)
            <option value="" disabled selected>{{ $placeholder }}</option>
        @endif
        
        @foreach($options as $value => $label)
            @if(is_array($label))
                <optgroup label="{{ $value }}">
                    @foreach($label as $optValue => $optLabel)
                        <option value="{{ $optValue }}" 
                                @if($multiple && is_array($currentValue))
                                    {{ in_array($optValue, $currentValue) ? 'selected' : '' }}
                                @else
                                    {{ $currentValue == $optValue ? 'selected' : '' }}
                                @endif>
                            {{ $optLabel }}
                        </option>
                    @endforeach
                </optgroup>
            @else
                <option value="{{ $value }}" 
                        @if($multiple && is_array($currentValue))
                            {{ in_array($value, $currentValue) ? 'selected' : '' }}
                        @else
                            {{ $currentValue == $value ? 'selected' : '' }}
                        @endif>
                    {{ $label }}
                </option>
            @endif
        @endforeach
    </select>
    
    @if($help)
        <div class="form-text">{{ $help }}</div>
    @endif
    
    @if($error)
        <div class="invalid-feedback">
            {{ $error }}
        </div>
    @endif
    
    @error($name)
        <div class="invalid-feedback d-block">
            {{ $message }}
        </div>
    @enderror
</div>
