@props([
    'name',
    'label' => null,
    'placeholder' => null,
    'value' => null,
    'rows' => 3,
    'cols' => null,
    'required' => false,
    'disabled' => false,
    'readonly' => false,
    'help' => null,
    'error' => null,
    'size' => 'md'
])

@php
    $baseClasses = 'form-control';
    
    // Size classes
    $sizeClasses = [
        'sm' => 'form-control-sm',
        'md' => '',
        'lg' => 'form-control-lg',
    ];
    
    $classes = $baseClasses . ' ' . $sizeClasses[$size];
    
    if ($error) {
        $classes .= ' is-invalid';
    }
    
    $inputId = $name . '_' . uniqid();
@endphp

<div class="mb-3">
    @if($label)
        <label for="{{ $inputId }}" class="form-label">
            {{ $label }}
            @if($required)
                <span class="text-danger">*</span>
            @endif
        </label>
    @endif
    
    <textarea id="{{ $inputId }}"
              name="{{ $name }}"
              rows="{{ $rows }}"
              @if($cols) cols="{{ $cols }}" @endif
              @if($placeholder) placeholder="{{ $placeholder }}" @endif
              @if($required) required @endif
              @if($disabled) disabled @endif
              @if($readonly) readonly @endif
              class="{{ $classes }}"
              {{ $attributes }}>{{ old($name, $value) }}</textarea>
    
    @if($help)
        <div class="form-text">{{ $help }}</div>
    @endif
    
    @if($error)
        <div class="invalid-feedback">
            {{ $error }}
        </div>
    @endif
    
    @error($name)
        <div class="invalid-feedback d-block">
            {{ $message }}
        </div>
    @enderror
</div>
