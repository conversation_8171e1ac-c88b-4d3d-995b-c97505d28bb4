@extends('layouts.auth')

@section('title', 'Login')

@section('content')
    <!-- Login -->
    <div class="card px-sm-6 px-0">
        <div class="card-body">
            <!-- Logo -->
            <div class="app-brand justify-content-center">
                <a href="{{ route('dashboard') }}" class="app-brand-link gap-2">
                    <span class="app-brand-logo demo">
                        <span class="text-primary">
                            <svg width="500" height="500" xmlns="http://www.w3.org/2000/svg">
                                <image href="{{ asset('assets/logo/PNG/E2_Main Logo.png') }}" x="0" y="0" width="100%" height="100%" />
                            </svg>
                        </span>
                    </span>
                </a>
            </div>
            <!-- /Logo -->
            <h4 class="mb-1">Welcome to e2!</h4>
            <p class="mb-6">Please sign-in to your account and start the adventure</p>

            <form id="formAuthentication" class="mb-6" action="index.html">
                <div class="mb-6">
                    <label for="email" class="form-label">Email or Username</label>
                    <input type="text" class="form-control" id="email" name="email-username"
                        placeholder="Enter your email or username" autofocus />
                </div>
                <div class="mb-6 form-password-toggle">
                    <label class="form-label" for="password">Password</label>
                    <div class="input-group input-group-merge">
                        <input type="password" id="password" class="form-control" name="password"
                            placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                            aria-describedby="password" />
                        <span class="input-group-text cursor-pointer"><i class="icon-base bx bx-hide"></i></span>
                    </div>
                </div>
                <div class="mb-8">
                    <div class="d-flex justify-content-between">
                        <div class="form-check mb-0">
                            <input class="form-check-input" type="checkbox" id="remember-me" />
                            <label class="form-check-label" for="remember-me"> Remember Me </label>
                        </div>
                        <a href="{{ route('password.request') }}">
                            <span>Forgot Password?</span>
                        </a>
                    </div>
                </div>
                <div class="mb-6">
                    <button class="btn btn-primary d-grid w-100" type="submit">Login</button>
                </div>
            </form>

            <p class="text-center">
                <span>New on our platform?</span>
                <a href="{{ route('register') }}">
                    <span>Create an account</span>
                </a>
            </p>
        </div>
    </div>
    <!-- /Login -->
@endsection
