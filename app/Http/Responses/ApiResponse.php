<?php

namespace App\Http\Responses;

use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use InvalidArgumentException;

class ApiResponse implements Responsable
{
    private const MIN_SUCCESS_CODE = 200;
    private const MAX_SUCCESS_CODE = 299;
    private const MIN_CLIENT_ERROR_CODE = 400;
    private const MAX_CLIENT_ERROR_CODE = 499;
    private const MIN_SERVER_ERROR_CODE = 500;
    private const MAX_SERVER_ERROR_CODE = 599;

    public function __construct(
        private readonly int $httpCode,
        private readonly array $data = [],
        private readonly string|array $errorMessage = '',
        private readonly array $headers = [],
        private readonly int $options = JSON_UNESCAPED_UNICODE
    ) {
        $this->validateHttpCode($httpCode);
    }

    public function toResponse($request): JsonResponse
    {
        $payload = $this->buildPayload();

        return response()->json(
            data: $payload,
            status: $this->httpCode,
            headers: $this->headers,
            options: $this->options
        );
    }

    private function validateHttpCode(int $httpCode): void
    {
        $isValid = ($httpCode >= self::MIN_SUCCESS_CODE && $httpCode <= self::MAX_SUCCESS_CODE) ||
            ($httpCode >= self::MIN_CLIENT_ERROR_CODE && $httpCode <= self::MAX_CLIENT_ERROR_CODE) ||
            ($httpCode >= self::MIN_SERVER_ERROR_CODE && $httpCode <= self::MAX_SERVER_ERROR_CODE);

        if (!$isValid) {
            throw new InvalidArgumentException("HTTP code {$httpCode} is not valid");
        }
    }

    private function buildPayload(): array
    {
        return match (true) {
            $this->isServerError() => $this->buildServerErrorPayload(),
            $this->isClientError() => $this->buildClientErrorPayload(),
            $this->isSuccess() => $this->buildSuccessPayload(),
            default => throw new InvalidArgumentException("Unhandled HTTP code: {$this->httpCode}")
        };
    }

    private function isServerError(): bool
    {
        return $this->httpCode >= self::MIN_SERVER_ERROR_CODE;
    }

    private function isClientError(): bool
    {
        return $this->httpCode >= self::MIN_CLIENT_ERROR_CODE && $this->httpCode <= self::MAX_CLIENT_ERROR_CODE;
    }

    private function isSuccess(): bool
    {
        return $this->httpCode >= self::MIN_SUCCESS_CODE && $this->httpCode <= self::MAX_SUCCESS_CODE;
    }

    private function buildServerErrorPayload(): array
    {
        $message = config('app.debug', false) ? $this->formatErrorMessage() : 'Internal server error';

        return [
            'error' => true,
            'message' => $message,
            'code' => $this->httpCode
        ];
    }

    private function buildClientErrorPayload(): array
    {
        $message = $this->formatErrorMessage() ?: 'Client error';

        return [
            'error' => true,
            'message' => $message,
            'code' => $this->httpCode
        ];
    }

    private function buildSuccessPayload(): array
    {
        return [
            'success' => true,
            'data' => $this->data,
            'code' => $this->httpCode
        ];
    }

    private function formatErrorMessage(): string|array
    {
        if (is_array($this->errorMessage)) {
            // For Laravel validation errors, return the array structure
            return $this->errorMessage;
        }

        return $this->errorMessage;
    }

    // Static factory methods for common responses
    public static function ok(array $data = [], array $headers = []): self
    {
        return new self(200, $data, headers: $headers);
    }

    public static function created(array $data = [], array $headers = []): self
    {
        return new self(201, $data, headers: $headers);
    }

    public static function accepted(array $data = [], array $headers = []): self
    {
        return new self(202, $data, headers: $headers);
    }

    public static function noContent(array $headers = []): self
    {
        return new self(204, headers: $headers);
    }

    public static function badRequest(string|array $message = 'Bad request', array $headers = []): self
    {
        return new self(400, errorMessage: $message, headers: $headers);
    }

    public static function unauthorized(string|array $message = 'Unauthorized', array $headers = []): self
    {
        return new self(401, errorMessage: $message, headers: $headers);
    }

    public static function forbidden(string|array $message = 'Forbidden', array $headers = []): self
    {
        return new self(403, errorMessage: $message, headers: $headers);
    }

    public static function notFound(string|array $message = 'Resource not found', array $headers = []): self
    {
        return new self(404, errorMessage: $message, headers: $headers);
    }

    public static function methodNotAllowed(string|array $message = 'Method not allowed', array $headers = []): self
    {
        return new self(405, errorMessage: $message, headers: $headers);
    }

    public static function conflict(string|array $message = 'Conflict', array $headers = []): self
    {
        return new self(409, errorMessage: $message, headers: $headers);
    }

    public static function unprocessableEntity(string|array $message = 'Unprocessable entity', array $headers = []): self
    {
        return new self(422, errorMessage: $message, headers: $headers);
    }

    public static function tooManyRequests(string|array $message = 'Too many requests', array $headers = []): self
    {
        return new self(429, errorMessage: $message, headers: $headers);
    }

    public static function internalServerError(string|array $message = 'Internal server error', array $headers = []): self
    {
        return new self(500, errorMessage: $message, headers: $headers);
    }

    public static function serviceUnavailable(string|array $message = 'Service unavailable', array $headers = []): self
    {
        return new self(503, errorMessage: $message, headers: $headers);
    }

    // Utility methods
    public function withHeaders(array $headers): self
    {
        return new self(
            $this->httpCode,
            $this->data,
            $this->errorMessage,
            array_merge($this->headers, $headers),
            $this->options
        );
    }

    public function withOptions(int $options): self
    {
        return new self(
            $this->httpCode,
            $this->data,
            $this->errorMessage,
            $this->headers,
            $options
        );
    }

    public function getHttpCode(): int
    {
        return $this->httpCode;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function getErrorMessage(): string|array
    {
        return $this->errorMessage;
    }

    // Convenience method for validation errors
    public static function validationError(array $errors, array $headers = []): self
    {
        return new self(422, errorMessage: $errors, headers: $headers);
    }
}
