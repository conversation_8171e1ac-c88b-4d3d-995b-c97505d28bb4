<?php

namespace App\Http\Controllers;

use App\Models\CouponCategory;
use Illuminate\Http\Request;

class CouponCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(CouponCategory $couponCategory)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CouponCategory $couponCategory)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CouponCategory $couponCategory)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CouponCategory $couponCategory)
    {
        //
    }
}
