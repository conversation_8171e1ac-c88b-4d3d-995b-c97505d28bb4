<?php

namespace App\Http\Controllers;

use App\Models\ProductIngredient;
use Illuminate\Http\Request;

class ProductIngredientController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ProductIngredient $productIngredient)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProductIngredient $productIngredient)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProductIngredient $productIngredient)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProductIngredient $productIngredient)
    {
        //
    }
}
