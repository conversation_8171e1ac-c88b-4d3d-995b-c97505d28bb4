<?php

namespace App\Http\Controllers;

use App\Models\CouponVendor;
use Illuminate\Http\Request;

class CouponVendorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(CouponVendor $couponVendor)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CouponVendor $couponVendor)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CouponVendor $couponVendor)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CouponVendor $couponVendor)
    {
        //
    }
}
