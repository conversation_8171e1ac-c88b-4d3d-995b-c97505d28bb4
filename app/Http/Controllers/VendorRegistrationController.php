<?php

namespace App\Http\Controllers;

use App\Models\VendorRegistration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class VendorRegistrationController extends Controller
{
    /**
     * Display a listing of the vendor registrations.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Fetches all vendor registrations from the database and passes them to the view.
        $registrations = VendorRegistration::all();
        return view('vendor-registrations.index', compact('registrations'));
    }

    /**
     * Show the form for creating a new vendor registration.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Displays the form for new vendor registration.
        return view('vendor-registrations.create');
    }

    /**
     * Store a newly created vendor registration in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate the incoming request data.
        $validatedData = $request->validate([
            'name' => 'required|string|max:191',
            'email' => 'required|string|email|unique:vendor_registrations,email|max:191',
            'phone_number' => 'required|string|max:20',
            'password' => 'required|string|min:8',
            'business_name' => 'required|string|max:191',
            'business_description' => 'required|string',
            'business_email' => 'required|string|email|max:191',
            'business_phone' => 'required|string|max:20',
            'business_address' => 'required|string',
            'woreda' => 'nullable|string|max:100',
            'house_number' => 'nullable|string|max:50',
            'website' => 'nullable|string|max:255',
            'tin_number' => 'required|string|max:50',
            'tax_id' => 'required|string|max:255',
            'trade_license_doc' => 'required|string|max:255',
            'tin_certificate_doc' => 'nullable|string|max:255',
            'id_card_doc' => 'nullable|string|max:255',
            'status' => ['nullable', Rule::in(['pending', 'approved', 'rejected', 'suspended'])],
        ]);

        // Hash the password before saving it to the database.
        $validatedData['password'] = Hash::make($validatedData['password']);

        // Create the new vendor registration record.
        VendorRegistration::create($validatedData);

        // Redirect with a success message.
        return redirect()->route('vendor-registrations.index')->with('success', 'Vendor registration created successfully!');
    }

    /**
     * Display the specified vendor registration.
     *
     * @param  \App\Models\VendorRegistration  $vendorRegistration
     * @return \Illuminate\View\View
     */
    public function show(VendorRegistration $vendorRegistration)
    {
        // Displays the details of a single vendor registration.
        return view('vendor-registrations.show', compact('vendorRegistration'));
    }

    /**
     * Show the form for editing the specified vendor registration.
     *
     * @param  \App\Models\VendorRegistration  $vendorRegistration
     * @return \Illuminate\View\View
     */
    public function edit(VendorRegistration $vendorRegistration)
    {
        // Displays the form to edit an existing vendor registration.
        return view('vendor-registrations.edit', compact('vendorRegistration'));
    }

    /**
     * Update the specified vendor registration in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\VendorRegistration  $vendorRegistration
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, VendorRegistration $vendorRegistration)
    {
        // Validate the incoming request data, excluding the unique email rule for the current record.
        $validatedData = $request->validate([
            'name' => 'required|string|max:191',
            'email' => ['required', 'string', 'email', 'max:191', Rule::unique('vendor_registrations')->ignore($vendorRegistration->id)],
            'phone_number' => 'required|string|max:20',
            'password' => 'nullable|string|min:8', // Password can be null for updates
            'business_name' => 'required|string|max:191',
            'business_description' => 'required|string',
            'business_email' => 'required|string|email|max:191',
            'business_phone' => 'required|string|max:20',
            'business_address' => 'required|string',
            'woreda' => 'nullable|string|max:100',
            'house_number' => 'nullable|string|max:50',
            'website' => 'nullable|string|max:255',
            'tin_number' => 'required|string|max:50',
            'tax_id' => 'required|string|max:255',
            'trade_license_doc' => 'required|string|max:255',
            'tin_certificate_doc' => 'nullable|string|max:255',
            'id_card_doc' => 'nullable|string|max:255',
            'status' => ['nullable', Rule::in(['pending', 'approved', 'rejected', 'suspended'])],
        ]);

        // If a new password is provided, hash it before updating.
        if (!empty($validatedData['password'])) {
            $validatedData['password'] = Hash::make($validatedData['password']);
        } else {
            unset($validatedData['password']);
        }

        // Update the vendor registration record.
        $vendorRegistration->update($validatedData);

        // Redirect with a success message.
        return redirect()->route('vendor-registrations.index')->with('success', 'Vendor registration updated successfully!');
    }

    /**
     * Remove the specified vendor registration from storage.
     *
     * @param  \App\Models\VendorRegistration  $vendorRegistration
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(VendorRegistration $vendorRegistration)
    {
        // Deletes the specified vendor registration record.
        $vendorRegistration->delete();

        // Redirect with a success message.
        return redirect()->route('vendor-registrations.index')->with('success', 'Vendor registration deleted successfully!');
    }
}
