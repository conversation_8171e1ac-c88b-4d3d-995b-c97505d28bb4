<?php

namespace App\Http\Controllers;

use App\Models\CouponProduct;
use Illuminate\Http\Request;

class CouponProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(CouponProduct $couponProduct)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CouponProduct $couponProduct)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CouponProduct $couponProduct)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CouponProduct $couponProduct)
    {
        //
    }
}
