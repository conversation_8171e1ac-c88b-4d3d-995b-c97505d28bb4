<?php

namespace App\Http\Controllers;

use App\Models\RecommendedProducts;
use Illuminate\Http\Request;

class RecommendedProductsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(RecommendedProducts $recommendedProducts)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RecommendedProducts $recommendedProducts)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RecommendedProducts $recommendedProducts)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RecommendedProducts $recommendedProducts)
    {
        //
    }
}
