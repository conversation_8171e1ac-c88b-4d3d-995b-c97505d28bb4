<?php

namespace App\Http\Controllers;

use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class VendorController extends Controller
{
    /**
     * Display a listing of the vendors.
     */
    public function index(): JsonResponse
    {
        $vendors = Vendor::with(['vendorType', 'imageUpload'])->paginate(15);
        return response()->json($vendors);
    }

    /**
     * Store a newly created vendor in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'vendor_type' => 'nullable|exists:vendor_types,id',
            'published' => 'boolean',
            'delivered' => 'boolean',
            'phone' => 'required|string|max:255',
            'mobilephone' => 'required|string|max:255',
            'address' => 'required|string',
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
            'image_id' => 'nullable|exists:image_uploads,id',
            'desc' => 'required|string',
            'fee' => 'required|numeric|min:0',
            'percent' => 'boolean',
            'city' => 'required|string',
            'minAmount' => 'required|numeric|min:0',
            'minDiscountAmount' => 'required|numeric|min:0',
            'discountAmount' => 'required|integer|min:0',
            'commission' => 'integer|min:0',
            'tax' => 'integer|min:0',
            'perkm' => 'boolean',
            'flatrate' => 'boolean',
        ]);

        $vendor = Vendor::create($validated);
        return response()->json($vendor->load(['vendorType', 'imageUpload']), 201);
    }

    /**
     * Display the specified vendor.
     */
    public function show(Vendor $vendor): JsonResponse
    {
        return response()->json($vendor->load(['vendorType', 'imageUpload', 'products', 'categories']));
    }

    /**
     * Update the specified vendor in storage.
     */
    public function update(Request $request, Vendor $vendor): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'vendor_type' => 'sometimes|exists:vendor_types,id',
            'published' => 'sometimes|boolean',
            'delivered' => 'sometimes|boolean',
            'phone' => 'sometimes|string|max:255',
            'mobilephone' => 'sometimes|string|max:255',
            'address' => 'sometimes|string',
            'lat' => 'sometimes|numeric',
            'lng' => 'sometimes|numeric',
            'image_id' => 'sometimes|exists:image_uploads,id',
            'desc' => 'sometimes|string',
            'fee' => 'sometimes|numeric|min:0',
            'percent' => 'sometimes|boolean',
            'city' => 'sometimes|string',
            'minAmount' => 'sometimes|numeric|min:0',
            'minDiscountAmount' => 'sometimes|numeric|min:0',
            'discountAmount' => 'sometimes|integer|min:0',
            'commission' => 'sometimes|integer|min:0',
            'tax' => 'sometimes|integer|min:0',
            'perkm' => 'sometimes|boolean',
            'flatrate' => 'sometimes|boolean',
        ]);

        $vendor->update($validated);
        return response()->json($vendor->load(['vendorType', 'imageUpload']));
    }

    /**
     * Remove the specified vendor from storage.
     */
    public function destroy(Vendor $vendor): JsonResponse
    {
        $vendor->delete();
        return response()->json(['message' => 'Vendor deleted successfully']);
    }
}
