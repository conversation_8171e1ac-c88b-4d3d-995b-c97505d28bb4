<?php

namespace App\Http\Controllers;

use App\Models\BranchProduct;
use Illuminate\Http\Request;

class BranchProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(BranchProduct $branchProduct)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BranchProduct $branchProduct)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BranchProduct $branchProduct)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BranchProduct $branchProduct)
    {
        //
    }
}
