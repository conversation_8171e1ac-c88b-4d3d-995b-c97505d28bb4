<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiResponse;
use App\Models\VendorRegistration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class VendorRegistrationController extends Controller
{
    /**
     * Display a listing of the vendor registrations.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Fetches all vendor registrations from the database and passes them to the view.
        $registrations = VendorRegistration::all();
        return view('vendor-registrations.index', compact('registrations'));
    }

    /**
     * Show the form for creating a new vendor registration.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Displays the form for new vendor registration.
        return view('vendor-registrations.create');
    }

    /**
     * Store a newly created vendor registration in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Responses\ApiResponse
     */
    public function store(Request $request)
    {
        // Validate the incoming request data.
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:191',
            'email' => 'required|string|email|unique:vendor_registrations,email|max:191',
            'phone_number' => ['required', 'string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/'],
            'password' => 'required|string|min:8|confirmed', // Added password confirmation
            'business_name' => 'required|string|max:191',
            'business_description' => 'required|string|max:1000', // Added max length
            'business_email' => 'required|string|email|unique:vendor_registrations,business_email|max:191',
            'business_phone' => ['required', 'string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/'],
            'business_address' => 'required|string|max:500', // Added max length
            'woreda' => 'nullable|string|max:100',
            'house_number' => 'nullable|string|max:50',
            'website' => 'nullable|url|max:255',
            'tin_number' => ['required', 'string', 'max:50', 'unique:vendor_registrations,tin_number'],
            'tax_id' => ['required', 'string', 'max:255', 'unique:vendor_registrations,tax_id'],

            // File upload validations
            'trade_license_doc' => 'required|file|mimes:pdf,jpg,jpeg,png|max:5120', // 5MB max
            'tin_certificate_doc' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
            'id_card_doc' => 'required|file|mimes:pdf,jpg,jpeg,png|max:5120',

            'status' => ['nullable', Rule::in(['pending', 'approved', 'rejected', 'suspended'])],
        ], [
            // Custom error messages
            'phone_number.regex' => 'Please enter a valid phone number format.',
            'business_phone.regex' => 'Please enter a valid business phone number format.',
            'email.unique' => 'This email address is already registered.',
            'business_email.unique' => 'This business email address is already registered.',
            'tin_number.unique' => 'This TIN number is already registered.',
            'tax_id.unique' => 'This Tax ID is already registered.',
            'trade_license_doc.required' => 'Trade license document is required.',
            'trade_license_doc.mimes' => 'Trade license document must be a PDF, JPG, JPEG, or PNG file.',
            'trade_license_doc.max' => 'Trade license document must not exceed 5MB.',
        ]);

        if ($validator->fails()) {
            return ApiResponse::validationError($validator->errors()->toArray());
        }

        try {
            // Start database transaction
            DB::beginTransaction();

            // Retrieve the validated input
            $validatedData = $validator->validated();

            // Hash the password before saving it to the database
            $validatedData['password'] = Hash::make($validatedData['password']);

            // Remove password_confirmation from data if it exists
            unset($validatedData['password_confirmation']);

            // Set default status if not provided
            if (!isset($validatedData['status'])) {
                $validatedData['status'] = 'pending';
            }

            // Handle file uploads
            $uploadedFiles = [];

            if ($request->hasFile('trade_license_doc')) {
                $uploadedFiles['trade_license_doc'] = $this->uploadFile(
                    $request->file('trade_license_doc'),
                    'vendor-documents/trade-licenses'
                );
                $validatedData['trade_license_doc'] = $uploadedFiles['trade_license_doc'];
            }

            if ($request->hasFile('tin_certificate_doc')) {
                $uploadedFiles['tin_certificate_doc'] = $this->uploadFile(
                    $request->file('tin_certificate_doc'),
                    'vendor-documents/tin-certificates'
                );
                $validatedData['tin_certificate_doc'] = $uploadedFiles['tin_certificate_doc'];
            }

            if ($request->hasFile('id_card_doc')) {
                $uploadedFiles['id_card_doc'] = $this->uploadFile(
                    $request->file('id_card_doc'),
                    'vendor-documents/id-cards'
                );
                $validatedData['id_card_doc'] = $uploadedFiles['id_card_doc'];
            }

            // Sanitize phone_number
            // Remove all non-digit characters
            $cleaned = preg_replace('/\D/', '', $validatedData['phone_number']);
            // Remove any leading zeros
            $cleaned = ltrim($cleaned, '0');
            $validatedData['phone_number'] = '251' . substr($cleaned, -9);

            // Sanitize business_phone
            // Remove all non-digit characters
            $cleaned = preg_replace('/\D/', '', $validatedData['business_phone']);
            // Remove any leading zeros
            $cleaned = ltrim($cleaned, '0');
            $validatedData['business_phone'] = '251' . substr($cleaned, -9);

            // Create the new vendor registration record
            $vendorRegistration = VendorRegistration::create($validatedData);

            // Commit the transaction
            DB::commit();

            // Log successful registration
            Log::info('Vendor registration created successfully', [
                'vendor_id' => $vendorRegistration->id,
                'email' => $vendorRegistration->email,
                'business_name' => $vendorRegistration->business_name
            ]);

            // Return success response with the created data (excluding sensitive info)
            return ApiResponse::created([
                'vendor_registration' => $vendorRegistration->makeHidden(['password']),
                'message' => 'Vendor registration submitted successfully. Your application is under review.',
                'registration_id' => $vendorRegistration->id
            ]);
        } catch (\Illuminate\Database\QueryException $e) {
            // Rollback transaction
            DB::rollback();

            // Clean up uploaded files if database operation failed
            $this->cleanupUploadedFiles($uploadedFiles ?? []);

            // Log database error
            Log::error('Database error during vendor registration: ' . $e->getMessage(), [
                'request_data' => $request->except(['password', 'password_confirmation']),
                'exception' => $e
            ]);

            // Check for specific database errors
            if ($e->getCode() === '23000') {
                return ApiResponse::conflict('A registration with this information already exists.');
            }

            return ApiResponse::internalServerError('Database error occurred while creating vendor registration.');
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollback();

            // Clean up uploaded files if operation failed
            $this->cleanupUploadedFiles($uploadedFiles ?? []);

            // Log the error for debugging
            Log::error('Vendor registration creation failed: ' . $e->getMessage(), [
                'request_data' => $request->except(['password', 'password_confirmation']),
                'exception' => $e
            ]);

            return ApiResponse::internalServerError('Failed to create vendor registration. Please try again.');
        }
    }

    /**
     * Upload file to storage and return the path
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string $directory
     * @return string
     */
    private function uploadFile($file, string $directory): string
    {
        // Generate unique filename
        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

        // Store file and return path
        return $file->storeAs($directory, $filename, 'public');
    }

    /**
     * Clean up uploaded files in case of failure
     *
     * @param array $filePaths
     * @return void
     */
    private function cleanupUploadedFiles(array $filePaths): void
    {
        foreach ($filePaths as $path) {
            if ($path && Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
            }
        }
    }

    /**
     * Display the specified vendor registration.
     *
     * @param  \App\Models\VendorRegistration  $vendorRegistration
     * @return ApiResponse
     */
    public function show(VendorRegistration $vendorRegistration)
    {
        return ApiResponse::ok($vendorRegistration->toArray());
    }

    /**
     * Show the form for editing the specified vendor registration.
     *
     * @param  \App\Models\VendorRegistration  $vendorRegistration
     * @return \Illuminate\View\View
     */
    public function edit(VendorRegistration $vendorRegistration)
    {
        // Displays the form to edit an existing vendor registration.
        return view('vendor-registrations.edit', compact('vendorRegistration'));
    }

    /**
     * Update the specified vendor registration in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\VendorRegistration  $vendorRegistration
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, VendorRegistration $vendorRegistration)
    {
        // Validate the incoming request data, excluding the unique email rule for the current record.
        $validatedData = $request->validate([
            'name' => 'required|string|max:191',
            'email' => ['required', 'string', 'email', 'max:191', Rule::unique('vendor_registrations')->ignore($vendorRegistration->id)],
            'phone_number' => 'required|string|max:20',
            'password' => 'nullable|string|min:8', // Password can be null for updates
            'business_name' => 'required|string|max:191',
            'business_description' => 'required|string',
            'business_email' => 'required|string|email|max:191',
            'business_phone' => 'required|string|max:20',
            'business_address' => 'required|string',
            'woreda' => 'nullable|string|max:100',
            'house_number' => 'nullable|string|max:50',
            'website' => 'nullable|string|max:255',
            'tin_number' => 'required|string|max:50',
            'tax_id' => 'required|string|max:255',
            'trade_license_doc' => 'required|string|max:255',
            'tin_certificate_doc' => 'nullable|string|max:255',
            'id_card_doc' => 'nullable|string|max:255',
            'status' => ['nullable', Rule::in(['pending', 'approved', 'rejected', 'suspended'])],
        ]);

        // If a new password is provided, hash it before updating.
        if (!empty($validatedData['password'])) {
            $validatedData['password'] = Hash::make($validatedData['password']);
        } else {
            unset($validatedData['password']);
        }

        // Update the vendor registration record.
        $vendorRegistration->update($validatedData);

        // Redirect with a success message.
        return redirect()->route('vendor-registrations.index')->with('success', 'Vendor registration updated successfully!');
    }

    /**
     * Remove the specified vendor registration from storage.
     *
     * @param  \App\Models\VendorRegistration  $vendorRegistration
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(VendorRegistration $vendorRegistration)
    {
        // Deletes the specified vendor registration record.
        $vendorRegistration->delete();

        // Redirect with a success message.
        return redirect()->route('vendor-registrations.index')->with('success', 'Vendor registration deleted successfully!');
    }
}
