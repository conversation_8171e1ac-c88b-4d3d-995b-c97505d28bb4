<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentGateway extends Model
{
    /** @use HasFactory<\Database\Factories\PaymentGatewayFactory> */
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'payment_gateways';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'api_key',
        'payment_method_id',
        'superadmin_status',
        'vendor_status',
        'vendor_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'superadmin_status' => 'boolean',
        'vendor_status' => 'boolean',
    ];

    /**
     * Get the payment method that this gateway belongs to.
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }

    /**
     * Get the vendor (company) that this gateway belongs to.
     */
    public function vendor(): BelongsTo
    {
        // Note: The schema specifies 'companies' table for the vendor.
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }
}
