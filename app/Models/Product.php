<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Product extends Model
{
    /** @use HasFactory<\Database\Factories\ProductFactory> */
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'products';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'price',
        'discount_price',
        'description',
        'brand_id',
        'category_id',
        'unit',
        'package_count',
        'weight',
        'can_deliver',
        'stars',
        'featured',
        'published',
        'stock_item',
        'vendor_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'can_deliver' => 'boolean',
        'featured' => 'boolean',
        'published' => 'boolean',
        'stock_item' => 'integer',
    ];

    /**
     * Get the brand that owns the product.
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the category that owns the product.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the vendor that owns the product.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the images for the product.
     */
    public function images(): HasMany
    {
        return $this->hasMany(ImageUpload::class);
    }

    /**
     * The ingredients that belong to the product.
     */
    public function ingredients(): BelongsToMany
    {
        return $this->belongsToMany(Ingredient::class);
    }

    /**
     * The extras that belong to the product.
     */
    public function extras(): BelongsToMany
    {
        return $this->belongsToMany(Extra::class);
    }

    /**
     * The related products for the product.
     */
    public function recommendedProducts(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'recommended_products', 'product_id', 'recommended_product_id');
    }
}
