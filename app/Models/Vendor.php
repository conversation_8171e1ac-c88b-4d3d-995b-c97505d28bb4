<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Vendor extends Model
{
    /** @use HasFactory<\Database\Factories\VendorFactory> */
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'vendor_type',
        'published',
        'delivered',
        'phone',
        'mobilephone',
        'address',
        'lat',
        'lng',
        'image_id',
        'desc',
        'fee',
        'percent',
        'openTimeMonday',
        'closeTimeMonday',
        'openTimeTuesday',
        'closeTimeTuesday',
        'openTimeWednesday',
        'closeTimeWednesday',
        'openTimeThursday',
        'closeTimeThursday',
        'openTimeFriday',
        'closeTimeFriday',
        'openTimeSaturday',
        'closeTimeSaturday',
        'openTimeSunday',
        'closeTimeSunday',
        'area',
        'minAmount',
        'minDiscountAmount',
        'discountAmount',
        'commission',
        'tax',
        'perkm',
        'flatrate',
        'city',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'published' => 'boolean',
        'delivered' => 'boolean',
        'percent' => 'boolean',
        'perkm' => 'boolean',
        'flatrate' => 'boolean',
        'fee' => 'decimal:2',
        'minAmount' => 'decimal:2',
        'minDiscountAmount' => 'decimal:2',
        'discountAmount' => 'integer',
        'commission' => 'integer',
        'tax' => 'integer',
        'area' => 'integer',
    ];

    /**
     * Get the vendor type that the vendor belongs to.
     */
    public function vendorType(): BelongsTo
    {
        return $this->belongsTo(VendorType::class, 'vendor_type');
    }

    /**
     * Get the image upload that the vendor belongs to.
     */
    public function imageUpload(): BelongsTo
    {
        return $this->belongsTo(ImageUpload::class, 'image_id');
    }

    /**
     * Get the products for the vendor.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the categories for the vendor.
     */
    public function categories(): HasMany
    {
        return $this->hasMany(Category::class);
    }

    /**
     * Get the users for the vendor.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the image uploads for the vendor.
     */
    public function imageUploads(): HasMany
    {
        return $this->hasMany(ImageUpload::class);
    }
}
