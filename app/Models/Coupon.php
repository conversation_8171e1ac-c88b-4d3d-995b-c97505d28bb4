<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Coupon extends Model
{
    /** @use HasFactory<\Database\Factories\CouponFactory> */
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'coupons';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'date_start',
        'date_end',
        'discount',
        'published',
        'in_percents',
        'amount',
        'description',
        'all_vendors',
        'all_categories',
        'all_products',
        'vendor_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'date_start' => 'datetime',
        'date_end' => 'datetime',
        'discount' => 'decimal:2',
        'published' => 'boolean',
        'in_percents' => 'boolean',
        'amount' => 'decimal:2',
        'all_vendors' => 'boolean',
        'all_categories' => 'boolean',
        'all_products' => 'boolean',
    ];

    /**
     * Get the vendor that owns the coupon.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * The categories that are included in the coupon.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class);
    }

    /**
     * The products that are included in the coupon.
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class);
    }
}
