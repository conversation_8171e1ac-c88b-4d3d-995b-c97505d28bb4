# Reusable Components Usage Guide

## Overview
This guide demonstrates how to use the reusable Blade components created for the E2 Admin Dashboard. All components are based on Bootstrap 5 and follow consistent design patterns.

## 📁 Component Organization

```
resources/views/components/
├── navigation/
│   ├── sidebar.blade.php
│   ├── navbar.blade.php
│   └── footer.blade.php
├── ui/
│   ├── button.blade.php
│   ├── card.blade.php
│   ├── alert.blade.php
│   ├── badge.blade.php
│   ├── modal.blade.php
│   ├── table.blade.php
│   ├── progress.blade.php
│   ├── spinner.blade.php
│   └── toast.blade.php
└── forms/
    ├── input.blade.php
    ├── select.blade.php
    ├── textarea.blade.php
    ├── checkbox.blade.php
    └── radio.blade.php
```

## 🎨 UI Components

### Button Component
```blade
{{-- Basic button --}}
<x-ui.button>Click me</x-ui.button>

{{-- Button with variants --}}
<x-ui.button variant="success" size="lg" icon="bx bx-plus">
    Add New
</x-ui.button>

{{-- Link button --}}
<x-ui.button href="/dashboard" variant="outline-primary">
    Go to Dashboard
</x-ui.button>

{{-- Disabled button --}}
<x-ui.button variant="danger" disabled>
    Delete
</x-ui.button>
```

**Available Props:**
- `variant`: primary, secondary, success, danger, warning, info, light, dark, outline-*
- `size`: sm, md, lg
- `disabled`: true/false
- `href`: URL for link buttons
- `icon`: Icon class (e.g., "bx bx-plus")
- `iconPosition`: left, right

### Card Component
```blade
{{-- Basic card --}}
<x-ui.card title="Card Title">
    <p>Card content goes here.</p>
</x-ui.card>

{{-- Card with subtitle and footer --}}
<x-ui.card 
    title="User Profile" 
    subtitle="Manage user information"
    variant="primary">
    <p>User details...</p>
    
    <x-slot name="footer">
        <x-ui.button variant="success">Save</x-ui.button>
        <x-ui.button variant="secondary">Cancel</x-ui.button>
    </x-slot>
</x-ui.card>
```

**Available Props:**
- `title`: Card title
- `subtitle`: Card subtitle
- `variant`: primary, secondary, success, danger, warning, info, light, dark
- `border`: true/false
- `shadow`: true/false
- `padding`: true/false

### Alert Component
```blade
{{-- Basic alert --}}
<x-ui.alert type="success">
    Operation completed successfully!
</x-ui.alert>

{{-- Dismissible alert with icon --}}
<x-ui.alert type="warning" dismissible icon="bx bx-warning">
    <strong>Warning!</strong> Please review your input.
</x-ui.alert>

{{-- Alert with title --}}
<x-ui.alert type="danger" title="Error" dismissible>
    An error occurred while processing your request.
</x-ui.alert>
```

**Available Props:**
- `type`: primary, secondary, success, danger, warning, info, light, dark
- `dismissible`: true/false
- `icon`: Custom icon class
- `title`: Alert title

### Badge Component
```blade
{{-- Basic badge --}}
<x-ui.badge type="success">Active</x-ui.badge>

{{-- Badge with different styles --}}
<x-ui.badge type="primary" style="outline">New</x-ui.badge>
<x-ui.badge type="warning" style="label" shape="pill">Pending</x-ui.badge>

{{-- Circle badge --}}
<x-ui.badge type="danger" shape="circle" size="lg">5</x-ui.badge>

{{-- Link badge --}}
<x-ui.badge href="/notifications" type="info">View All</x-ui.badge>
```

**Available Props:**
- `type`: primary, secondary, success, danger, warning, info, light, dark
- `style`: solid, outline, label
- `shape`: default, pill, circle
- `size`: default, sm, lg
- `href`: URL for link badges

### Modal Component
```blade
{{-- Basic modal --}}
<x-ui.modal id="exampleModal" title="Example Modal">
    <p>Modal content goes here.</p>
</x-ui.modal>

{{-- Modal with custom footer --}}
<x-ui.modal id="confirmModal" title="Confirm Action" size="sm" centered>
    <p>Are you sure you want to delete this item?</p>
    
    <x-slot name="footer">
        <x-ui.button variant="danger" data-bs-dismiss="modal">Delete</x-ui.button>
        <x-ui.button variant="secondary" data-bs-dismiss="modal">Cancel</x-ui.button>
    </x-slot>
</x-ui.modal>

{{-- Large scrollable modal --}}
<x-ui.modal id="largeModal" title="Large Content" size="lg" scrollable>
    <div style="height: 1000px;">
        <!-- Large content -->
    </div>
</x-ui.modal>
```

**Available Props:**
- `id`: Modal ID (required)
- `title`: Modal title
- `size`: sm, default, lg, xl
- `centered`: true/false
- `scrollable`: true/false
- `staticBackdrop`: true/false
- `closeButton`: true/false
- `saveButton`: true/false

### Table Component
```blade
{{-- Basic table --}}
<x-ui.table>
    <thead>
        <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Status</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>John Doe</td>
            <td><EMAIL></td>
            <td><x-ui.badge type="success">Active</x-ui.badge></td>
        </tr>
    </tbody>
</x-ui.table>

{{-- Advanced table --}}
<x-ui.table 
    striped 
    hover 
    bordered 
    responsive 
    caption="User List">
    <!-- Table content -->
</x-ui.table>
```

**Available Props:**
- `striped`: true/false
- `bordered`: true/false
- `hover`: true/false
- `responsive`: true/false
- `size`: sm, default, lg
- `variant`: primary, secondary, success, danger, warning, info, light, dark
- `caption`: Table caption

### Progress Component
```blade
{{-- Basic progress bar --}}
<x-ui.progress value="75" />

{{-- Progress with label --}}
<x-ui.progress value="60" variant="success" label="60% Complete" />

{{-- Animated progress --}}
<x-ui.progress value="45" type="animated" variant="warning" />

{{-- Multiple progress bars --}}
<div>
    <x-ui.progress value="25" size="sm" />
    <x-ui.progress value="50" size="default" />
    <x-ui.progress value="75" size="lg" />
</div>
```

**Available Props:**
- `value`: Progress value (0-100)
- `min`: Minimum value
- `max`: Maximum value
- `type`: default, striped, animated
- `size`: sm, default, lg
- `variant`: primary, secondary, success, danger, warning, info, light, dark
- `label`: Custom label
- `showValue`: true/false

### Spinner Component
```blade
{{-- Basic spinner --}}
<x-ui.spinner />

{{-- Spinner with text --}}
<x-ui.spinner text="Loading..." />

{{-- Different types and sizes --}}
<x-ui.spinner type="grow" size="lg" variant="success" />
<x-ui.spinner type="border" size="sm" variant="warning" />
```

**Available Props:**
- `type`: border, grow
- `size`: sm, default, lg
- `variant`: primary, secondary, success, danger, warning, info, light, dark
- `text`: Loading text
- `role`: ARIA role

### Toast Component
```blade
{{-- Basic toast --}}
<x-ui.toast id="successToast" type="success">
    Operation completed successfully!
</x-ui.toast>

{{-- Toast with title and custom delay --}}
<x-ui.toast 
    id="warningToast" 
    type="warning" 
    title="Warning" 
    delay="3000">
    Please review your input before proceeding.
</x-ui.toast>

{{-- Toast without auto-hide --}}
<x-ui.toast 
    id="infoToast" 
    type="info" 
    autohide="false">
    This toast will not auto-hide.
</x-ui.toast>
```

**Available Props:**
- `id`: Toast ID (required)
- `title`: Toast title
- `subtitle`: Toast subtitle
- `type`: default, success, danger, warning, info
- `autohide`: true/false
- `delay`: Auto-hide delay in milliseconds
- `animation`: true/false
- `closeButton`: true/false
- `icon`: Custom icon class

## 📝 Form Components

### Input Component
```blade
{{-- Basic input --}}
<x-forms.input name="email" label="Email Address" type="email" required />

{{-- Input with help text --}}
<x-forms.input 
    name="username" 
    label="Username" 
    placeholder="Enter your username"
    help="Username must be at least 3 characters long." />

{{-- Input with prepend/append --}}
<x-forms.input 
    name="price" 
    label="Price" 
    prepend="$" 
    append=".00" />

{{-- Input with error --}}
<x-forms.input 
    name="email" 
    label="Email" 
    error="Please enter a valid email address." />
```

**Available Props:**
- `name`: Field name (required)
- `label`: Field label
- `type`: Input type (text, email, password, etc.)
- `placeholder`: Placeholder text
- `value`: Default value
- `required`: true/false
- `disabled`: true/false
- `readonly`: true/false
- `help`: Help text
- `error`: Error message
- `size`: sm, md, lg
- `prepend`: Prepend text
- `append`: Append text

### Select Component
```blade
{{-- Basic select --}}
<x-forms.select 
    name="country" 
    label="Country"
    :options="['us' => 'United States', 'ca' => 'Canada', 'uk' => 'United Kingdom']" />

{{-- Select with placeholder --}}
<x-forms.select 
    name="category" 
    label="Category"
    placeholder="Select a category"
    :options="$categories" />

{{-- Multiple select --}}
<x-forms.select 
    name="tags[]" 
    label="Tags"
    multiple
    :options="$tags" />

{{-- Select with optgroups --}}
<x-forms.select 
    name="product" 
    label="Product"
    :options="[
        'Electronics' => ['phone' => 'Phone', 'laptop' => 'Laptop'],
        'Clothing' => ['shirt' => 'Shirt', 'pants' => 'Pants']
    ]" />
```

**Available Props:**
- `name`: Field name (required)
- `label`: Field label
- `placeholder`: Placeholder text
- `options`: Array of options
- `selected`: Selected value(s)
- `required`: true/false
- `disabled`: true/false
- `multiple`: true/false
- `size`: sm, md, lg
- `help`: Help text
- `error`: Error message

### Textarea Component
```blade
{{-- Basic textarea --}}
<x-forms.textarea 
    name="description" 
    label="Description" 
    rows="4" />

{{-- Textarea with placeholder --}}
<x-forms.textarea 
    name="bio" 
    label="Biography"
    placeholder="Tell us about yourself..."
    rows="6" />

{{-- Large textarea --}}
<x-forms.textarea 
    name="content" 
    label="Content"
    size="lg"
    rows="10" />
```

**Available Props:**
- `name`: Field name (required)
- `label`: Field label
- `placeholder`: Placeholder text
- `value`: Default value
- `rows`: Number of rows
- `cols`: Number of columns
- `required`: true/false
- `disabled`: true/false
- `readonly`: true/false
- `help`: Help text
- `error`: Error message
- `size`: sm, md, lg

### Checkbox Component
```blade
{{-- Basic checkbox --}}
<x-forms.checkbox 
    name="terms" 
    label="I agree to the terms and conditions" 
    required />

{{-- Inline checkbox --}}
<x-forms.checkbox 
    name="newsletter" 
    label="Subscribe to newsletter" 
    inline />

{{-- Checkbox with custom value --}}
<x-forms.checkbox 
    name="permissions[]" 
    value="admin" 
    label="Admin Access" />
```

**Available Props:**
- `name`: Field name (required)
- `value`: Checkbox value
- `label`: Checkbox label
- `checked`: true/false
- `required`: true/false
- `disabled`: true/false
- `help`: Help text
- `error`: Error message
- `inline`: true/false

### Radio Component
```blade
{{-- Basic radio --}}
<x-forms.radio 
    name="gender" 
    value="male" 
    label="Male" />

{{-- Inline radio buttons --}}
<x-forms.radio 
    name="gender" 
    value="female" 
    label="Female" 
    inline />

{{-- Radio with custom value --}}
<x-forms.radio 
    name="status" 
    value="active" 
    label="Active" />
```

**Available Props:**
- `name`: Field name (required)
- `value`: Radio value (required)
- `label`: Radio label
- `checked`: true/false
- `required`: true/false
- `disabled`: true/false
- `help`: Help text
- `error`: Error message
- `inline`: true/false

## 🧭 Navigation Components

### Using the Layout
```blade
@extends('layouts.app')

@section('title', 'Dashboard')

@section('content')
    <div class="row">
        <div class="col-12">
            <h1>Welcome to Dashboard</h1>
            <!-- Your content here -->
        </div>
    </div>
@endsection
```

### Customizing Navigation
The navigation components (`sidebar.blade.php`, `navbar.blade.php`, `footer.blade.php`) can be customized by editing the files directly or by creating new versions.

## 🎯 Best Practices

1. **Consistent Naming**: Use consistent naming conventions for component props
2. **Accessibility**: All components include proper ARIA attributes
3. **Responsive Design**: Components are built with responsive design in mind
4. **Error Handling**: Form components include built-in error handling
5. **Validation**: Use Laravel's validation with form components
6. **Reusability**: Components are designed to be highly reusable

## 🔧 Customization

### Creating Custom Variants
You can extend components by creating custom variants:

```blade
{{-- Custom button variant --}}
<x-ui.button class="btn-custom">
    Custom Button
</x-ui.button>
```

### Styling
All components use Bootstrap 5 classes and can be customized with CSS:

```css
.btn-custom {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border: none;
    color: white;
}
```

## 📚 Examples

### Complete Form Example
```blade
@extends('layouts.app')

@section('content')
    <x-ui.card title="User Registration" subtitle="Create a new user account">
        <form method="POST" action="{{ route('users.store') }}">
            @csrf
            
            <div class="row">
                <div class="col-md-6">
                    <x-forms.input 
                        name="first_name" 
                        label="First Name" 
                        required />
                </div>
                <div class="col-md-6">
                    <x-forms.input 
                        name="last_name" 
                        label="Last Name" 
                        required />
                </div>
            </div>
            
            <x-forms.input 
                name="email" 
                label="Email Address" 
                type="email" 
                required />
            
            <x-forms.select 
                name="role" 
                label="Role"
                :options="['user' => 'User', 'admin' => 'Administrator']" />
            
            <x-forms.textarea 
                name="notes" 
                label="Notes" 
                rows="3" />
            
            <x-forms.checkbox 
                name="active" 
                label="Account is active" />
            
            <div class="mt-3">
                <x-ui.button type="submit" variant="success">
                    Create User
                </x-ui.button>
                <x-ui.button href="{{ route('users.index') }}" variant="secondary">
                    Cancel
                </x-ui.button>
            </div>
        </form>
    </x-ui.card>
@endsection
```

This comprehensive component system provides a solid foundation for building consistent, maintainable, and accessible user interfaces in your Laravel application.
