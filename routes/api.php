<?php

use App\Http\Controllers\Api\VendorRegistrationController;
use App\Http\Controllers\VendorController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Vendor Registration Routes
// GET: Retrieve a list of all vendor registrations.
// Route::get('vendors', [VendorRegistrationController::class, 'index']);

// POST: Create a new vendor registration.
Route::post('vendors', [VendorRegistrationController::class, 'store']);

// GET: Retrieve the details of a specific vendor registration.
Route::get('vendors/{vendorRegistration}', [VendorRegistrationController::class, 'show']);

// PUT/PATCH: Update an existing vendor registration.
Route::put('vendors/{vendorRegistration}', [VendorRegistrationController::class, 'update']);
Route::patch('vendors/{vendorRegistration}', [VendorRegistrationController::class, 'update']);

// DELETE: Delete a vendor registration.
Route::delete('vendors/{vendorRegistration}', [VendorRegistrationController::class, 'destroy']);
