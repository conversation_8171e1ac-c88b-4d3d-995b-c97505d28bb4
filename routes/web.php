<?php

use Illuminate\Support\Facades\Route;

// Auth routes (placeholders)
Route::get('/login', function () {
    return view('components.pages.auth.login');
})->name('login');

Route::get('/register', function () {
    return view('components.pages.auth.register');
})->name('register');

Route::get('/password/reset', function () {
    return view('components.pages.auth.forgot-password');
})->name('password.request');

// Dashboard
Route::get('/', function () {
    return view('pages.dashboard');
})->middleware(['auth', 'permission:view dashboard'])->name('dashboard');

// Vendors
Route::get('/vendors', function () {
    return view('pages.vendors.index');
})->middleware(['auth', 'permission:view vendors'])->name('vendors.index');

Route::get('/vendors/create', function () {
    return view('pages.vendors.create');
})->middleware(['auth', 'permission:create vendors'])->name('vendors.create');

// Vendor registrations
Route::get('/vendor-registrations', function () {
    return view('pages.vendor-registrations.index');
})->middleware(['auth', 'permission:view vendor registrations'])->name('vendor-registrations.index');

// Products
Route::get('/products', function () {
    return view('pages.products.index');
})->middleware(['auth', 'permission:view products'])->name('products.index');

Route::get('/products/create', function () {
    return view('pages.products.create');
})->middleware(['auth', 'permission:create products'])->name('products.create');

// Categories
Route::get('/categories', function () {
    return view('pages.categories.index');
})->middleware(['auth', 'permission:view categories'])->name('categories.index');

// Orders
Route::get('/orders', function () {
    return view('pages.orders.index');
})->middleware(['auth', 'permission:view orders'])->name('orders.index');

// Users
Route::get('/users', function () {
    return view('pages.users.index');
})->middleware(['auth', 'permission:view users'])->name('users.index');

// Settings
Route::get('/settings/general', function () {
    return view('pages.settings.general');
})->middleware(['auth', 'permission:manage settings'])->name('settings.general');

Route::get('/settings/notifications', function () {
    return view('pages.settings.notifications');
})->middleware(['auth', 'permission:manage settings'])->name('settings.notifications');

// Profile
Route::get('/profile/edit', function () {
    return view('pages.profile.edit');
})->middleware(['auth'])->name('profile.edit');

// Logout (placeholder)
Route::post('/logout', function () {
    return redirect()->route('dashboard');
})->middleware(['auth'])->name('logout');

// Migrated pages (batch 1)
Route::get('/coupons', fn() => view('pages.coupons.index'))->middleware(['auth', 'permission:view coupons'])->name('coupons.index');
Route::get('/banners', fn() => view('pages.banners.index'))->middleware(['auth', 'permission:view banners'])->name('banners.index');
Route::get('/media', fn() => view('pages.media.index'))->middleware(['auth', 'permission:view media'])->name('media.index');
Route::get('/reviews', fn() => view('pages.reviews.index'))->middleware(['auth', 'permission:view reviews'])->name('reviews.index');
Route::get('/payments', fn() => view('pages.payments.index'))->middleware(['auth', 'permission:view payments'])->name('payments.index');
Route::get('/roles', fn() => view('pages.roles.index'))->middleware(['auth', 'role:Admin'])->name('roles.index');
Route::get('/wallet', fn() => view('pages.wallet.index'))->middleware(['auth', 'permission:view wallet'])->name('wallet.index');
Route::get('/chat', fn() => view('pages.chat.index'))->middleware(['auth', 'permission:view chat'])->name('chat.index');
Route::get('/payment-methods', fn() => view('pages.payment-methods.index'))->middleware(['auth', 'permission:view payment methods'])->name('payment-methods.index');
Route::get('/payment-gateways', fn() => view('pages.payment-gateways.index'))->middleware(['auth', 'permission:view payment gateways'])->name('payment-gateways.index');

// Migrated pages (batch 2)
Route::middleware([])->group(function () {
    Route::get('/admin', fn() => view('pages.admin.index'))->middleware('role:Admin')->name('admin.index');
    Route::get('/advert', fn() => view('pages.advert.index'))->middleware('permission:view advert')->name('advert.index');
    Route::get('/ap-settings', fn() => view('pages.ap-settings.index'))->middleware('permission:manage settings')->name('ap-settings.index');
    Route::get('/branch', fn() => view('pages.branch.index'))->middleware('permission:view branches')->name('branch.index');
    Route::get('/brand', fn() => view('pages.brand.index'))->middleware('permission:view brands')->name('brand.index');
    Route::get('/bulk-upload', fn() => view('pages.bulk-upload.index'))->middleware('permission:bulk upload')->name('bulk-upload.index');
    Route::get('/ca-layout', fn() => view('pages.ca-layout.index'))->middleware('permission:manage appearance')->name('ca-layout.index');
    Route::get('/ca-layout-colors', fn() => view('pages.ca-layout-colors.index'))->middleware('permission:manage appearance')->name('ca-layout-colors.index');
    Route::get('/ca-layout-sizes', fn() => view('pages.ca-layout-sizes.index'))->middleware('permission:manage appearance')->name('ca-layout-sizes.index');
    Route::get('/ca-skin', fn() => view('pages.ca-skin.index'))->middleware('permission:manage appearance')->name('ca-skin.index');
    Route::get('/ca-theme', fn() => view('pages.ca-theme.index'))->middleware('permission:manage appearance')->name('ca-theme.index');
    Route::get('/city', fn() => view('pages.city.index'))->middleware('permission:view cities')->name('city.index');
    Route::get('/companies', fn() => view('pages.companies.index'))->middleware('permission:view companies')->name('companies.index');
    Route::get('/companies-reviews', fn() => view('pages.companies-reviews.index'))->middleware('permission:view company reviews')->name('companies-reviews.index');
    Route::get('/currencies', fn() => view('pages.currencies.index'))->middleware('permission:view currencies')->name('currencies.index');
    Route::get('/documents', fn() => view('pages.documents.index'))->middleware('permission:view documents')->name('documents.index');
    Route::get('/drivers', fn() => view('pages.drivers.index'))->middleware('permission:view drivers')->name('drivers.index');
    Route::get('/extras', fn() => view('pages.extras.index'))->middleware('permission:view extras')->name('extras.index');
    Route::get('/extras-group', fn() => view('pages.extras-group.index'))->middleware('permission:view extra groups')->name('extras-group.index');
    Route::get('/faq', fn() => view('pages.faq.index'))->middleware('permission:view faq')->name('faq.index');
    Route::get('/home', fn() => view('pages.home.index'))->middleware('permission:view dashboard')->name('home.index');
    Route::get('/ingredient-group', fn() => view('pages.ingredient-group.index'))->middleware('permission:view ingredient groups')->name('ingredient-group.index');
    Route::get('/ingredients', fn() => view('pages.ingredients.index'))->middleware('permission:view ingredients')->name('ingredients.index');
    Route::get('/markets-review', fn() => view('pages.markets-review.index'))->middleware('permission:view markets')->name('markets-review.index');
    Route::get('/menu-employee', fn() => view('pages.menu-employee.index'))->middleware('permission:view menus')->name('menu-employee.index');
    Route::get('/menu-owner', fn() => view('pages.menu-owner.index'))->middleware('permission:view menus')->name('menu-owner.index');
    Route::get('/menu-vendor', fn() => view('pages.menu-vendor.index'))->middleware('permission:view menus')->name('menu-vendor.index');
    Route::get('/most-popular', fn() => view('pages.most-popular.index'))->middleware('permission:view analytics')->name('most-popular.index');
    Route::get('/most-purchase', fn() => view('pages.most-purchase.index'))->middleware('permission:view analytics')->name('most-purchase.index');
    Route::get('/movet-interprise', fn() => view('pages.movet-interprise.index'))->middleware('permission:view settings')->name('movet-interprise.index');
    Route::get('/notify', fn() => view('pages.notify.index'))->middleware('permission:send notifications')->name('notify.index');
    Route::get('/order-statuses', fn() => view('pages.order-statuses.index'))->middleware('permission:view order statuses')->name('order-statuses.index');
    Route::get('/product-ingredients', fn() => view('pages.product-ingredients.index'))->middleware('permission:view product ingredients')->name('product-ingredients.index');
    Route::get('/products-tree', fn() => view('pages.products-tree.index'))->middleware('permission:view products')->name('products-tree.index');
    Route::get('/settings', fn() => view('pages.settings.index'))->middleware('permission:manage settings')->name('settings.index');
    Route::get('/telebirr-api', fn() => view('pages.telebirr-api.index'))->middleware('permission:manage payments')->name('telebirr-api.index');
    Route::get('/top-companies', fn() => view('pages.top-companies.index'))->middleware('permission:view analytics')->name('top-companies.index');
    Route::get('/top-companies-2', fn() => view('pages.top-companies-2.index'))->middleware('permission:view analytics')->name('top-companies-2.index');
    Route::get('/top-products', fn() => view('pages.top-products.index'))->middleware('permission:view analytics')->name('top-products.index');
    Route::get('/transactions', fn() => view('pages.transactions.index'))->middleware('permission:view transactions')->name('transactions.index');
    Route::get('/vendor-type', fn() => view('pages.vendor-type.index'))->middleware('permission:view vendor types')->name('vendor-type.index');
    Route::get('/vendor-banners', fn() => view('pages.vendor-banners.index'))->middleware('permission:view banners')->name('vendor-banners.index');
    Route::get('/vendor-market', fn() => view('pages.vendor-market.index'))->middleware('permission:view markets')->name('vendor-market.index');
    Route::get('/vendors-market', fn() => view('pages.vendors-market.index'))->middleware('permission:view markets')->name('vendors-market.index');
    Route::get('/web-seller', fn() => view('pages.web-seller.index'))->middleware('permission:view sellers')->name('web-seller.index');
    Route::get('/web-settings', fn() => view('pages.web-settings.index'))->middleware('permission:manage settings')->name('web-settings.index');
});
