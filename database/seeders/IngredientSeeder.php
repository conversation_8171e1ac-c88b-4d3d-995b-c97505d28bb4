<?php

namespace Database\Seeders;

use App\Models\Ingredient;
use App\Models\IngredientGroup;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class IngredientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create ingredients for each ingredient group
        IngredientGroup::all()->each(function ($ingredientGroup) {
            Ingredient::factory()->count(fake()->numberBetween(3, 8))->create([
                'ingredient_group_id' => $ingredientGroup->id,
                'vendor_id' => $ingredientGroup->vendor_id,
            ]);
        });

        // Create additional ingredients
        Ingredient::factory()->count(100)->create();
    }
}
