<?php

namespace Database\Seeders;

use App\Models\Branch;
use App\Models\BranchProduct;
use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BranchProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create branch products for each branch
        Branch::all()->each(function ($branch) {
            $products = Product::inRandomOrder()->take(fake()->numberBetween(5, 15))->get();
            
            foreach ($products as $product) {
                BranchProduct::factory()->create([
                    'branch_id' => $branch->id,
                    'product_id' => $product->id,
                ]);
            }
        });

        // Create additional branch products
        BranchProduct::factory()->count(200)->create();
    }
}
