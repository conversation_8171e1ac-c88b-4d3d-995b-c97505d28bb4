<?php

namespace Database\Seeders;

use App\Models\Banner;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BannerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create banners for each vendor
        Vendor::all()->each(function ($vendor) {
            Banner::factory()->count(fake()->numberBetween(2, 5))->create([
                'vendor_id' => $vendor->id,
            ]);
        });

        // Create additional banners
        Banner::factory()->count(30)->create();
    }
}
