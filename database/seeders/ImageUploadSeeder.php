<?php

namespace Database\Seeders;

use App\Models\ImageUpload;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ImageUploadSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create images for vendors
        Vendor::all()->each(function ($vendor) {
            ImageUpload::factory()->count(fake()->numberBetween(1, 3))->create([
                'vendor_id' => $vendor->id,
            ]);
        });

        // Create images for products
        Product::all()->each(function ($product) {
            ImageUpload::factory()->count(fake()->numberBetween(1, 5))->create([
                'product_id' => $product->id,
            ]);
        });

        // Create images for products
        ProductVariant::all()->each(function ($productVariant) {
            ImageUpload::factory()->count(fake()->numberBetween(1, 5))->create([
                'variant_id' => $productVariant->id,
            ]);
        });

        // Create some standalone images
        ImageUpload::factory()->count(50)->create();
    }
}
