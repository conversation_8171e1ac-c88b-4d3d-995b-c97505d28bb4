<?php

namespace Database\Seeders;

use App\Models\PaymentGateway;
use App\Models\PaymentMethod;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create payment gateways for each vendor
        Vendor::all()->each(function ($vendor) {
            $paymentMethods = PaymentMethod::inRandomOrder()->take(3)->get();
            
            foreach ($paymentMethods as $paymentMethod) {
                PaymentGateway::factory()->create([
                    'payment_method_id' => $paymentMethod->id,
                    'vendor_id' => $vendor->id,
                ]);
            }
        });

        // Create additional payment gateways
        PaymentGateway::factory()->count(50)->create();
    }
}
