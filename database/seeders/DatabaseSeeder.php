<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Call seeders in order to maintain relationships
        $this->call([
            UserSeeder::class,
            PermissionRoleSeeder::class,
            VendorTypeSeeder::class,
            VendorSeeder::class,
            ImageUploadSeeder::class,
            CategorySeeder::class,
            BrandSeeder::class,
            ProductSeeder::class,
            BannerSeeder::class,
            AdvertImageSeeder::class,
            ChatSeeder::class,
            DocSeeder::class,
            CouponSeeder::class,
            FaqSeeder::class,
            NotificationSeeder::class,
            PaymentMethodSeeder::class,
            PaymentGatewaySeeder::class,
            ExtraGroupSeeder::class,
            ExtraSeeder::class,
            IngredientGroupSeeder::class,
            IngredientSeeder::class,
            OrderTimeSeeder::class,
            OrderStatusSeeder::class,
            ProductIngredientSeeder::class,
            BranchSeeder::class,
            BranchProductSeeder::class,
            OtpSessionSeeder::class,
            AddressSeeder::class,
        ]);
    }
}
