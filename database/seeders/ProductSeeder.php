<?php

namespace Database\Seeders;

use App\Models\Brand;
use App\Models\Category;
use App\Models\Product;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create products for each vendor
        Vendor::all()->each(function ($vendor) {
            $categories = Category::where('vendor_id', $vendor->id)->get();
            $brands = Brand::where('vendor_id', $vendor->id)->get();

            if ($categories->count() > 0 && $brands->count() > 0) {
                Product::factory()->count(fake()->numberBetween(10, 30))->create([
                    'vendor_id' => $vendor->id,
                ]);
            }
        });

        // Create some featured products
        Product::factory()->count(20)->featured()->create();

        // Create some products on discount
        Product::factory()->count(30)->onDiscount()->create();

        // Create additional products
        Product::factory()->count(100)->create();
    }
}
