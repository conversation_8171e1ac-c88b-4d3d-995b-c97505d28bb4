<?php

namespace Database\Seeders;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create notifications for each user
        User::all()->each(function ($user) {
            Notification::factory()->count(fake()->numberBetween(5, 15))->create([
                'user_id' => $user->id,
            ]);
        });

        // Create additional notifications
        Notification::factory()->count(100)->create();
    }
}
