<?php

namespace Database\Seeders;

use App\Models\OrderTime;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class OrderTimeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create order times for different orders
        for ($i = 1; $i <= 100; $i++) {
            OrderTime::factory()->count(fake()->numberBetween(1, 3))->create([
                'order_id' => $i,
            ]);
        }

        // Create additional order times
        OrderTime::factory()->count(200)->create();
    }
}
