<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 1, // Admin role
            'phone' => '+1234567890',
            'active' => true,
        ]);

        // Create regular users
        User::factory()->count(100)->create([
            'role' => 2, // Regular user role
        ]);

        // Create some inactive users
        User::factory()->count(10)->create([
            'role' => 2,
            'active' => false,
        ]);
    }
}
