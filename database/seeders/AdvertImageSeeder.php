<?php

namespace Database\Seeders;

use App\Models\AdvertImage;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdvertImageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create advert images for each vendor
        Vendor::all()->each(function ($vendor) {
            AdvertImage::factory()->count(fake()->numberBetween(3, 8))->create([
                'vendor_id' => $vendor->id,
            ]);
        });

        // Create additional advert images
        AdvertImage::factory()->count(50)->create();
    }
}
