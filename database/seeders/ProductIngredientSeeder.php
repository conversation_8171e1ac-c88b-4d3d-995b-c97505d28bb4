<?php

namespace Database\Seeders;

use App\Models\Ingredient;
use App\Models\Product;
use App\Models\ProductIngredient;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductIngredientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create product ingredients for each product
        Product::all()->each(function ($product) {
            $ingredients = Ingredient::where('vendor_id', $product->vendor_id)->inRandomOrder()->take(fake()->numberBetween(2, 6))->get();
            
            foreach ($ingredients as $ingredient) {
                ProductIngredient::factory()->create([
                    'product_id' => $product->id,
                    'ingredient_id' => $ingredient->id,
                    'vendor_id' => $product->vendor_id,
                ]);
            }
        });

        // Create additional product ingredients
        ProductIngredient::factory()->count(200)->create();
    }
}
