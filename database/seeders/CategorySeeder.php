<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\ImageUpload;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create categories for each vendor
        Vendor::all()->each(function ($vendor) {
            // Create main categories
            Category::factory()->count(fake()->numberBetween(3, 8))->create([
                'vendor_id' => $vendor->id,
            ]);

            // Create some subcategories
            Category::where('vendor_id', $vendor->id)->take(3)->get()->each(function ($parentCategory) {
                Category::factory()->count(fake()->numberBetween(2, 5))->create([
                    'vendor_id' => $parentCategory->vendor_id,
                    'parent_id' => $parentCategory->id,
                ]);
            });
        });
    }
}
