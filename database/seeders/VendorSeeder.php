<?php

namespace Database\Seeders;

use App\Models\Vendor;
use App\Models\VendorType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class VendorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create vendors for each vendor type
        VendorType::all()->each(function ($vendorType) {
            Vendor::factory()->count(fake()->numberBetween(3, 8))->create([
                'vendor_type' => $vendorType->id,
            ]);
        });

        // Create additional random vendors
        Vendor::factory()->count(20)->create();
    }
}
