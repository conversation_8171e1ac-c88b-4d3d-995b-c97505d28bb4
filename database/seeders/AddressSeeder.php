<?php

namespace Database\Seeders;

use App\Models\Address;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AddressSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create addresses for existing users
        User::all()->each(function ($user) {
            Address::factory()->count(fake()->numberBetween(1, 3))->create([
                'user_id' => $user->id,
            ]);
        });

        // Create additional addresses with new users
        Address::factory()->count(50)->create();
    }
}
