<?php

namespace Database\Seeders;

use App\Models\VendorRegistration;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class VendorRegistrationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Creates 10 sample seller registration records for testing.
        VendorRegistration::factory()->count(10)->create();
    }
}
