<?php

namespace Database\Seeders;

use App\Models\Doc;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DocSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create documents for each vendor
        Vendor::all()->each(function ($vendor) {
            $params = [
                'about_us',
                'terms_conditions',
                'privacy_policy',
                'delivery_policy',
                'return_policy',
                'contact_info',
                'faq',
                'help_center'
            ];

            foreach ($params as $param) {
                Doc::factory()->create([
                    'param' => $param,
                    'vendor_id' => $vendor->id,
                ]);
            }
        });
    }
}
