<?php

namespace Database\Seeders;

use App\Models\Brand;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create brands for each vendor
        Vendor::all()->each(function ($vendor) {
            Brand::factory()->count(fake()->numberBetween(2, 6))->create([
                'vendor_id' => $vendor->id,
            ]);
        });

        // Create additional brands
        Brand::factory()->count(30)->create();
    }
}
