<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;

class PermissionRoleSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 */
	public function run(): void
	{
		$permissions = [
			'view dashboard',
			'view vendors','create vendors','view vendor registrations',
			'view products','create products','view categories',
			'view orders',
			'view users',
			'manage settings',
			'view coupons',
			'view banners',
			'view media',
			'view reviews',
			'view payments',
			'view wallet',
			'view chat',
			'view payment methods',
			'view payment gateways',
			'view branches','view brands','bulk upload','manage appearance','view cities','view companies','view company reviews','view currencies','view documents','view drivers','view extras','view extra groups','view faq','view ingredient groups','view ingredients','view markets','view menus','view analytics','send notifications','view order statuses','view product ingredients','view transactions','view vendor types','view sellers'
		];

		// Create permissions
		foreach ($permissions as $name) {
			Permission::findOrCreate($name);
		}

		// Create roles
		$admin = Role::findOrCreate('Admin');
		$manager = Role::findOrCreate('Manager');
		$vendor = Role::findOrCreate('Vendor');
		$staff = Role::findOrCreate('Staff');

		// Assign permissions to roles
		$admin->syncPermissions(Permission::all());

		$managerPerms = [
			'view dashboard','view vendors','view products','view categories','view orders','view users','view coupons','view banners','view media','view reviews','view payments','view wallet','view chat','view payment methods','view payment gateways','view analytics','view transactions'
		];
		$manager->syncPermissions($managerPerms);

		$vendorPerms = [
			'view dashboard','view products','create products','view orders','view reviews','view wallet','view media','view menus'
		];
		$vendor->syncPermissions($vendorPerms);

		$staffPerms = [
			'view dashboard','view orders','view products','view categories','view users'
		];
		$staff->syncPermissions($staffPerms);

		// Assign Admin role to the first user if exists
		$user = User::query()->first();
		if ($user && !$user->hasRole('Admin')) {
			$user->assignRole('Admin');
		}
	}
}




