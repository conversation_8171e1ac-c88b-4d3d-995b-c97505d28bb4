<?php

namespace Database\Seeders;

use App\Models\VendorType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class VendorTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $vendorTypes = [
            'Restaurant',
            'Cafe',
            'Fast Food',
            'Bakery',
            'Grocery Store',
            'Pharmacy',
            'Electronics Store',
            'Clothing Store',
            'Bookstore',
            'Hardware Store'
        ];

        foreach ($vendorTypes as $type) {
            VendorType::firstOrCreate(['type' => $type]);
        }
    }
}
