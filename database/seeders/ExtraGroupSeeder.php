<?php

namespace Database\Seeders;

use App\Models\ExtraGroup;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExtraGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create extra groups for each vendor
        Vendor::all()->each(function ($vendor) {
            ExtraGroup::factory()->count(fake()->numberBetween(2, 5))->create([
                'vendor_id' => $vendor->id,
            ]);
        });

        // Create additional extra groups
        ExtraGroup::factory()->count(30)->create();
    }
}
