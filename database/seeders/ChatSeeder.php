<?php

namespace Database\Seeders;

use App\Models\Chat;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ChatSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create chat conversations between users
        User::all()->each(function ($user) {
            $otherUsers = User::where('id', '!=', $user->id)->inRandomOrder()->take(3)->get();
            
            foreach ($otherUsers as $otherUser) {
                Chat::factory()->count(fake()->numberBetween(5, 15))->create([
                    'from_user_id' => $user->id,
                    'to_user_id' => $otherUser->id,
                ]);
            }
        });

        // Create additional random chats
        Chat::factory()->count(200)->create();
    }
}
