<?php

namespace Database\Seeders;

use App\Models\CouponCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CouponCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 25 records for the 'coupon_category' table using the factory.
        CouponCategory::factory()->count(25)->create();
    }
}
