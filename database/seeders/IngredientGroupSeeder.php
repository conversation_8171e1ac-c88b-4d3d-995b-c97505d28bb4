<?php

namespace Database\Seeders;

use App\Models\IngredientGroup;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class IngredientGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create ingredient groups for each vendor
        Vendor::all()->each(function ($vendor) {
            IngredientGroup::factory()->count(fake()->numberBetween(2, 5))->create([
                'vendor_id' => $vendor->id,
            ]);
        });

        // Create additional ingredient groups
        IngredientGroup::factory()->count(30)->create();
    }
}
