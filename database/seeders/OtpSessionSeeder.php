<?php

namespace Database\Seeders;

use App\Models\OtpSession;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class OtpSessionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create OTP sessions for some users
        User::inRandomOrder()->take(20)->get()->each(function ($user) {
            OtpSession::factory()->count(fake()->numberBetween(1, 3))->create([
                'user_id' => $user->id,
                'phone' => $user->phone,
            ]);
        });

        // Create additional OTP sessions
        OtpSession::factory()->count(50)->create();
    }
}
