<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            'Cash On Delivery',
            'telebirr',
            'telebirr SuperApp',
            'M-PESA | Safaricom Superapp',
            'M-PESA | Safaricom Online Checkout',
            'EthSwitch',
            'Credit Card',
            'Debit Card',
            'Bank Transfer',
            'PayPal'
        ];

        foreach ($paymentMethods as $paymentMethod) {
            PaymentMethod::firstOrCreate(['name' => $paymentMethod]);
        }
    }
}
