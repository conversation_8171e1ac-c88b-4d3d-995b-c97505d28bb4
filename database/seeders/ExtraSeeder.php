<?php

namespace Database\Seeders;

use App\Models\Extra;
use App\Models\ExtraGroup;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExtraSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create extras for each extra group
        ExtraGroup::all()->each(function ($extraGroup) {
            Extra::factory()->count(fake()->numberBetween(3, 8))->create([
                'extra_group_id' => $extraGroup->id,
                'vendor_id' => $extraGroup->vendor_id,
            ]);
        });

        // Create additional extras
        Extra::factory()->count(100)->create();
    }
}
