<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('favorites', function (Blueprint $table) {
            $table->id();
            // Foreign key to the users table, following Laravel naming conventions.
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            // Foreign key to the products table.
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            // Foreign key to the branches table.
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('favorites');
    }
};
