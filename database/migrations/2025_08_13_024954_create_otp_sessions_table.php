<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('otp_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('phone');
            $table->string('otp');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('channel')->nullable(); // How the OTP was sent (e.g., 'sms', 'email').
            $table->integer('attempts')->default(0); // Tracks failed attempts for security.
            $table->timestamp('expires_at')->nullable(); // For explicit expiration.
            $table->timestamp('verified_at')->nullable(); // To prevent reuse of the same OTP.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('otp_sessions');
    }
};
