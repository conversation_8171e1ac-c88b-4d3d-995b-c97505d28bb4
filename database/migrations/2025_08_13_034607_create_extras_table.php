<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('extras', function (Blueprint $table) {
            $table->id();
            // Foreign key to the extra_groups table.
            $table->foreignId('extra_group_id')->constrained('extra_groups')->onDelete('cascade');
            $table->string('name');
            $table->decimal('price', 15, 2);
            // Using a boolean for status is best practice.
            $table->boolean('status');
            // Foreign key to the companies table.
            $table->foreignId('vendor_id')->constrained('vendors')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('extras');
    }
};
