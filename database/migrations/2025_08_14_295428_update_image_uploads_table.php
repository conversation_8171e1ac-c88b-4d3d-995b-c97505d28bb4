<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('image_uploads', function (Blueprint $table) {
            // Adds a nullable foreign key column to link to the 'products' table.
            $table->foreignId('product_id')->nullable()->after('vendor_id')->constrained('products')->onDelete('cascade');

            // Adds a nullable foreign key column to link to the 'product_variants' table.
            $table->foreignId('variant_id')->nullable()->after('product_id')->constrained('product_variants')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // When rolling back the migration, remove the foreign key constraints and the columns.
        Schema::table('image_uploads', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
            $table->dropColumn('product_id');
            $table->dropForeign(['variant_id']);
            $table->dropColumn('variant_id');
        });
    }
};
