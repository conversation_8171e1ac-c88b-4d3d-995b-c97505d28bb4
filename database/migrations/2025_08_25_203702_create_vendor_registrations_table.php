<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_registrations', function (Blueprint $table) {
            $table->id();
            // Account Info
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone_number', 20);
            $table->string('password');

            // Business Info
            $table->string('business_name');
            $table->string('business_description');
            $table->string('business_email');
            $table->string('business_phone');

            // $table->enum('business_type', ['sole_proprietor', 'private_limited', 'share_company', 'cooperative', 'ngo', 'other']);
            $table->text('business_address');
            $table->string('woreda', 100)->nullable();
            $table->string('house_number', 50)->nullable();

            // Documents
            $table->string('website')->nullable();
            $table->string('tin_number', 50);
            $table->string('tax_id');
            $table->string('trade_license_doc');
            $table->string('tin_certificate_doc')->nullable();
            $table->string('id_card_doc')->nullable();

            // Status
            $table->enum('status', ['pending', 'approved', 'rejected', 'suspended'])->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_registrations');
    }
};
