<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            // Foreign key to the products table.
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->string('name');
            // Foreign key to the image_uploads table, can be null.
            $table->foreignId('image_id')->nullable()->constrained('image_uploads')->onDelete('set null');
            $table->decimal('price', 15, 2);
            $table->decimal('discount_price', 15, 2)->default(0.00);
            $table->boolean('stock_item')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
};
