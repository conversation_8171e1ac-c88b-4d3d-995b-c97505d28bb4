<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add foreign key to vendors table
            $table->foreignId('vendor_id')->nullable()->after('google_id')->constrained('vendors')->onDelete('set null');
            
            // Add foreign key to branches table
            $table->foreignId('branch_id')->nullable()->after('vendor_id')->constrained('branches')->onDelete('set null');
            
            // Add boolean field for branch admin
            $table->boolean('is_branch_admin')->default(false)->after('branch_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['vendor_id']);
            $table->dropForeign(['branch_id']);
            $table->dropColumn(['vendor_id', 'branch_id', 'is_branch_admin']);
        });
    }
};

