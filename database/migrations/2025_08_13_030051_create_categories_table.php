<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            // Foreign key to the image_uploads table.
            $table->unsignedInteger('image_id');
            $table->foreign('image_id')->references('id')->on('image_uploads')->onDelete('cascade');
            $table->longText('desc');
            $table->boolean('visible');
            $table->integer('top_category')->default(0);
            // Self-referencing foreign key for nested categories.
            $table->foreignId('parent_id')->nullable()->constrained('categories')->onDelete('set null');
            // Foreign key to the vendors table.
            $table->foreignId('vendor_id')->constrained('vendors')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
