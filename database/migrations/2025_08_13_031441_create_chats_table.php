<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chats', function (Blueprint $table) {
            $table->id();
            // Foreign key for the sender.
            $table->foreignId('from_user_id')->constrained('users')->onDelete('cascade');
            // Foreign key for the recipient.
            $table->foreignId('to_user_id')->constrained('users')->onDelete('cascade');
            $table->string('text');
            // Using a boolean for delivered and read status.
            $table->boolean('delivered')->default(false);
            $table->boolean('read')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chats');
    }
};
