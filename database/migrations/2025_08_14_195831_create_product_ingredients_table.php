<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_ingredients', function (Blueprint $table) {
            $table->id();
            // Foreign key to the products table.
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            // Foreign key to the ingredients table.
            $table->foreignId('ingredient_id')->constrained('ingredients')->onDelete('cascade');
            // Status of the ingredient for the product. Using boolean is a good practice.
            $table->boolean('status');
            // Foreign key to the vendors table.
            $table->foreignId('vendor_id')->constrained('vendors')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_ingredients');
    }
};
