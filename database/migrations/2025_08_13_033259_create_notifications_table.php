<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            // Foreign key to the users table.
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('title');
            $table->longText('text');
            $table->string('image')->nullable();
            // Using a boolean for the 'show' and 'read' status.
            $table->boolean('show')->default(true);
            $table->boolean('read')->default(false);
            $table->uuid('uid')->unique(); // Using a UUID for a unique identifier.
            $table->softDeletes(); // For soft deleting notifications.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
