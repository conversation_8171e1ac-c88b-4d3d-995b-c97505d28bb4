<?php

use App\Models\PaymentMethod;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name', 102);
            // Foreign key to the image_uploads table.
            $table->unsignedBigInteger('image_id')->nullable();
            $table->foreign('image_id')->references('id')->on('image_uploads')->onDelete('set null');
            $table->timestamps();
        });

        $paymentMethods = [
            'Cash On Delivery',
            'telebirr',
            'telebirr SuperApp',
            'M-PESA | Safaricom Superapp',
            'M-PESA | Safaricom Online Checkout',
            'EthSwitch',
        ];

        foreach ($paymentMethods as $paymentMethod) {
            PaymentMethod::firstOrCreate(['name' => $paymentMethod]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
