<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_gateways', function (Blueprint $table) {
            $table->id();
            $table->string('api_key')->nullable();
            // Foreign key to the payment_methods table.
            $table->foreignId('payment_method_id')->constrained('payment_methods')->onDelete('cascade');
            // Using booleans for status fields.
            $table->boolean('superadmin_status')->default(false);
            $table->boolean('vendor_status')->default(false);
            $table->foreignId('vendor_id')->constrained('vendors')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_gateways');
    }
};
