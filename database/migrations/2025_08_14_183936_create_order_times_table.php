<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_times', function (Blueprint $table) {
            $table->id();
            // Foreign key to the orders table.
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            // Using a string for status, as it's often more descriptive.
            $table->string('status');
            // Foreign key to the users table (assuming 'driver' is a user).
            $table->foreignId('driver_id')->constrained('users')->onDelete('cascade');
            $table->string('comment')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_times');
    }
};
