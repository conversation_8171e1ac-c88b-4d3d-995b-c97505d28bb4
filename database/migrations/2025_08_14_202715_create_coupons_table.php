<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->dateTime('date_start');
            $table->dateTime('date_end');
            $table->decimal('discount', 15, 2);
            $table->boolean('published')->default(false);
            $table->boolean('in_percents')->default(false);
            $table->decimal('amount', 15, 2);
            $table->string('description');
            $table->boolean('all_vendors')->default(false);
            $table->boolean('all_categories')->default(false);
            $table->boolean('all_products')->default(false);
            // Foreign key to the vendors table.
            $table->foreignId('vendor_id')->constrained('vendors')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
