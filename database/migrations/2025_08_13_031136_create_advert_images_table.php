<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('advert_images', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->integer('position');
            // Foreign key to the image_uploads table.
            $table->unsignedInteger('image_id')->nullable();
            $table->foreign('image_id')->references('id')->on('image_uploads')->onDelete('set null');
            $table->boolean('visible'); // Using boolean for a true/false value.
            // Foreign key to the vendors table.
            $table->foreignId('vendor_id')->constrained('vendors')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advert_images');
    }
};
