<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendors', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('vendor_type')->nullable()->constrained()->onDelete('set null');
            $table->boolean('published');
            $table->boolean('delivered');
            $table->string('phone');
            $table->string('mobilephone');
            $table->string('address');
            $table->float('lat');
            $table->float('lng');
            $table->unsignedInteger('image_id')->nullable();
            $table->foreign('image_id')->references('id')->on('image_uploads')->onDelete('set null');
            $table->longText('desc');
            $table->decimal('fee', 15, 2);
            $table->boolean('percent');
            $table->string('openTimeMonday')->nullable();
            $table->string('closeTimeMonday')->nullable();
            $table->string('openTimeTuesday')->nullable();
            $table->string('closeTimeTuesday')->nullable();
            $table->string('openTimeWednesday')->nullable();
            $table->string('closeTimeWednesday')->nullable();
            $table->string('openTimeThursday')->nullable();
            $table->string('closeTimeThursday')->nullable();
            $table->string('openTimeFriday')->nullable();
            $table->string('closeTimeFriday')->nullable();
            $table->string('openTimeSaturday')->nullable();
            $table->string('closeTimeSaturday')->nullable();
            $table->string('openTimeSunday')->nullable();
            $table->string('closeTimeSunday')->nullable();
            $table->integer('area')->nullable();
            $table->decimal('minAmount', 15, 2);
            $table->decimal('minDiscountAmount', 15, 2);
            $table->integer('discountAmount');
            $table->integer('commission')->default(0);
            $table->integer('tax')->default(0);
            $table->boolean('perkm')->default(0);
            $table->boolean('flatrate')->default(0);
            $table->text('city');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendors');
    }
};
