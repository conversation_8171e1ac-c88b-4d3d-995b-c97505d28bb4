<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->decimal('price', 15, 2);
            $table->decimal('discount_price', 15, 2);
            $table->longText('description');
            // Foreign key to the brands table.
            $table->foreignId('brand_id')->nullable()->constrained('brands')->onDelete('set null');
            // Foreign key to the categories table.
            $table->foreignId('category_id')->constrained('categories')->onDelete('cascade');
            $table->string('unit');
            $table->integer('package_count');
            $table->integer('weight');
            $table->boolean('can_deliver');
            $table->integer('stars');
            $table->boolean('featured');
            $table->boolean('published');
            // 'nutritions' is handled with a many-to-many relationship.
            $table->integer('stock_item')->default(0);
            // Foreign key to the vendors table.
            $table->foreignId('vendor_id')->constrained('vendors')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
