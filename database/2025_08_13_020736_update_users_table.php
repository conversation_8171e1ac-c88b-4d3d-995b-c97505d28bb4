<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->integer('image_id')->nullable();
            $table->foreignId('vendor_id')->nullable()->constrained('vendors')->onDelete('set null');
            $table->foreignId('sub_vendor_id')->nullable()->constrained('vendors')->onDelete('set null');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('set null');
            $table->integer('is_branch_admin')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Dropping the column if the migration is rolled back.
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['vendor_id', 'sub_vendor_id']);
            $table->dropColumn('vendor_id');
            $table->dropColumn('sub_vendor_id');
        });
    }
};
