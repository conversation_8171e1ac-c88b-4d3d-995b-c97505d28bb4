<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Notification>
 */
class NotificationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'title' => fake()->sentence(),
            'text' => fake()->paragraph(),
            'image' => fake()->optional()->imageUrl(300, 200, 'notification', true),
            'show' => fake()->boolean(90), // 90% chance of being shown
            'read' => fake()->boolean(30), // 30% chance of being read
            'uid' => Str::uuid(),
        ];
    }
}
