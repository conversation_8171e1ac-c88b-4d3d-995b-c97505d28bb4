<?php

namespace Database\Factories;

use App\Models\VendorRegistration;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VendorRegistration>
 */
class VendorRegistrationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = VendorRegistration::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone_number' => $this->faker->unique()->phoneNumber(),
            'password' => Hash::make('password'),
            'password_confirmation' => Hash::make('password'),
            'business_name' => $this->faker->company(),
            'business_type' => $this->faker->randomElement([
                'Electronics & Gadgets',
                'Clothing & Fashion',
                'Beauty & Personal Care',
                'Books & Stationery',
                'Restaurants & Food',
                'Auto & Spare Parts',
                'Other Products'
            ]),
            'tin_number' => $this->faker->unique()->numerify('##########'),
            'trade_license_number' => $this->faker->unique()->bothify('??-########'),
            'business_description' => $this->faker->address(),
            'business_email' => $this->faker->address(),
            'business_phone' => $this->faker->address(),
            'business_address' => $this->faker->address(),
            'tax_id' => $this->faker->address(),
            'trade_license_doc' => 'documents/trade_license_' . Str::random(10) . '.pdf',
            'id_card_doc' => 'documents/id_card_' . Str::random(10) . '.jpg',
            'region' => $this->faker->state(),
            'city' => $this->faker->city(),
            'sub_city' => $this->faker->citySuffix(),
            'woreda' => $this->faker->streetName(),
            'house_number' => $this->faker->buildingNumber(),
            'tin_certificate_doc' => 'documents/tin_certificate_' . Str::random(10) . '.pdf',
            'bank_name' => $this->faker->randomElement(['Commercial Bank of Ethiopia', 'Dashen Bank', 'Awash Bank']),
            'bank_account_number' => $this->faker->unique()->bankAccountNumber(),
            'mobile_money_number' => $this->faker->unique()->phoneNumber(),
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected', 'suspended']),
        ];
    }
}
