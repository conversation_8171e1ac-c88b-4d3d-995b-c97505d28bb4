<?php

namespace Database\Factories;

use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AdvertImage>
 */
class AdvertImageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->words(2, true),
            'position' => fake()->numberBetween(1, 10),
            'image_id' => null, // Will be set by seeder if needed
            'visible' => fake()->boolean(80), // 80% chance of being visible
            'vendor_id' => Vendor::factory(),
        ];
    }
}
