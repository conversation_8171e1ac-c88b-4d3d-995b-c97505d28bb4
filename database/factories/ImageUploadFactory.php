<?php

namespace Database\Factories;

use App\Models\ImageUpload;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ImageUpload>
 */
class ImageUploadFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ImageUpload::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'filename' => fake()->imageUrl(640, 480, 'food', true),
            'product_id' => null, // Will be set by seeder if needed
            'variant_id' => null, // Will be set by seeder if needed
            'vendor_id' => null, // Will be set by seeder if needed
        ];
    }

    /**
     * Indicate that the image belongs to a product.
     */
    public function forProduct(): static
    {
        return $this->state(fn(array $attributes) => [
            'product_id' => Product::factory(),
            'variant_id' => ProductVariant::factory(),
            'vendor_id' => null,
        ]);
    }

    /**
     * Indicate that the image belongs to a vendor.
     */
    public function forVendor(): static
    {
        return $this->state(fn(array $attributes) => [
            'product_id' => null,
            'variant_id' => null,
            'vendor_id' => Vendor::factory(),
        ]);
    }
}
