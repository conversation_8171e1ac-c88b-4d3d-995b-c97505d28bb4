<?php

namespace Database\Factories;

use App\Models\Ingredient;
use App\Models\IngredientGroup;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Ingredient>
 */
class IngredientFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Ingredient::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'ingredient_group_id' => IngredientGroup::factory(),
            'name' => fake()->randomElement([
                'Chicken Breast',
                'Beef',
                'Pork',
                'Fish',
                'Shrimp',
                'Tofu',
                'Broccoli',
                'Carrots',
                'Spinach',
                'Bell Peppers',
                'Onions',
                'Garlic',
                'Rice',
                'Pasta',
                'Bread',
                'Cheese',
                'Milk',
                'Eggs',
                'Butter',
                'Olive Oil',
                'Salt',
                'Pepper',
                '<PERSON>',
                'Oregano',
                'Thy<PERSON>',
                'Tomato Sauce',
                'Soy Sauce',
                'Worcestershire Sauce',
                'Honey',
                'Sugar'
            ]),
            'price' => fake()->randomFloat(2, 0.50, 10.00),
            'status' => fake()->boolean(80), // 80% chance of being active
            'vendor_id' => Vendor::factory(),
        ];
    }
}
