<?php

namespace Database\Factories;

use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ExtraGroup>
 */
class ExtraGroupFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->randomElement([
                'Toppings',
                'Sauces',
                'Sides',
                'Drinks',
                'Desserts',
                'Add-ons',
                'Extras',
                'Customizations',
                'Options',
                'Modifiers'
            ]),
            'vendor_id' => Vendor::factory(),
        ];
    }
}
