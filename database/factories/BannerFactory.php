<?php

namespace Database\Factories;

use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Banner>
 */
class BannerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->words(2, true),
            'image_id' => null, // Will be set by seeder if needed
            'type' => fake()->randomElement(['slider', 'banner', 'promotion', 'advertisement']),
            'details' => fake()->optional()->paragraph(),
            'visible' => fake()->boolean(80), // 80% chance of being visible
            'position' => fake()->randomElement(['top', 'bottom', 'sidebar', 'main', 'header', 'footer']),
            'vendor_id' => Vendor::factory(),
        ];
    }
}
