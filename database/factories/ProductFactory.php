<?php

namespace Database\Factories;

use App\Models\Brand;
use App\Models\Category;
use App\Models\Product;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $price = fake()->randomFloat(2, 5, 200);

        return [
            'name' => fake()->words(3, true),
            'price' => $price,
            'discount_price' => fake()->boolean(30) ? fake()->randomFloat(2, 1, $price) : 0,
            'description' => fake()->paragraph(),
            'brand_id' => Brand::inRandomOrder()->first()->id ?? Brand::factory(),
            'category_id' => Category::inRandomOrder()->first()->id ?? Category::factory(),
            'unit' => fake()->randomElement(['kg', 'g', 'ml', 'pcs', 'l', 'oz']),
            'package_count' => fake()->numberBetween(1, 10),
            'weight' => fake()->numberBetween(100, 5000),
            'can_deliver' => fake()->boolean(90), // 90% chance of being deliverable
            'stars' => fake()->numberBetween(1, 5),
            'featured' => fake()->boolean(20), // 20% chance of being featured
            'published' => fake()->boolean(80), // 80% chance of being published
            'stock_item' => fake()->numberBetween(0, 100),
            'vendor_id' => Vendor::inRandomOrder()->first()->id ?? Vendor::factory(),
        ];
    }

    /**
     * Indicate that the product is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'featured' => true,
            'published' => true,
        ]);
    }

    /**
     * Indicate that the product is on discount.
     */
    public function onDiscount(): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_price' => fake()->randomFloat(2, 1, $attributes['price'] * 0.8),
        ]);
    }
}
