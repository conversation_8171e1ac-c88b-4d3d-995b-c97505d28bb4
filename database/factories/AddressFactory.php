<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Address>
 */
class AddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'text' => fake()->address(),
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
            'type' => fake()->randomElement(['home', 'work', 'other']),
            'default' => fake()->boolean(20), // 20% chance of being default
        ];
    }
}
