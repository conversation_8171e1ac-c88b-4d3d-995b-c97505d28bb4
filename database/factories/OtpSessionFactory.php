<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OtpSession>
 */
class OtpSessionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'phone' => fake()->phoneNumber(),
            'otp' => fake()->numerify('######'), // 6-digit OTP
            'user_id' => User::factory(),
            'channel' => fake()->randomElement(['sms', 'email', 'whatsapp']),
            'attempts' => fake()->numberBetween(0, 3),
            'expires_at' => fake()->dateTimeBetween('+5 minutes', '+15 minutes'),
            'verified_at' => fake()->optional()->dateTimeBetween('-1 hour', 'now'),
        ];
    }
}
