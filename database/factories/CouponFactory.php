<?php

namespace Database\Factories;

use App\Models\Coupon;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Coupon>
 */
class CouponFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Coupon::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $vendor = Vendor::inRandomOrder()->first();

        // Ensure a vendor exists before creating a Coupon.
        if (!$vendor) {
            return [];
        }

        return [
            'name' => $this->faker->unique()->word() . ' Coupon',
            'date_start' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'date_end' => $this->faker->dateTimeBetween('now', '+1 month'),
            'discount' => $this->faker->randomFloat(2, 5, 50),
            'published' => $this->faker->boolean(),
            'in_percents' => $this->faker->boolean(),
            'amount' => $this->faker->randomFloat(2, 10, 200),
            'description' => $this->faker->sentence(),
            'all_vendors' => $this->faker->boolean(),
            'all_categories' => $this->faker->boolean(),
            'all_products' => $this->faker->boolean(),
            'vendor_id' => $vendor->id,
        ];
    }
}
