<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VendorType>
 */
class VendorTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type' => fake()->randomElement([
                'Restaurant',
                'Cafe',
                'Fast Food',
                'Bakery',
                'Grocery Store',
                'Pharmacy',
                'Electronics Store',
                'Clothing Store',
                'Bookstore',
                'Hardware Store'
            ]),
        ];
    }
}
