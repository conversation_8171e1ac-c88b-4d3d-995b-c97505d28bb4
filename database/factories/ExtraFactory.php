<?php

namespace Database\Factories;

use App\Models\Extra;
use App\Models\ExtraGroup;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Extra>
 */
class ExtraFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Extra::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'extra_group_id' => ExtraGroup::factory(),
            'name' => fake()->randomElement([
                'Cheese',
                'Bacon',
                'Mushrooms',
                'Onions',
                'Tomatoes',
                'Lettuce',
                'Mayo',
                'Ketchup',
                'Mustard',
                'Hot Sauce',
                'Ranch',
                'BBQ Sauce',
                'Extra Meat',
                'Extra Cheese',
                'Avocado',
                'Jalapeños',
                'Pickles',
                'Olives',
                'Pepperoni',
                'Ham'
            ]),
            'price' => fake()->randomFloat(2, 0.50, 5.00),
            'status' => fake()->boolean(80), // 80% chance of being active
            'vendor_id' => Vendor::factory(),
        ];
    }
}
