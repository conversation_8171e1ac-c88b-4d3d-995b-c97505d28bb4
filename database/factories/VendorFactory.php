<?php

namespace Database\Factories;

use App\Models\Vendor;
use App\Models\VendorType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Vendor>
 */
class VendorFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Vendor::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company(),
            'vendor_type' => VendorType::factory(),
            'published' => fake()->boolean(80), // 80% chance of being published
            'delivered' => fake()->boolean(90), // 90% chance of offering delivery
            'phone' => fake()->phoneNumber(),
            'mobilephone' => fake()->phoneNumber(),
            'address' => fake()->address(),
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
            'image_id' => null, // Will be set by seeder if needed
            'desc' => fake()->paragraph(),
            'fee' => fake()->randomFloat(2, 0, 50),
            'percent' => fake()->boolean(),
            'openTimeMonday' => fake()->time('H:i'),
            'closeTimeMonday' => fake()->time('H:i'),
            'openTimeTuesday' => fake()->time('H:i'),
            'closeTimeTuesday' => fake()->time('H:i'),
            'openTimeWednesday' => fake()->time('H:i'),
            'closeTimeWednesday' => fake()->time('H:i'),
            'openTimeThursday' => fake()->time('H:i'),
            'closeTimeThursday' => fake()->time('H:i'),
            'openTimeFriday' => fake()->time('H:i'),
            'closeTimeFriday' => fake()->time('H:i'),
            'openTimeSaturday' => fake()->time('H:i'),
            'closeTimeSaturday' => fake()->time('H:i'),
            'openTimeSunday' => fake()->time('H:i'),
            'closeTimeSunday' => fake()->time('H:i'),
            'area' => fake()->optional()->numberBetween(1, 100),
            'minAmount' => fake()->randomFloat(2, 10, 100),
            'minDiscountAmount' => fake()->randomFloat(2, 20, 200),
            'discountAmount' => fake()->numberBetween(5, 50),
            'commission' => fake()->numberBetween(0, 20),
            'tax' => fake()->numberBetween(0, 15),
            'perkm' => fake()->boolean(),
            'flatrate' => fake()->boolean(),
            'city' => fake()->city(),
        ];
    }
}
