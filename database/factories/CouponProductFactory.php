<?php

namespace Database\Factories;

use App\Models\Coupon;
use App\Models\CouponProduct;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CouponProduct>
 */
class CouponProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CouponProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Get a random coupon and product to ensure relationships exist.
        $coupon = Coupon::inRandomOrder()->first();
        $product = Product::inRandomOrder()->first();

        // Check if records exist before creating a pivot table entry.
        if (!$coupon || !$product) {
            return [];
        }

        return [
            'coupon_id' => $coupon->id,
            'product_id' => $product->id,
        ];
    }
}
