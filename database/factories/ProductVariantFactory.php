<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductVariant>
 */
class ProductVariantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProductVariant::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'name' => fake()->words(2, true),
            'image_id' => null, // Will be set by seeder if needed
            'price' => fake()->randomFloat(2, 10, 1000),
            'discount_price' => fake()->optional()->randomFloat(2, 5, 500),
            'stock_item' => fake()->boolean(80), // 80% chance of being in stock
        ];
    }
}
