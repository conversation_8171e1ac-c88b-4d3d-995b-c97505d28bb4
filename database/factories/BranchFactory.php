<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Branch>
 */
class BranchFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company() . ' Branch',
            'address' => fake()->address(),
            'phone' => fake()->phoneNumber(),
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
            'active' => fake()->boolean(80), // 80% chance of being active
        ];
    }
}
