<?php

namespace Database\Factories;

use App\Models\IngredientGroup;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\IngredientGroup>
 */
class IngredientGroupFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = IngredientGroup::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->randomElement([
                'Proteins',
                'Vegetables',
                'Grains',
                'Dairy',
                'Spices',
                'Sauces',
                'Fruits',
                'Nuts',
                'Seeds',
                'Herbs'
            ]),
            'vendor_id' => Vendor::factory(),
        ];
    }
}
