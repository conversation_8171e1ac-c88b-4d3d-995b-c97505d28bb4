<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\ImageUpload;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Category::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->randomElement([
                'Burgers',
                'Pizza',
                'Salads',
                'Desserts',
                'Beverages',
                'Appetizers',
                'Main Course',
                'Soups',
                'Sandwiches',
                'Pasta',
                'Seafood',
                'Vegetarian',
                'Breakfast',
                'Lunch',
                'Dinner',
                'Snacks',
                'Drinks',
                'Coffee',
                'Tea',
                'Juices'
            ]),
            'image_id' => ImageUpload::factory(), // Create a new image upload
            'desc' => fake()->sentence(),
            'visible' => fake()->boolean(80), // 80% chance of being visible
            'top_category' => fake()->numberBetween(0, 10),
            'parent_id' => null, // Will be set by seeder if needed
            'vendor_id' => Vendor::factory(),
        ];
    }

    /**
     * Indicate that the category is a top category.
     */
    public function topCategory(): static
    {
        return $this->state(fn (array $attributes) => [
            'top_category' => fake()->numberBetween(1, 10),
            'visible' => true,
        ]);
    }

    /**
     * Indicate that the category is a subcategory.
     */
    public function subcategory(): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => Category::factory(),
            'top_category' => 0,
        ]);
    }
}
