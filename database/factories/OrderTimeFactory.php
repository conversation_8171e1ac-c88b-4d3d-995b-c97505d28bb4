<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderTime>
 */
class OrderTimeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'order_id' => fake()->numberBetween(1, 1000), // Will be set by seeder if needed
            'status' => fake()->randomElement(['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled']),
            'driver_id' => User::factory(),
            'comment' => fake()->optional()->sentence(),
        ];
    }
}
