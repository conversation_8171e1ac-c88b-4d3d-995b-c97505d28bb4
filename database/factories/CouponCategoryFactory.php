<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\Coupon;
use App\Models\CouponCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CouponCategory>
 */
class CouponCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CouponCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Get a random coupon and category to ensure relationships exist.
        $coupon = Coupon::inRandomOrder()->first();
        $category = Category::inRandomOrder()->first();

        // Check if records exist before creating a pivot table entry.
        if (!$coupon || !$category) {
            return [];
        }

        return [
            'coupon_id' => $coupon->id,
            'category_id' => $category->id,
        ];
    }
}
