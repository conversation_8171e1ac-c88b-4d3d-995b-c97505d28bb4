<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentMethod>
 */
class PaymentMethodFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'payment_method' => fake()->randomElement([
                'Cash On Delivery',
                'telebirr',
                'telebirr SuperApp',
                'M-PESA | Safaricom Superapp',
                'M-PESA | Safaricom Online Checkout',
                'EthSwitch',
                'Credit Card',
                'Debit Card',
                'Bank Transfer',
                'PayPal'
            ]),
            'image_id' => null, // Will be set by seeder if needed
        ];
    }
}
