<?php

namespace Database\Factories;

use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Doc>
 */
class DocFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'param' => fake()->randomElement([
                'about_us',
                'terms_conditions',
                'privacy_policy',
                'delivery_policy',
                'return_policy',
                'contact_info',
                'faq',
                'help_center'
            ]),
            'value' => fake()->paragraphs(3, true),
            'vendor_id' => Vendor::factory(),
        ];
    }
}
