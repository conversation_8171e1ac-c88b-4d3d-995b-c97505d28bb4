<?php

namespace Database\Factories;

use App\Models\PaymentMethod;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentGateway>
 */
class PaymentGatewayFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'api_key' => fake()->optional()->uuid(),
            'payment_method_id' => PaymentMethod::factory(),
            'superadmin_status' => fake()->boolean(70), // 70% chance of being enabled for superadmin
            'vendor_status' => fake()->boolean(60), // 60% chance of being enabled for vendor
            'vendor_id' => Vendor::factory(),
        ];
    }
}
