<?php

namespace Database\Factories;

use App\Models\Branch;
use App\Models\Favorite;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Favorite>
 */
class FavoriteFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Favorite::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Get a random user, product, and branch to ensure relationships exist.
        $user = User::inRandomOrder()->first();
        $product = Product::inRandomOrder()->first();
        $branch = Branch::inRandomOrder()->first();

        // Check if the required records exist before creating a pivot table entry.
        if (!$user || !$product || !$branch) {
            return [];
        }

        return [
            'user_id' => $user->id,
            'product_id' => $product->id,
            'branch_id' => $branch->id,
        ];
    }
}
