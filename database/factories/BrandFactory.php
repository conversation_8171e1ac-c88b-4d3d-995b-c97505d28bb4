<?php

namespace Database\Factories;

use App\Models\Brand;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Brand>
 */
class BrandFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Brand::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->randomElement([
                'Nike',
                'Adidas',
                'Apple',
                'Samsung',
                'Coca-Cola',
                'Pepsi',
                'McDonald\'s',
                'KFC',
                'Starbucks',
                'Nestle',
                'Unilever',
                'P&G',
                'Sony',
                'LG',
                'Dell',
                'HP',
                'Canon',
                'Nikon',
                'Toyota',
                'Honda'
            ]),
            'image_id' => null, // Will be set by seeder if needed
            'visible' => fake()->boolean(80), // 80% chance of being visible
            'vendor_id' => Vendor::factory(),
        ];
    }
}
