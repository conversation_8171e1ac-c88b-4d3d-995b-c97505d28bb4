<?php

use App\Http\Controllers\AccountController;
use App\Http\Controllers\AddressController;
use App\Http\Controllers\AdvertController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\Admin\AuthController as AdminAuthController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\BusinessCalculatorController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\Customer\ChatController as CustomerChatController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ImageController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\OrderCompleteController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\Admin\OrderController as AdminOrderController;
use App\Http\Controllers\OrderInfoController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\RequestedProductController;
use App\Http\Controllers\RequestProductController;
use App\Http\Controllers\SMSController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\UserController;
use App\Mail\TestMail;
use App\Mail\EmailVerification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;

use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;
use App\Mail\SendEmail;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Route::get('test/page', function () {
//     // Get the current time
//     $currentTime = date('Y-m-d H:i:s');
//     echo "Current time: " . $currentTime . "<br>";

//     // Add 2 minutes to the current time
//     $futureTime = date('Y-m-d H:i:s', strtotime('+2 minutes', strtotime($currentTime)));
//     $difference = strtotime($futureTime) - strtotime($currentTime);
//     echo "Future time (2 minutes from now): " . $futureTime;
//     echo "Difference time: " . $difference;
//     return;
//     // dd(url()->full(), Request::root() . '?param=1', url('/page?param1=value1&param2=value2'));
//     // $code = Str::uuid();

//     // dd($code);
// });

Route::get('test-mail', function () {
    Mail::to('<EMAIL>')->send(new EmailVerification('<EMAIL>', '4422'));
    // Mail::send(new TestMail());
    return 'Send';
});

/******************************************************************************************************************************************/
/*********** Landing Page ***********/
/** Home page */
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('products-get', [HomeController::class, 'productsGetPage'])->name('productsGet');

/** Detail page */
Route::get('details/{id}', [HomeController::class, 'details'])->name('details');

/** Wishlist page */
Route::get('wishlist', [HomeController::class, 'index'])->name('wishlist');

/** Search page */
Route::get('search', [HomeController::class, 'search'])->name('search');

/** Get product info */
Route::get('product-info', [HomeController::class, 'productsInfo'])->name('product.info');
Route::post('product-branch-check', [HomeController::class, 'productsBranchCheck'])->name('products.branch.check');

/** Branches */
Route::get('products-addresses', [HomeController::class, 'addresses'])->name('product.addresses');

/** User Authentication */
Route::get('account', [AuthController::class, 'account'])->name('customer.account');
Route::get('profile', [AuthController::class, 'profile'])->name('customer.profile');
Route::get('request-product', [RequestProductController::class, 'request_product'])->name('requestProduct');
Route::get('get-request-products', [RequestProductController::class, 'show'])->name('request-product.show');
Route::get('login', [AuthController::class, 'index'])->name('login');
Route::get('2FA-login', [AuthController::class, 'Login2FAView'])->name('2faLogin');
Route::get('forgot', [AuthController::class, 'forgot'])->name('forgot');
Route::get('register', [AuthController::class, 'register'])->name('register');
Route::get('send-otp', [AuthController::class, 'sendOtpView'])->name('sendOtp');
Route::get('verify-otp', [AuthController::class, 'verifyOtpView'])->name('verifyOtp');
Route::get('forgot-password', [AuthController::class, 'forgotPasswordView'])->name('forgotPassword');
Route::get('reset-password', [AuthController::class, 'resetPasswordView'])->name('resetPassword');

Route::post('check-user', [AuthController::class, 'checkUser'])->name('checkUser');
Route::post('otp-send', [AuthController::class, 'sendOtp'])->name('otpSend');
Route::post('otp-verify', [AuthController::class, 'verifyOtp'])->name('otpVerify');
Route::post('otp-login', [AuthController::class, 'otpLogin'])->name('customer.2faOtpLogin');
Route::post('forgot-pwd', [AuthController::class, 'sendForgotOtp'])->name('sendForgotOtp');
Route::post('reset-pwd', [AuthController::class, 'sendResetPwd'])->name('sendResetPwd');

Route::post('register-cutomer', [AuthController::class, 'customerSignup'])->name('customer.register');
Route::post('send-request-product', [RequestProductController::class, 'sellerSignup'])->name('sendRequestProduct');
Route::post('sell-share', [RequestProductController::class, 'sellShare'])->name('sellShare');
Route::post('login-request', [AuthController::class, 'customerLogin'])->name('customer.loginRequest');
Route::get('requested-product/{requestProduct}', [RequestedProductController::class, 'edit'])->name('customer.requested-product-get');

/** Legals pages like: about us, privacy, terms... */
Route::get('about-us', [HomeController::class, 'getAboutUs'])->name('aboutUs');
Route::get('privacy-policy', [HomeController::class, 'getPrivacy'])->name('privacyPolicy');
Route::get('terms-&-conditions', [HomeController::class, 'getTerms'])->name('termsCondition');
Route::get('delivery-info', [HomeController::class, 'getDelivery'])->name('deliveryInfo');
Route::get('refund-policy', [HomeController::class, 'getRefund'])->name('refundPolicy');


/** Auth guarded routes */
Route::middleware(['auth'])->group(function () {
    // Chat
    Route::get('chat', [CustomerChatController::class, 'load'])->name('chat');
    Route::post('new-message', [ChatController::class, 'getChatMessagesNewCount'])->name('newMessage');
    Route::get('get-msgs', [ChatController::class, 'get_messages'])->name('getMsgs');
    Route::post('send-msg', [ChatController::class, 'send_msg'])->name('sendMsg');
    Route::get('new-msgs', [ChatController::class, 'new_messages'])->name('newMsgs');
});

Route::post('/getChatMessages', 'ChatController@getChatMessages');
Route::post('/chatSendMessage', 'ChatController@chatSendMessage');
Route::get('/chatUsers2', 'ChatController@chatUsersApi');
Route::get('/chatMessages2', 'ChatController@chatMessages2');


/******************************************************************************************************************************************/
/*********** DASHBOARD ***********/

// Superadmin routes
Route::middleware(['web', 'is_admin'])->group(function () {
    // Clear cache, route, config, view
    // Route::get('/clear-cache', function () {
    //     $text = "Start...";
    //     Artisan::call('cache:clear');
    //     $text = $text . Artisan::output();
    //     Artisan::call('route:clear');
    //     $text = $text . Artisan::output();
    //     Artisan::call('config:clear');
    //     $text = $text . Artisan::output();
    //     Artisan::call('view:clear');
    //     $text = $text . Artisan::output();
    //     return $text;
    // });

    Route::get('clear-cache', 'WebSiteSettingsController@clear');

    // companies
    Route::get('companies', 'CompaniesController@load');
    Route::post('marketReviewGoPage', 'VendorMarketController@GoPage');
    Route::post('marketReviewDelete', 'VendorMarketController@delete');
    Route::get('marketsreviews', 'MarketsReviewsController@load');
    Route::post('companyEnable', 'CompaniesController@companyEnable');

    // top companies
    Route::get('topcompanies2', 'TopCompaniesController@topcompanies');
    Route::post('topCompaniesAdd', 'TopCompaniesController@topCompaniesAdd');
    Route::post('topCompaniesDelete', 'TopCompaniesController@delete');

    // users roles
    Route::get('usersRoles', 'RoleController@index');
    Route::post('roleAdd', 'RoleController@add');
    Route::post('usersRolesList', 'RoleController@roleGoPage');
    Route::post('roleGetInfo', 'RoleController@roleInfo');
    Route::post('deleteRole', 'RoleController@destroy');

    // payment methods
    Route::get('payment-methods', 'PaymentMethodController@index');
    Route::post('payment-method-list', 'PaymentMethodController@paymentMethodGoPage');
    Route::post('add-method', 'PaymentMethodController@add');
    Route::post('get-method', 'PaymentMethodController@methodInfo');
    Route::post('delete-method', 'PaymentMethodController@destroy');

    // payment gateways
    Route::get('payment-gateways', 'PaymentGatewayController@index');
    Route::post('payment-gateways-list', 'PaymentGatewayController@paymentGatewayGoPage');
    Route::post('add-gateway', 'PaymentGatewayController@add');
    Route::post('get-gateway', 'PaymentGatewayController@gatewayInfo');
    Route::post('delete-gateway', 'PaymentGatewayController@destroy');

    // settings
    Route::get('settings', 'SettingsController@load');
    Route::post('settingschange', 'SettingsController@change');
    Route::get('currencies', 'SettingsController@currencies');
    Route::post('currencyadd', 'SettingsController@currencyadd');
    Route::post('currencydelete', 'SettingsController@currencydelete');
    Route::post('currencyedit', 'SettingsController@currencyedit');
    Route::post('currencyChange', 'SettingsController@currencyChange');
    Route::post('setRightSymbol', 'SettingsController@setRightSymbol');
    Route::post('settingsSetLang', 'SettingsController@settingsSetLang');

    // vendor types
    Route::get('vendorTypes', 'VendorTypeController@index');
    Route::post('vendorTypeAdd', 'VendorTypeController@add');
    Route::post('vendorTypesList', 'VendorTypeController@goPage');
    Route::post('vendorTypeGetInfo', 'VendorTypeController@info');
    Route::post('deleteVendorType', 'VendorTypeController@destroy');

    // vendor
    Route::get('vendors', 'VendorsController@vendors');
    Route::post('vendorsGoPage', 'VendorsController@vendorsGoPage');
    Route::post('vendoradd', 'VendorsController@add');
    Route::post('vendorGetInfo', 'VendorsController@getInfo');
    Route::post('vendorDelete', 'VendorsController@delete');
});

// Roles for superadmin and vendors
Route::middleware(['web', 'is_admin'])->group(function () {});

// Payment Methods
Route::get('payments-methods', 'SettingsController@payments');
Route::post('paymentsSave', 'SettingsController@paymentsSave');

// Telebirr APIs
Route::get('telebirrapi', 'TelebirrAPIsController@index');
Route::post('telebirrapiAdd', 'TelebirrAPIsController@store');
Route::post('telebirrapiGoPage', 'TelebirrAPIsController@TelebirrAPIsGoPage');
Route::post('telebirrapiGoInfo', 'TelebirrAPIsController@getInfo');
Route::post('telebirrapiDelete', 'TelebirrAPIsController@delete');

// Branch
Route::get('branch', 'BranchController@index');
Route::post('branchGoPage', 'BranchController@branchGoPage');
Route::post('branchGetInfo', 'BranchController@branchGetInfo');
Route::post('branchAdd', 'BranchController@add');
Route::post('branchDelete', 'BranchController@delete');
Route::post('allBranches', 'BranchController@getAllBranches');

// products


// variant
// Route::post('productVariantsAdd', 'ProductsController@productVariantsAdd');
// Route::post('productVariantsDelete', 'ProductsController@productVariantsDelete');
// Route::post('productVariantsInfo', 'ProductsController@productVariantsInfo');
// Route::post('deleteBranchProductVariant', 'ProductsController@deleteBranchProductVariant');

// recommended product

// product reviews
Route::get('productsreviews', 'ProductReviewsController@load');
Route::post('productreviewsdelete', 'ProductReviewsController@delete');
Route::post('productReviewGoPage', 'ProductReviewsController@GoPage');

// nutrition
Route::get('nutrition', 'NutritionController@load');
Route::post('nutritiongroupadd', 'NutritionController@add');
Route::post('nutritiongroupdelete', 'NutritionController@delete');
Route::post('nutritiongroupedit', 'NutritionController@edit');
Route::post('nutritionadd', 'NutritionController@addnutrition');
Route::post('nutritiondelete', 'NutritionController@deletenutrition');
Route::post('nutritionedit', 'NutritionController@editnutrition');

// top products
// Route::get('topproducts', 'TopProductsController@topproducts');
// Route::post('topproductdelete', 'TopProductsController@delete');
// Route::post('topProductsAdd', 'TopProductsController@topProductsAdd');

// roles
Route::get('roles', 'UserController@roles');
Route::get('permissions', 'PermissionsController@permissions');
Route::post('permissionSet', 'PermissionsController@permissionSet');
// drivers
Route::get('drivers', 'DriversController@load');
Route::post('driversGoPage', 'DriversController@goPage');


// reports
Route::get('mostpopular', 'ReportsController@mostpopular');
Route::get('mostpurchase', 'ReportsController@mostpurchase');
Route::get('topcompanies', 'ReportsController@topcompanies');

// settings

// image manager


// faq
Route::get('faq', 'FaqController@load');
Route::post('faqdetete', 'FaqController@delete');
Route::post('faqGoPage', 'FaqController@GoPage');
Route::post('faqAdd', 'FaqController@add');
Route::post('faqGetInfo', 'FaqController@GetInfo');



// settings customer app ui
Route::get('caLayout', 'UIController@caLayout');
Route::post('caLayout_change', 'UIController@caLayout_change');
Route::get('caLayoutColors', 'UIController@caLayoutColors');
Route::post('caLayout_changeColors', 'UIController@caLayout_changeColors');
Route::get('caTheme', 'UIController@caTheme');
Route::post('caLayout_changeTheme', 'UIController@caLayout_changeTheme');
Route::get('caLayoutSizes', 'UIController@caLayoutSizes');
Route::post('caLayoutSizeChange', 'UIController@caLayoutSizeChange');
Route::get('caSkins', 'UIController@caSkins');
Route::post('caSkin_set', 'UIController@caSkin_set');

// Logging
Route::get('logging', 'LoggingController@load');
Route::post('loggingPage', 'LoggingController@loadPage');

// wallet
Route::get('wallet', 'WalletController@wallet');
Route::post('walletDetails', 'WalletController@walletDetails');
Route::post('walletChangeBalans', 'WalletController@walletChangeBalans');

// documents
Route::get('documents', 'DocumentsController@load');
Route::post('docsave', 'DocumentsController@save');


// coupons
Route::get('coupons', 'CouponsController@coupons');
Route::post('couponsAdd', 'CouponsController@add');
Route::post('coupondelete', 'CouponsController@delete');
Route::post('couponedit', 'CouponsController@edit');

// banners
Route::get('banners', 'BannersController@load');
Route::post('bannersAdd', 'BannersController@add');
Route::post('bannersGoPage', 'BannersController@GoPage');
Route::post('bannerGetInfo', 'BannersController@GetInfo');
Route::post('bannersDelete', 'BannersController@delete');


Route::get('vendormarket', 'VendorMarketController@load');
Route::post('marketedit', 'VendorMarketController@edit');

//
Route::get('productsTree', 'ProductsTreeController@load');

// bulk upload
Route::get('bulkUpload', 'BulkUploadController@load');
Route::post('csvUpload', 'BulkUploadController@csvUpload');
Route::post('csvProcess', 'BulkUploadController@csvProcess');
Route::post('csvDestroy', 'BulkUploadController@csvDestroy');

//
Route::get('transactions', 'TransactionsController@load');
Route::post('transactionsGoPage', 'TransactionsController@goPage');

// Admin Panel Settings
Route::get('apSettings', 'AdminPanelSettingsController@load');
Route::post('apSaveSettings', 'AdminPanelSettingsController@apSaveSettings');
Route::post('apRestoreSettings', 'AdminPanelSettingsController@apRestoreSettings');

// Web Site Settings
Route::get('webSettings', 'WebSiteSettingsController@load');
Route::post('webSaveSettings', 'WebSiteSettingsController@webSaveSettings');
Route::post('webRestoreSettings', 'WebSiteSettingsController@webRestoreSettings');
Route::get('webSeller', 'WebSiteSettingsController@webSeller');
Route::post('webSellerSaveSettings', 'WebSiteSettingsController@webSellerSaveSettings');

//
Route::post('sellerRegDelete', 'VendorsController@sellerRegDelete');

// Route::middleware(['web', 'role:7'])->group(function () {
//     // notifications
//     Route::get('notify', 'MessagingController@load')->name('notify');
//     Route::post('sendmsg', 'MessagingController@send');
//     Route::post('sendNotify', 'MessagingController@sendNotify');

//     // orders
//     Route::get('ordersstatuses', 'OrderStatusesController@load');
//     Route::get('orders', 'OrdersController@load')->name('orders');
//     Route::post('ordersedit', 'OrdersController@edit');
//     Route::post('orderdelete', 'OrdersController@delete');
//     Route::post('orderDetailsDelete', 'OrdersController@orderDetailsDelete');
//     Route::post('orderDetailsAdd', 'OrdersController@orderDetailsAdd');
//     Route::post('orderview', 'OrdersController@orderview');
//     Route::post('changeStatus', 'OrdersController@changeStatus');
//     Route::post('changeDriver', 'OrdersController@changeDriver');
//     Route::post('ordersGoPage', 'OrdersController@ordersGoPage');

//     Route::post('orderdone', 'OrdersController@done');
//     Route::post('orderDelivery', 'OrdersController@delivery');

//     // chat
//     Route::get('chat', 'ChatController@chat')->name('chat');
//     Route::post('chatNewMessage', 'ChatController@chatNewMessage');
//     Route::post('getChatMessages', 'ChatController@getChatMessages');
//     Route::post('getChatMessagesNewCount', 'ChatController@getChatMessagesNewCount');
//     // Route::post('chatNewUsers', 'ChatController@chatNewUsers');
//     Route::post('chatNewUsers', 'ChatController@chatNewUsers_2');
// });

/*********** LANDING ***********/
Route::middleware(['web'])->group(function () {
    /** Image */
    Route::post('image/upload/store', [ImageController::class, 'storeAdmin']);
    Route::post('image/upload/store-customer', [ImageController::class, 'storeCustomer']);
    Route::post('image/delete/{image}', [ImageController::class, 'destroy']);
});

Route::middleware([
    'web',
    'auth'
])->group(function () {
    Route::get('logout', [AuthController::class, 'logout'])->name('logout');

    /*********** LANDING ***********/
    /** Cart page */
    Route::get('cart', [OrderController::class, 'index'])->name('cart');
    Route::post('add-to-cart', [OrderController::class, 'addToCart'])->name('addToCart');
    Route::post('get-cart', [OrderController::class, 'getCart'])->name('getCart');
    Route::post('remove-from-cart', [OrderController::class, 'removeFromCart'])->name('removeFromCart');
    Route::post('cart-branch-fee', [OrderController::class, 'branchFee'])->name('cartBranchFee');

    /** Account */
    Route::post('change-password', [AccountController::class, 'changePassword'])->name('changePassword');
    Route::post('generate-referral', [AccountController::class, 'generateReferal'])->name('generateReferal');
    Route::post('update-two-fa', [AccountController::class, 'updateTwoFA'])->name('customer.two_fa');

    /** Order Info */
    Route::get('order-details/{id}', [OrderInfoController::class, 'load'])->name('orderDetails');

    /** Address */
    Route::get('addresses', [AddressController::class, 'get'])->name('addresses');
    Route::post('address-save', [AddressController::class, 'save'])->name('addressSave');
    Route::post('address-delete', [AddressController::class, 'delete'])->name('addressDelete');

    /** Complete page */
    Route::get('complete', [OrderCompleteController::class, 'curbsidePickupComplete'])->name('complete');
    Route::get('payment-complete/{order}', [OrderCompleteController::class, 'telebirrPaymentComplete'])->name('telebirrComplete');
});

// tele txs notify Url
// ->middleware('signed');
Route::post('tele_tx_notify', [OrderCompleteController::class, 'tx_notify'])->name('tele_tx_notify');

Route::get('send-email', [NotificationController::class, 'sendEmail'])->name('sendEmail');
Route::get('check-email', function () {
    Mail::to('<EMAIL>')->send(new SendEmail('<EMAIL>', '0101', 'test', 'emails.2fa-otp'));
});

/*********** DASHBOARD ***********/
Route::prefix('admin')->group(function () {
    /** Admin Authentication */
    Route::get('login', [AdminAuthController::class, 'index'])->name('admin.login');
    Route::get('2FA-login', [AdminAuthController::class, 'Login2FAView'])->name('admin.login2fa');
    Route::post('admin-login-request', [AuthController::class, 'adminLogin'])->name('admin.loginRequest');

    Route::post('otp-send', [AdminAuthController::class, 'sendOtp'])->name('admin.otpSend');
    Route::post('otp-verify', [AdminAuthController::class, 'verifyOtp'])->name('admin.otpVerify');
    Route::post('otp-login', [AdminAuthController::class, 'otpLogin'])->name('2faOtpLogin');

    Route::middleware([
        'web',
        'auth.is_dashboard_auth'
    ])->group(function () {

        /** Dashboard page **/
        Route::get('dashboard', [DashboardController::class, 'index'])->name('admin.dashboard');
        Route::get('logout', [AdminAuthController::class, 'logout'])->name('admin.logout');

        // Super admin
        Route::middleware([
            'is_super_admin'
        ])->group(function () {
            /** Payment Method */
            Route::get('payment-method', [PaymentController::class, 'index'])->name('admin.payment-method');
            Route::get('payment-methods-get', [PaymentController::class, 'show'])->name('admin.payment-methods-get');
            Route::post('payment-method-add', [PaymentController::class, 'store'])->name('admin.payment-method-add');
            Route::get('payment-method-get/{payment}', [PaymentController::class, 'edit'])->name('admin.payment-method-get');

            /** SMS Api */
            Route::get('sms-api', [SMSController::class, 'index'])->name('admin.sms-api');
            Route::get('sms-api-get', [SMSController::class, 'edit'])->name('admin.sms-api-get');
            Route::post('sms-api-add', [SMSController::class, 'store'])->name('admin.sms-api-add');

            /** Company settings */
            Route::get('settings', [CompanyController::class, 'index'])->name('admin.settings');
            Route::post('settings-add', [CompanyController::class, 'store'])->name('admin.settings-add');
        });

        // Super admin | Admin
        Route::middleware([
            'is_admin',
        ])->group(function () {
            /** Users */
            Route::get('user', [UserController::class, 'index'])->name('admin.user');
            Route::get('users-get', [UserController::class, 'show'])->name('admin.users-get');
            Route::post('user-add', [UserController::class, 'store'])->name('admin.user-add');
            Route::get('user-get/{user}', [UserController::class, 'edit'])->name('admin.user-get');
            Route::delete('delete-user/{user}', [UserController::class, 'delete'])->name('admin.user-delete');

            /** Coupon */
            Route::get('coupon', [CouponController::class, 'index'])->name('admin.coupon');
            Route::get('coupons-get', [CouponController::class, 'show'])->name('admin.coupons-get');
            Route::post('coupon-add', [CouponController::class, 'store'])->name('admin.coupon-add');
            Route::get('coupon-get/{coupon}', [CouponController::class, 'edit'])->name('admin.coupon-get');
            Route::delete('delete-coupon/{coupon}', [CouponController::class, 'delete'])->name('admin.coupon-delete');

            /* Address */
            Route::get('addresses', [BranchController::class, 'index'])->name('admin.addresses');
            Route::get('addresses-get', [BranchController::class, 'show'])->name('admin.addresses-get');
            Route::post('address-add', [BranchController::class, 'store'])->name('admin.address-add');
            Route::get('address-get/{address}', [BranchController::class, 'edit'])->name('admin.address-get');
            Route::delete('delete-address/{address}', [BranchController::class, 'delete'])->name('admin.address-delete');

            /** Banner */
            Route::get('advert', [AdvertController::class, 'index'])->name('admin.advert');
            Route::get('adverts-get', [AdvertController::class, 'show'])->name('admin.adverts-get');
            Route::post('advert-add', [AdvertController::class, 'store'])->name('admin.advert-add');
            Route::get('advert-get/{advert}', [AdvertController::class, 'edit'])->name('admin.advert-get');
            Route::delete('delete-advert/{advert}', [AdvertController::class, 'delete'])->name('admin.advert-delete');
        });

        // Product manager
        Route::middleware([
            'is_product_manager',
        ])->group(function () {
            /** Product */
            Route::get('products', [ProductController::class, 'index'])->name('admin.product');
            Route::get('products-get', [ProductController::class, 'show'])->name('admin.products-get');
            Route::get('product-address-get', [ProductController::class, 'productAddressShow'])->name('admin.product-address-get');
            Route::post('product-add', [ProductController::class, 'store'])->name('admin.product-add');
            Route::get('product-get/{product}', [ProductController::class, 'edit'])->name('admin.product-get');
            Route::delete('delete-product', [ProductController::class, 'delete'])->name('admin.delete-product');

            // product address
            Route::get('branch-product-get/{branchProduct}', [ProductController::class, 'editBranchProduct'])->name('admin.branch-product-get');
            Route::post('update-branch-product', [ProductController::class, 'updateBranchProduct'])->name('admin.update-branch-product');
            Route::delete('delete-product-address/{branchProduct}', [ProductController::class, 'deleteBranchProduct'])->name('admin.delete-branch-product');

            // recommended products
            Route::post('rproduct-add', [ProductController::class, 'storeRProduct'])->name('admin.rproduct-add');
            Route::delete('delete-rproduct/{rProduct}', [ProductController::class, 'deleteRProduct'])->name('admin.delete-rproduct');

            // product variants
            Route::post('product-variant-add', [ProductController::class, 'storeProductVariant'])->name('admin.product-variant-add');
            Route::delete('product-variant-delete/{productVariant}', [ProductController::class, 'deleteProductVariant'])->name('admin.product-variant-delete');
            Route::post('product-variant-info', [ProductController::class, 'productVariantsInfo'])->name('admin.product-variant-get');
            Route::post('delete-branch-product-variant', [ProductController::class, 'deleteBranchProductVariant'])->name('admin.product-variant-branch-delete');

            /* Product Business calculator */
            Route::get('product-business-calculators-get', [ProductController::class, 'getProductBusinessCalculator'])->name('admin.product-business-calculators-get');
            Route::post('product-business-calculators-show', [ProductController::class, 'showBusinessCalculator'])->name('admin.product-business-calculators-show');
            Route::post('product-business-calculator-add', [ProductController::class, 'storeBusinessCalculator'])->name('admin.product-business-calculator-add');
            Route::get('product-business-calculator-get/{prBusinessCalculator}', [ProductController::class, 'editBusinessCalculator'])->name('admin.product-business-calculator-get');
            Route::delete('delete-product-business-calculator/{prBusinessCalculator}', [ProductController::class, 'deleteBusinessCalculator'])->name('admin.product-business-calculator-delete');

            /* Business calculator */
            Route::get('business-calculators', [BusinessCalculatorController::class, 'index'])->name('admin.business-calculators');
            Route::get('business-calculators-get', [BusinessCalculatorController::class, 'show'])->name('admin.business-calculators-get');
            Route::post('business-calculator-add', [BusinessCalculatorController::class, 'store'])->name('admin.business-calculator-add');
            Route::get('business-calculator-get/{businessCalculator}', [BusinessCalculatorController::class, 'edit'])->name('admin.business-calculator-get');
            Route::delete('delete-business-calculator/{businessCalculator}', [BusinessCalculatorController::class, 'delete'])->name('admin.business-calculator-delete');

            /** Requested products */
            Route::get('requested-product', [RequestedProductController::class, 'index'])->name('admin.requested-product');
            Route::get('requested-products', [RequestedProductController::class, 'show'])->name('admin.requested-products-get');
            Route::post('requested-products-update', [RequestedProductController::class, 'store'])->name('admin.requested-products-add');
            Route::get('requested-product/{requestProduct}', [RequestedProductController::class, 'edit'])->name('admin.requested-product-get');
            Route::delete('delete-requested-product/{requestProduct}', [RequestedProductController::class, 'delete'])->name('admin.requested-product-delete');
        });

        Route::middleware([
            'is_staff'
        ])->group(function () {
            /** Order */
            Route::get('orders', [AdminOrderController::class, 'load'])->name('admin.orders');
            Route::get('orders-get', [AdminOrderController::class, 'show'])->name('admin.orders-get');;
            Route::post('order-delete', [AdminOrderController::class, 'delete'])->name('admin.order-delete');
            Route::post('order-view', [AdminOrderController::class, 'view'])->name('admin.order-view');
            Route::post('change-status', [AdminOrderController::class, 'changeStatus'])->name('admin.order-change-status');
            Route::post('update-payment-status', [AdminOrderController::class, 'updatePaymentStatus'])->name('admin.update-order-payment-status');
            Route::post('add-comment', [AdminOrderController::class, 'addComment'])->name('admin.add-comment');

            Route::get('ordersstatuses', 'OrderStatusesController@load');
            Route::post('ordersedit', 'OrdersController@edit');
            Route::post('orderdelete', 'OrdersController@delete');
            Route::post('orderDetailsDelete', 'OrdersController@orderDetailsDelete');
            Route::post('orderDetailsAdd', 'OrdersController@orderDetailsAdd');
            Route::post('orderview', 'OrdersController@orderview');
            Route::post('changeStatus', 'OrdersController@changeStatus');
            Route::post('changeDriver', 'OrdersController@changeDriver');

            Route::post('orderdone', 'OrdersController@done');
            Route::post('orderDelivery', 'OrdersController@delivery');
            /* -------------------------------- -------------------------------- --------------------------------*/
        });

        // Finance admin
        Route::middleware([
            'is_finance_manager',
        ])->group(function () {
            /** Transaction */
            Route::get('transaction', [TransactionController::class, 'index'])->name('admin.transaction');
            Route::get('transaction-get', [TransactionController::class, 'show'])->name('admin.transactions-get');
        });

        // Customer support
        Route::middleware([
            'is_customer_support',
        ])->group(function () {
            /** Chat */
            Route::get('chat', [ChatController::class, 'index'])->name('admin.chat');
            Route::post('chat/get-new-msgs-count', [ChatController::class, 'getNewMsgsCount'])->name('admin.new-msgs-count');
            Route::post('chats-get', [ChatController::class, 'getUserMsgs'])->name('admin.chats-get');
            Route::post('send-msg', [ChatController::class, 'sendNewMsg'])->name('admin.send-msg');
            Route::post('get-users', [ChatController::class, 'getUsers'])->name('admin.get-users');
        });
    });
});
