<?php

return [
    '0' => "Home",
    '1' => "Product",
    '2' => "Categories",
    '3' => "Products",
    '4' => "Extras",
    '5' => "Nutritions",
    '6' => "Products Reviews",
    '7' => "Top Products on Home Screen",
    '8' => "Markets",
    '9' => "Markets Reviews",
    '10' => "Top Markets on Home Screen",
    '11' => "Users",
    '12' => "Roles",
    '13' => "Permissions",
    '14' => "Orders",
    '15' => "Order Statuses",
    '16' => "Reports",
    '17' => "Most Popular Products",
    '18' => "Most Purchase Products",
    '19' => "Most Purchase Markets",
    '20' => "Drivers",
    '21' => "Coupons",
    '22' => "Notifications",
    '23' => "CHAT",
    '24' => "Wallet",
    '25' => "Media Library",
    '26' => "FAQ",
    '27' => "Settings",
    '28' => "Currencies",
    '29' => "Payments Methods",
    '30' => "Customer App Settings",
    '31' => "General",
    '32' => "Home Screen Layout",
    '33' => "Home Screen Colors",
    '34' => "Home Screen Sizes",
    '35' => "Logging",
    '36' => "Version",
    '37' => "MAIN NAVIGATION",
    '38' => "Total Earnings",
    '39' => "Total Orders",
    '40' => "Total Users",
    '41' => "Total Markets",
    '42' => "Last 10 Orders",
    '43' => "Order ID",
    '44' => "Total",
    '45' => "Client",
    '46' => "Order Status",
    '47' => "Market",
    '48' => "Details",
    '49' => "Updated At",
    '50' => "January",
    '51' => "February",
    '52' => "March",
    '53' => "April",
    '54' => "May",
    '55' => "June",
    '56' => "July",
    '57' => "August",
    '58' => "September",
    '59' => "October",
    '60' => "November",
    '61' => "December",
    '62' => "Earnings",
    '63' => "Categories - Categories Management",
    '64' => "LIST",
    '65' => "CREATE",
    '66' => "EDIT",
    '67' => "CATEGORIES LIST",
    '68' => "Id",
    '69' => "Name",
    '70' => "Image",
    '71' => "Description",
    '72' => "Updated At",
    '73' => "Published",
    '74' => "Action",
    '75' => "Published item",
    '76' => "Insert description",
    '77' => "Select from Library",
    '78' => "Drop files here or click to upload.",
    '79' => "Save Category",
    '80' => "Save Changes",
    '81' => "Are you sure?",
    '82' => "You will not be able to recover this item!",
    '83' => "Yes, delete it!",
    '84' => "No, cancel please!",
    '85' => "The Name field is required.",
    '86' => "The Image field is required.",
    '87' => "PRODUCTS LIST",
    '88' => "Price",
    '89' => "Market",
    '90' => "Category",
    '91' => "Insert Name",
    '92' => "Insert price",
    '93' => "Select Market",
    '94' => "Select Category",
    '95' => "Addition Parameters",
    '96' => "Discount Price",
    '97' => "Insert Discount Price",
    '98' => "Unit",
    '99' => "Enter the unit of product (ex:L, ml, Kg, g)",
    '100' => "Package",
    '101' => "Number of item per package (ex: 1, 6, 10)",
    '102' => "Weight",
    '103' => "Insert Weight of this product default unit is gramme (g)",
    '104' => "Ingredients",
    '105' => "Insert ingredients",
    '106' => "Extras",
    '107' => "Select Extras",
    '108' => "Nutritions",
    '109' => "Select Nutritions",
    '110' => "Save Product",
    '111' => "The Price field is required.",
    '112' => "The Category field is required.",
    '113' => "The Market field is required.",
    '114' => "No",
    '115' => "Products - Products Management",
    '116' => "Products - Extras Management",
    '117' => "CREATE EXTRAS GROUP",
    '118' => "EXTRAS GROUP",
    '119' => "EXTRAS",
    '120' => "Add New",
    '121' => "Create Extras Group",
    '122' => "Save Extras",
    '123' => "Product - Nutrition Management",
    '124' => "CREATE NUTRITIONS GROUP",
    '125' => "NUTRITION GROUPS",
    '126' => "NUTRITIONS",
    '127' => "Value",
    '128' => "Create Nutrition Group",
    '129' => "Insert value",
    '130' => "Save Nutrition",
    '131' => "Cancel",
    '132' => "The Value field is required.",
    '133' => "Products - Product Reviews Management",
    '134' => "PRODUCTS REVIEWS LIST",
    '135' => "Review",
    '136' => "Rate",
    '137' => "User",
    '138' => "Product",
    '139' => "Rate from 1 to 5",
    '140' => "Review",
    '141' => "Insert review",
    '142' => "Save",
    '143' => "The Product field is required.",
    '144' => "The Rate field is required.",
    '145' => "The Review field is required.",
    '146' => "Products - Top Products Management",
    '147' => "Add Products to List",
    '148' => "Markets - Markets Management",
    '149' => "MARKETS LIST",
    '150' => "Address",
    '151' => "Phone",
    '152' => "Phone",
    '153' => "Insert Address",
    '154' => "Insert Phone number",
    '155' => "Mobile phone",
    '156' => "Insert Mobile Phone number",
    '157' => "Delivery fee",
    '158' => "Insert Delivery Fee",
    '159' => "Percents",
    '160' => "Delivery fee may be in percentages from order or a given amount.",
    '161' => "If `percent` CheckBox is clear, the delivery fee in application set a given amount.",
    '162' => "Current",
    '163' => "Delivery Area",
    '164' => "Insert delivery area in km or miles (select in settings)",
    '165' => "Latitude",
    '166' => "Insert Latitude. Example: 52.2165157",
    '167' => "Longitude",
    '168' => "Insert Longitude. Example: 2.331359",
    '169' => "Opening Time",
    '170' => "Open",
    '171' => "Close",
    '172' => "Monday",
    '173' => "Tuesday",
    '174' => "Wednesday",
    '175' => "Thursday",
    '176' => "Friday",
    '177' => "Saturday",
    '178' => "Sunday",
    '179' => "The Latitude field is required.",
    '180' => "The Longitude field is required.",
    '181' => "Add to List",
    '182' => "Markets - Markets Reviews Management",
    '183' => "MARKET REVIEWS LIST",
    '184' => "The `Markets` field is required.",
    '185' => "Top Markets Management",
    '186' => "Add Markets",
    '187' => "Add Markets to List",
    '188' => "Market added",
    '191' => "Email",
    '192' => "Role",
    '193' => "Insert Email",
    '194' => "Password",
    '195' => "Insert Password",
    '196' => "Important.",
    '197' => "Select Markets for this Manager.",
    '198' => "Manager can edit Market Info, Products, Orders only for selected Markets on this list.",
    '199' => "Create User",
    '200' => "The `Email` field is required.",
    '201' => "Users - Roles Management",
    '202' => "ROLES LIST",
    '203' => "Default",
    '204' => "USERS LIST",
    '205' => "PERMISSION LIST",
    '206' => "Admin",
    '207' => "Manager",
    '208' => "Driver",
    '209' => "Client",
    '210' => "",
    '211' => "VIEW ORDER",
    '212' => "ORDERS LIST",
    '213' => "Curbside Pickup",
    '214' => "Customer arrived",
    '215' => "Client phone",
    '216' => "Client Address",
    '217' => "Create At",
    '218' => "Comments",
    '219' => "Market Address",
    '220' => "Market Phone",
    '221' => "Market Mobile Phone",
    '222' => "Status",
    '223' => "Date/Time",
    '224' => "Price",
    '225' => "Quantity",
    '226' => "Extras Price",
    '227' => "Extras quantity",
    '228' => "Add Products",
    '229' => "Coupon",
    '230' => "Subtotal",
    '231' => "Tax",
    '232' => "Rejected by Driver. Comment:",
    '233' => "Activate by Driver",
    '234' => "Complete by Driver",
    '235' => "Customer Arrived",
    '236' => "Set Driver:",
    '237' => "With coupon",
    '238' => "Count",
    '239' => "Add",
    '240' => "Add Products to Order",
    '241' => "Orders - Order Statuses Management",
    '242' => "ORDER STATUSES LIST",
    '243' => "Most Popular Products - Choice of Customers",
    '244' => "Customer Count",
    '245' => "Last Added Products To Favorites",
    '246' => "Customer Name",
    '247' => "Most Purchase Products",
    '248' => "Purchase Count",
    '249' => "Most Purchase Markets",
    '250' => "Orders Count",
    '251' => "Drivers - Drivers Management",
    '252' => "DRIVERS LIST",
    '253' => "Online",
    '254' => "Coupons - Coupons Management",
    '255' => "COUPONS LIST",
    '256' => "Coupon Code",
    '257' => "Discount",
    '258' => "Minimum amount",
    '259' => "Date Start",
    '260' => "Date End",
    '261' => "Insert Discount Fixed Amount (Ex: 10 for $10) or Percent (Ex: 8 for 8%)",
    '262' => "Minimum purchase amount",
    '263' => "0 for any purchase",
    '264' => "Max 250 symbols",
    '265' => "Date and Time Start",
    '266' => "Date and Time End",
    '267' => "Coupon for Markets",
    '268' => "Coupon for Category",
    '269' => "Coupon for Products",
    '270' => "Save Coupon",
    '271' => "Published Coupon",
    '272' => "Coupon for All markets",
    '273' => "Coupon for All categories",
    '274' => "Coupon for All products",
    '275' => "Discount In percents",
    '276' => "The `Discount` field is required",
    '277' => "The `Date and Time End` field is required",
    '278' => "The start time is greater than the end time",
    '279' => "The End time less than now",
    '280' => "Notifications Management",
    '281' => "NOTIFICATIONS LIST",
    '282' => "Date",
    '283' => "Title",
    '284' => "Text",
    '285' => "Statistics",
    '286' => "Send to All Users",
    '287' => "Insert message title",
    '288' => "Body text",
    '289' => "Insert message text",
    '290' => "Image for Notification",
    '291' => "Send Notification",
    '292' => "Chat",
    '293' => "Input Message",
    '294' => "Avatar",
    '295' => "Balans",
    '296' => "Arrive",
    '297' => "Loss",
    '298' => "Detail Information",
    '299' => "Current Balance",
    '300' => "New Balance",
    '301' => "Input New Balance",
    '302' => "Difference",
    '303' => "Input Comments",
    '304' => "Change Balance",
    '305' => "IN APP WALLET",
    '306' => "Upload File",
    '307' => "This is Demo App. You can't delete images",
    '308' => "Delete",
    '309' => "Done",
    '310' => "FAQ - FAQ Management",
    '311' => "FAQ LIST",
    '312' => "Question",
    '313' => "Answer",
    '314' => "Insert Question",
    '315' => "Insert Answer",
    '316' => "The `Question` field is required.",
    '317' => "The `Answer` field is required.",
    '318' => "Default tax",
    '319' => "Enter default tax in percents. 10 for (10%)",
    '320' => "Default unit of distance",
    '321' => "Enter the unit of distance (must restart the app to refresh it)",
    '322' => "Firebase Cloud Messaging Key",
    '323' => "Enter Firebase Cloud Messaging Key",
    '324' => "Enter Google Maps Api Key",
    '325' => "Create your own Google Maps API key at",
    '326' => "More information in",
    '327' => "Currencies - Currencies Management",
    '328' => "Default Currency",
    '329' => "Save Default Currency",
    '330' => "Currency symbol on right",
    '331' => "CURRENCIES LIST",
    '332' => "Symbol",
    '333' => "Code",
    '334' => "Digits",
    '335' => "Insert Currency Name",
    '336' => "Insert Currency Code. For US Dollar - USD",
    '337' => "Insert Currency Symbol. For US Dollar - $",
    '338' => "Insert Decimal Digits, after comma",
    '339' => "Save Currency",
    '340' => "The 'Code' field is required.",
    '341' => "The 'Symbol' field is required.",
    '342' => "Settings - Payments Methods",
    '343' => "Stripe (for Visa and MasterCards card)",
    '344' => "Publishable key",
    '345' => "Insert Publishable key",
    '346' => "Secret key",
    '347' => "Insert Secret key",
    '348' => "To get your individual keys go to the",
    '349' => "Stripe Dashboard.",
    '350' => "Razorpay",
    '351' => "Key Id",
    '352' => "Insert key",
    '353' => "Your company name",
    '354' => "Insert Company Name which will display on screen while payment process",
    '355' => "Razorpay Dashboard.",
    '356' => "PayPal",
    '357' => "Client Id",
    '358' => "Insert Client Id",
    '359' => "Secret key",
    '360' => "Insert Secret key",
    '361' => "PayPal Dashboard.",
    '362' => "PayStack",
    '363' => "PayStack Dashboard.",
    '364' => "Save Settings",
    '365' => "Enable Stripe Payments",
    '366' => "Enable Razorpay Payments",
    '367' => "Enable Cash on Delivery button",
    '368' => "Enable PayPal Payments",
    '369' => "PayPal SandBox Mode",
    '370' => "Enable In App Wallet",
    '371' => "Enable PayStack Payments",
    '372' => "The Stripe Key field is required",
    '373' => "The Stripe Secret Key field is required",
    '374' => "The RazorPay Key field is required",
    '375' => "The RazorPay Company Name field is required",
    '376' => "The PayPal Client Id field is required",
    '377' => "The PayPal Secret Key field is required",
    '378' => "The PayStack Key field is required",
    '379' => "Home Screen Theme",
    '380' => "Application language",
    '381' => "Select the default language of the application",
    '382' => "Main Color",
    '383' => "Radius",
    '384' => "Input value from 0 to 100",
    '385' => "Shadow",
    '386' => "Input value from 0 to 100",
    '387' => "Home Screen Theme Saved",
    '388' => "Type #1",
    '389' => "Type #2",
    '390' => "Dark Mode",
    '391' => "Home Screen Layout Builder",
    '392' => "Top Markets this week",
    '393' => "Top Trends this week",
    '394' => "Reviews",
    '395' => "Most Popular",
    '396' => "Categories",
    '397' => "Markets Near Your",
    '398' => "Search bar",
    '399' => "visible",
    '400' => "Home Screen Layout Saved",
    '401' => "Home Screen Colors",
    '402' => "Title Bar Color",
    '403' => "Icons Color",
    '404' => "Markets Title Color",
    '405' => "Markets Background Color",
    '406' => "Most Popular Title Color",
    '407' => "Most Popular Background Color",
    '408' => "Categories Title Color",
    '409' => "Categories Background Title Color",
    '410' => "Reviews Title Color",
    '411' => "Reviews Background Title Color",
    '412' => "Search Title Color",
    '413' => "Bottom Bar Color",
    '414' => "Home Screen Colors Saved",
    '415' => "Home Screen Sizes",
    '416' => "Market card width",
    '417' => "Input value from 20 to 100",
    '418' => "Market card height",
    '419' => "Top Markets card height",
    '420' => "Input value from 35 to 100",
    '421' => "Product card height",
    '422' => "Input value from 30 to 100",
    '423' => "Category card width",
    '424' => "Category card height",
    '425' => "Text Size",
    '426' => "Input value from 10 to 30",
    '427' => "Text Color",
    '428' => "Elements in one line",
    '429' => "Elements is circle",
    '430' => "Logging",
    '431' => "ADMIN PANEL",
    '432' => "APPLICATION API",
    '433' => "ADMIN PANEL LOGGING",
    '434' => "IP",
    '435' => "APPLICATION API LOGGING",
    '436' => "Select Language for Admin Panel",
    '437' => "Set language",
    '438' => "Received",
    '439' => "Preparing",
    '440' => "Ready",
    '441' => "On the Way",
    '442' => "Delivered",
    '443' => "Canceled",
    '444' => "Yandex.Kassa",
    '445' => "Client App Key",
    '446' => "Insert Client App Key",
    '447' => "Secret Key",
    '448' => "Insert Secret Key",
    '449' => "Shop Id",
    '450' => "Insert Shop Id",
    '451' => "instamojo",
    '452' => "Api key",
    '453' => "Insert Api key",
    '454' => "Private Token",
    '455' => "Insert Private Token",
    '456' => "Yandex.Kassa Dashboard",
    '457' => "instamojo Dashboard",
    '458' => "Enable Yandex.Kassa Payments",
    '459' => "Enable Instamojo Payments",
    '460' => "Enable SandBox Mode",
    '461' => "The Yandex.Kassa Shop Id field is required",
    '462' => "The Yandex.Kassa Client App Key field is required",
    '463' => "The Yandex.Kassa Secret Key field is required",
    '464' => "The Instamojo Private Api Key field is required",
    '465' => "The Instamojo Private Token field is required",
    '466' => "Payments Methods Saved",
    '467' => "This is demo app. You can not change this section",
    '468' => "Set Time Zone",
    '469' => "Select default Time Zone for Admin Panel",
    '470' => 'Order status changed',
    '471' => "New order arrived",
    '472' => "Order #",
    '473' => "New Order. Send message to admin. ",
    '474' => "New Order. Send message to manager ",
    '475' => "You order #",
    '476' => ' was ',
    '477' => 'Chat Message',
    '478' => 'Parent Category',

    '479' => 'Something went wrong',
    '480' => 'Search',
    '481' => 'Filter',
    '482' => "The `Password` field is required.",
    '483' => 'New user created',
    '484' => 'User with this address email already exist',
    '485' => 'Data saved successfully',
    '486' => 'Change Permissions can only Adminnistrator',
    '487' => 'Abort! This is demo mode',
    '488' => 'Permission Save',
    '489' => 'This is demo app. You can\'t change this section',
    '490' => "Unpublished item",
    '491' => "Categories Screen",
    '492' => "Offline",
    '493' => "No found",
    '494' => "Using the picture",
    '495' => "Image used",
    '496' => "Image unused",
    '497' => "Documents",
    '498' => "Documents Screen",
    '499' => "About Us",
    '500' => "Context",
    '501' => "Terms and Condition",
    '502' => "Privacy Policy",
    '503' => "Delivery info",
    '504' => "Refund Policy",
    '505' => "Banners",
    '506' => "Banners Screen",
    '507' => "Banners - Banners Management",
    '508' => "BANNERS LIST",
    '509' => "Banner 1",
    '510' => "Banner 2",
    '511' => "Is in",
    '512' => "Type",
    '513' => "Open Product",
    '514' => "Open External URL",
    '515' => "URL",
    '516' => "Insert External Link",
    '517' => "Enable About Us Page",
    '518' => "Enable Delivery info Page",
    '519' => "Enable Privacy Policy Page",
    '520' => "Enable Terms and Condition Page",
    '521' => "Enable Refund Policy Page",
    '522' => "Enable FAQ Page",
    '523' => "Banner 1",
    '524' => "Banner 2",
    '525' => "Company deleted",
    '526' => "Company already in list",
    '527' => "Product deleted",
    '528' => "Information",
    '529' => "Category Details",
    '530' => "Copyright",
    '531' => "Copyright text",
    '532' => "Insert Copyright text",
    '533' => "Enable Google Sign In",
    '534' => "Enable Facebook Sign In",
    '535' => "Default position on map in Application",
    '536' => "New order #",
    '537' => " was received",
    '538' => "New order received",
    '539' => "Items count",
    '540' => "Enable Phone Verification by SMS (OTP)",
    '541' => "Enable Curbside Pickup",
    '542' => "Enable Delivering",
    '543' => "Enable Coupons",
    '544' => "Enable Delivering Time",
    '545' => "Product variants",
    '546' => "Add new variant",
    '547' => "Recommended products",
    '548' => "Add product",
    '549' => "Product",
    '550' => "Minimum purchase amount",
    '551' => "For ex: 100. If 0 - no Minimum purchase amount",
    '552' => "Vendors",
    '553' => "VENDORS LIST",
    '554' => "Add to Order",
    '555' => "Attention!",
    '556' => "Changes will take effect after ",
    '557' => "restarting the application twice!",
    '558' => "All",
    '559' => "Create your own Top Trending list",
    '560' => "Create your own Top Market list",
    '561' => 'View',
    '562' => 'Profile',
    '563' => "Sign Out",
    '564' => "Edit",
    '565' => "The `Title` field is required.",
    '566' => "The `Text` field is required.",
    '567' => 'Google Maps Api Key',
    '568' => 'Home Screen Sizes Saved',
    '569' => 'Sign in to start your session',
    '570' => 'Remember Me',
    '571' => 'Send',
    '572' => 'Products Tree',
    '573' => 'TREE',
    '574' => 'PRODUCTS TREE',
    '575' => 'Products not found',
    '576' => 'Global category',
    "577" => "Deleted",
    '578' => 'Market Updated successfully',
    '579' => 'MARKETS REVIEWS LIST',
    '580' => "Admin Commission",
    '581' => "Insert commission for this vendor in percentages",
    '582' => "Default TAX",
    "583" => "Insert TAX for this vendor in percentages",
    '584' => "Bulk Upload",
    '585' => 'Drop CSV file here or click to upload.',
    '586' => "Delete",
    '587' => "You can not upload more then one file.",
    '588' => "Upload CSV file with data",
    '589' => "Upload",
    '590' => "Select CSV file",
    '591' => "Number of products added",
    '592' => "UPLOAD PRODUCTS",
    '593' => "Important!",
    '594' => "You can find example csv file in csv folder. Also you can find example ods file (for Open Office calc)",
    '595' => "All images need upload to server, to public/images folder",
    '596' => 'Transactions',
    '597' => 'Order Id',
    '598' => 'Order Total',
    '599' => 'Pay to Admin',
    '600' => 'Take from Admin',
    '601' => 'TRANSACTIONS',
    '602' => 'Pay to Vendor',
    '603' => 'Admin revenue',
    '604' => 'Admin Panel Settings',
    '605' => 'Restore settings',
    '606' => 'Alert color',
    '607' => 'Second Color',
    '608' => 'Send to All Users',
    '609' => 'Web Site Settings',
    '610' => 'Main Color Hover ',
    '611' => 'per kilometer or mile',
    '612' => 'Select Skin',
    '613' => 'Application Skin',
    '614' => 'Skin Saved',
    '615' => 'Seller Registration Page',
    '616' => 'Sellers Registration Request',
    '617' => 'Accept',
    '618' => 'Encryption Key',
    '619' => 'Insert Encryption Key',
    '620' => 'Encryption Key',
    '621' => 'Insert Encryption Key',
    '622' => 'Enable FlutterWave',
    '623' => 'Enable MercadoPago',
    '624' => 'Enable PayMob',
    '625' => 'The PayMob Api Key field is required',
    '626' => 'The PayMob Frame field is required',
    '627' => 'The PayMob Integration Id field is required',
    '628' => 'The MercadoPago Access Token field is required',
    '629' => 'The MercadoPago Public Key field is required',
    '630' => 'The FlutterWave Encryption Key field is required',
    '631' => 'The FlutterWave Public Key field is required',
    '632' => 'Links for Share This App Menu item',
    '633' => 'Google Play Link',
    '634' => 'Insert Google Play Link',
    '635' => 'AppStore Link',
    '636' => 'AppStore Link',
    '637' => "City",
    '638' => "Insert new city",
    '639' => 'Add city',
    '640' => 'City name',
    '641' => 'The City field is required.',
    '642' => 'Select cities for market',
    '643' => ' rejected by driver',
    '644' => 'Order #',
    '645' => ' accepted by driver',
    '646' => 'The Branch field is required.',
    '647' => 'The Brand field is required.',
    '648' => 'Product added successfully',

];
