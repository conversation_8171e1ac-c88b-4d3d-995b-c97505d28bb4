<?php

namespace App\Mail;

use App\Models\Company;
use App\Models\Image;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\HtmlString;

class EmailVerification extends Mailable
{
    use Queueable, SerializesModels;

    public $email;
    public $otp;
    public $companyName;
    public $companyLogo;

    /**
     * Create a new message instance.
     */
    public function __construct($email, $otp)
    {
        $company = Company::with(['image'])->first();
        if (!is_null($company->image)) {
            Image::updateImageFile($company->image);
            $this->companyName = $company->name;
            $this->companyLogo = $company->image->image_path;
        }
        $this->email = $email;
        $this->otp = str_split($otp);
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->companyName . ' Account Verification',
            // from: '<EMAIL>',
            to: $this->email,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.otp-verify',
            with: [
                'company_name' => $this->companyName,
                'logo' => $this->companyLogo,
                'otp' => $this->companyName,
            ]
            // html: (new MailMessage)
            //     ->subject("Kircha Email Verification Required")
            //     ->greeting("Hello, Welcome to Kircha!")
            //     ->line("Confirm Your Email")
            //     ->line("Here is your account activation code:")
            //     ->line(new HtmlString("<h1 style='color: green; font-weight: bold;'>" . $this->otp . "</h1>"))
            //     ->line("If you don't recognize this activity, please ignore this message.")
            //     ->action("Go to Kircha ", url(config('app.url')))
            //     ->salutation("Thank you for using Kircha")
            //     ->render()
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
