<?php

namespace App\Mail;

use App\Models\Company;
use App\Models\Image;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ResetEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $email;
    public $otp;
    public $companyName;
    public $companyLogo;

    /**
     * Create a new message instance.
     */
    public function __construct($email, $otp)
    {
        $company = Company::with(['image'])->first();
        if (!is_null($company->image)) {
            Image::updateImageFile($company->image);
            $this->companyName = $company->name;
            $this->companyLogo = $company->image->image_path;
        }
        $this->email = $email;
        $this->otp = str_split($otp);
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->companyName . ' Reset Password',
            // from: '<EMAIL>',
            to: $this->email,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.reset-pwd',
            with: [
                'company_name' => $this->companyName,
                'logo' => $this->companyLogo,
                'otp' => $this->companyName,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
