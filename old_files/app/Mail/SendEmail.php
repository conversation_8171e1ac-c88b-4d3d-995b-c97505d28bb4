<?php

namespace App\Mail;

use App\Models\Company;
use App\Models\Image;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SendEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $company;
    public $email;
    public $otp;
    public $view;

    /**
     * Create a new message instance.
     */
    public function __construct(
        $email,
        $otp,
        $subject,
        $view
    ) {
        $company = Company::with(['image'])->first();
        if (!is_null($company->image)) {
            Image::updateImageFile($company->image);
            $this->company = $company;
        }
        $this->email = $email;
        $this->otp = str_split($otp);
        $this->subject = $subject;
        $this->view = $view;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->company->name . ' ' . $this->subject,
            // from: '<EMAIL>',
            to: $this->email,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: $this->view,
            with: [
                'company_name' => $this->company->name,
                'logo' => $this->company->image->image_path,
                'otp' => $this->company->name,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
