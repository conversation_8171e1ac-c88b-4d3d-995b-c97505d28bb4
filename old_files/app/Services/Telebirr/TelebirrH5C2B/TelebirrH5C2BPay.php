<?php

namespace App\Services\Telebirr\TelebirrH5C2B;

use App\Services\Telebirr\TelebirrH5C2B\CreateOrder;
use Illuminate\Http\Request;

class TelebirrH5C2BPay
{
    // Base URL for fabric token
    private $baseUrl;
    private $req;
    private $fabricAppId;
    private $appSecret;
    private $merchantAppId;
    private $merchantCode;
    private $privateKey;
    private $notifyUrl;
    private $redirectUrl;

    function __construct() {}

    /**
     * Creates a new order in Telebirr and returns the H5 checkout link.
     *
     * @param string $baseUrl The base URL of the fabric token.
     * @param Request $req The request object containing the transaction ID, notify URL and return URL.
     *
     * @return array The H5 checkout link in the response format.
     */
    public function makeOrder(
        string $baseUrl,
        Request $req
    ) {
        $this->baseUrl = $baseUrl;
        $this->req = $req;
        $this->fabricAppId = env('FABRIC_APP_ID');
        $this->appSecret = env('APP_SECRET');
        $this->merchantAppId = env('APP_ID');
        $this->merchantCode = env('SHORTCODE');
        $this->privateKey = env('PRIVATE_KEY');
        $this->notifyUrl = $req->notifyUrl;
        $this->redirectUrl = $req->returnUrl;

        // Check if all the credentials are provided
        if (
            empty($this->fabricAppId) ||
            empty($this->appSecret) ||
            empty($this->merchantAppId) ||
            empty($this->merchantCode) ||
            empty($this->privateKey) ||
            empty($this->redirectUrl)
        ) {
            return ["error" => true, "msg" => "Some credentials are missing!"];
        }

        $createOrderService = new CreateOrder(
            $this->baseUrl,
            $this->req,
            $this->fabricAppId,
            $this->appSecret,
            $this->merchantAppId,
            $this->merchantCode,
            $this->privateKey,
            $this->notifyUrl,
            $this->redirectUrl
        );

        return $createOrderService->createOrder();
    }

    /**
     * Handle the payment callback request.
     *
     * @param array $payload
     * @return array
     */
    public function handleWebhook(array $payload)
    {
        // Response structure
        // {
        //     notify_url: 'http://197.156.68.29:5050/v2/api/order-v2/mini/payment',
        //     appid: '853694808089634',
        //     notify_time: '1670575472482',
        //     merch_code: '245445',
        //     merch_order_id: '1670575560882',
        //     payment_order_id: '00801104C911443200001002',
        //     total_amount: '10.00',
        //     "trans_id":"49485948475845",
        //     trans_currency: 'ETB',
        //     trade_status: 'Completed',
        //     trans_end_time: '1670575472000',
        //     sign: 'AOwWQF0QDg0jzzs5otLYOunoR65GGgC3hyr+oYn8mm1Qph6Een7C…',
        //     sign_type: 'SHA256WithRSA'
        //  }

        // // Extract required fields
        // $event = $payload['webHookEvent'] ?? null;
        // $signature = $payload['signature'] ?? null;
        // $bodyJson = $payload['body'] ?? null;

        // // Decode the JSON body
        // $body = json_decode($bodyJson, true);
        // if (!$body) {
        //     return ['success' => false, 'message' => 'Invalid JSON body'];
        // }

        // Extract payment details
        $paymentIntentId = $payload['merch_order_id'] ?? $body['payment_order_id'] ?? null;
        if ($paymentIntentId) {
            $orderId = $paymentIntentId;
            list($generatedUuid, $extractedOrderId) = explode('0A0', $orderId);
            $orderId = $extractedOrderId;
            $paymentStatus = $payload['trade_status'] ?? null;
            $totalAmount = $payload['total_amount'] ?? null;
            $currency = $payload['trans_currency'] ?? 'ETB';

            $data = [
                'order_id' => $orderId,
                'paymentIntentId' => $paymentIntentId,
                'status' => $paymentStatus,
                'amount' => $totalAmount,
                'currency' => $currency,
            ];

            // Log webhook request
            // Log::info('Payment Webhook Received:', $data);

            // Process payment success event
            if ($paymentStatus === 'Completed') {
                return ['success' => true, 'data' => $data];
            }
        }

        return ['success' => false, 'message' => 'Unhandled webhook event'];
    }
}
