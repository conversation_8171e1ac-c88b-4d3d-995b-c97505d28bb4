<?php

namespace App\Services\Telebirr\TelebirrH5C2B;

use Crypt_RSA;
use Exception;

class Utils
{
    /**
     * Signs the provided request data using RSA SHA-256.
     *
     * @param array $request The request data to be signed.
     * @return string Base64-encoded RSA signature.
     * @throws Exception If private key loading fails.
     */
    public static function sign($request, $privateKey)
    {
        $excludeFields = array("sign", "sign_type", "header", "refund_info", "openType", "raw_request");

        // Sort request data and remove excluded fields
        $filteredData = array_filter($request, fn($key) => !in_array($key, $excludeFields), ARRAY_FILTER_USE_KEY);

        // Sort request data alphabetically
        ksort($filteredData);

        // Build the query string
        $queryString = self::buildQueryString($filteredData);

        // Sort the string alphabetically
        $sortedString = self::sortedString($queryString);

        // Sign the sorted string
        return self::signWithRSA($sortedString, $privateKey);
    }

    /**
     * Converts an associative array into a query string.
     *
     * @param array $data The array to convert.
     * @return string The query string.
     */
    public static function buildQueryString($data)
    {
        $queryParts = [];

        foreach ($data as $key => $value) {
            if ($key === "biz_content" && is_array($value)) {
                foreach ($value as $subKey => $subValue) {
                    $queryParts[] = "{$subKey}={$subValue}";
                }
            } else {
                $queryParts[] = "{$key}={$value}";
            }
        }

        return implode('&', $queryParts);
    }

    /**
     * Sorts a query string alphabetically.
     *
     * @param string $queryString The string to sort.
     * @return string The sorted query string.
     */
    public static function sortedString($queryString)
    {
        $queryArray = explode("&", $queryString);
        sort($queryArray);
        return implode('&', $queryArray);
    }

    /**
     * Signs data using RSA SHA-256.
     *
     * @param string $data The data to sign.
     * @param string $privateKey The private key to use to sign.
     * @return string The base64-encoded signature.
     * @throws Exception If the private key cannot be loaded.
     */
    public static function SignWithRSA($data, $privateKey)
    {
        $rsa = new Crypt_RSA();

        // // TODO: It might not be needed
        // // Load the private key
        // $private_key_load = file_get_contents('./config/private_key.pem');
        // if (!$private_key_load) {
        //     throw new Exception("Error loading private key");
        // }
        // $private_key = self::trimPrivateKey($private_key_load)[2];
        $private_key = $privateKey;

        // Load the key into RSA
        if ($rsa->loadKey($private_key) != TRUE) {
            throw new Exception("Failed to load private key.");
        };

        // Set RSA signature mode and hash algorithm
        $rsa->setMGFHash("sha256");
        $rsa->setHash("sha256");

        // Generate the signature
        $signtureByte = $rsa->sign($data);

        return base64_encode($signtureByte);
    }

    /**
     * @Purpose: To trim the private key
     *
     * @Param: $stringData -> the private key to be trimmed
     * @Return: array of the return of explode function
     */
    public static function trimPrivateKey($stringData)
    {

        return explode("-----", (string)$stringData);
    }

    /**
     * @Purpose: Generate unique merchant order id
     *
     * @Param: no-Parameter is required.
     * @Return: String format of the time function.
     */
    public static function createMerchantOrderId()
    {
        return (string) time();
    }

    /**
     * @Purpose: Generate timestamp
     *
     * @Param: no-Parameter is required.
     * @Return: String format of the time function.
     */
    public static function createTimeStamp()
    {
        return (string) time();
    }

    /**
     * Generates a random alphanumeric string of a given length.
     *
     * @param int $length The length of the generated string (default: 32).
     * @return string The generated random string.
     */
    public static function createNonceStr($length = 32)
    {
        $chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        return substr(str_shuffle(str_repeat($chars, $length)), 0, $length);
    }
}
