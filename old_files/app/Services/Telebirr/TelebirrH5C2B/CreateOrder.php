<?php

namespace App\Services\Telebirr\TelebirrH5C2B;

use App\Helpers\Tool;
use App\Services\Telebirr\TelebirrH5C2B\Utils as H5Tools;
use Illuminate\Support\Facades\Http;

class CreateOrder
{
    public $req;
    public $BASE_URL;
    public $fabricAppId;
    public $appSecret;
    public $merchantAppId;
    public $merchantCode;
    public $privateKey;
    public $notifyUrl;
    public $redirectUrl;

    function __construct($baseUrl, $req, $fabricAppId, $appSecret, $merchantAppId, $merchantCode, $privateKey, $notifyUrl, $redirectUrl)
    {
        $this->BASE_URL = $baseUrl;
        $this->req = $req;
        $this->fabricAppId = $fabricAppId;
        $this->appSecret = $appSecret;
        $this->merchantAppId = $merchantAppId;
        $this->merchantCode = $merchantCode;
        $this->privateKey = $privateKey;
        $this->notifyUrl = $notifyUrl;
        $this->redirectUrl = $redirectUrl;
    }

    /**
     * @Purpose: Creating Order
     *
     * @Param: no parameters it takes from the constructor
     * @Return: rawRequest|String
     */
    function createOrder(): array
    {
        $applyFabricTokenResult = new ApplyFabricToken(
            $this->BASE_URL,
            $this->fabricAppId,
            $this->appSecret,
            $this->merchantAppId
        );

        // Fabric Token
        $result = $applyFabricTokenResult->applyFabricToken();
        if (isset($result->errorCode)) {
            return ["error" => true, "data" => $result];
        }
        $fabricToken = $result->token;

        // Get PrepayId
        $createOrderResult = $this->requestCreateOrder($fabricToken);
        if (isset($createOrderResult->errorCode)) {
            return ["error" => true, "data" => $createOrderResult];
        }
        $prepayId = $createOrderResult->biz_content->prepay_id;
        $rawRequest = $this->createRawRequest($prepayId);

        // H5 C2B Checkout link
        $CHECKOUT_URL = [env("TELEBIRR_DEV_CHECKOUT_URL_1")];
        if (!$this->req->test) {
            $CHECKOUT_URL = [env("TELEBIRR_PROD_CHECKOUT_URL_1")];
        }
        array_push($CHECKOUT_URL, env("TELEBIRR_CHECKOUT_URL_2"));
        $link =  $CHECKOUT_URL[0] . "?" . $rawRequest . $CHECKOUT_URL[1];
        return [
            "error" => false,
            "checkout_link" => $link
        ];
    }

    /**
     * @Purpose: Requests CreateOrder
     *
     * @Param: fabricToken|String title|string amount|string
     * @Return: String | Boolean
     */
    function requestCreateOrder($fabricToken)
    {
        // Header parameters
        $headers = array(
            "Content-Type: application/json",
            "X-APP-Key: " . $this->fabricAppId,
            "Authorization: " . $fabricToken
        );

        // Body parameters
        $payload = $this->createRequestObject($this->req->subject, $this->req->amount);

        $data = $payload;

        return Tool::makeRequest($this->BASE_URL . env("TELEBIRR_ORDER_URL"), $data, $headers, 20, false, 'POST');
    }

    /**
     * @Purpose: Creating a new merchantOrderId
     *
     * @Param: no parameters
     * @Return: returns a string format of time (UTC)
     */
    function createMerchantOrderId_()
    {
        return (string)time();
    }

    /**
     * @Purpose: Creating Request Object
     *
     * @Param: title|String and amount|String
     * @Return: Json encoded string
     */
    function createRequestObject($title, $amount)
    {
        $req = array(
            'nonce_str' => $this->req->nonce,
            'method' => 'payment.preorder',
            'timestamp' => $this->req->timestamp,
            'version' => '1.0',
            'biz_content' => [],
        );

        $biz = array(
            'notify_url' => $this->notifyUrl,
            'business_type' => 'BuyGoods',
            'trade_type' => 'Checkout',
            'appid' => $this->merchantAppId,
            'merch_code' => $this->merchantCode,
            'merch_order_id' => $this->req->transaction_id,
            'title' => $title,
            'total_amount' => $amount,
            'trans_currency' => 'ETB',
            'timeout_express' => '120m',
            'payee_identifier' => $this->merchantCode,
            'payee_identifier_type' => '04',
            'payee_type' => '5000',
            'redirect_url' => $this->redirectUrl,
            'callback_info' => 'From web',
        );

        $req['biz_content'] = $biz;
        $req['sign_type'] = 'SHA256WithRSA';

        $req['sign'] = H5Tools::sign($req, $this->privateKey);

        return json_encode($req);
    }

    /**
     * @Purpose: Create a rawRequest string for H5 page to start pay
     *
     * @Param: prepayId returned from the createRequestObject
     * @Return: rawRequest|string
     */
    function createRawRequest($prepayId)
    {
        $maps = array(
            "appid" => $this->merchantAppId,
            "merch_code" => $this->merchantCode,
            "nonce_str" => $this->req->nonce,
            "prepay_id" => $prepayId,
            "timestamp" => $this->req->timestamp,
            "sign_type" => "SHA256WithRSA"
        );

        $rawRequest = "";
        foreach ($maps as $map => $m) {
            $rawRequest .= $map . '=' . $m . "&";
        }
        $sign = H5Tools::sign($maps, $this->privateKey);
        // order by ascii in array
        $rawRequest = $rawRequest . 'sign=' . $sign;

        return $rawRequest;
    }
}
