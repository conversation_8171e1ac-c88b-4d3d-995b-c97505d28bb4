<?php

namespace App\Services\Telebirr\TelebirrH5C2B;

use App\Helpers\Tool;
use Illuminate\Support\Facades\Http;

class ApplyFabricToken
{
    public $BASE_URL;
    public $fabricAppId;
    public $appSecret;
    public $merchantAppId;

    function __construct($BASE_URL, $fabricAppId, $appSecret, $merchantAppId)
    {
        $this->BASE_URL = $BASE_URL;
        $this->fabricAppId = $fabricAppId;
        $this->appSecret = $appSecret;
        $this->merchantAppId = $merchantAppId;
    }
    /**
     * @Purpose: Apply fabric token generated by et-server
     *
     * @Param: no parameters needed it takes the fabricAppId and the appSecrete from the constructor of class ApplyFabricToken
     * @Return: response of "token", "effectiveDate", "expirationDate"
     */
    public function applyFabricToken(): object
    {
        $headers = array(
            "Content-Type: application/json",
            "X-APP-Key: " . $this->fabricAppId
        );

        $payload =  array(
            "appSecret" => $this->appSecret
        );
        $payload = json_encode($payload);

        $token = Tool::makeRequest($this->BASE_URL . "/payment/v1/token", $payload, $headers, 20, false, 'POST');
        return $token;
    }
}
