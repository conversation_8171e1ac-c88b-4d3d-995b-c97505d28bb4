<?php

namespace App\Helpers;

class Tool
{
    /**
     * Sends an HTTP request using cURL.
     *
     * @param string $url API endpoint URL.
     * @param array|string $data Request data.
     * @param array $headers HTTP headers.
     * @param int $timeout Request timeout (default: 30 seconds).
     * @param bool $useSSLVerification Enable SSL verification (default: false).
     * @param string $customRequestType HTTP method (default: 'POST').
     * @return object The response from the server.
     */
    public static function makeRequest($url, $data, $headers = [], $timeout = 30, $useSSLVerification = false, $customRequestType = 'POST'): object
    {
        $ch = curl_init($url);

        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $customRequestType,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => $useSSLVerification,
            CURLOPT_SSL_VERIFYHOST => $useSSLVerification ? 2 : 0,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_POSTFIELDS => is_array($data) ? json_encode($data) : $data
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            return (object) ['error' => curl_error($ch)];
        }

        curl_close($ch);

        return json_decode($response, false) ?? (object) ['raw_response' => $response];
    }
}
