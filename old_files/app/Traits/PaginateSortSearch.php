<?php

namespace App\Traits;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

trait PaginateSortSearch
{
    // How to
    // $request, $api_sms_query, ['message_status','jasmin_msg_id'], [], false, false
    private function run(Request $request, $stmt, $queryFields, $deepQueries = [], $paginate = true, $order =  true)
    {
        $validData = Validator::make($request->all(), [
            'query' => ['string'],
            'page' => ['integer'],
            'per_page' => ['integer'],
            'sort' => ['string'],
            'visible' => ['boolean'],
            'invisible' => ['boolean'],
            'order_by' => ['string'],
        ]);

        if ($validData->fails()) {
            return ['error' => true, 'msg' => $validData->errors()];
        }

        if ($request->has('query')) { // check for query parameter in Input
            $query = urldecode($request->get('query'));
            if (count($queryFields) > 0) {
                $stmt = $stmt->where(function ($stmt) use ($queryFields, $query) { // this will create condition like 'and (`segmentName` LIKE ? or `segmentJson` LIKE ?)'
                    $stmt->where($queryFields[0], 'LIKE', '%' . $query . '%');
                    for ($i = 1; $i < count($queryFields); $i++) {
                        $stmt->orWhere($queryFields[$i], 'LIKE', '%' . $query . '%');
                    }
                });
            }
            foreach ($deepQueries as $deepQuery => $deepValue) {
                # code...
                if (count($deepValue) > 0) {
                    $stmt = $stmt->whereHas($deepQuery, function ($stmt) use ($deepValue, $query) {
                        // this will create condition like 'and (`segmentName` LIKE ? or `segmentJson` LIKE ?)'
                        $stmt->where($deepValue[0], 'LIKE', '%' . $query . '%');
                        for ($i = 1; $i < count($deepValue); $i++) {
                            $stmt->orWhere($deepValue[$i], 'LIKE', '%' . $query . '%');
                        }
                    });
                }
            }
        }

        if ($request->has("start_date"))
            $stmt->where('created_at', '>=', $request->get("start_date"));

        if ($request->has("end_date"))
            $stmt->where('created_at', '<=', $request->get("end_date"));

        // Visible or Invisible
        if ($request->has('visible') && $request->has('invisible')) {
            if ($request->get('visible') == 0 && $request->get('invisible') == 0) {
                $stmt->whereNotIn('visible', [0, 1]);
            } else if (($request->has('visible') && $request->get('visible') == 1) && ($request->has('invisible') && $request->get('invisible') == 1)) {
                $stmt->whereIn('visible', [0, 1]);
            } else if ($request->has('visible') && $request->get('visible') == 1) {
                $stmt->where('visible', 1);
            } else if ($request->has('invisible') && $request->get('invisible') == 1) {
                $stmt->where('visible', 0);
            }
        }

        // Active or Inactive
        if ($request->has('active') && $request->has('inactive')) {
            if ($request->get('active') == 0 && $request->get('inactive') == 0) {
                $stmt->whereNotIn('active', [0, 1]);
            } else if (($request->has('active') && $request->get('active') == 1) && ($request->has('inactive') && $request->get('inactive') == 1)) {
                $stmt->whereIn('active', [0, 1]);
            } else if ($request->has('active') && $request->get('active') == 1) {
                $stmt->where('active', 1);
            } else if ($request->has('inactive') && $request->get('inactive') == 1) {
                $stmt->where('active', 0);
            }
        }

        // Status or Instatus
        if ($request->has('status') && $request->has('instatus')) {
            if ($request->get('status') == 0 && $request->get('instatus') == 0) {
                $stmt->whereNotIn('status', [0, 1]);
            } else if (($request->has('status') && $request->get('status') == 1) && ($request->has('instatus') && $request->get('instatus') == 1)) {
                $stmt->whereIn('status', [0, 1]);
            } else if ($request->has('status') && $request->get('status') == 1) {
                $stmt->where('status', 1);
            } else if ($request->has('instatus') && $request->get('instatus') == 1) {
                $stmt->where('status', 0);
            }
        }

        // Sorting
        $sort = 'desc';  // Default sort order
        if ($request->has('sort') && in_array($request->get('sort'), ['asc', 'desc'])) {
            $sort = $request->get('sort');
        }

        // Ordering
        if ($order) {
            if ($request->has('order_by')) {
                $stmt = $stmt->orderBy($request->get('order_by'), $sort);
            } else {
                $stmt = $stmt->orderBy('updated_at', $sort);
            }
        }

        if (!$paginate)
            return ['error' => false, 'data' => $stmt];

        // Per page limit
        $per_page = 10;
        if ($request->has("per_page"))
            $per_page = $request->get('per_page');

        return ['error' => false, 'data' => $stmt->paginate($per_page)];
    }
}
