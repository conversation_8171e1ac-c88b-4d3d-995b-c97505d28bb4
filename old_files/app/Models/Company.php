<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Company extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'phone',
        'mobile_phone',
        'address',
        'lat',
        'lng',
        'image_id',
        'min_purchase_amount',
        'min_discount_amount',
        'discount_amount',
        'tax',
        'fee',
        'percent',
        'per_km',
        'flat_rate',
        'image_id',
        'openTimeMonday',
        'closeTimeMonday',
        'openTimeTuesday',
        'closeTimeTuesday',
        'openTimeWednesday',
        'closeTimeWednesday',
        'openTimeThursday',
        'closeTimeThursday',
        'openTimeFriday',
        'closeTimeFriday',
        'openTimeSaturday',
        'closeTimeSaturday',
        'openTimeSunday',
        'closeTimeSunday',
    ];

    public function image(): HasOne
    {
        return $this->hasOne(Image::class, 'id', 'image_id');
    }
}
