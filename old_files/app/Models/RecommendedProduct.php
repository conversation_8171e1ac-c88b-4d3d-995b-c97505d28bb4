<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class RecommendedProduct extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_id',
        'recommended_product_id'
    ];

    public function images(): HasManyThrough
    {
        return $this->hasManyThrough(Image::class, ImageUpload::class, 'product_id', 'id', 'recommended_product_id', 'image_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function rproduct(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'recommended_product_id', 'id');
    }

    public function branchProducts(): Has<PERSON>any
    {
        return $this->hasMany(BranchProduct::class, 'product_id', 'recommended_product_id');
    }

    public function branches(): HasManyThrough
    {
        return $this->hasManyThrough(Branch::class, BranchProduct::class, 'product_id', 'id', 'recommended_product_id', 'branch_id');
    }

    // Fill products
    public static function fillRProduct($data)
    {
        if (gettype($data) != 'array') {
            $data = $data->toArray();
        }

        // For recommended product fill
        if (array_key_exists('rproduct', $data)) {
            if (count($data['rproduct']) != 0) {
                // Default Image
                if (empty($data['rproduct']['images'])) {
                    // TODO: Only add one
                    $data['rproduct']['images_link'] = [request()->getSchemeAndHttpHost() . '/storage/no-image.jpg', request()->getSchemeAndHttpHost() . '/storage/no-image.jpg', request()->getSchemeAndHttpHost() . '/storage/no-image.jpg'];
                } else {
                    $images = array();
                    for ($i = 0; $i < count($data['rproduct']['images']); $i++) {
                        $images[] = $data['rproduct']['images'][$i]['image_path'];
                    }
                    $data['rproduct']['images_link'] = $images;
                }

                // Price Format
                $data['rproduct']['price2'] = Util::makePrice($data['rproduct']['price']);
                $data['rproduct']['discount_price2'] = Util::makePrice($data['rproduct']['discount_price']);

                // Sold
                $sold = 0;
                $count = Order::whereHas('orderDetails', function ($query) use ($data) {
                    $query->where('orders.is_complete', 1)->where('order_details.product_id', $data['rproduct']['id']);
                })->with('orderDetails')->get();

                if (!empty($count) && !empty($count->toArray()[0]['order_details'])) {
                    $orderDetails = $count->toArray()[0]['order_details'];
                    foreach ($orderDetails as $orderDetail) {
                        $sold += $orderDetail['count'];
                    }
                }

                // Users
                $userOrders = Order::whereHas('orderDetails', function ($query) use ($data) {
                    $query->where('orders.is_complete', 1)->where('order_details.product_id', $data['rproduct']['id']);
                })
                    ->distinct('user_id')
                    ->with('orderDetails')
                    ->get();
                $noBoughtUsers = count($userOrders);
                $data['rproduct']['users_bought'] = $noBoughtUsers;

                // In stock
                $inStockQuant = 0;
                if (!empty($data['rproduct']['branch_products'])) {
                    foreach ($data['rproduct']['branch_products'] as $branchProduct) {
                        $inStockQuant += $branchProduct['quantity'];
                    }
                }

                // Max quantity
                $inStockMaxQuant = 0;
                $brProducts = BranchProduct::where('product_id', $data['rproduct']['id'])
                    ->whereNull('branch_products.product_variant_id')
                    ->whereHas('branch', function ($query) {
                        return $query->where('visible', 1);
                    })
                    ->get();
                if (!empty($brProducts)) {
                    foreach ($brProducts as $brProduct) {
                        // $inStockMaxQuant += $brProduct['maxquantity'];
                        $inStockMaxQuant += $brProduct['quantity'];
                    }
                }

                $data['rproduct']['in_stock'] = $inStockQuant;
                $data['rproduct']['sold'] = $sold;

                // $percentage = Util::calculateStock($inStockQuant, $sold);
                $stockPercentage = Util::calculateInStock($inStockMaxQuant, $inStockQuant);

                // $data['rproduct']['in_stock2'] = $percentage['in_stock'];
                $data['rproduct']['in_stock2'] = $stockPercentage['in_stock'];
                // $data['rproduct']['sold2'] = $percentage['sold'];
                $data['rproduct']['sold2'] = 100 - $stockPercentage['in_stock'];
            }
        }

        return $data;
    }

    // Fill recommeneded products
    public static function fillRProducts($data)
    {
        $data = $data->toArray();
        $updatedData = array_map(function ($data) {
            return RecommendedProduct::fillRProduct($data);
        }, $data);

        return $updatedData;
    }
}
