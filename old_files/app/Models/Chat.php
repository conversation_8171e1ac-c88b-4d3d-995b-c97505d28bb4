<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class Chat extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'from_user',
        'to_user',
        'msg',
        'delivered',
        'read'
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(User::class, 'from_user', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'to_user', 'id');
    }

    // public static function getUserUnreadMessagesCount()
    // {
    //     $user = Auth::user();
    //     if ($user == null)
    //         return 0;
    //     return count(DB::table('chat')->where("user", $user->id)->where('read', '=', "false")->where('author', '=', "manager")->get());
    // }

    // public static function getUserAllMessages()
    // {
    //     $user = Auth::user();
    //     if ($user == null)
    //         return 0;
    //     DB::table('chat')
    //         ->where('user', '=', $user->id)
    //         ->where('author', '=', "manager")
    //         ->update(array('read' => 'true', 'updated_at' => new \DateTime()));
    //     return DB::table('chat')->where('user', '=', $user->id)->orderBy('created_at', 'asc')->get();
    // }

    // public static function NewUserMessage($text)
    // {
    //     $user = Auth::user();
    //     if ($user == null)
    //         return 0;
    //     DB::table('chat')->insert(array(
    //         'user' => $user->id, 'text' => "$text", 'author' => "customer",
    //         'delivered' => "false", 'read' => "false",
    //         'created_at' => new \DateTime(),
    //         'updated_at' => new \DateTime(),
    //     ));

    //     Chat::SendNotifyToAdminAndManager(Lang::get(148), $text); // Chat Message
    //     // to user
    //     $defaultImage = DB::table('settings')->where('param', '=', "notify_image")->first()->value;
    //     Chat::sendNotify($user->id, Lang::get(148), $text, $defaultImage, "true"); // Chat Message
    // }

    // public static function SendNotifyToAdminAndManager($title, $text)
    // {
    //     //
    //     // Send Notifications to Admin and Managers
    //     //
    //     $managers = DB::table('users')->where('role', "<", "3")->get();
    //     $defaultImage = DB::table('settings')->where('param', '=', "notify_image")->first()->value;
    //     foreach ($managers as &$value) {
    //         Chat::sendNotify($value->id, $title, $text, $defaultImage, "true"); // Chat Message
    //     }
    // }

    // static public function sendNotify($id, $title, $body, $imageid, $chat)
    // {
    //     // $users = DB::table('users')->get();
    //     $uid = uniqid();

    //     $path_to_FCM = 'https://fcm.googleapis.com/fcm/send';
    //     $server_key = DB::table('settings')->where('param', '=', "firebase_key")->first()->value;
    //     $headers = array(
    //         'Authorization:key=' . $server_key,
    //         'Content-Type:application/json'
    //     );

    //     $user = DB::table('users')->where('id', '=', "$id")->first();
    //     $token = $user->fcbToken;

    //     $field = array(
    //         'notification' => array('body' => $body, 'title' => $title, 'click_action' => 'FLUTTER_NOTIFICATION_CLICK', 'sound' => 'default'), //, 'image' => $imageToSend),
    //         'priority' => 'high',
    //         'sound' => 'default',
    //         'data' => array('click_action' => 'FLUTTER_NOTIFICATION_CLICK', 'id' => '1', 'status' => 'done', 'body' => $body, 'title' => $title, 'sound' => 'default', 'chat' => $chat),
    //         'to' => $token,
    //     );

    //     //echo json_encode($field, JSON_PRETTY_PRINT);

    //     $payload = json_encode($field);
    //     $curl_session = curl_init();
    //     curl_setopt($curl_session, CURLOPT_URL, $path_to_FCM);
    //     curl_setopt($curl_session, CURLOPT_POST, true);
    //     curl_setopt($curl_session, CURLOPT_HTTPHEADER, $headers);
    //     curl_setopt($curl_session, CURLOPT_RETURNTRANSFER, true);
    //     curl_setopt($curl_session, CURLOPT_SSL_VERIFYPEER, false);
    //     curl_setopt($curl_session, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
    //     curl_setopt($curl_session, CURLOPT_POSTFIELDS, $payload);
    //     $result = curl_exec($curl_session);

    //     curl_close($curl_session);
    //     if ($result) {
    //         if ($chat == "false") {
    //             // add to database
    //             $values = array(
    //                 'title' => $title,
    //                 'text' => $body, 'user' => $id,
    //                 'image' => $imageid,
    //                 'uid' => $uid, 'delete' => 0,
    //                 'show' => 1, "read" => 0,
    //                 'updated_at' => new \DateTime()
    //             );
    //             $values['created_at'] = new \DateTime();
    //             DB::table('notifications')->insert($values);
    //         }
    //     }
    //     //curl_error($curl_session);
    // }
}
