<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'role',
        'email',
        'email_verified',
        'phone',
        'phone_verified_at',
        'password',
        'purchased_product',
        'identification_image_id',
        'referral_link',
        'active',
        '2fa',
        'remember_token',
        'role_id',
        'profession',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed'
    ];

    public function identificationImage()
    {
        return $this->belongsTo(Image::class, 'identification_image_id');
    }

    public function tradeLicenseImage()
    {
        return $this->belongsTo(Image::class, 'trade_license_image_id');
    }

    public function requestedProductImage()
    {
        return $this->belongsTo(Image::class, 'requested_product_image_id');
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id');
    }
}
