<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Wallet extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'balance',
        'status'
    ];

    public static function getBalance()
    {
        $balance = Wallet::where('user_id', '=', Auth::user()->id)->first();
        if (!is_null($balance)) {
            return [
                'balance' => $balance->balance
            ];
        }

        return null;
    }
}
