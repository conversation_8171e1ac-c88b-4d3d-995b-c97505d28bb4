<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Order extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'order_status', // enum('Order Received','Preparing','Ready','On the Way','Delivered','Canceled')
        'order_subtotal',
        'order_total',
        'payment_id',
        'phone',
        'curbside_pickup',
        'dropoff_address_id',
        'coupon_name',
        'coupon_discount_amount',
        'discount_amount',
        'min_discount_amount',
        'order_tax',
        'order_fee',
        'order_fee_amount',
        'per_km',
        'flat_rate',
        'percent',
        'is_complete',
        'view',
        'order_comment',
        'total_investment_discount_amount',
        'total_investment_incentive',
        'total_investment_point'
    ];

    public function orderDetails(): HasMany
    {
        return $this->hasMany(OrderDetail::class, 'order_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function payment_method(): BelongsTo
    {
        return $this->belongsTo(Payment::class, 'payment_id');
    }

    public static function getOrders()
    {
        $userid = Auth::user()->id;
        $page = 1;
        $offset = (($page - 1) * 8);
        $query = Order::where('user_id', $userid)->where('is_complete', 1)->with('orderDetails')->orderBy('id', 'DESC')->select('orders.*', DB::raw('(@row_number:=@row_number + 1) AS No'));
        $orders = $query->skip($offset)->take(8)->get();
        $orders->each(function ($userOrder, $index) {
            $userOrder->order_number = $index + 1;

            $ordersdata = $userOrder->orderDetails;
            Util::getOrderDetailsInfo($userOrder, $ordersdata);
        });

        $count = count($query->get());
        $t = ceil($count / 8);
        $orders = $orders->toArray();
        return [
            "orders" => $orders,
            "page" => $page,
            "pages" => (int)$t
        ];
    }

    public static function getOrdersPerPage($page)
    {
        $userid = Auth::user()->id;
        $offset = (($page - 1) * 8);
        DB::statement(DB::raw('set @row_number:=0'));
        $query = Order::where('user_id', $userid)->where('is_complete', 1)->with('orderDetails')->orderBy('id', 'DESC')->select('orders.*', DB::raw('(@row_number:=@row_number + 1) AS No'));
        $orders = $query->skip($offset)->take(8)->get();
        // $query = "SELECT orders.*, orderstatuses.status, (@row_number:=@row_number + 1) AS No
        //     FROM orders
        //     -- INNER JOIN ordersdetails ON ordersdetails.order=orders.id
        //     LEFT JOIN orderstatuses ON orderstatuses.id=orders.status
        //     WHERE orders.user=$userid AND orders.send=1
        //     GROUP BY orders.id
        //     ORDER BY orders.created_at DESC
        // ";
        // $orders = DB::select("$query LIMIT 8 OFFSET $offset");
        foreach ($orders as $userOrder) {
            // order details
            $ordersdata = $userOrder->order_details;
            // $ordersdata = DB::table('ordersdetails')->where('ordersdetails.order', '=', $userOrder->id)->select("ordersdetails.*")->get();
            Util::getOrderDetailsInfo($userOrder, $ordersdata);
        }
        $count = count(DB::select($query));
        $t = ceil($count / 8);
        return [
            "orders" => $orders,
            "page" => (int)$page,
            "pages" => (int)$t
        ];
    }
}
