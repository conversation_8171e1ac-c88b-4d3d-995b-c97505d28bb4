<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductBusinessCalculator extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'product_id',
        'from_amount',
        'to_amount',
        'discount_amount',
        'incentive',
        'point',
        'percent',
        'visible',
        'amount_of_items',
        'rental_yield',
        'property_appreciation',
    ];
}
