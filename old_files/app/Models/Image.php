<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;

class Image extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'image_path',
    ];

    public function userWithIdentificationImage()
    {
        return $this->hasMany(User::class, 'identification_image_id');
    }

    public function userWithTradeLicenseImage()
    {
        return $this->hasMany(User::class, 'trade_license_image_id');
    }

    public function userWithRequestedProductImage()
    {
        return $this->hasMany(User::class, 'product_image_id');
    }

    public function imageUploads(): HasMany
    {
        return $this->hasMany(ImageUpload::class, 'image_id');
    }

    public static function updateImageFile($image)
    {
        $domainName = Request::root();
        if ($image) {
            // file name
            $filename = '-';
            $filepath = explode("/", $image->image_path);
            if (is_array($filepath)) $filename = end($filepath);
            $image->filename = $filename;
            // file size
            $size = file_exists($image->image_path) ? filesize($image->image_path) : 0;
            $image->size = $size;
            // file path
            $image->image_path = $domainName . '/' . $image->image_path;
        } else {
            $image = is_null($image) ? [] : $image;
            $image['image_path'] = $domainName . '/' . 'storage/no-image.jpg';
        }
        return $image;
    }
}
