<?php

namespace App\Models;

use App\Mail\EmailVerification;
use App\Mail\ResetEmail;
use App\Mail\SendEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class OtpSession extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'phone',
        'otp',
        'session',
        'expires_at',
    ];

    /**
     * Send OTP to the selected OTP channel.
     * @param mixed $type
     * @param mixed $phone
     * @param mixed $email
     * @return array
     */
    public static function sendOTP($type, $phone = null, $email = null, $subject, $view)
    {
        if ($type != 'phone' && $type != 'email') {
            return [
                'error' => true,
                'msg' => 'OTP channel not found.'
            ];
        }

        // OTP Session time
        $otpSessionTime = 2;
        // $otpSessionTime = env('OTP_EXPIRE_MIN');
        // dd($otpSessionTime);
        $otp = rand(1111, 9999);
        $uuid = Str::uuid()->toString();
        $data = null;

        // Phone
        if ($type == 'phone' && $phone) {
            $phone = '251' . substr($phone, -9);
            // Check sms service
            $smsApi = Setting::where('param', 'sms_api')->first() ? Setting::where('param', 'sms_api')->first()->value : null;
            $smsStatus = Setting::where('param', 'sms_status')->first() ? Setting::where('param', 'sms_status')->first()->value : null;

            if ($smsApi == null && ($smsStatus == null || $smsStatus == '0')) {
                return [
                    'error' => true,
                    'data' => null,
                    'msg' => 'SMS service is not available. Will be available soon!',
                ];
            }

            // TODO: testing phone number
            $data = $phone;
            $res = otpSession::sendPhoneOTP($phone, $smsApi, $otp, $subject);
            if ($res['error'] == true) {
                return [
                    'error' => true,
                    'data' => $res['data'],
                    'msg' => $res['msg'],
                ];
            }
            // $otp = $res['data'];
        }

        // Email
        if ($type == 'email' && $email) {
            $data = $email;
            otpSession::sendEmailOTP($email, $otp, $subject, $view);
        }

        // Get the current time
        $currentTime = date('Y-m-d H:i:s');
        // Add 2 minutes to the current time
        $futureTime = date('Y-m-d H:i:s', strtotime('+' . $otpSessionTime . ' minutes', strtotime($currentTime)));

        OtpSession::create(
            [
                "phone" => $data,
                "session" => $uuid,
                "expires_at" => $futureTime,
                "otp" => $otp
            ]
        );

        return [
            'error' => false,
            'data' => $uuid,
            'msg' => 'Something went wrong.',
        ];
    }

    public static function sendOTP2($type, $phone = null, $email = null)
    {
        if ($type != 'sms' && $type != 'email') {
            return;
        }

        // OTP Session time
        $otpSessionTime = 2;
        $otp = rand(1111, 9999);
        $uuid = Str::uuid()->toString();
        $data = null;

        // Phone
        if ($type == 'sms' && $phone) {
            // Check sms service
            $smsApi = Setting::where('param', 'sms_api')->first() ? Setting::where('param', 'sms_api')->first()->value : null;
            $smsStatus = Setting::where('param', 'sms_status')->first() ? Setting::where('param', 'sms_status')->first()->value : null;

            if ($smsApi == null && ($smsStatus == null || $smsStatus == '0')) {
                return [
                    'error' => true,
                    'data' => null,
                    'msg' => 'SMS service is not available. Will be available soon!',
                ];
            }

            // TODO: testing phone number
            $phone = "************";
            $data = $phone;
            // $host = "https://api.geezsms.com/api/v1/sms/otp";
            // $response = Http::post($host, ["token" => $smsApi, "phone" => $phone]);
            // Http::post("https://webhook.site/bf413ea1-0786-44c9-a93b-09e8447c2d73", ["phone" => $phone, "otp" => $response->object()]);
            // $response = $response->object();
            // if ($response->data->error == true) {
            //     return [
            //         'error' => true,
            //         'data' => null,
            //         'msg' => 'Something went wrong sending otp.',
            //     ];
            // }
            // $otp = $response->code;
        }

        // Email
        if ($type == 'email' && $email) {
            $data = $email;
            // Mail::to($email)->send(new EmailVerification($email, $otp));
            Mail::to($email)->send(new SendEmail($email, $otp, 'Account Verification', 'emails.otp-verify'));
        }

        // Get the current time
        $currentTime = date('Y-m-d H:i:s');
        // Add 2 minutes to the current time
        $futureTime = date('Y-m-d H:i:s', strtotime('+' . $otpSessionTime . ' minutes', strtotime($currentTime)));

        OtpSession::create(
            [
                "phone" => $data,
                "session" => $uuid,
                "expires_at" => $futureTime,
                "otp" => $otp
            ]
        );

        return [
            'error' => false,
            'data' => $uuid,
            'msg' => 'Something went wrong.',
        ];
    }

    public static function resetPwd($type, $phone = null, $email = null)
    {
        if ($type != 'sms' && $type != 'email') {
            return;
        }

        // OTP Session time
        $otpSessionTime = 2;
        $otp = rand(1111, 9999);
        $uuid = Str::uuid()->toString();
        $data = null;

        // Phone
        if ($type == 'sms' && $phone) {
            // Check sms service
            $smsApi = Setting::where('param', 'sms_api')->first() ? Setting::where('param', 'sms_api')->first()->value : null;
            $smsStatus = Setting::where('param', 'sms_status')->first() ? Setting::where('param', 'sms_status')->first()->value : null;

            if ($smsApi == null && ($smsStatus == null || $smsStatus == '0')) {
                return [
                    'error' => true,
                    'data' => null,
                    'msg' => 'SMS service is not available. Will be available soon!',
                ];
            }

            // TODO: testing phone number
            $phone = "************";
            $data = $phone;
            // $host = "https://api.geezsms.com/api/v1/sms/otp";
            // $response = Http::post($host, ["token" => $smsApi, "phone" => $phone]);
            // Http::post("https://webhook.site/bf413ea1-0786-44c9-a93b-09e8447c2d73", ["phone" => $phone, "otp" => $response->object()]);
            // $response = $response->object();
            // if ($response->data->error == true) {
            //     return [
            //         'error' => true,
            //         'data' => null,
            //         'msg' => 'Something went wrong sending otp.',
            //     ];
            // }
            // $otp = $response->code;
        }

        // Email
        if ($type == 'email' && $email) {
            $data = $email;
            // Mail::to($email)->send(new ResetEmail($email, $otp));
            Mail::to($email)->send(new SendEmail($email, $otp, 'Reset Password', 'emails.reset-pwd'));
        }

        // Get the current time
        $currentTime = date('Y-m-d H:i:s');
        // Add 2 minutes to the current time
        $futureTime = date('Y-m-d H:i:s', strtotime('+' . $otpSessionTime . ' minutes', strtotime($currentTime)));

        OtpSession::create(
            [
                "phone" => $data,
                "session" => $uuid,
                "expires_at" => $futureTime,
                "otp" => $otp
            ]
        );

        return [
            'error' => false,
            'data' => $uuid,
            'msg' => 'Something went wrong.',
        ];
    }

    public static function sendPhoneOTP($phone, $api, $otp, $subject)
    {
        // $host = "https://api.geezsms.com/api/v1/sms/otp";
        // "https://api.geezsms.com/api/v1/sms/send";
        $host = env("GEEZ_ENDPOINT");
        $response = Http::post($host, ["token" => $api, "phone" => $phone, "msg" => "{$subject}\n\nYour OTP is {$otp}. Do not share it. If you didn't request this, ignore this message."]);
        // Http::post("https://webhook.site/bf413ea1-0786-44c9-a93b-09e8447c2d73", ["phone" => $phone, "otp" => $response->object()]);
        $responseData = $response->object();
        if ($responseData->error == true) {
            Log::info("OTP not sent.");
            Log::error("Failed to send OTP", [
                'error' => $responseData->error,
                'phone' => $phone,
                'response' => $response->body(),
                'api_endpoint' => $host,
                'timestamp' => now()->toDateTimeString()
            ]);
            return [
                'error' => true,
                'data' => null,
                'msg' => 'Something went wrong sending otp.',
            ];
        }
        Log::info("OTP sent to {$phone} with code {$otp}");
        // $otp = $response->code;
        return [
            'error' => false,
            'data' => $otp,
        ];
        // $clientMsg = "OTP Verification\nOTP code: 1234";
        // $smsClientResponse = Http::post(
        //     "https://api.geezsms.com/api/v1/sms/send",
        //     [
        //         "token" => $geezSMStoken,
        //         "phone" => $clientPhone,
        //         "msg" => $clientMsg,
        //         "shortcode_id" => 7
        //     ]
        // );
    }

    public static function sendEmailOtp($email, $otp, $subject, $view): void
    {
        Mail::to($email)->send(new SendEmail($email, $otp, $subject, $view));
    }
}
