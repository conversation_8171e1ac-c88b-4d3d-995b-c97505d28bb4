<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class ProductVariant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_id',
        'name',
        'price',
        'discount_price',
        'desc',
        'video_link',
        'soon_instock',
        'visible'
    ];

    public function images(): HasManyThrough
    {
        return $this->hasManyThrough(Image::class, ImageUpload::class, 'product_variant_id', 'id', 'id', 'image_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function branchProducts(): HasMany
    {
        return $this->hasMany(BranchProduct::class, 'product_variant_id', 'id');
    }

    public function branches(): HasManyThrough
    {
        return $this->hasManyThrough(Branch::class, BranchProduct::class, 'product_variant_id', 'id', 'id', 'branch_id');
    }
}
