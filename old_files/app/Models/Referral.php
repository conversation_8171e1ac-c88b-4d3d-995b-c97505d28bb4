<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;

class Referral extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'referrer_user_id',
        'referred_user_id',
        'referral_status',
        'reward_amount'
    ];

    // Generate link
    public static function generate()
    {
        $link = Str::uuid();
        $user = User::find(Auth::user()->id);
        $user->referral_link = $link;
        $user->save();
        return Referral::buildUrl($link);
    }

    // Get referral link
    public static function referalLink()
    {
        if (!Auth::check())
            return;

        $user = User::find(Auth::user()->id);
        return Referral::buildUrl($user->referral_link);
    }

    public static function referalPoints()
    {
        if (!Auth::check())
            return;

        $user = User::find(Auth::user()->id);
        return $user->referral_points;
    }

    // Generate url
    public static function buildUrl($link)
    {
        $url = null;
        if ($link) $url = Request::root() . '?referalCode=' . $link;
        return $url;
    }

    public static function userPoints()
    {
        $referral = Referral::where('referrer_user_id', Auth::user()->id)->where('referral_status', 'Complete')->get();

        $nowYear = Carbon::now()->year;
        $e1 = 0;
        $e2 = 0;
        $e3 = 0;
        $e4 = 0;
        $e5 = 0;
        $e6 = 0;
        $e7 = 0;
        $e8 = 0;
        $e9 = 0;
        $e10 = 0;
        $e11 = 0;
        $e12 = 0;

        foreach ($referral as &$value) {
            if ($nowYear == Carbon::createFromFormat('Y-m-d H:i:s', $value->updated_at)->year) {
                $month = Carbon::createFromFormat('Y-m-d H:i:s', $value->updated_at)->month;
                if ($month == 1)
                    $e1 += $value->total;
                if ($month == 2)
                    $e2 += $value->total;
                if ($month == 3)
                    $e3 += $value->total;
                if ($month == 4)
                    $e4 += $value->total;
                if ($month == 5)
                    $e5 += $value->total;
                if ($month == 6)
                    $e6 += $value->total;
                if ($month == 7)
                    $e7 += $value->total;
                if ($month == 8)
                    $e8 += $value->total;
                if ($month == 9)
                    $e9 += $value->total;
                if ($month == 10)
                    $e10 += $value->total;
                if ($month == 11)
                    $e11 += $value->total;
                if ($month == 12)
                    $e12 += $value->total;
            }
        }

        return [
            'e1' => $e1, 'e2' => $e2, 'e3' => $e3, 'e4' => $e4, 'e5' => $e5, 'e6' => $e6,
            'e7' => $e7, 'e8' => $e8, 'e9' => $e9, 'e10' => $e10, 'e11' => $e11, 'e12' => $e12,
        ];
    }

    // convert to money
    public static function convertPoints($points)
    {
        $amount = Util::percent_calculate($points, 1) * $points;
        return $amount;
    }

    // convert to points
    public static function convertMoney($money)
    {
        $points = Util::percent_calculate($money, 1) * $money;
        return $points;
    }
}
