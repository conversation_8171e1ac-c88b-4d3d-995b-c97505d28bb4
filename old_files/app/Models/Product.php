<?php

namespace App\Models;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\QueryException;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

class Product extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'price',
        'discount_price',
        'desc',
        'video_link',
        'soon_instock',
        'featured',
        'visible'
    ];

    public function imageUploads(): HasMany
    {
        return $this->hasMany(ImageUpload::class, 'product_id', 'id');
    }

    public function images(): HasManyThrough
    {
        return $this->hasManyThrough(Image::class, ImageUpload::class, 'product_id', 'id', 'id', 'image_id');
    }

    public function branchProducts(): HasMany
    {
        return $this->hasMany(BranchProduct::class, 'product_id', 'id')->whereNull('product_variant_id');
    }

    public function branches(): HasManyThrough
    {
        return $this->hasManyThrough(Branch::class, BranchProduct::class, 'product_id', 'id', 'id', 'branch_id');
    }

    public function recommended(): HasMany
    {
        return $this->hasMany(RecommendedProduct::class, 'product_id', 'id');
    }

    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'product_id', 'id');
    }

    public function businessCalculator(): HasMany
    {
        return $this->hasMany(ProductBusinessCalculator::class, 'product_id', 'id');
    }

    // Fill products
    public static function fillProduct($data, $step = 0)
    {
        if (gettype($data) != 'array') {
            $data = $data->toArray();
        }

        $data['price2'] = Util::makePrice($data['price']);
        $data['discount_price2'] = Util::makePrice($data['discount_price']);
        if (empty($data['images'])) {
            // TODO: Only add one
            // $data['images'] = [request()->getSchemeAndHttpHost() . '/storage/no-image.jpg', request()->getSchemeAndHttpHost() . '/storage/no-image.jpg', request()->getSchemeAndHttpHost() . '/storage/no-image.jpg'];
            for ($i = 0; $i < 4; $i++) {
                $data['images'][$i]['image_path'] = 'storage/no-image.jpg';
            }
        } else {
            // $images = array();
            // for ($i = 0; $i < count($data['images']); $i++) {
            //     $images[] = $data['images'][$i]['image_path'];
            // }
            // $data['images'] = $images;
        }

        // For product variant fill
        if (array_key_exists('variants', $data)) {
            if (count($data['variants']) != 0) {
                $data['variants'] = array_map(function ($var) {
                    // Default Image
                    if (empty($var['images'])) {
                        // TODO: Only add one
                        $var['images_link'] = ['storage/no-image.jpg', 'storage/no-image.jpg', 'storage/no-image.jpg'];
                    } else {
                        $images = array();
                        for ($i = 0; $i < count($var['images']); $i++) {
                            $images[] = $var['images'][$i]['image_path'];
                        }
                        $var['images_link'] = $images;
                    }

                    // Price Format
                    $var['price2'] = Util::makePrice($var['price']);
                    $var['discount_price2'] = Util::makePrice($var['discount_price']);

                    return $var;
                }, $data['variants']);
            }
        }

        // For recommended product fill
        if ($step == 0) {
            if (array_key_exists('recommended', $data)) {
                if (count($data['recommended']) != 0) {
                    $data['recommended'] = array_map(function ($var) {
                        $var['rproduct'] = Product::fillProduct($var['rproduct'], 1);
                        return $var;
                    }, $data['recommended']);
                }
            }
        }

        // Calculate Total sold
        $sold = 0;
        $count = Order::whereHas('orderDetails', function ($query) use ($data) {
            $query->where('orders.send', 1)->where('orders.is_complete', 1)->where('orders.view', 1)->where('order_details.product_id', $data['id']);
        })->with('orderDetails')->get();

        // Calculate total sold
        if (!empty($count)) {
            $ordersList = $count;
            foreach ($ordersList as $ordersListVal) {
                $ordersListVal = $ordersListVal->orderDetails;
                foreach ($ordersListVal as $orderDetail) {
                    $sold += $orderDetail['count'];
                }
            }
        }

        // Users who bought
        $userOrders = Order::whereHas('orderDetails', function ($query) use ($data) {
            $query->where('orders.send', 1)->where('orders.is_complete', 1)->where('orders.view', 1)->where('order_details.product_id', $data['id']);
        })
            ->distinct('user_id')
            ->with('orderDetails')
            ->get();
        $noBoughtUsers = count($userOrders);

        // Current stock (total quantity across all branches)
        $inStockQuant = 0;
        if (!empty($data['branch_products'] ?? [])) {
            // $inStockQuant = collect($data['branch_products'] ?? [])->sum('quantity');
            $inStockQuant = $data['branch_products'][0]['quantity'];
        }

        // Calculate total volume (current stock + sold)
        $totalVolume = $inStockQuant + $sold;

        // Calculate percentages based on total volume
        // $inStockPercentage = $totalVolume > 0 ? ($inStockQuant / $totalVolume) * 100 : 0;
        // $soldPercentage = $totalVolume > 0 ? ($sold / $totalVolume) * 100 : 0;

        // $soldPercentage = $inStockQuant > 0 ? ($sold / $inStockQuant) * 100 : 0;
        $soldPercentage = $sold;
        $inStockPercentage = $inStockQuant > 0 ? 100 - $soldPercentage : 0;

        // Set the data
        $data['in_stock'] = $inStockQuant;
        $data['sold'] = $sold . '%';
        // $data['in_stock2'] = round($inStockPercentage);
        $data['in_stock2'] = round($inStockQuant);
        $data['sold2'] = round($soldPercentage);
        // $data['users_bought'] = $noBoughtUsers;
        $data['users_bought'] = Order::whereHas('orderDetails', function ($query) use ($data) {
            $query->where('orders.is_complete', 1)
                ->where('orders.view', 1)
                ->where('order_details.product_id', $data['id']);
        })->distinct('user_id')->count();

        return $data;
    }

    // Fill products
    public static function fillProducts($data)
    {
        $data = $data->toArray();
        $updatedData = array_map(function ($data) {
            return Product::fillProduct($data);
        }, $data);

        return $updatedData;
    }

    // Fill product vairants
    public static function fillProductVariant($data)
    {
        $data->price = number_format($data->price, 0);
        $data->discountprice = number_format($data->discountprice, 0);
        // variant branches
        $data->branches = Branch::getVariantBranches($data->variant_id);
        $data->defaultBranch = Branch::getBranch($data->branch_id);
    }

    // Get product's info by id
    public static function getProduct($id, $cat = 0)
    {
        $product = Product::where('visible', 1)->where('id', $id);

        // If cat == 2 get out of stock products
        if ($cat == 2) {
            $product = $product->where('products.soon_instock', 0)
                ->whereHas('branchProducts', function ($query) use ($id) {
                    $query->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id');
                })
                ->whereHas('branches', function ($query) use ($id) {
                    $query->whereHas('branchProducts', function ($subQuery) use ($id) {
                        $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id');
                    });
                })
                ->with([
                    'images',
                    'branchProducts' => function ($query) use ($id) {
                        $query->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id')->with(['branch']);
                    },
                    'branches' => function ($query) use ($id) {
                        $query->whereHas('branchProducts', function ($subQuery) use ($id) {
                            $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id');
                        });
                    },
                    'variants' => function ($query) use ($id) {
                        $query->where('product_variants.product_id', $id)->whereHas('branchProducts', function ($subQuery) use ($id) {
                            $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNotNull('product_variant_id');
                        })->with(['images', 'branches', 'branchProducts.branch']);
                    },
                    'recommended' => function ($query) use ($id) {
                        $query->where('recommended_products.product_id', $id)->whereHas('branchProducts', function ($subQuery) use ($id) {
                            $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNotNull('product_variant_id');
                        })->with(['images', 'branches', 'branchProducts.branch']);
                    },
                    'businessCalculator' => function ($query) use ($id) {
                        $query->where('product_business_calculators.visible', 1);
                    },
                ]);
        }
        // If cat == 3 get soon to be in stock products
        else if ($cat == 3) {
            $product = $product->where('products.soon_instock', 1)
                ->with([
                    'images',
                    'branches',
                    'branchProducts.branch',
                    'variants' => function ($query) {
                        $query->whereHas('branchProducts', function ($subQuery) {
                            $subQuery->whereNotNull('product_variant_id');
                        })->with(['images', 'branches', 'branchProducts.branch']);
                    },
                    'recommended' => function ($query) use ($id) {
                        $query->whereHas('branchProducts', function ($subQuery) {
                            $subQuery->whereNotNull('product_variant_id');
                        })->with(['images', 'branches', 'branchProducts.branch']);
                    },
                    'businessCalculator' => function ($query) use ($id) {
                        $query->where('product_business_calculators.visible', 1);
                    },
                ]);
        } else if ($cat == 1) {
            $product = $product->where('products.soon_instock', 0)
                ->whereHas('branchProducts', function ($query) use ($id) {
                    $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id');
                })
                ->whereHas('branches', function ($query) use ($id) {
                    $query->whereHas('branchProducts', function ($subQuery) use ($id) {
                        $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id');
                    });
                })
                ->with([
                    'images',
                    'branchProducts' => function ($query) use ($id) {
                        $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id')->with(['branch']);
                    },
                    'branches' => function ($query) use ($id) {
                        $query->whereHas('branchProducts', function ($subQuery) use ($id) {
                            $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id');
                        });
                    },
                    'variants' => function ($query) use ($id) {
                        $query
                            ->where('product_variants.visible', 1)
                            ->where('product_variants.product_id', $id)
                            ->whereHas('branchProducts', function ($subQuery) use ($id) {
                                $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNotNull('product_variant_id');
                            })->with(['images', 'branches', 'branchProducts.branch']);
                    },
                    'recommended' => function ($query) use ($id) {
                        $query
                            ->where('recommended_products.product_id', $id)
                            ->whereHas('rproduct', function ($subQuery) {
                                $subQuery->where('products.visible', 1)->where('products.soon_instock', 0);
                            })
                            // ->whereHas('branchProducts', function ($subQuery) {
                            //     $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->whereNotNull('product_variant_id');
                            // })
                            ->with(['rproduct.images', 'rproduct.branchProducts.branch', 'rproduct.branches']);
                    },
                    'businessCalculator' => function ($query) use ($id) {
                        $query->where('product_business_calculators.visible', 1);
                    },
                ]);
        } else {
            $product = $product
                ->whereHas('branchProducts', function ($query) use ($id) {
                    $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id');
                })
                ->whereHas('branches', function ($query) use ($id) {
                    $query->whereHas('branchProducts', function ($subQuery) use ($id) {
                        $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id');
                    });
                })
                ->with([
                    'images',
                    'branchProducts' => function ($query) use ($id) {
                        $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id')->with(['branch']);
                    },
                    'branches' => function ($query) use ($id) {
                        $query->whereHas('branchProducts', function ($subQuery) use ($id) {
                            $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id');
                        });
                    },
                    'variants' => function ($query) use ($id) {
                        $query
                            ->where('product_variants.visible', 1)
                            ->where('product_variants.product_id', $id)
                            ->whereHas('branchProducts', function ($subQuery) use ($id) {
                                $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNotNull('product_variant_id');
                            })->with(['images', 'branches', 'branchProducts.branch']);
                    },
                    'recommended' => function ($query) use ($id) {
                        $query
                            ->where('recommended_products.product_id', $id)
                            ->whereHas('rproduct', function ($subQuery) {
                                $subQuery->where('products.visible', 1)->where('products.soon_instock', 0);
                            })
                            // ->whereHas('branchProducts', function ($subQuery) {
                            //     $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->whereNotNull('product_variant_id');
                            // })
                            ->with(['rproduct.images', 'rproduct.branchProducts.branch', 'rproduct.branches']);
                    },
                    'businessCalculator' => function ($query) use ($id) {
                        $query->where('product_business_calculators.visible', 1);
                    },
                ]);
        }

        $product = $product->select('*', 'products.id as product_id')->first();

        if (!is_null($product)) {
            $product = Product::fillProduct($product);
        }

        return $product;
    }

    public static function getMainProduct($id)
    {
        $product = Product::where('visible', 1)->where('id', $id);

        $product = $product
            ->with([
                'images',
                'branchProducts' => function ($query) use ($id) {
                    $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id')->with(['branch']);
                },
                'branches' => function ($query) use ($id) {
                    $query->whereHas('branchProducts', function ($subQuery) use ($id) {
                        $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNull('branch_products.product_variant_id');
                    });
                },
                'variants' => function ($query) use ($id) {
                    $query
                        ->where('product_variants.visible', 1)
                        ->where('product_variants.product_id', $id)
                        ->whereHas('branchProducts', function ($subQuery) use ($id) {
                            $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_id', $id)->whereNotNull('product_variant_id');
                        })->with(['images', 'branches', 'branchProducts.branch']);
                },
                'recommended' => function ($query) use ($id) {
                    $query
                        ->where('recommended_products.product_id', $id)
                        ->whereHas('rproduct', function ($subQuery) {
                            $subQuery->where('products.visible', 1)->where('products.soon_instock', 0);
                        })
                        // ->whereHas('branchProducts', function ($subQuery) {
                        //     $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->whereNotNull('product_variant_id');
                        // })
                        ->with(['rproduct.images', 'rproduct.branchProducts.branch', 'rproduct.branches']);
                },
                'businessCalculator' => function ($query) use ($id) {
                    $query->where('product_business_calculators.visible', 1);
                },
            ]);

        $product = $product->select('*', 'products.id as product_id')->first();

        if (!is_null($product)) {
            $product = Product::fillProduct($product);
        }

        return $product;
    }

    // Get product's info by id and branch
    public static function getBranchProduct($id, $branch_id, $cat = 1)
    {
        try {
            // Validate input parameters
            if (!is_numeric($id) || !is_numeric($branch_id) || !is_numeric($cat)) {
                throw new InvalidArgumentException('Invalid input parameters');
            }

            if (!in_array($cat, [1, 2, 3, 4])) {
                throw new InvalidArgumentException('Invalid category value');
            }

            $product = Product::where('visible', 1)->where('id', $id);

            // Common relationships
            $commonRelations = [
                'images',
                'businessCalculator' => function ($query) {
                    $query->where('product_business_calculators.visible', 1);
                }
            ];

            switch ($cat) {
                case 2: // Out of stock products
                    $product = $product->where('products.soon_instock', 0)
                        ->whereHas('branchProducts', function ($query) use ($id, $branch_id) {
                            $query->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')
                                ->where('branch_products.product_id', $id)
                                ->where('branch_products.branch_id', $branch_id)
                                ->whereNull('branch_products.product_variant_id');
                        })
                        ->with(array_merge($commonRelations, [
                            'branchProducts' => function ($query) use ($id, $branch_id) {
                                $query->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')
                                    ->where('branch_products.product_id', $id)
                                    ->where('branch_products.branch_id', $branch_id)
                                    ->whereNull('branch_products.product_variant_id')
                                    ->with(['branch']);
                            },
                            'branches' => function ($query) use ($id, $branch_id) {
                                $query->whereHas('branchProducts', function ($subQuery) use ($id, $branch_id) {
                                    $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')
                                        ->where('branch_products.product_id', $id)
                                        ->where('branch_products.branch_id', $branch_id)
                                        ->whereNull('branch_products.product_variant_id');
                                });
                            },
                            'variants' => function ($query) use ($id) {
                                $query->where('product_variants.product_id', $id)
                                    ->whereHas('branchProducts', function ($subQuery) use ($id) {
                                        $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')
                                            ->where('branch_products.product_id', $id)
                                            ->whereNotNull('product_variant_id');
                                    })
                                    ->with(['images', 'branches', 'branchProducts.branch']);
                            }
                        ]));
                    break;

                case 3: // Soon to be in stock products
                    $product = $product->where('products.soon_instock', 1)
                        ->with(array_merge($commonRelations, [
                            'images',
                            'branches',
                            'branchProducts.branch',
                            'variants' => function ($query) {
                                $query->whereHas('branchProducts', function ($subQuery) {
                                    $subQuery->whereNotNull('product_variant_id');
                                })->with(['images', 'branches', 'branchProducts.branch']);
                            },
                        ]));
                    break;

                case 4: // Get product by branch
                    $product = $product
                        ->whereHas('branchProducts')
                        ->with(array_merge($commonRelations, [
                            'images',
                            'branchProducts' => function ($query) use ($id, $branch_id) {
                                $query->where('branch_products.product_id', $id)->where('branch_products.branch_id', $branch_id)->whereNull('branch_products.product_variant_id')->with(['branch']);
                            },
                        ]));
                    break;

                default: // Available products
                    $product = $product->where('products.soon_instock', 0)
                        ->whereHas('branchProducts', function ($query) use ($id, $branch_id) {
                            $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')
                                ->where('branch_products.product_id', $id)
                                ->where('branch_products.branch_id', $branch_id)
                                ->whereNull('branch_products.product_variant_id');
                        })
                        ->whereHas('branches', function ($query) use ($id, $branch_id) {
                            $query->whereHas('branchProducts', function ($subQuery) use ($id, $branch_id) {
                                $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')
                                    ->where('branch_products.product_id', $id)
                                    ->where('branch_products.branch_id', $branch_id)
                                    ->whereNull('branch_products.product_variant_id');
                            });
                        })
                        ->with(array_merge($commonRelations, [
                            'images',
                            'branchProducts' => function ($query) use ($id, $branch_id) {
                                $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')
                                    ->where('branch_products.product_id', $id)
                                    ->where('branch_products.branch_id', $branch_id)
                                    ->whereNull('branch_products.product_variant_id')
                                    ->with(['branch']);
                            },
                            'branches' => function ($query) use ($id, $branch_id) {
                                $query->whereHas('branchProducts', function ($subQuery) use ($id, $branch_id) {
                                    $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')
                                        ->where('branch_products.product_id', $id)
                                        ->where('branch_products.branch_id', $branch_id)
                                        ->whereNull('branch_products.product_variant_id');
                                });
                            },
                            'variants' => function ($query) use ($id) {
                                $query->where('product_variants.product_id', $id)
                                    ->whereHas('branchProducts', function ($subQuery) use ($id) {
                                        $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')
                                            ->where('branch_products.product_id', $id)
                                            ->whereNotNull('product_variant_id');
                                    })
                                    ->with(['images', 'branches', 'branchProducts.branch']);
                            },
                        ]));
                    break;
            }

            $product = $product->select('*', 'products.id as product_id')->first();

            if (is_null($product)) {
                throw new ModelNotFoundException('Product not found');
            }

            return Product::fillProduct($product);
        } catch (InvalidArgumentException $e) {
            Log::error('Invalid input in getBranchProduct', [
                'error' => $e->getMessage(),
                'id' => $id,
                'branch_id' => $branch_id,
                'cat' => $cat
            ]);
            throw new HttpResponseException(
                response()->json([
                    'error' => true,
                    'msg' => 'Invalid input parameters'
                ], 400)
            );
        } catch (ModelNotFoundException $e) {
            Log::info('Product not found in getBranchProduct', [
                'id' => $id,
                'branch_id' => $branch_id,
                'cat' => $cat
            ]);
            throw new HttpResponseException(
                response()->json([
                    'error' => true,
                    'msg' => 'Product not found'
                ], 404)
            );
        } catch (QueryException $e) {
            Log::error('Database error in getBranchProduct', [
                'error' => $e->getMessage(),
                'id' => $id,
                'branch_id' => $branch_id,
                'cat' => $cat
            ]);
            throw new HttpResponseException(
                response()->json([
                    'error' => true,
                    'msg' => 'Database error occurred'
                ], 500)
            );
        } catch (Exception $e) {
            Log::error('Unexpected error in getBranchProduct', [
                'error' => $e->getMessage(),
                'id' => $id,
                'branch_id' => $branch_id,
                'cat' => $cat
            ]);
            throw new HttpResponseException(
                response()->json([
                    'error' => true,
                    'msg' => 'An unexpected error occurred'
                ], 500)
            );
        }
    }

    // Get product's variant info by product id, branch id and variant id
    public static function getBranchProductVariant($id, $branch_id, $variant_id, $cat = 1)
    {
        $productVariant = ProductVariant::where('visible', 1)->where('id', $variant_id);

        // If cat == 2 get out of stock products
        if ($cat == 2) {
            $productVariant = $productVariant->where('product_variants.soon_instock', 0)
                ->whereHas('branchProducts', function ($query) use ($id, $branch_id) {
                    $query->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->where('branch_products.product_variant_id', $id)->where('branch_products.branch_id', $branch_id);
                })
                ->whereHas('branches', function ($query) use ($id, $branch_id) {
                    $query->whereHas('branchProducts', function ($subQuery) use ($id, $branch_id) {
                        $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->where('branch_products.product_variant_id', $id)->where('branch_products.branch_id', $branch_id);
                    });
                })
                ->with([
                    'images',
                    'branchProducts' => function ($query) use ($id, $branch_id) {
                        $query->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->where('branch_products.product_variant_id', $id)->where('branch_products.branch_id', $branch_id)->with(['branch']);
                    },
                    'branches' => function ($query) use ($id, $branch_id) {
                        $query->whereHas('branchProducts', function ($subQuery) use ($id, $branch_id) {
                            $subQuery->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')->where('branch_products.product_variant_id', $id)->where('branch_products.branch_id', $branch_id);
                        });
                    },
                ]);
        }
        // If cat == 3 get soon to be in stock products
        else if ($cat == 3) {
            $productVariant = $productVariant->where('products.soon_instock', 1)
                ->with([
                    'images',
                    'branches',
                    'branchProducts.branch',
                ]);
        } else {
            $productVariant = $productVariant->where('product_variants.soon_instock', 0)
                ->whereHas('branchProducts', function ($query) use ($id, $branch_id) {
                    $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_variant_id', $id)->where('branch_products.branch_id', $branch_id);
                })
                ->whereHas('branches', function ($query) use ($id, $branch_id) {
                    $query->whereHas('branchProducts', function ($subQuery) use ($id, $branch_id) {
                        $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_variant_id', $id)->where('branch_products.branch_id', $branch_id);
                    });
                })
                ->with([
                    'images',
                    'branchProducts' => function ($query) use ($id, $branch_id) {
                        $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_variant_id', $id)->where('branch_products.branch_id', $branch_id)->with(['branch']);
                    },
                    'branches' => function ($query) use ($id, $branch_id) {
                        $query->whereHas('branchProducts', function ($subQuery) use ($id, $branch_id) {
                            $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->where('branch_products.product_variant_id', $id)->where('branch_products.branch_id', $branch_id);
                        });
                    },
                ]);
        }

        $productVariant = $productVariant->select('*', 'product_variants.id as variant_id')->first();

        if (!is_null($productVariant)) {
            $productVariant = Product::fillProduct($productVariant);
        }

        return $productVariant;
    }

    /**
     * filteredProducts - get products filtered with available, out of stock or soon to be in stock
     *
     * Return: array of products filtered
     */
    public static function filteredProducts(
        Request $request,
        $orderby,
        $productMinPrice,
        $productMaxPrice,
        $cat,
        $search,
    ) {
        // Reset price range
        if (($productMinPrice == 0 || $productMinPrice > 0) && $productMaxPrice === 0) {
            $productMinPrice = 0;
            $productMaxPrice = 100000000;
        }
        if ($request->has('max_price') && !$request->get('min_price')) {
            $productMinPrice = 0;
        }
        if (!$request->has('max_price') && $request->get('min_price')) {
            $productMaxPrice = 100000000;
        }
        if (is_null($productMinPrice)) $productMinPrice = 0;
        if (is_null($productMaxPrice)) $productMaxPrice = 10000000000;

        // Defaults
        $products = [];
        $count = 0;
        $minmax = [
            'minPrice' => 0,
            'maxPrice' => 0,
        ];

        $products = Product::query()->where('products.visible', 1);

        // If cat == 2 get out of stock products
        if ($cat == 2) {
            $products = $products
                ->where('products.soon_instock', 0)
                // ->whereHas('branchProducts', function ($query) {
                //     $query->whereColumn('branch_products.minquantity', '>', 'branch_products.quantity')
                //         ->whereNotExists(function ($subSubQuery) {
                //             $subSubQuery->select('branch_products.id')
                //                 ->from('branch_products as bp')
                //                 ->whereColumn('bp.quantity', '>=', 'bp.minquantity')
                //                 ->whereNull('bp.product_variant_id')
                //                 ->whereRaw('bp.product_id = branch_products.product_id');
                //         })
                //     ;
                // })
                ->whereHas('branchProducts', function ($query) {
                    $query
                        ->whereColumn('branch_products.quantity', '<', 'branch_products.minquantity')
                        ->whereNotIn('branch_products.id', function ($subSubQuery) {
                            $subSubQuery
                                ->select('branch_products.id')
                                ->from('branch_products as bp')
                                ->whereColumn('bp.quantity', '>=', 'bp.minquantity')
                                ->whereNull('bp.product_variant_id');
                        })
                        ->whereNull('branch_products.product_variant_id');
                })
                // ->whereHas('branches', function ($query) {
                //     $query->whereHas('branchProducts', function ($subQuery) {
                //         $subQuery->whereColumn('branch_products.minquantity', '>', 'branch_products.quantity')
                //             // ->whereNotExists(function ($subSubQuery) {
                //             //     $subSubQuery->select('branch_products.id')
                //             //         ->from('branch_products as bp')
                //             //         ->whereColumn('bp.quantity', '>=', 'bp.minquantity')
                //             //         ->whereNull('bp.product_variant_id')
                //             //         ->whereRaw('bp.product_id = branch_products.product_id');
                //             // })
                //         ;
                //     });
                // })
                ->with([
                    'images',
                    'businessCalculator',
                    // 'branches' => function ($query) {
                    //     $query->whereHas('branchProducts', function ($subQuery) {
                    //         $subQuery->whereColumn('branch_products.minquantity', '>', 'branch_products.quantity')
                    //             // ->whereNotExists(function ($subSubQuery) {
                    //             //     $subSubQuery->select('branch_products.id')
                    //             //         ->from('branch_products as bp')
                    //             //         ->whereColumn('bp.quantity', '>=', 'bp.minquantity')
                    //             //         ->whereNull('bp.product_variant_id')
                    //             //         ->whereRaw('bp.product_id = branch_products.product_id');
                    //             // })
                    //         ;
                    //     });
                    // },
                    'branchProducts' => function ($query) {
                        $query->whereColumn('branch_products.minquantity', '>', 'branch_products.quantity')
                            // ->whereNotExists(function ($subSubQuery) {
                            //     $subSubQuery->select('branch_products.id')
                            //         ->from('branch_products as bp')
                            //         ->whereColumn('bp.quantity', '>=', 'bp.minquantity')
                            //         ->whereNull('bp.product_variant_id')
                            //         ->whereRaw('bp.product_id = branch_products.product_id');
                            // })
                            ->with(['branch'])
                        ;
                    },
                    'variants' => function ($query) {
                        $query->whereHas('branchProducts', function ($subQuery) {
                            $subQuery->whereColumn('branch_products.minquantity', '>', 'branch_products.quantity')
                                // ->whereNotExists(function ($subSubQuery) {
                                //     $subSubQuery->select('branch_products.id')
                                //         ->from('branch_products as bp')
                                //         ->whereColumn('bp.quantity', '>=', 'bp.minquantity')
                                //         ->whereNull('bp.product_variant_id')
                                //         ->whereRaw('bp.product_id = branch_products.product_id');
                                // })->whereNotNull('product_variant_id')
                            ;
                        })->with(['images', 'branches', 'branchProducts.branch']);
                    },
                ]);

            // $count = $products->where('products.soon_instock', 0)->with(['businessCalculator'])
            //     ->whereHas('branchProducts', function ($query) {
            //         $query->whereColumn('branch_products.minquantity', '>', 'branch_products.quantity')
            //             ->whereNotExists(function ($subSubQuery) {
            //                 $subSubQuery->select('branch_products.id')
            //                     ->from('branch_products as bp')
            //                     ->whereColumn('bp.quantity', '>=', 'bp.minquantity')
            //                     ->whereNull('bp.product_variant_id')
            //                     ->whereRaw('bp.product_id = branch_products.product_id');
            //             });
            //     })
            //     ->whereHas('branches', function ($query) {
            //         $query->whereHas('branchProducts', function ($subQuery) {
            //             $subQuery->whereColumn('branch_products.minquantity', '>', 'branch_products.quantity')
            //                 ->whereNotExists(function ($subSubQuery) {
            //                     $subSubQuery->select('branch_products.id')
            //                         ->from('branch_products as bp')
            //                         ->whereColumn('bp.quantity', '>=', 'bp.minquantity')
            //                         ->whereNull('bp.product_variant_id')
            //                         ->whereRaw('bp.product_id = branch_products.product_id');
            //                 });
            //         });
            //     });
        }
        // If cat == 3 get soon to be in stock products
        else if ($cat == 3) {
            $products = $products
                ->where('products.soon_instock', 1)
                ->with([
                    'images',
                    'businessCalculator',
                    'branches',
                    'branchProducts.branch',
                    'variants' => function ($query) {
                        $query->whereHas('branchProducts', function ($subQuery) {
                            $subQuery->whereNotNull('product_variant_id');
                        })->with(['images', 'branches', 'branchProducts.branch']);
                    },
                ]);

            // $count = $products->where('products.soon_instock', 1)->with(['businessCalculator']);
        }
        // Else get available products
        else {
            $products = $products
                ->where('products.soon_instock', 0)
                ->whereHas('branchProducts', function ($query) {
                    $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')
                        ->whereNull('branch_products.product_variant_id')
                        ->whereHas('branch', function ($query) {
                            return $query->where('visible', 1);
                        })
                        ->with(['branch']);
                })
                // ->whereHas('branches', function ($query) {
                //     $query->whereHas('branchProducts', function ($subQuery) {
                //         $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->whereNull('branch_products.product_variant_id');
                //     });
                // })
                ->with([
                    'images',
                    'businessCalculator',
                    'branches' => function ($query) {
                        $query->whereHas('branchProducts', function ($subQuery) {
                            $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->whereNull('branch_products.product_variant_id');
                        });
                    },
                    'branchProducts' => function ($query) {
                        $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->whereNull('branch_products.product_variant_id')->with(['branch']);
                    },
                    'variants' => function ($query) {
                        $query->whereHas('branchProducts', function ($subQuery) {
                            $subQuery->whereColumn('branch_products.quantity', '>', 'branch_products.minquantity')->whereNotNull('product_variant_id');
                        })->with(['images', 'branches', 'branchProducts.branch']);
                    },
                ]);

            // $count = Product::where('products.soon_instock', 0)->with(['businessCalculator'])
            //     ->whereHas('branchProducts', function ($query) {
            //         $query->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->whereNull('branch_products.product_variant_id');
            //     })
            //     ->whereHas('branches', function ($query) {
            //         $query->whereHas('branchProducts', function ($subQuery) {
            //             $subQuery->whereColumn('branch_products.quantity', '>=', 'branch_products.minquantity')->whereNull('branch_products.product_variant_id');
            //         });
            //     });
        }

        // All Products
        $products = $products
            ->when($request->has('search') && $search, function ($query) use (
                $request,
                $search,
                $productMinPrice,
                $productMaxPrice,
            ) {
                return $query->whereAny([
                    'products.name',
                    'products.desc',
                ], 'LIKE', '%' . $search . '%')
                    ->when((($request->has('productMinPrice') && $request->get('productMinPrice') != 0) ||
                        ($request->has('productMaxPrice') && $request->get('productMaxPrice') != 0)
                    ), function ($subQuery) use ($productMinPrice, $productMaxPrice) {
                        return $subQuery
                            ->whereRaw('? < CASE WHEN products.discount_price != 0 THEN products.discount_price
                                    ELSE products.price
                                    END', $productMinPrice)
                            ->whereRaw('? > CASE WHEN products.discount_price != 0 THEN products.discount_price
                                    ELSE products.price
                                    END', $productMaxPrice)
                            // ->where(function ($subSubQuery) use ($productMinPrice, $productMaxPrice) {
                            //     return $subSubQuery
                            //         ->where('products.visible', 1)
                            //         ->where('products.discount_price', '!=', Null)
                            //         ->orWhere('products.discount_price', '!=', '0')
                            //         ->whereBetween('products.discount_price', [$productMinPrice, $productMaxPrice]);
                            // })
                            // ->orWhere(function ($subSubQuery) use ($productMinPrice, $productMaxPrice) {
                            //     return $subSubQuery
                            //         ->where('products.visible', 1)
                            //         ->where('products.discount_price', '=', Null)
                            //         ->orWhere('products.discount_price', '=', '0')
                            //         ->whereBetween('products.price', [$productMinPrice, $productMaxPrice]);
                            // })
                        ;
                    })
                ;
            });

        // Price range filter
        $products = $products->when(($request->has('productMinPrice') && $request->get('productMinPrice') != 0) ||
            ($request->has('productMaxPrice') && $request->get('productMaxPrice') != 0), function ($query) use (
            $productMinPrice,
            $productMaxPrice,
            $search
        ) {
            return $query
                ->where(function ($subQuery) use ($productMinPrice, $productMaxPrice, $search) {
                    return $subQuery
                        ->where('products.visible', 1)
                        ->where(function ($subSubQuery) use ($productMinPrice, $productMaxPrice, $search) {
                            $subSubQuery
                                ->whereRaw('? < CASE WHEN products.discount_price != 0 THEN products.discount_price
                                    ELSE products.price
                                    END', $productMinPrice)
                                ->whereRaw('? > CASE WHEN products.discount_price != 0 THEN products.discount_price
                                    ELSE products.price
                                    END', $productMaxPrice)
                                ->when($search, function ($query) use ($search) {
                                    return $query->where(function ($subQuery) use ($search) {
                                        return $subQuery
                                            ->where('products.visible', 1)
                                            ->whereAny([
                                                'products.name',
                                                'products.desc',
                                            ], 'LIKE', '%' . $search . '%');
                                    });
                                })
                            ;
                        })
                    ;
                });
        });

        $count = clone $products;
        $minmax = clone $products;
        $products = $products
            ->when($orderby, function ($query) use ($orderby) {
                $query->orderByRaw($orderby);
            })
            ->when(!$orderby, function ($query) use ($orderby) {
                $query->orderByRaw("products.id DESC");
            });

        // $products = $products->where(function ($query) use ($productMinPrice) {
        //     // $query->where('products.price', '>=', $productMinPrice)
        //     //     ->orWhere('products.discount_price', '>=', $productMinPrice);
        //     $query->where(DB::raw('COALESCE(products.discount_price, products.price)'), '>=', $productMinPrice);
        // })
        //     ->where(function ($query) use ($productMaxPrice) {
        //         // $query->where('products.price', '<=', $productMaxPrice)
        //         //     ->orWhere('products.discount_price', '<=', $productMaxPrice);
        //         $query->where(DB::raw('COALESCE(products.discount_price, products.price)'), '<=', $productMaxPrice);
        //     })
        //     ->when($orderby, function ($query) use ($orderby) {
        //         $query->orderByRaw($orderby);
        //     })
        //     ->where('products.visible', 1);

        // $count = $count
        //     ->when($search, function ($query) use ($search) {
        //         $query->whereAny([
        //             'products.name',
        //             'products.desc',
        //         ], 'LIKE', '%' . $search . '%');
        //     })
        //     ->where(function ($query) use ($productMinPrice) {
        //         // $query->where('products.price', '>=', $productMinPrice)
        //         //     ->orWhere('products.discount_price', '>=', $productMinPrice);
        //         $query->where(DB::raw('COALESCE(products.discount_price, products.price)'), '>=', $productMinPrice);
        //     })
        //     ->where(function ($query) use ($productMaxPrice) {
        //         // $query->where('products.price', '<=', $productMaxPrice)
        //         //     ->orWhere('products.discount_price', '<=', $productMaxPrice);
        //         $query->where(DB::raw('COALESCE(products.discount_price, products.price)'), '<=', $productMaxPrice);
        //     })
        //     ->where('products.visible', 1);

        // Count
        $count = $count->count();

        // Min and Max prices
        $minmax = $minmax
            ->select(
                DB::raw('MIN(CASE WHEN products.discount_price != NULL OR products.discount_price != 0 THEN products.discount_price ELSE products.price END) as minPrice'),
                DB::raw('MAX(CASE WHEN products.discount_price != NULL OR products.discount_price != 0 THEN products.discount_price ELSE products.price END) as maxPrice'),
            )
            ->get();

        // $minmax = $minmax->select(
        //     DB::raw('MIN(CASE WHEN products.discount_price != 0 THEN products.discount_price ELSE products.price END) AS minPrice'),
        //     DB::raw('MAX(CASE WHEN products.discount_price != 0 THEN products.discount_price ELSE products.price END) AS maxPrice'),

        //     // DB::raw('MIN(COALESCE(products.discount_price, products.price)) AS minPrice'),
        //     // DB::raw('MAX(COALESCE(products.discount_price, products.price)) AS maxPrice'),
        // )->get();

        return [
            'products' => $products,
            'minmax' => $minmax,
            'count' => $count
        ];
    }

    /**
     * featured - get featured products
     *
     * Return: array of featured products
     */
    public static function featured()
    {
        $products = Product::where('products.visible', 1)
            ->where('products.soon_instock', 0)
            ->where('products.featured', 1)
            ->whereHas('branchProducts', function ($query) {
                $query->whereColumn('quantity', '>=', 'minquantity')->whereNull('branch_products.product_variant_id');
            })
            ->whereHas('branches', function ($query) {
                $query->whereHas('branchProducts', function ($subQuery) {
                    $subQuery->whereColumn('quantity', '>=', 'minquantity')->whereNull('branch_products.product_variant_id');
                });
            })
            ->with([
                'images',
                'branches' => function ($query) {
                    $query->whereHas('branchProducts', function ($subQuery) {
                        $subQuery->whereColumn('quantity', '>=', 'minquantity')->whereNull('branch_products.product_variant_id');
                    });
                },
                'branchProducts' => function ($query) {
                    $query->whereColumn('quantity', '>=', 'minquantity')->whereNull('branch_products.product_variant_id')->with(['branch']);
                },
                'variants' => function ($query) {
                    $query->whereHas('branchProducts', function ($subQuery) {
                        $subQuery->whereNotNull('product_variant_id');
                    })->with(['images', 'branches', 'branchProducts.branch']);
                },
            ])->get();

        if (!empty($products)) {
            $products = Product::fillProducts($products);
        } else {
            $products = NULL;
        }

        return $products;
    }
}
