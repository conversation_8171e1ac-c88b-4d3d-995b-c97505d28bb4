<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BusinessCalculator extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'amount_of_items',
        'from_amount',
        'to_amount',
        'discount_amount',
        'incentive',
        'point',
        'percent',
        'visible',
        'rental_yield',
        'property_appreciation',
    ];
}
