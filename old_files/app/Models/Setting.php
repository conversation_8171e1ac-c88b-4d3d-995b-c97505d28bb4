<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'param',
        'value',
    ];

    public static function getKmOrMiles()
    {
        $unit = Setting::where('param', "distanceUnit")->first();
        if (!is_null($unit)) {
            $unit = $unit->value;
        }
        return $unit ?? 'km';
    }

    // Get market pricing
    public static function companyPricing()
    {
        $company = Company::first();
        if ($company != NULL) {
            $tax = $company->tax ?? 0;
            // Delivery fee
            $fee = $company->fee ?? 0;
            // company delivery fee by percent
            $percent = $company->percent ?? 0;
            // company delivery fee by distance in km
            $perkm = $company->per_km ?? 0;
            // company delivery fee by given price(flate rate)
            $flatrate = $company->flat_rate ?? 0;
            // update fee if fee is by percent
            // $fee = Products::percent_calculate($fee, $percent);
            // minimum discount amount and discount amount
            $minDiscountAmount = $company->minDiscountAmount ?? 0;
            $minDiscountAmount2 = Util::makePrice($company->minDiscountAmount ?? 0);
            $discountAmount = $company->discountAmount ?? 0 . "%";
            $discountAmount2 = Util::percent_calculate($company->discountAmount ?? 0, 1);
            return [
                "tax" => $tax,
                "fee" => $fee,
                "percent" => $percent,
                "flatrate" => $flatrate,
                "perkm" => $perkm,
                "minDiscountAmount" => $minDiscountAmount,
                "minDiscountAmount2" => $minDiscountAmount2,
                "discountAmount" => $discountAmount,
                "discountAmount2" => $discountAmount2
            ];
        }
        return [];
    }

    // Payment Methods
    public static function getPaymentMethods()
    {
        $paymentMethods = Payment::where('visible', 1)->with('image')->select('id', 'name', 'image_id')->get();
        return $paymentMethods;
    }

    public static function getUserPhone()
    {
        $user = Auth::user();
        return ($user) ? '0' . substr($user->phone, -9) : "";
    }

    public static function getDefaultLat()
    {
        $defaultLat = 9.020097;
        $company = Company::first();
        if ($company)
            $defaultLat = $company->lat;
        return $defaultLat;
    }

    public static function getDefaultLng()
    {
        $defaultLng = 38.801228;
        $company = Company::first();
        if ($company)
            $defaultLng = $company->lng;
        return $defaultLng;
    }

    public static function getGoogleMapKey()
    {
        $apikey = Setting::where('param', '=', "mapapikey")->first();
        if (!is_null($apikey))
            $apikey = $apikey->value;

        return $apikey ?? 'AIzaSyBBX32kKNA2HLVmvpPO5MqurMTfV0TWUHQ';
    }

    public static function legalsInfo($param = null)
    {
        if (!is_null($param)) {
            $data = Setting::whereIn('param', [
                $param,
                $param . '_visible',
                'point_amount',
            ])->get();
        } else {
            $data = Setting::whereIn('param', [
                'about',
                'about_visible',
                'terms',
                'terms_visible',
                'privacy',
                'privacy_visible',
                'delivery',
                'delivery_visible',
                'refund',
                'refund_visible',
                'point_amount',
            ])->get();
        }
        $data = $data->toArray();

        // About us
        $about = array_filter($data, function ($dt) {
            return $dt['param'] == 'about';
        });
        $about = end($about) ? end($about)['value'] : null;
        $aboutVisible = array_filter($data, function ($dt) {
            return $dt['param'] == 'about_visible';
        });
        $aboutVisible = end($aboutVisible) ? end($aboutVisible)['value'] : null;

        // Terms and conditions
        $terms = array_filter($data, function ($dt) {
            return $dt['param'] == 'terms';
        });
        $terms = end($terms) ? end($terms)['value'] : null;
        $termsVisible = array_filter($data, function ($dt) {
            return $dt['param'] == 'terms_visible';
        });
        $termsVisible = end($termsVisible) ? end($termsVisible)['value'] : null;

        // Privacy and Policy
        $privacy = array_filter($data, function ($dt) {
            return $dt['param'] == 'privacy';
        });
        $privacy = end($privacy) ? end($privacy)['value'] : null;
        $privacyVisible = array_filter($data, function ($dt) {
            return $dt['param'] == 'privacy_visible';
        });
        $privacyVisible = end($privacyVisible) ? end($privacyVisible)['value'] : null;

        // Delivery and info
        $delivery = array_filter($data, function ($dt) {
            return $dt['param'] == 'delivery';
        });
        $delivery = end($delivery) ? end($delivery)['value'] : null;
        $deliveryVisible = array_filter($data, function ($dt) {
            return $dt['param'] == 'delivery_visible';
        });
        $deliveryVisible = end($deliveryVisible) ? end($deliveryVisible)['value'] : null;

        // Refund policy
        $refund = array_filter($data, function ($dt) {
            return $dt['param'] == 'refund';
        });
        $refund = end($refund) ? end($refund)['value'] : null;
        $refundVisible = array_filter($data, function ($dt) {
            return $dt['param'] == 'refund_visible';
        });
        $refundVisible = end($refundVisible) ? end($refundVisible)['value'] : null;

        // Point amount
        $pointAmount = array_filter($data, function ($dt) {
            return $dt['param'] == 'point_amount';
        });
        $pointAmount = end($pointAmount) ? end($pointAmount)['value'] : null;

        return [
            'about' => $about,
            'about_visible' => $aboutVisible,
            'terms' => $terms,
            'terms_visible' => $termsVisible,
            'privacy' => $privacy,
            'privacy_visible' => $privacyVisible,
            'delivery' => $delivery,
            'delivery_visible' => $deliveryVisible,
            'refund' => $refund,
            'refund_visible' => $refundVisible,
            'point_amount' => $pointAmount,
        ];
    }

    public static function socialMediasInfo($param = null)
    {
        if (!is_null($param)) {
            $data = Setting::whereIn('param', [
                $param . '_link',
                $param . '_visible',
            ])->get();
        } else {
            $data = Setting::whereIn('param', [
                'facebook_link',
                'facebook_visible',
                'youtube_link',
                'youtube_visible',
                'instagram_link',
                'instagram_visible',
                'tiktok_link',
                'tiktok_visible',
                'telegram_link',
                'telegram_visible',
            ])->get();
        }
        $data = $data->toArray();

        // facebook
        $fb = array_filter($data, function ($dt) {
            return $dt['param'] == 'facebook_link';
        });
        $fb = end($fb) ? end($fb)['value'] : null;
        $fbVisible = array_filter($data, function ($dt) {
            return $dt['param'] == 'facebook_visible';
        });
        $fbVisible = end($fbVisible) ? end($fbVisible)['value'] : null;

        // youtube
        $yt = array_filter($data, function ($dt) {
            return $dt['param'] == 'youtube_link';
        });
        $yt = end($yt) ? end($yt)['value'] : null;
        $ytVisible = array_filter($data, function ($dt) {
            return $dt['param'] == 'youtube_visible';
        });
        $ytVisible = end($ytVisible) ? end($ytVisible)['value'] : null;

        // Instagram
        $insta = array_filter($data, function ($dt) {
            return $dt['param'] == 'instagram_link';
        });
        $insta = end($insta) ? end($insta)['value'] : null;
        $instaVisible = array_filter($data, function ($dt) {
            return $dt['param'] == 'instagram_visible';
        });
        $instaVisible = end($instaVisible) ? end($instaVisible)['value'] : null;

        // tiktok
        $tiktok = array_filter($data, function ($dt) {
            return $dt['param'] == 'tiktok_link';
        });
        $tiktok = end($tiktok) ? end($tiktok)['value'] : null;
        $tiktokVisible = array_filter($data, function ($dt) {
            return $dt['param'] == 'tiktok_visible';
        });
        $tiktokVisible = end($tiktokVisible) ? end($tiktokVisible)['value'] : null;

        // telegram
        $tele = array_filter($data, function ($dt) {
            return $dt['param'] == 'telegram_link';
        });
        $tele = end($tele) ? end($tele)['value'] : null;
        $teleVisible = array_filter($data, function ($dt) {
            return $dt['param'] == 'telegram_visible';
        });
        $teleVisible = end($teleVisible) ? end($teleVisible)['value'] : null;

        return [
            'facebook' => $fb,
            'facebook_visible' => $fbVisible,
            'youtube' => $yt,
            'youtube_visible' => $ytVisible,
            'instagram' => $insta,
            'instagram_visible' => $instaVisible,
            'tiktok' => $tiktok,
            'tiktok_visible' => $tiktokVisible,
            'telegram' => $tele,
            'telegram_visible' => $teleVisible,
        ];
    }

    public static function getInfo($param = null)
    {
        if (!is_null($param)) {
            $data = Company::with(['image'])->first();
            if (!is_null($data)) {
                Image::updateImageFile($data->image);
                $data['logo'] = $data->image->image_path;

                return $data[$param];
            }
        }

        return null;
    }

    public static function getAdvert($position = 0)
    {
        if (!$position) return null;

        $advert = Advert::with(['image'])->where('ad_type', '=', 'advert')->where('ad_position', '=', $position)->where('visible', 1)->first();
        if (!is_null($advert)) {
            Image::updateImageFile($advert->image);
            return $advert->image->image_path;
        }

        return null;
    }
}
