<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use stdClass;

class Util extends Model
{
    use HasFactory;

    public static function timeago($date)
    {
        $timestamp = strtotime($date);

        $strTime = array("second", "minute", "hour", "day", "month", "year");
        $length = array("60", "60", "24", "30", "12", "10");

        $currentTime = time();
        if ($currentTime >= $timestamp) {
            $diff = time() - $timestamp;
            for ($i = 0; $diff >= $length[$i] && $i < count($length) - 1; $i++) {
                $diff = $diff / $length[$i];
            }

            $diff = round($diff);
            return $diff . " " . $strTime[$i] . "(s) ago ";
        }
    }

    public static function formatDate($date)
    {
        $date = Carbon::parse($date);
        return Carbon::createFromFormat('Y-m-d H:i:s', $date)->format('d M, Y m:sA');
    }

    public static function formatMonthYear($date)
    {
        $date = Carbon::parse($date);
        return Carbon::createFromFormat('Y-m-d H:i:s', $date)->format('d M, Y');
    }

    public static function formatTime($date)
    {
        $date = Carbon::parse($date);
        return Carbon::createFromFormat('Y-m-d H:i:s', $date)->format('m:s A');
    }

    public static function getSubElements($cat, $parent, $level, $ret)
    {
        foreach ($cat as &$data) {
            if ($data->parent == $parent) {
                if ($level == 1) $data->name = '&nbsp;&nbsp;&nbsp;&nbsp;' . $data->name;
                if ($level == 2) $data->name = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . $data->name;
                if ($level == 3) $data->name = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . $data->name;
                if ($level == 4) $data->name = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . $data->name;
                if ($level == 5) $data->name = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . $data->name;
                //$ret[] = (object) array('id'=> 1, 'name' => $level);
                $ret[] = $data;
                $ret = Util::getSubElements($cat, $data->id, $level + 1, $ret);
            }
        }
        return $ret;
    }

    public static function makePrice($price)
    {
        // $abbreviations = array('K', 'M', 'B', 'T');

        // $abbreviationsCount = count($abbreviations);

        // $currency = ' ETB';
        // $formattedPrice = (float) $price;

        // for ($i = $abbreviationsCount - 1; $i >= 0; $i--) {
        //     $size = pow(10, ($i + 1) * 3);

        //     if ($size <= abs($price)) {
        //         $formattedPrice = round($price / $size, 1) . $abbreviations[$i];
        //         break;
        //     }
        // }

        // return $formattedPrice . $currency;

        $rightSymbol = true;
        $symbolDigits = 2;
        $currency = ' ETB';
        $price = number_format((float) $price, 2);
        return $price . $currency;

        // if ($rightSymbol == false)
        //     $price = $currency . ' ' . sprintf('%0.' . $symbolDigits . 'f', $price);
        // else
        //     $price = sprintf('%0.' . $symbolDigits . 'f', $price) . ' ' . $currency;

        // return $price;
    }

    public static function calculateStock($inStock, $sold)
    {
        // Limit the values to the range of 0 to 100
        $value1 = max(0, min($inStock, 100));
        $value2 = max(0, min($sold, 100));

        // Calculate the percentage values
        $percentage1 = ($value1 / 100) * 100;
        $percentage2 = ($value2 / 203) * 100;

        return [
            'in_stock' => ceil($percentage1),
            'sold' => ceil($percentage2),
        ];
    }

    public static function calculateInStock($max_quant, $quant)
    {
        $inStock = ($quant / $max_quant) * 100;
        return [
            'in_stock' => ceil($inStock)
        ];
    }

    public static function getFirstCharacters($string)
    {
        $words = explode(' ', $string); // Split the string into an array of words
        $firstChars = '';

        foreach ($words as $word) {
            $firstChars .= $word[0]; // Extract the first character of each word
        }

        return $firstChars;
    }

    public static function getDistanceFromLatLonInKm($lat1, $lon1, $lat2, $lon2, $unit)
    {
        $distance = 0;
        $decimals = 2;
        $degrees = rad2deg(acos((sin(deg2rad($lat1)) * sin(deg2rad($lat2))) + (cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($lon1 - $lon2)))));
        switch ($unit) {
            case 'km':
                $distance = $degrees * 111.13384;
                break;
            case 'mi':
                $distance = $degrees * 69.05482;
                break;
            case 'nmi':
                $distance =  $degrees * 59.97662;
        }
        $distance = round($distance, $decimals);
        return number_format($distance, 2) . ' ' . $unit;
    }

    /**
     * percent_calculate - calculates the percent of a number if it is
     * needed in percent or just returns the number
     * @num: number
     * @status: number 1 or 0 value to indicate if it's needed in percent or not
     *
     * Return: percent calculated num or the num itself
     */
    public static function percent_calculate($num, $status)
    {
        if ($status == 1)
            $num = $num / 100;

        return $num;
    }

    public static function getOrderDetailsInfo($order, $orderDetails)
    {
        // order's info
        $tax = $order->order_tax;
        $fee = (float)$order->order_fee;
        $percent = $order->percent;
        $perKm = $order->per_km;
        $flatrate = $order->flat_rate;

        // subtotal
        $subtotal = 0;

        $products = array();
        // Order products
        foreach ($orderDetails as &$order_detail) {
            $detailTotal = $order_detail->price * $order_detail->count;
            $subtotal += $detailTotal;
            $order_detail->price2 = Util::makePrice($order_detail->price);
            $order_detail->total2 = Util::makePrice($order_detail->total);
            $products[] = $order_detail->product->name;
        };

        $order->tax2 = Util::makePrice(($subtotal * Util::percent_calculate($tax == null ? 0 : $tax, 1)));
        // dd(1, $order, $orderDetails);

        // product names
        $products = implode(", ", $products);
        $order->productNames = $products;

        // Time
        $order->updated_at2 = Util::timeago($order->updated_at);

        // Order status
        if (is_null($order->order_status)) $order->order_status = '-';

        if ($order->curbside_pickup == 0) {
            // pickup_address_id

            // dropoff_address_id
            $dropoff_address = Address::where('id', $order->dropoff_address_id)->first();

            if (!is_null($dropoff_address)) {
                // user location
                $lat = $dropoff_address->lat;
                $lng = $dropoff_address->lng;

                // order product branches
                $orderProductBranch = array();

                // branches
                $branches = array();

                // total distance
                $totalDistance = 0;

                // Order products branch
                foreach ($orderDetails as &$orderValue) {
                    if (!collect($branches)->contains($orderValue->branch_id)) {
                        $branches[] = $orderValue->branch_id;
                    };
                };

                // Delivery fee
                foreach ($branches as &$value) {
                    $branchesData = Branch::where('id', $value)->first();
                    if (is_null($branchesData) || !$branchesData->lat || !$branchesData->lng) {
                        continue;
                    }

                    if (empty($orderProductBranch)) {
                        $branch_ = [
                            'id' => $value,
                            'name' => $branchesData->name,
                            'lat' => $branchesData->lat,
                            'lng' => $branchesData->lng
                        ];
                        $orderProductBranch[] = (object)$branch_;
                    } else {
                        foreach ($orderProductBranch as &$branchValue) {
                            if (!collect($branchValue->id)->contains($value)) {
                                $branch_ = [
                                    'id' => $value,
                                    'name' => $branchesData->name,
                                    'lat' => $branchesData->lat,
                                    'lng' => $branchesData->lng
                                ];
                                $orderProductBranch[] = (object)$branch_;
                            };
                        }
                    }
                }

                if (!empty($orderProductBranch)) {
                    foreach ($orderProductBranch as &$branch) {
                        $branchLat = $branch->lat;
                        $branchLng = $branch->lng;
                        $unit = 'km';
                        $distance = Util::getDistanceFromLatLonInKm((float)$branchLat, (float)$branchLng, (float)$lat, (float)$lng, $unit);
                        $branch->deliveryDistance = $distance;
                        $deliveryFee = (float)$fee * (float)$distance;
                        $branch->deliveryFee = $deliveryFee;
                        $totalDistance += (float)$distance;
                    };
                };
                // by perKm
                if ($perKm == 1) {
                    $fee = (float)$fee * $totalDistance;
                }
                // by percent
                if ($percent == 1) {
                    $fee = ((float)$fee / 100) * $totalDistance;
                }
            }
        }

        $subtotalAll = $subtotal;
        if ($order->couponName != null) {
            $coupons = Coupon::where('name', $order->couponName)->first();
            // if there is coupon apply
            if ($coupons != null) $subtotalAll = Util::couponCalculate($subtotal, $coupons, $orderDetails, $order);
        }
        $tax = (($tax == null ? 0 : $tax) / 100) * $subtotal;
        $total = $subtotal + ($fee == null ? 0 : $fee) + ($tax == null ? 0 : $tax);
        if ($order->discountAmount && $total >= $order->minDiscountAmount) {
            $discount = ($order->discountAmount / 100) * $total;
            $total = $total - $discount;
        }
        $order->total = $total;
        $order->total2 = Util::makePrice($total);

        $order->order_subtotal2 = Util::makePrice($order->order_subtotal);
        $order->order_total2 = Util::makePrice($order->order_total);
        return [
            "fee" => $fee,
            "tax" => $tax,
            "subtotal" => $subtotal,
            "subtotalAll" => $subtotalAll,
            "total" => $total
        ];
    }

    public static function couponCalculate($subtotal, $coupons, $ordersdata, $order)
    {
        $productsList = explode(",", $coupons->productsList);
        $allCompanies = explode(",", $coupons->allCompanies);

        $text = "";

        if ($subtotal > $coupons->amount) {

            $text = $text . "min amount: " . $coupons->amount . "<br><br>";

            $total = 0;
            foreach ($ordersdata as &$product) {
                //+ $product->extrasprice * $product->extrascount
                $price = $product->total;
                $priceCoupon = $price;
                $text = $text . "order id=" . $product->id . " product id=" .  $product->product_id . " name: " . $product->product . " price: " . $price . "<br>";

                if ($coupons->allCompanies == '1') {        // allCompanies
                    $text = $text . "allCompanies = true<br>";
                    $priceCoupon = Util::_couponCalculate($price, $coupons);
                    if ($coupons->allProducts != '1' && !in_array($product->product_id, $productsList)) {
                        $priceCoupon = $price;
                        $text = $text . "no this product. product id=" . $product->product_id . " all: " . implode($productsList) . "<br>";
                    }
                    $text = $text . "priceCoupon " . $priceCoupon . "<br><br>";
                } else {
                    if (in_array($order->company, $allCompanies)) {
                        $text = $text . "no this company<br>";
                        $priceCoupon = Util::_couponCalculate($price, $coupons);
                        if ($coupons->allProducts != '1' && !in_array($product->product_id, $productsList)) {
                            $priceCoupon = $price;
                            $text = $text . "no this product. product id=" . $product->product_id . " all: " . implode($productsList) . "<br>";
                        }
                    } else {
                        $priceCoupon = $price;
                    }
                }
                $total += $priceCoupon;
            }

            if ($total != $subtotal)
                if ($coupons->inpercents != '1')
                    $total = $subtotal - $coupons->discount;
            //            return $text . " total: " . $total . " subtotal: " . $subtotal; // debug
            return $total;
        }
    }

    public static function _couponCalculate($total, $coupons)
    {
        if ($coupons->inpercents == '1')  // inpercents
            $total = (100 - $coupons->discount) / 100 * $total;  // discount
        else
            $total -= $coupons->discount; //discount
        return $total;
    }

    public static function getOrdersStatus()
    {
        $orderStatus = array(
            [
                "id" => 1,
                "name" => 'Order Received'
            ],
            [
                "id" => 2,
                "name" => 'Preparing'
            ],
            [
                "id" => 3,
                "name" => 'Ready'
            ],
            [
                "id" => 4,
                "name" => 'On the Way'
            ],
            [
                "id" => 5,
                "name" => 'Delivered'
            ],
            [
                "id" => 6,
                "name" => 'Canceled'
            ]
        );
        return $orderStatus;
    }

    public static function getPaymentStatus($status = null)
    {
        $paymentStatus = array(
            [
                "id" => 1,
                "name" => 'Pending'
            ],
            [
                "id" => 2,
                "name" => 'Completed'
            ],
        );
        if ($status) {
            $paymentStatus = array_filter($paymentStatus, function ($dt) use ($status) {
                return $dt['id'] == $status;
            });
            $paymentStatus = end($paymentStatus) ? end($paymentStatus)['name'] : '-';
        }
        return $paymentStatus;
    }

    public static function getOrderStatus($id)
    {
        $orderStatus = Util::getOrdersStatus();
        foreach ($orderStatus as &$value) {
            if ($value['id'] == $id) {
                return $value['name'];
            }
        }

        return '';
    }

    public static function getBannerVal($type)
    {
        if ($type == 'banner') {
            return (1);
        }
        if ($type == 'external link') {
            return (2);
        }
        if ($type == 'advert') {
            return (3);
        }
    }

    public static function getBannerEnum($type)
    {
        if ($type == 1) {
            return ('banner');
        }
        if ($type == 2) {
            return ('external link');
        }
        if ($type == 3) {
            return ('advert');
        }
    }

    public static function getBannerTypes()
    {
        return array(
            [
                'id' => 1,
                'name' => 'Open Product',
            ],
            [
                'id' => 2,
                'name' => 'Open External Link',
            ],
            [
                'id' => 3,
                'name' => 'Show Advert Image',
            ],
        );
    }

    public static function getBannerPositions()
    {
        return array(
            [
                'id' => 1,
                'name' => 'Home page first ad',
            ],
            [
                'id' => 2,
                'name' => 'Home page second ad',
            ],
            [
                'id' => 3,
                'name' => 'Details page ad',
            ]
        );
    }

    public static function getRoles()
    {
        $roles = Role::get()->makeHidden(['created_at', 'updated_at']);
        return $roles->toArray();
    }

    public static function getBranches()
    {
        return Branch::all()->toArray();
    }

    public static function discountAmountTypes()
    {
        $types = array(
            [
                'id' => 1,
                'name' => 'Amount',
            ],
            [
                'id' => 2,
                'name' => 'Percent',
            ],
        );
        return $types;
    }

    public static function discountTypes()
    {
        $types = array(
            [
                'id' => 1,
                'name' => 'Discount',
                'value' => 'discount_type',
            ],
            [
                'id' => 2,
                'name' => 'Incentive',
                'value' => 'incentive_type',
            ],
            [
                'id' => 3,
                'name' => 'Point',
                'value' => 'point_type',
            ],
        );
        return $types;
    }

    public static function requestProductsStatus()
    {
        $data = array(
            [
                'id' => 'Pending',
                'name' => 'Pending',
            ],
            [
                'id' => 'Accepted',
                'name' => 'Accepted',
            ],
            [
                'id' => 'Rejected',
                'name' => 'Rejected',
            ]
        );
        return $data;
    }

    public static function getLogo()
    {
        $company = Company::with(['image'])->first();
        if (!is_null($company)) {
            Image::updateImageFile($company->image);
            return $company->image->image_path;
        }

        return 'img/logo.png';
    }
}
