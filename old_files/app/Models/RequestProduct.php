<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RequestProduct extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'desc',
        'product_image_id',
        'identification_image_id',
        'trade_license_image_id',
        'type',
        'status',
        'tin_number',
        'order_detail_id',
    ];

    public function identificationImage(): BelongsTo
    {
        return $this->belongsTo(Image::class, 'identification_image_id', 'id');
    }

    public function tradeLicenseImage(): BelongsTo
    {
        return $this->belongsTo(Image::class, 'trade_license_image_id', 'id');
    }

    public function requestedProductImage(): BelongsTo
    {
        return $this->belongsTo(Image::class, 'product_image_id', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public static function getRequestedProducts($page = 1)
    {
        $userid = Auth::user()->id;
        $offset = (($page - 1) * 10);
        $query = RequestProduct::where('user_id', $userid)->with(['identificationImage', 'tradeLicenseImage', 'requestedProductImage'])->orderBy('id', 'DESC')->select('request_products.*', DB::raw('(@row_number:=@row_number + 1) AS No'));
        $requestedProducts = $query->skip($offset)->take(10)->get();
        $requestedProducts->each(function ($data, $index) {
            $data->created_at2 = Util::timeago($data->created_at);
            $data->created_at3 = Carbon::parse($data->created_at)->format('M d, Y');
        });

        $count = count($query->get());
        $t = ceil($count / 10);
        return [
            "data" => $requestedProducts->toArray(),
            "page" => (int)$page,
            "pages" => (int)$t
        ];
    }
}
