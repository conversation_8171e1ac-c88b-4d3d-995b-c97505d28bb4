<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class IsDashboardAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (
            auth()->user() &&
            auth()->user()->role_id === 2
        ) {
            return redirect()->route('home');
        }

        if (!auth()->user()) {
            return redirect()->route('admin.login');
        }


        return $next($request);
    }
}
