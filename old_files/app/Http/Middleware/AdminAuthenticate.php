<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class AdminAuthenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
        if ($request->expectsJson()) {
            $intendedUrl = Session::get('intended_urls');
            if (!is_null($intendedUrl)) {
                return route($intendedUrl);
            }

            return null;
        }

        // Store the intended URL in the session
        Session::put('intended_url', url()->current());

        return route('admin.login');
        // TODO: Update to admin login route
        // return $request->expectsJson() ? null : route('login');
    }
}
