<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use App\Services\Telebirr\TelebirrH5C2B\TelebirrH5C2BPay;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Validator;

class OrderCompleteController extends Controller
{
    // private $webhook = "https://webhook.site/c32d629e-f9ca-4f35-8051-f7f68f029b97"; // new webhook
    // private $webhook = "https://webhook.site/0a99fb0c-195b-4663-bff7-b2e86996ba1d";

    public function curbsidePickupComplete()
    {
        $userEmail = Auth::user() ? Auth::user()->email : '';
        $bankAccount = Payment::where('id', 1)->first();
        return view('pages.customer.complete.complete', [
            'title' => 'Order Complete',
            'method' => "cash",
            'paymentId' => "",
            'token' => "",
            'PayerID' => "",
            'bank_account' => $bankAccount,
            "userEmail" => $userEmail
        ]);
    }

    public function telebirrPaymentComplete(Order $order)
    {
        if (!$order) {
            return redirect()->route('cart')->with('error', 'Order not found or not yet complete.');
        }

        // Check if the order is completed
        $orderStatusUpdate = OrderController::checkOrder($order->id);
        if ($orderStatusUpdate == 1) {
            $data = [
                'title' => 'Order Complete',  // Order Complete
                'method' => $order->payment_method->name,
                'paymentId' => $order->id,
            ];
            $orderUserId = $order->user;
            if ($orderUserId->email) {
                $data['userEmail'] = $orderUserId->email;
            }
            return view('pages.customer.complete.complete', $data);
        }

        return redirect()->route('cart');
    }

    /**
     * telebirr payment complete
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse|mixed|\Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function tx_notify(Request $request)
    {
        // webhook for tele c2b
        // Http::post(
        //     $this->webhook,
        //     [
        //         "info" => "kircha telebirr webcheckout notify",
        //         "data" => $request->all()
        //     ]
        // );

        $data = $request->all();
        $telebirrPayService = new TelebirrH5C2BPay();
        $response = $telebirrPayService->handleWebhook($data);
        // Http::post($this->webhook, [
        //     "info" => "kircha telebirr webcheckout notify 2",
        //     "data" => $response
        // ]);

        if ($response['success'] == false) {
            $resultCode = 400;
            $response['desc'] = "Payment failed";
            return response()->json($response, $resultCode);
        }

        // Update order
        $response = OrderController::updateOrder($response['data']['order_id'], '');

        return response()->json($response, 200);
    }
}
