<?php

namespace App\Http\Controllers;

use App\Models\Advert;
use App\Models\Image;
use App\Models\Product;
use App\Models\Util;
use App\Traits\PaginateSortSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Illuminate\Support\Facades\Validator;

class AdvertController extends Controller
{
    use PaginateSortSearch;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $products = ProductController::getProducts();
        return view('pages.admin.advert.advert', ['products' => $products['data']->toArray(), 'page' => $products['page'], 'pages' => $products['pages']]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => ['string'],
            'name' => ['required'],
            'ad_type' => ['required'],
            'product_id' => ['integer'],
            'link' => ['url'],
            'ad_position' => ['integer'],
            'image' => ['required', 'integer'],
            'visible' => ['required', 'boolean']
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $editId = $request->input('id');
        $name = $request->input('name');
        $ad_type = $request->input('ad_type');
        $product_id = $request->input('product_id');
        $link = $request->input('lng');
        $ad_position = $request->input('ad_position');
        $image = $request->input('image');
        $visible = $request->input('visible', 0);

        // Check product
        if ($ad_type == 1) {
            $is_productAvailable = Product::find($product_id);
            if (is_null($is_productAvailable)) {
                return response()->json(['error' => true, 'msg' => 'Product not found'], 400);
            }
        }

        // Check image
        if (is_null(Image::find($image))) {
            return response()->json(['error' => true, 'msg' => 'Please upload image'], 400);
        }

        $values = array(
            'name' => $name,
            'ad_type' => Util::getBannerEnum($ad_type),
            'image_id' => $image,
            'visible' => $visible
        );

        // Banner
        if ($ad_type == 1) {
            $values['product_id'] = $product_id;
        }
        // External link
        if ($ad_type == 2) {
            $values['link'] = $link;
        }
        // Ad position
        if ($ad_type == 3) {
            $values['ad_position'] = $ad_position;
        }

        if ($editId != "0") {
            Advert::where('id', $editId)
                ->update($values);
            $data = Advert::find($editId);
        } else {
            $isAlreadyAdded = Advert::where('ad_position', '=', $ad_position)->where('ad_type', '=', 'advert')->first();
            if ($isAlreadyAdded) {
                return response()->json(['error' => true, 'msg' => 'Ad already exists'], 400);
            }
            $data = Advert::create($values);
        };

        return AdvertController::edit($data);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request)
    {
        $datas = Advert::query();
        $datas = $this->run($request, $datas, ['name', 'ad_type'], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data'];
        foreach ($datas as $data) {
            $data->image;
            if ($data->image) {
                $domainName = FacadesRequest::root();
                $data->image->image_path = $domainName . '/' . $data->image->image_path;
            }
        }
        $datas = $datas->toArray();
        $datas['data'] = array_map(function ($data) {
            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);

        return response()->json(['error' => false, 'data' => $datas], 200);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Advert $advert)
    {
        if (is_null($advert)) {
            return response()->json(['error' => false, 'msg' => 'Advert not found'], 404);
        }

        $advert->timeago = Util::timeago($advert->updated_at);
        $advert->updated_at2 = Util::formatDate($advert->updated_at);
        $advert->ad_type_id = Util::getBannerVal($advert->ad_type);
        $advert->image;
        if ($advert->image) {
            $domainName = FacadesRequest::root();
            $advert->image->image_path = $domainName . '/' . $advert->image->image_path;
        }

        return response()->json([
            'error' => false,
            'data' => $advert
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function delete(Advert $advert)
    {
        $advert->delete();

        return response()->json(['error' => false]);
    }
}
