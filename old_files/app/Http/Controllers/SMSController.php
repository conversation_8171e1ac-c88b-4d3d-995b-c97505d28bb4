<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SMSController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $smsApi = Setting::where('param', 'sms_api')->first() ? Setting::where('param', 'sms_api')->first()->value : null;
        $smsStatus = Setting::where('param', 'sms_status')->first() ? Setting::where('param', 'sms_status')->first()->value : null;

        return view('pages.admin.sms-api.sms-api', array(
            'data' => [
                'api' => $smsApi,
                'api_status' => $smsStatus,
            ]
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'api' => ['required', 'string'],
            'status' => ['required', 'boolean'],
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $api = $request->input('api');
        $status = $request->input('status', 0);

        $values = array(
            'param' => 'sms_api',
            'value' => $api,
        );

        $values2 = array(
            'param' => 'sms_status',
            'value' => $status,
        );
        $msg = 'Something went wrong';
        if (Setting::where('param', 'sms_api')->exists()) {
            if (
                Setting::where('param', 'sms_api')->first()->value != $values["value"] ||
                Setting::where('param', 'sms_status')->first()->value != $values2["value"]
            ) {
                Setting::where('param', 'sms_api')->update($values);
                Setting::where('param', 'sms_status')->update($values2);
                $msg = 'Updated successfully';
            } else {
                $msg = 'Already added';
            }
        } else {
            Setting::create($values);
            Setting::create($values2);
            $msg = 'Created successfully';
        }

        return response()->json([
            'error' => false,
            'msg' => $msg,
            'data' => [
                'api' => $values['value'],
                'api_status' => $values2['value'],
            ],
        ], 200);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit()
    {
        $smsApi = Setting::where('param', 'sms_api')->first() ? Setting::where('param', 'sms_api')->first()->value : null;
        $smsStatus = Setting::where('param', 'sms_status')->first() ? Setting::where('param', 'sms_status')->first()->value : null;

        return response()->json([
            'error' => false,
            'data' => [
                'api' => $smsApi,
                'api_status' => $smsStatus,
            ],
        ], 200);
    }
}
