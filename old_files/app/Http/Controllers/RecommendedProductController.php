<?php

namespace App\Http\Controllers;

use App\Models\RecommendedProduct;
use Illuminate\Http\Request;

class RecommendedProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(RecommendedProduct $recommendedProduct)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RecommendedProduct $recommendedProduct)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RecommendedProduct $recommendedProduct)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RecommendedProduct $recommendedProduct)
    {
        //
    }
}
