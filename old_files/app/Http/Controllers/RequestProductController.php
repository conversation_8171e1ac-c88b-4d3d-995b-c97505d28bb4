<?php

namespace App\Http\Controllers;

use App\Models\ImageUpload;
use App\Models\OrderDetail;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\RequestProduct;
use App\Models\Util;
use App\Traits\PaginateSortSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class RequestProductController extends Controller
{
    use PaginateSortSearch;

    /**
     * Display a listing of the resource.
     */
    public function request_product()
    {
        if (!Auth::check()) {
            // Store the intended URL in the session
            Session::put('intended_url', url()->current());
            return redirect()->route('login');
        }

        $page = "account";
        $breadcrumb = 'My Account';

        return view('pages.customer.request_product.request_product', [
            'title' => $breadcrumb,
            'page' => $page
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function sellerSignup(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'trade_license' => 'required',
            'identif' => 'required',
            'requested_product' => 'required',
            'requested_product_desc' => 'required|string',
            'requested_product_name' => 'required|string',
            'tinnum' => 'string',
            'type' => 'string|in:new,bought',
            'editId' => 'numeric',
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        // TODO: check images is uploaded
        // User
        $user_id = Auth::user()->id;
        $validatedData = array(
            'name' => $request->get('requested_product_name'),
            'desc' => $request->get('requested_product_desc'),
            'product_image_id' => $request->get('requested_product'),
            'identification_image_id' => $request->get('identif'),
            'trade_license_image_id' => $request->get('trade_license'),
            'tin_number' => $request->get('tinnum', null),
            'type' => $request->get('type', 'new'),
        );

        $editId = $request->get('editId', 0);

        if ($editId != 0) {
            $product = RequestProduct::where('id', $editId)->where('user_id', $user_id)->first();
            if (!is_null($product)) {
                $product->update($validatedData);
            }
        } else {
            $validatedData['user_id'] = $user_id;
            $validatedData['status'] = 'Pending';
            $product = RequestProduct::create($validatedData);
        }

        if ($product) {
            if ($editId != 0) {
                return response()->json(['error' => false, 'msg' => 'Updated successfully!'], 201);
            }
            return response()->json(['error' => false, 'msg' => 'Sent successfully!'], 201);
        }

        return response()->json(['error' => true, 'msg' => 'Something went wrong'], 201);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function sellShare(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'id' => 'required',
            'quantity' => 'required',
            'APPP' => 'required',
            'WTAMP' => 'required',
            'PPM' => 'required',
            'RFS' => 'required|in:0,1,2',
            'AC' => 'required',
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        // TODO: check images is uploaded
        // User
        $user_id = Auth::user()->id;
        $orderDetailsId = $request->input('id', 0);
        $quantity = $request->input('quantity', 0);
        $property = OrderDetail::where('id', $orderDetailsId)->first();
        if (is_null($property)) {
            return response()->json(['error' => true, 'msg' => 'Property not found', 'title' => 'Property Not Found'], 404);
        }

        if (Util::getPaymentStatus($property->order->is_complete + $property->order->view) == 'Pending') {
            return response()->json(['error' => true, 'msg' => 'Property payment process is pending. Please contact us to complete the payment.', 'title' => 'Payment Pending'], 400);
        }

        $existingProperty = RequestProduct::where('order_detail_id', $orderDetailsId)
            ->select('status')
            ->first();

        if ($existingProperty) {
            $message = $existingProperty->status === 'Accepted'
                ? 'This property has already been accepted and listed for sale on our platform.'
                : 'Property already added successfully! We will notify you soon.';

            return response()->json(['error' => false, 'msg' => $message], 200);
        }

        $rfsVal = $request->input('RFS');
        if ($rfsVal == 2) {
            $requestValidated = Validator::make($request->all(), [
                'OTHER' => 'required|min=6',
            ]);
            if ($requestValidated->fails()) {
                return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
            }
        }

        if ($quantity > $property->count) {
            return response()->json(['error' => true, 'msg' => 'You can not sell more than your current share', 'title' => 'Insufficient Share'], 400);
        }

        $product = null;
        if (!is_null($property->product_variant_id)) {
            $product = ProductVariant::where('product_variants.id', $property->product_variant_id)
                ->with([
                    'images',
                    'branches' => function ($query) use ($property) {
                        $query->where('id', $property->branch_id);
                    },
                    'branchProducts' => function ($query) use ($property) {
                        $query->where('branch_products.product_id', $property->product_id)->where('branch_products.branch_id', $property->branch_id)->where('branch_products.product_variant_id', $property->product_variant_id)->with(['branch']);
                    },
                ]);
            $product = $product->select('*', 'product_variants.id as product_variants_id')->first();
        } else {
            $product = Product::where('products.id', $property->product_id)
                ->with([
                    'images',
                    'branches' => function ($query) use ($property) {
                        $query->where('branches.id', $property->branch_id);
                    },
                    'branchProducts' => function ($query) use ($property) {
                        $query->where('branch_products.product_id', $property->product_id)->where('branch_products.branch_id', $property->branch_id)->whereNull('branch_products.product_variant_id')->with(['branch']);
                    },
                ]);
            $product = $product->select('*', 'products.id as product_id')->first();
        }

        $product = Product::fillProduct($product);
        $userImage = ImageUpload::where('user_id', $user_id)->first();
        if (!is_null($userImage)) {
            $userImage = $userImage->image_id;
        }
        $imageId = NULL;
        if (isset($product['images'][0]['id'])) {
            $imageId = $product['images'][0]['id'];
        }
        $date = Util::formatMonthYear($property->created_at);
        $rfs = ["Personal financial needs", "Investment reallocation", "Other"];
        $rfs = $rfs[$rfsVal];

        $descData = [
            "NOS" => $quantity,
            "PDS" => $date,
            "APPP" => $request->input('APPP'),
            "WTAMP" => $request->input('WTAMP'),
            "PPM" => $request->input('PPM'),
            "RFS" => $rfsVal == 2 ? $request->input('OTHER') : $rfs,
            "AC" => $request->input('AC'),
        ];

        $validatedData = array(
            'name' => $property->name,
            'desc' => json_encode($descData),  // Convert the array to JSON format
            'product_image_id' => $imageId,
            'identification_image_id' => $userImage,
            'trade_license_image_id' => null,
            'tin_number' => null,
            'type' => 'bought',
        );

        $validatedData['user_id'] = $user_id;
        $validatedData['status'] = 'Pending';
        $validatedData['order_detail_id'] = $orderDetailsId;
        $product = RequestProduct::create($validatedData);

        if ($product) {
            return response()->json(['error' => false, 'msg' => 'Sent successfully! We will notify you.'], 201);
        }

        return response()->json(['error' => true, 'msg' => 'Something went wrong'], 201);
    }

    // public static function getRequestedProducts($page = 1)
    // {
    //     $userid = Auth::user()->id;
    //     $offset = (($page - 1) * 10);
    //     $query = RequestProduct::where('user_id', $userid)->with(['identificationImage', 'tradeLicenseImage', 'requestedProductImage'])->orderBy('id', 'DESC')->select('request_products.*', DB::raw('(@row_number:=@row_number + 1) AS No'));
    //     $requestedProducts = $query->skip($offset)->take(10)->get();
    //     $requestedProducts->each(function ($data, $index) {
    //         $data->created_at2 = Util::timeago($data->created_at);
    //         $data->created_at3 = Carbon::parse($data->created_at)->format('M d, Y');
    //     });

    //     $count = count($query->get());
    //     $t = ceil($count / 10);
    //     return [
    //         "data" => $requestedProducts->toArray(),
    //         "page" => (int)$page,
    //         "pages" => (int)$t
    //     ];
    // }

    /**
     * Display the specified resource.
     */
    public function show(Request $request)
    {
        $userid = Auth::user()->id;
        $datas = RequestProduct::query();
        $datas->where('user_id', $userid);
        $datas->orderBy('id', 'DESC')->select('request_products.*', DB::raw('(@row_number:=@row_number + 1) AS No'));
        $datas = $this->run($request, $datas, [
            'name',
            'desc',
        ], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data'];
        $domainName = FacadesRequest::root();
        foreach ($datas as $data) {
            $data->identificationImage;
            if ($data->identificationImage) {
                $data->identificationImage->image_path = $domainName . '/' . $data->identificationImage->image_path;
            } else {
                $data->image_path = $domainName . '/' . 'storage/no-image.jpg';
            }
            $data->tradeLicenseImage;
            if ($data->tradeLicenseImage) {
                $data->tradeLicenseImage->image_path = $domainName . '/' . $data->tradeLicenseImage->image_path;
            } else {
                $data->image_path = $domainName . '/' . 'storage/no-image.jpg';
            }
            $data->requestedProductImage;
            if ($data->requestedProductImage) {
                $data->requestedProductImage->image_path = $domainName . '/' . $data->requestedProductImage->image_path;
            } else {
                $data->image_path = $domainName . '/' . 'storage/no-image.jpg';
            }
        }
        $datas = $datas->toArray();
        $datas['data'] = array_map(function ($data) {
            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);

        return response()->json(['error' => false, 'data' => $datas], 200);
    }
}


