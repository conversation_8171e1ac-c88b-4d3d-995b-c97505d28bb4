<?php

namespace App\Http\Controllers;

use App\Models\Address;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AddressController extends Controller
{
    // Customer's
    public function get()
    {
        $userId = Auth::user()->id;
        $addresses = Address::where('user_id', $userId)->get();
        $response = [
            'error' => false,
            'address' => $addresses
        ];
        return response()->json($response, 200);
    }

    public function save(Request $request)
    {
        $def = $request->input('default') == true ? 1 : 0;

        if ($def == 1)
            Address::where("user_id", Auth::user()->id)->update(array(
                'default' => 0,
                'updated_at' => new \DateTime(),
            ));

        $values = array(
            'user_id' => Auth::user()->id,
            'name' => $request->input('text'),
            'lat' => $request->input('lat'),
            'lng' => $request->input('lng'),
            'type' => $request->input('type'),
            'default' => $def,
            'created_at' => new \DateTime(),
            'updated_at' => new \DateTime(),
        );
        $lastAddressId = Address::insertGetId($values);

        $response = [
            'error' => '0',
            'address' => Address::where("user_id", Auth::user()->id)->get(),
            'lastInsertedAddressId' => $lastAddressId,
            'lastInsertedAddressName' => $request->input('text')
        ];
        return response()->json($response, 200);
    }

    public function delete(Request $request)
    {
        Address::where('id', $request->input('id'))->where('user_id', Auth::user()->id)->delete();

        $response = [
            'error' => '0',
            'address' => Address::where('user_id', Auth::user()->id)->get(),
        ];
        return response()->json($response, 200);
    }
}
