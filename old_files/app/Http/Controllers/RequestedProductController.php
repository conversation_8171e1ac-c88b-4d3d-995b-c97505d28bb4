<?php

namespace App\Http\Controllers;

use App\Models\Image;
use App\Models\RequestProduct;
use App\Models\Util;
use App\Traits\PaginateSortSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Illuminate\Support\Facades\Validator;

class RequestedProductController extends Controller
{
    use PaginateSortSearch;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('pages.admin.requested-product.requested-product');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => ['string'],
            'status' => ['required', 'in:Pending,Accepted,Rejected'],
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $editId = $request->input('id');
        $status = $request->input('status');

        $values = array(
            'status' => $status
        );

        if ($editId != "0") {
            RequestProduct::where('id', $editId)
                ->update($values);
            $data = RequestProduct::find($editId);
        }

        return RequestedProductController::edit($data);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request)
    {
        $datas = RequestProduct::query();
        $datas = $this->run($request, $datas, [
            'name',
            'desc',
        ], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data'];
        $domainName = FacadesRequest::root();
        foreach ($datas as $data) {
            $data->user;
            $data->image_path = $domainName . '/' . 'storage/no-image.jpg';
            if ($data->identificationImage) {
                Image::updateImageFile($data->identificationImage);
            }
            if ($data->tradeLicenseImage) {
                Image::updateImageFile($data->tradeLicenseImage);
            }
            if ($data->requestedProductImage) {
                Image::updateImageFile($data->requestedProductImage);
            }
        }
        $datas = $datas->toArray();
        $datas['data'] = array_map(function ($data) {
            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);

        return response()->json(['error' => false, 'data' => $datas], 200);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RequestProduct $requestProduct)
    {
        if (is_null($requestProduct)) {
            return response()->json(['error' => false, 'msg' => 'Requested product not found'], 404);
        }

        $requestProduct->timeago = Util::timeago($requestProduct->updated_at);
        $requestProduct->updated_at2 = Util::formatDate($requestProduct->updated_at);
        $requestProduct->ad_type_id = Util::getBannerVal($requestProduct->ad_type);
        $requestProduct->user;
        $requestProduct->user->phone = '+' . $requestProduct->user->phone;

        $domainName = FacadesRequest::root();
        $requestProduct->image_path = $domainName . '/' . 'storage/no-image.jpg';
        $requestProduct->identificationImage;
        if ($requestProduct->identificationImage) {
            Image::updateImageFile($requestProduct->identificationImage);
            // $requestProduct->identificationImage->image_path = $domainName . '/' . $requestProduct->identificationImage->image_path;
        }
        $requestProduct->tradeLicenseImage;
        if ($requestProduct->tradeLicenseImage) {
            Image::updateImageFile($requestProduct->tradeLicenseImage);
            // $requestProduct->tradeLicenseImage->image_path = $domainName . '/' . $requestProduct->tradeLicenseImage->image_path;
        }
        $requestProduct->requestedProductImage;
        if ($requestProduct->requestedProductImage) {
            Image::updateImageFile($requestProduct->requestedProductImage);
            // $requestProduct->requestedProductImage->image_path = $domainName . '/' . $requestProduct->requestedProductImage->image_path;
        }

        return response()->json([
            'error' => false,
            'data' => $requestProduct
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function delete(RequestProduct $requestProduct)
    {
        $requestProduct->delete();

        return response()->json(['error' => false]);
    }
}
