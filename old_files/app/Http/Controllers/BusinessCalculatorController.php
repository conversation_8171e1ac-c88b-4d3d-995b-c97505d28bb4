<?php

namespace App\Http\Controllers;

use App\Models\BusinessCalculator;
use App\Models\Util;
use App\Traits\PaginateSortSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BusinessCalculatorController extends Controller
{
    use PaginateSortSearch;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('pages.admin.business-calculator.business-calculator', []);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => ['string'],
            'name' => ['required', 'string'],
            'year' => ['required', 'numeric'],
            'rentalYield' => ['required', 'numeric'],
            'propertyAppreciation' => ['required', 'numeric'],
            'fromAmount' => ['required', 'numeric', 'gt:0', 'lt:toAmount'],
            'toAmount' => ['required', 'numeric', 'gt:0'],
            'discountType' => 'required|in:1,2,3',
            'discountAmount' => ['numeric'],
            'incentive' => ['string'],
            'point' => ['numeric', 'gt:0'],
            'percent' => ['required', 'boolean'],
            'visible' => ['required', 'boolean']
        ], [
            'fromAmount.lt' => 'The  Minimum purchase amount range is not valid.',
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $editId = $request->input('id');
        $discountType = $request->input('discountType');
        $discountAmount = null;
        $incentive = null;
        $point = null;
        $percent = 0;

        // In discount amount
        if ($discountType == '1') {
            $discountAmount = $request->input('discountAmount', 0);
            $percent = $request->input('percent', 0);
            if (is_null($discountAmount)) {
                return response()->json(['error' => true, 'msg' => 'Discount amount must not be empty'], 400);
            }
        }

        // In incentive
        if ($discountType == '2') {
            $incentive = $request->input('incentive', '');
            if (is_null($incentive)) {
                return response()->json(['error' => true, 'msg' => 'Incentive must not be empty'], 400);
            }
        }

        // In points
        if ($discountType == '3') {
            $point = $request->input('point', 0);
            if (is_null($point)) {
                return response()->json(['error' => true, 'msg' => 'Point must not be empty'], 400);
            }
        }

        $values = array(
            'name' => $request->input('name'),
            'amount_of_items' => $request->input('year'),
            'rental_yield' => $request->input('rentalYield'),
            'property_appreciation' => $request->input('propertyAppreciation'),
            'from_amount' => $request->input('fromAmount'),
            'to_amount' => $request->input('toAmount'),
            'discount_amount' => $discountAmount,
            'incentive' => $incentive,
            'point' => $point,
            'percent' => $percent,
            'visible' => $request->input('visible', 0),
        );

        if (!is_null($editId) && $editId != "0") {
            BusinessCalculator::where('id', $editId)
                ->update($values);
            $branch = BusinessCalculator::find($editId);
        } else {
            $branch = BusinessCalculator::create($values);
        };

        return BusinessCalculatorController::edit($branch);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request)
    {
        $datas = BusinessCalculator::query();
        $datas = $this->run($request, $datas, ['name', 'from_amount', 'to_amount'], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data']->toArray();
        $datas['data'] = array_map(function ($data) {
            $data['from_amount'] = Util::makePrice($data['from_amount']);
            $data['to_amount'] = Util::makePrice($data['to_amount']);
            if ($data['discount_amount']) $data['discount_amount'] = Util::makePrice($data['discount_amount']);
            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);

        return response()->json(['error' => false, 'data' => $datas], 200);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BusinessCalculator $businessCalculator)
    {
        if (is_null($businessCalculator)) {
            return response()->json(['error' => false, 'msg' => 'Business calculator not found'], 404);
        }

        $businessCalculator->timeago = Util::timeago($businessCalculator->updated_at);
        $businessCalculator->updated_at2 = Util::formatDate($businessCalculator->updated_at);

        return response()->json([
            'error' => false,
            'data' => $businessCalculator
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function delete(BusinessCalculator $businessCalculator)
    {
        $businessCalculator->delete();

        return response()->json(['error' => false]);
    }
}
