<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Util;
use App\Traits\PaginateSortSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BranchController extends Controller
{
    use PaginateSortSearch;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('pages.admin.branch.branch', []);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => ['string'],
            'name' => ['required', 'string'],
            'address' => ['required', 'string'],
            'lat' => ['required', 'decimal:2,20'],
            'lng' => ['required', 'decimal:2,20'],
            'visible' => ['required', 'boolean']
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $editId = $request->input('id');
        $name = $request->input('name');
        $address = $request->input('address');
        $lat = $request->input('lat');
        $lng = $request->input('lng');
        $visible = $request->input('visible', 1);

        $values = array(
            'name' => $name,
            'address' => $address,
            'lat' => $lat,
            'lng' => $lng,
            'visible' => $visible
        );

        if ($editId != "0") {
            Branch::where('id', $editId)
                ->update($values);
            $branch = Branch::find($editId);
        } else {
            $branch = Branch::create($values);
        };

        return BranchController::edit($branch);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request)
    {
        $datas = Branch::query();
        $datas = $this->run($request, $datas, ['name', 'address'], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data']->toArray();
        $datas['data'] = array_map(function ($data) {
            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);

        return response()->json(['error' => false, 'data' => $datas], 200);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Branch $address)
    {
        if (is_null($address)) {
            return response()->json(['error' => false, 'msg' => 'Address not found'], 404);
        }

        $address->timeago = Util::timeago($address->updated_at);
        $address->updated_at2 = Util::formatDate($address->updated_at);

        return response()->json([
            'error' => false,
            'data' => $address
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function delete(Branch $address)
    {
        $address->delete();

        return response()->json(['error' => false]);
    }
}
