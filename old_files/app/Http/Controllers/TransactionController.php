<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Util;
use Illuminate\Http\Request;

class TransactionController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('pages.admin.transaction.transaction');
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request)
    {
        $page = (int) $request->input('page') ?: 1;
        $count = $request->input('count', 15);
        $sortBy = $request->input('sortBy') ?: "id";
        $sortAscDesc = $request->input('sortAscDesc') ?: "asc";

        $offset = ($page - 1) * $count;

        // if (Auth::user()->role == 2) {
        //     $vendorId = Auth::user()->vendor;
        //     $vend = "AND vendor=$vendorId";
        // }
        $query = Order::query();
        $query->where('is_complete', 1);
        $total = count($query->get());
        $datas = $query->orderBy($sortBy, $sortAscDesc)
            ->limit($count)
            ->offset($offset)
            ->get();

        foreach ($datas as $data) {
            $data->order_total2 = Util::makePrice($data->order_total);
            $data->timeago = Util::timeago($data->updated_at);
            $data->updated_at2 = Util::formatDate($data->updated_at);
        }

        $t = ceil($total / $count);

        return response()->json(['error' => 0, 'data' => $datas, 'page' => $page, 'pages' => $t, 'total' => $total], 200);
    }
}
