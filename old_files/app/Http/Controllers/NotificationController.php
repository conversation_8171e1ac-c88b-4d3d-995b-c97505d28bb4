<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    public function sendEmail(Request $request)
    {
        // if (!Auth::check())
        //     return;

        $validator = Validator::make($request->all(), [
            'userEmail' => ['required', 'email'],
            'orderid' => ['required', 'numeric'],
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $userMailAddress = $request->userEmail;
        $orderid = $request->orderid;

        // Send Mail to Customer
        if ($userMailAddress) {
            $email = $userMailAddress;
            // Setting::where("param", 'web_mainColor')->get() ? Setting::where("param", 'web_mainColor')->first()->value :
            $mainColor = "219ebc";

            $user = User::where('email', $email)->first();
            $vendorName = Company::first() ? Company::first()->name : "Vendor shop order";
            // if ($user == null)
            //     return response(['error' => 500, "text" => 'User not found']);   // User not found

            // $recipient = $email;
            $recipient = '<EMAIL>';

            // super admin
            // $vendorUser = User::where('role_id', 1)->first();
            // $sender = $vendorUser ? $vendorUser->email : '<EMAIL>';
            $sender = '<EMAIL>';
            $subject = $vendorName;

            $orderedUrl = URL::to("/orderinfo/$orderid");
            $cartImage = URL::to("/images/ordersuccessfulcart.png");
            $message = '<html><body><div style="text-align: center;">';
            $message .= '<h1>ORDERED SUCCESSFULLY</h1>';
            $message .= '<img style="display: block; margin: 0 auto; width: 10%;" src=' . $cartImage . ' alt="Order made successfully" srcset="" />';
            $message .= '<p>You\'ve successfully placed the order. Thank you for your purchase.</p>';
            // Order button
            $message .= '<a style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, Roboto, Helvetica, Arial, sans-serif;
            position: relative; -webkit-text-size-adjust: none; border-radius: 4px; color: #fff; display: inline-block; overflow: hidden;
            text-decoration: none; text-transform: capitalize; border: 1px solid #' . $mainColor . '; background-color: #' . $mainColor . ';
            padding: 8px 22px; margin: 12px auto; transition: all .2s;" href=' . $orderedUrl . '> View Order </p>';
            $message .= '</div></body></html>';

            // To send HTML mail, the Content-type header must be set
            $headers  = 'MIME-Version: 1.0' . "\r\n";
            $headers .= 'Content-type: text/html; charset=utf-8' . "\r\n";
            // Create email headers
            $headers .= 'From: ' . $sender . "\r\n" .
                'Reply-To: ' . $sender . "\r\n" .
                'X-Mailer: PHP/' . phpversion();

            try {
                if (mail($recipient, $subject, $message, $headers)) {
                    //
                } else {
                    return response(['error' => 501, 'text' => 'Can not send email1']);   // Can not send email
                }
            } catch (\Exception $ex) {
                return response(['error' => 501, 'text' => 'Can not send email2']);   // Can not send email
            } catch (\Throwable $ex) {
                return response(['error' => 501, 'text' => 'Can not send email3']);   // Can not send email
            }

            return response(['error' => '0']);

            // Other email method
            // Mail::send(new OrderMade($userMailAddress, $orderid));
        }
    }

    static public function sendNotify(Request $request)
    {
        $id = $request->input('user');
        $title = $request->input('title');
        $body = $request->input('text');
        $imageid = $request->input('imageid');
        $chat = $request->input('chat');
        if ($chat == null)
            $chat = "false";
        $users = User::get();
        $uid = uniqid();


        $path_to_FCM = 'https://fcm.googleapis.com/fcm/send';
        $server_key = Setting::where('param', '=', "firebase_key")->first()->value;
        $headers = array(
            'Authorization:key=' . $server_key,
            'Content-Type:application/json'
        );

        if (!Auth::check())
            return Redirect::route('home');


        $user = User::where('id', $id)->first();
        $token = $user->fcbToken;

        $field = array(
            'notification' => array('body' => $body, 'title' => $title, 'click_action' => 'FLUTTER_NOTIFICATION_CLICK', 'sound' => 'default'), //, 'image' => $imageToSend),
            'priority' => 'high',
            'sound' => 'default',
            'data' => array('click_action' => 'FLUTTER_NOTIFICATION_CLICK', 'id' => '1', 'status' => 'done', 'body' => $body, 'title' => $title, 'sound' => 'default', 'chat' => $chat),
            'to' => $token,
        );

        //echo json_encode($field, JSON_PRETTY_PRINT);

        $payload = json_encode($field);
        $curl_session = curl_init();
        curl_setopt($curl_session, CURLOPT_URL, $path_to_FCM);
        curl_setopt($curl_session, CURLOPT_POST, true);
        curl_setopt($curl_session, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl_session, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl_session, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl_session, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
        curl_setopt($curl_session, CURLOPT_POSTFIELDS, $payload);
        $result = curl_exec($curl_session);

        //echo $result;
        curl_close($curl_session);
        // if ($result) {
        //     if ($chat == "false") {
        //         // add to database
        //         $values = array(
        //             'title' => $title,
        //             'text' => $body,
        //             'user' => $id,
        //             'image' => $imageid,
        //             'uid' => $uid,
        //             'delete' => 0,
        //             'show' => 1,
        //             "read" => 0,
        //             'updated_at' => new \DateTime()
        //         );
        //         $values['created_at'] = new \DateTime();
        //         DB::table('notifications')->insert($values);
        //     }
        //     DB::table('logging')->insert(array(
        //         'data' => "Firebase send msg title=$title, user id = $id",
        //         'created_at' => new \DateTime(),
        //         'updated_at' => new \DateTime()
        //     ));
        // } else {
        //     DB::table('logging')->insert(array(
        //         'data' => 'Firebase send msg error ' . curl_error($curl_session),
        //         'created_at' => new \DateTime(),
        //         'updated_at' => new \DateTime()
        //     ));
        // }
    }
}
