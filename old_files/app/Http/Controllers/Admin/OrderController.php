<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Product;
use App\Models\User;
use App\Models\Util;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Throwable;

class OrderController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function load(Request $request)
    {
        $edit = $request->input('edit', '');
        $coupons = Coupon::get();
        // $orderstatus = Util::getOrdersStatus();
        $paymentstatus = Util::getPaymentStatus();
        return view('pages.admin.order.order', [
            'coupons' => $coupons,
            // 'iorderstatus' => $orderstatus,
            'ipaymentstatus' => $paymentstatus,
            'edit' => $edit
        ]);
    }

    public function show(Request $request)
    {
        $search = $request->input('search') ?: "";
        $cat = $request->input('cat', "0");
        $page = (int) $request->input('page') ?: 1;
        $count = $request->input('count', 15);
        $sortBy = $request->input('sortBy') ?: "id";
        $sortAscDesc = $request->input('sortAscDesc') ?: "asc";
        $sortPublished = $request->input('sortPublished', "1");
        $sortUnPublished = $request->input('sortUnPublished', "1");

        $offset = ($page - 1) * $count;

        $searchVisible = "";
        if ($sortPublished != '1' || $sortUnPublished != '1') {
            if ($sortPublished == '1')
                $searchVisible = "is_complete = 1 AND ";
            if ($sortUnPublished == '1')
                $searchVisible = "is_complete = 0 AND ";
        }

        if ($sortPublished == '0' && $sortUnPublished == '0')
            $searchVisible = "";

        $searchCat = "";
        if ($cat != "0")
            $searchCat = "`order_status` = '" . Util::getOrderStatus($cat) . "'";

        // Order::table('orders')->update(['view' => 1]);

        $query = Order::with(['user', 'payment_method'])
            ->when($search, function ($query) use ($search) {
                $query->where('orders.id', 'LIKE', '%' . $search . '%')
                    ->orWhereHas('user', function ($query) use ($search) {
                        $query->where('name', 'LIKE', '%' . $search . '%');
                    });
            })
            ->where('is_complete', 1)
            ->where('send', 1)
            ->where('view', 1);
        // if ($searchVisible) {
        //     $query = $query->whereRaw($searchVisible);
        // }
        if ($searchCat) {
            $query = $query->whereRaw($searchCat);
        }
        $total = count($query->get());
        $datas = $query
            ->orderBy($sortBy, $sortAscDesc)
            ->limit($count)
            ->offset($offset)
            ->get();

        foreach ($datas as &$data) {
            $data->timeago = Util::timeago($data->updated_at);
            $data->updated_at2 = Util::formatDate($data->updated_at);
            $data->order_total1 = Util::makePrice($data->order_total);
            $data->order_subtotal2 = 0;
            $data->order_tax2 = 0;
            $data->order_total2 = 0;
            if ($data->total_investment_discount_amount) {
                $data->order_subtotal2 = (float)$data->order_subtotal - (float)$data->total_investment_discount_amount;
                $data->order_tax2 = (float)$data->order_tax * $data->order_subtotal2 / 100;
                $data->order_total2 = $data->order_tax2 + $data->order_subtotal2;
                // coupon_discount_amount
                if ($data->coupon_discount_amount) {
                    $data->order_total2 = $data->order_total2 - $data->coupon_discount_amount;
                }
                // discount_amount
                if ($data->discount_amount) {
                    $data->order_total2 = $data->order_total2 - $data->discount_amount;
                }
                $data->order_subtotal22 = Util::makePrice($data->order_subtotal2);
                $data->order_total22 = Util::makePrice($data->order_total2);
            }
            if ($data->payment_method) {
                continue;
            }
            if ($data->payment_method) unset($data->payment_method->api_key);
        }

        $t = ceil($total / $count);

        return response()->json([
            'error' => false,
            'data' => $datas,
            'page' => $page,
            'pages' => $t,
            'total' => $total
        ], 200);
    }

    public function delete(Request $request)
    {
        $id = $request->input('id');
        Order::where('id', $id)->delete();
        return response()->json(['error' => false]);
    }

    public function view(Request $request)
    {
        $id = $request->input('id');
        $data =  OrderController::orderview2($id);

        return response()->json([
            'error' => $data['error'],
            'order' => $data['order'],
            'company' => $data['company'],
            'user' => $data['user'],
            'currency' => "ETB",
            'ordersdetails' => $data['ordersdetails'],
            'products' => $data['products']
        ]);
    }

    public function orderview2($id)
    {
        try {
            $orders = Order::where('id', $id)->with('payment_method')->first();
            if (!is_null($orders)) {
                $orders->timeago = Util::timeago($orders->updated_at);
                $orders->updated_at2 = Util::formatDate($orders->updated_at);
                $orders->order_total1 = Util::makePrice($orders->order_total);
                $orders->order_subtotal1 = Util::makePrice($orders->order_subtotal);
                $orders->order_tax1 = Util::makePrice(Util::percent_calculate($orders->order_tax, 1) * $orders->order_subtotal);

                $orders->order_subtotal2 = 0;
                $orders->order_tax2 = 0;
                $orders->order_total2 = 0;
                if ($orders->total_investment_discount_amount) {
                    $orders->order_subtotal2 = (float)$orders->order_subtotal - (float)$orders->total_investment_discount_amount;
                    $orders->order_tax2 = (float)$orders->order_tax * $orders->order_subtotal2 / 100;
                    $orders->order_tax22 = Util::makePrice($orders->order_tax2);
                    $orders->order_total2 = $orders->order_tax2 + $orders->order_subtotal2;
                    // coupon_discount_amount
                    if ($orders->coupon_discount_amount) {
                        $orders->order_total2 = $orders->order_total2 - $orders->coupon_discount_amount;
                    }
                    // discount_amount
                    if ($orders->discount_amount) {
                        $orders->order_total2 = $orders->order_total2 - $orders->discount_amount;
                    }
                    $orders->order_subtotal22 = Util::makePrice($orders->order_subtotal2);
                    $orders->order_total22 = Util::makePrice($orders->order_total2);
                    $orders->total_investment_discount_amount1 = Util::makePrice($orders->total_investment_discount_amount);
                }
                if ($orders->total_investment_incentive) {
                    $orders->total_investment_incentive = explode(',', $orders->total_investment_incentive);
                }
                unset($orders->payment_method->api_key);

                if ($orders->discount_amount) {
                    $orders->discount_amount2 = Util::makePrice($orders->discount_amount);
                }
                $orders->coupon_discount_amount2 = Util::makePrice($orders->coupon_discount_amount);
                $orders->order_fee_amount2 = Util::makePrice($orders->order_fee_amount);
                $orders->created_at2 = Util::timeago($orders->created_at);

                $company = Company::first();
                $user = User::where('id', $orders->user_id)->first();
                $ordersdetails = OrderDetail::where('order_details.order_id', $orders->id)->with(['branch'])
                    ->get();

                // Role
                // if (Auth()->user()->role == 7) {
                //     $branch = Auth()->user()->branch;
                //     $ordersdetails = DB::table('ordersdetails')
                //         ->where('order', '=', $orders->id)
                //         ->where('branchid', $branch)
                //         ->get();
                // }

                if (empty($ordersdetails)) {
                    $ordersdetails = "Order id is not found";
                } else {
                    foreach ($ordersdetails as $orderDetail) {
                        $orderDetail->price2 = Util::makePrice($orderDetail->price);
                        $orderDetail->total2 = Util::makePrice($orderDetail->total);
                        $orderDetail->investment_discount_amount2 = Util::makePrice($orderDetail->investment_discount_amount);
                    }
                }

                // $settings = DB::table('settings')->where('param', '=', "default_currencies")->first();
                $products = Product::with('images')->limit(10)->get();


                return array(
                    'error' => false,
                    'order' => $orders,
                    'company' => $company,
                    'user' => $user,
                    'currency' => "ETB",
                    'ordersdetails' => $ordersdetails,
                    'products' => $products
                );
            } else {
                return array('error' => true, 'order' => $orders, 'id' => $id);
            }
        } catch (Throwable $e) {
            dd($e);
            return array('error' => true, 'msg' => $e);
        }
    }

    public function changeStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
            'status' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $id = $request->input('id') ?: "";
        $status = $request->input('status') ?: "";
        $values = array('order_status' => $status);

        $order = Order::where('id', $id)->first();
        if (is_null($order)) {
            return response()->json([
                'error' => true,
                'msg' => 'Order not found',
            ], 404);
        }

        $order->update($values);
        return response()->json([
            'error' => false,
            'data' => OrderController::orderview2($id),
            'msg' => 'Order status updated successfully',
        ]);

        //
        // Send Notifications to user
        //
        // $myRequest = new \Illuminate\Http\Request();
        // $myRequest->setMethod('POST');
        // $id = Order::where('id', $order_id)->first()->user;
        // $myRequest->request->add(['user' => $id]);
        // $myRequest->request->add(['title' => Lang::get(470)]); // 'Order status changed',
        // $status_text = DB::table('orderstatuses')->where('id', $status)->first()->status;
        // $myRequest->request->add(['text' => Lang::get(475) . $order_id . Lang::get(476) . $status_text]);  // "You order #",  ' was '
        // $defaultImage = DB::table('settings')->where('param', '=', "notify_image")->first()->value;
        // $myRequest->request->add(['imageid' => $defaultImage]);
        // MessagingController::sendNotify($myRequest);

        // Notification SMS for a user
        // $user = User::where('id', $id)->first();
        // if ($user->vendor_id == 9 || $user->vendor_id == 10) {
        //     Http::get(
        //         "https://users.move.et/api/user/send_push_message",
        //         [
        //             "mobile" => substr($user->phone, -9),
        //             "push_message" => $status_text
        //         ]
        //     );
        // }

        //
        // save to OrdersTime details
        //
        // $values = array(
        //     'order_id' => "$order_id",
        //     'status' => "$status",
        //     'driver' => 0,
        //     'comment' => "",
        //     'created_at' => new \DateTime(),
        //     'updated_at' => new \DateTime(),
        // );
        // DB::table('ordertimes')->insert($values);

        // return
        // $orderstatuses = DB::table('orderstatuses')->get();
        // $ordertimes = DB::table('ordertimes')->where('order_id', '=', $order_id)->orderBy('updated_at', 'desc')->get();
        // $drivers = DB::table('users')->where('role', '=', '3')->get();
    }

    public function updatePaymentStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
            'status' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $id = $request->input('id') ?: "";
        $status = $request->input('status') ?: "";
        $values = array();
        // Pending
        if ($status == 1) {
            $values = array('is_complete' => true);
        }
        // Completed
        if ($status == 2) {
            $values = array(
                'is_complete' => true,
                'view' => true
            );
        }

        $order = Order::where('id', $id)->first();
        if (is_null($order)) {
            return response()->json([
                'error' => true,
                'msg' => 'Order not found',
            ], 404);
        }

        $order->update($values);
        return response()->json([
            'error' => false,
            'data' => OrderController::orderview2($id),
            'msg' => 'Order status updated successfully',
        ]);

        //
        // Send Notifications to user
        //
        // $myRequest = new \Illuminate\Http\Request();
        // $myRequest->setMethod('POST');
        // $id = Order::where('id', $order_id)->first()->user;
        // $myRequest->request->add(['user' => $id]);
        // $myRequest->request->add(['title' => Lang::get(470)]); // 'Order status changed',
        // $status_text = DB::table('orderstatuses')->where('id', $status)->first()->status;
        // $myRequest->request->add(['text' => Lang::get(475) . $order_id . Lang::get(476) . $status_text]);  // "You order #",  ' was '
        // $defaultImage = DB::table('settings')->where('param', '=', "notify_image")->first()->value;
        // $myRequest->request->add(['imageid' => $defaultImage]);
        // MessagingController::sendNotify($myRequest);

        // Notification SMS for a user
        // $user = User::where('id', $id)->first();
        // if ($user->vendor_id == 9 || $user->vendor_id == 10) {
        //     Http::get(
        //         "https://users.move.et/api/user/send_push_message",
        //         [
        //             "mobile" => substr($user->phone, -9),
        //             "push_message" => $status_text
        //         ]
        //     );
        // }

        //
        // save to OrdersTime details
        //
        // $values = array(
        //     'order_id' => "$order_id",
        //     'status' => "$status",
        //     'driver' => 0,
        //     'comment' => "",
        //     'created_at' => new \DateTime(),
        //     'updated_at' => new \DateTime(),
        // );
        // DB::table('ordertimes')->insert($values);

        // return
        // $orderstatuses = DB::table('orderstatuses')->get();
        // $ordertimes = DB::table('ordertimes')->where('order_id', '=', $order_id)->orderBy('updated_at', 'desc')->get();
        // $drivers = DB::table('users')->where('role', '=', '3')->get();
    }

    public function addComment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
            'comment' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $id = $request->input('id') ?: "";
        $comment = $request->input('comment') ?: "";
        $values = array('order_comment' => $comment);

        $order = Order::where('id', $id)->first();
        if (is_null($order)) {
            return response()->json([
                'error' => true,
                'msg' => 'Order not found',
            ], 404);
        }

        $order->update($values);
        return response()->json([
            'error' => false,
            'data' => OrderController::orderview2($id),
            'msg' => 'Order comment updated successfully',
        ]);
    }
}
