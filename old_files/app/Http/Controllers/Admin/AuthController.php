<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\OtpSession;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    public function index()
    {
        if (Auth::check()) {
            return redirect()->route('admin.dashboard');
        }

        $page = "Admin login";
        $breadcrumb = 'Admin Login';

        return view('pages.admin.auth.login', [
            'title' => $breadcrumb,
            'page' => $page
        ]);
    }

    public function Login2FAView()
    {
        if (Auth::check()) {
            return redirect()->route('home');
        }

        $session = Session::get('otp_session');
        if (!$session) {
            return redirect()->route('admin.login');
        }

        $page = "2FA Login";
        $breadcrumb = '2FA Login';

        return view('pages.admin.auth.login-2FA', [
            'title' => $breadcrumb,
            'page' => $page
        ]);
    }

    public function account()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $page = "account";
        $breadcrumb = 'My Account';

        return view('pages.admin.account.account', [
            'title' => $breadcrumb,
            'page' => $page
        ]);
    }

    public function resetPasswordView()
    {
        $session = Session::get('forgot_otp_session');
        if (!$session) {
            return redirect()->route('forgotPassword');
        }

        return view('pages.admin.auth.reset-password', [
            'title' => 'Reset Password'
        ]);
    }

    public function sendOtp(Request $request, $fromLogin = false)
    {
        $validator = Validator::make($request->all(), [
            'phone' => ['sometimes', 'string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            // 'unique:users'
            'email' => ['sometimes', 'email'],
            'otp_channel' => ['required'],
            // 'required_without:phone,email',
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $email = $request->get('email');
        $phone = '251' . substr($request->get('phone'), -9);

        $subject = '2FA Authentication';
        $view = 'emails.2fa-otp';

        if (!$fromLogin) {
            // Check phone duplicate
            $is_phoneRegistered = User::where('phone', $phone)->first();
            if (!is_null($is_phoneRegistered)) {
                return response()->json(['error' => true, 'msg' => 'The Phone number has already been taken'], 409);
            }

            // Check email duplicate
            $is_emailRegistered = User::where('email', $email)->first();
            if (!is_null($is_emailRegistered)) {
                return response()->json(['error' => true, 'msg' => 'The Email has already been taken'], 409);
            }

            $subject = 'Account Verification';
            $view = 'emails.otp-verify';
        }

        $phone = $request->phone;

        $uuid = OtpSession::sendOTP($request->get('otp_channel'), $phone, $request->email, $subject, $view);

        if ($uuid['error'] == true) {
            return response()->json(["error" => true, "msg" => $uuid['msg']], 400);
        }

        if ($uuid['error'] == false && is_null($uuid['data'])) {
            return response()->json(["error" => true, "msg" => $uuid['msg']], 400);
        }

        Session::put('otp_session', $uuid['data']);
        return response()->json(["error" => false, "session" => $uuid['data'], "msg" => "OTP sent successfully"], 200);
    }

    public function verifyOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session' => 'required|string|max:255',
            'otp' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $session = Session::get('otp_session');
        if ($request->has('session')) {
            $session = $request->get("session");
        }

        $otpSession = OtpSession::where(['session' => $session, "otp" => $request->otp])->first();

        if (is_null($otpSession))
            return response()->json(["msg" => "Incorrect OTP.", "error" => true], 400);

        // $lapsed = abs($otpSession->expires_at->diffInMinutes(Carbon::now()));
        $lapsed = strtotime($otpSession->expires_at) - strtotime(Carbon::now());

        // if ($lapsed > env("OTP_EXPIRE_MIN"))
        if ($lapsed < 0)
            return response()->json(["msg" => "OTP expired", "error" => true], 400);

        $phone = $otpSession->phone;
        $otpSession->delete();

        return response()->json(["error" => false, "msg" => "Otp Verified"], 200);
    }

    public function otpLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session' => 'required|string|max:255',
            'email' => ['email'],
            'phone' => ['string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'password' => ['required'],
            'otp' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $session = Session::get('otp_session');
        if ($request->has('session')) {
            $session = $request->get("session");
        }

        $otpSession = OtpSession::where(['session' => $session, "otp" => $request->otp])->first();

        if (is_null($otpSession))
            return response()->json(["msg" => "Incorrect OTP.", "error" => true], 400);

        // $lapsed = abs($otpSession->expires_at->diffInMinutes(Carbon::now()));
        $lapsed = strtotime($otpSession->expires_at) - strtotime(Carbon::now());

        // if ($lapsed > env("OTP_EXPIRE_MIN"))
        if ($lapsed < 0)
            return response()->json(["msg" => "OTP expired.", "error" => true], 400);

        $phone = $otpSession->phone;
        $otpSession->delete();

        return AuthController::login($request);
    }

    /**
     * 2FA check
     */
    public function check2FA(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'email' => ['email'],
            'phone' => ['string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'password' => ['required'],
            'remember_me' => ['boolean']
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        // Login with email and password
        if ($request->has('email')) {
            // Get user
            $user = User::where('email', $request->get('email'))->where('2fa', 1)->first();

            // Validate if user is present
            if (!is_null($user) && Hash::check($request->get('password'), $user->password)) {
                // adding otp channel
                $request->request->add(['otp_channel' => 'email']);
                // redirect to otp verification page
                return AuthController::sendOtp($request, true);
            }
            return AuthController::login($request);
        }

        // Login with phone and password
        if ($request->has('phone')) {
            $validatedPhone = '251' . substr($request->get('phone'), -9);

            // Get user
            $user = User::where('phone', $validatedPhone)->where('2fa', 1)->first();

            // Validate if user is present
            if (!is_null($user) && Hash::check($request->get('password'), $user->password)) {
                // adding otp channel
                $request->request->add(['otp_channel' => 'phone']);
                // redirect to otp verification page
                return AuthController::sendOtp($request, true);
            }

            return AuthController::login($request);
        }

        return response()->json(['error' => true, 'msg' => 'These credentials do not match'], 401);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function login(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'email' => ['email'],
            'phone' => ['string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'password' => ['required'],
            'remember_me' => ['boolean']
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        $remember_me = $request->get('remember_me') ? true : false;

        // Retrieve the intended URL from the session
        $intendedUrl = Session::get('intended_url');

        // TODO: redirect to landing page
        // Login with email and password
        if ($request->has('email')) {
            if (auth()->attempt(array('email' => $request->get('email'), 'password' => $request->get('password')), $remember_me)) {
                return redirect()->route('dashboard');
                return response()->json(['error' => false, 'msg' => 'Logged in successfully.'], 200);
            }
        }

        // Login with phone and password
        if ($request->has('phone')) {
            $validatedPhone = '251' . substr($request->get('phone'), -9);
            if (auth()->attempt(array('phone' => $validatedPhone, 'password' => $request->get('password')), $remember_me)) {
                return redirect()->route('dashboard');
                return response()->json(['error' => false, 'msg' => 'Logged in successfully.'], 200);
            }
        }

        return response()->json(['error' => true, 'msg' => 'These credentials do not match'], 401);
    }

    public function sendForgotOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => ['sometimes', 'string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'email' => ['sometimes', 'email'],
            'otp_channel' => ['required']
        ]);

        if ($validator->fails()) {
            return response()->json(["error" => true, "msg" => $validator->errors()], 400);
        }

        $session = Session::get('forgot_otp_session');
        $otpSession = OtpSession::where(['session' => $session])->first();

        if (is_null($otpSession))
            return response()->json(["msg" => "Incorrect OTP.", "error" => true], 400);

        $lapsed = strtotime($otpSession->expires_at) - strtotime(Carbon::now());
        // $lapsed = abs($otpSession->expires_at->diffInMinutes(Carbon::now()));
        if ($lapsed > 0)
            return response()->json(["error" => false, "msg" => "OTP already sent"], 200);

        // if ($lapsed > env("OTP_EXPIRE_MIN"))
        if ($lapsed < 0)
            return response()->json(["msg" => "OTP expired", "error" => true], 400);

        $email = $request->get('email');
        $phone = '251' . substr($request->get('phone'), -9);
        if ($request->get('otp_channel') == 'phone') {
            // Check phone duplicate
            $is_phoneRegistered = User::where('phone', $phone)->where('active', 1)->first();
            if (is_null($is_phoneRegistered)) {
                return response()->json(['error' => true, 'msg' => 'User not found'], 404);
            }
        } else if ($request->get('otp_channel') == 'email') {
            // Check email duplicate
            $is_emailRegistered = User::where('email', $email)->where('active', 1)->first();
            if (is_null($is_emailRegistered)) {
                return response()->json(['error' => true, 'msg' => 'User not found'], 404);
            }
        } else {
            return response()->json(['error' => true, 'msg' => 'User not found'], 404);
        }

        $subject = 'Reset Password';
        $view = 'emails.reset-pwd';
        // $uuid = OtpSession::resetPwd($request->get('otp_channel'), $phone, $request->email);
        $uuid = OtpSession::sendOTP($request->get('otp_channel'), $phone, $request->email, $subject, $view);

        if ($uuid['error'] == true) {
            return response()->json(["error" => true, "msg" => $uuid['msg']], 400);
        }

        if ($uuid['error'] == false && is_null($uuid['data'])) {
            return response()->json(["error" => true, "msg" => $uuid['msg']], 400);
        }

        Session::put('forgot_otp_session', $uuid['data']);
        return response()->json(["error" => false, "session" => $uuid['data'], "msg" => "OTP sent successfully"], 200);
    }

    /**
     * Display a listing of the resource.
     */
    public function sendResetPwd(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session' => 'required|string|max:255',
            'otp' => 'required|integer',
            'password' => ['required', 'min:8', 'confirmed'],
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $session = Session::get('forgot_otp_session');
        if ($request->has('session')) {
            $session = $request->get("session");
        }

        $otpSession = OtpSession::where(['session' => $session, "otp" => $request->otp])->first();

        if (is_null($otpSession))
            return response()->json(["msg" => "Incorrect OTP", "error" => true], 400);

        $lapsed = strtotime($otpSession->expires_at) - strtotime(Carbon::now());
        // $lapsed = abs($otpSession->expires_at->diffInMinutes(Carbon::now()));
        if ($lapsed < 0)
            return response()->json(["msg" => "OTP expired", "error" => true], 400);

        $phone_email = $otpSession->phone;
        $otpSession->delete();

        // if ($lapsed > env("OTP_EXPIRE_MIN"))
        $password = $request->get('password');

        // Check phone duplicate
        $user = User::where('phone', $phone_email)
            ->orWhere('email', $phone_email)
            ->update(
                array(
                    'password' => Hash::make($password),
                )
            );

        if ($user) {
            return response()->json(['error' => false, 'msg' => 'Password Updated Successfully!'], 200);
        }

        return response()->json(['error' => true, 'msg' => 'Something went wrong'], 201);
    }

    public function logout(Request $request)
    {
        Auth::logout();
        return redirect()->route('admin.login');
    }
}
