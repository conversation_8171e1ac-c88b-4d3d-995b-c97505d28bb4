<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Setting;
use App\Models\User;
use App\Models\Util;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth.admin');
    }

    /**
     * Show the application dashboard.
     *
     */
    public function index()
    {
        // if (!Auth::check())
        //     return redirect()->route('home-page');

        $usersCount = count(User::whereNotIn('role_id', [1, 3, 4, 5, 6])->get());

        $orders = Order::where('is_complete', 1)->get();

        $ordersCount = count($orders);

        $nowYear = Carbon::now()->year;

        $all = 0;

        $e1 = 0;
        $e2 = 0;
        $e3 = 0;
        $e4 = 0;
        $e5 = 0;
        $e6 = 0;
        $e7 = 0;
        $e8 = 0;
        $e9 = 0;
        $e10 = 0;
        $e11 = 0;
        $e12 = 0;

        foreach ($orders as &$value) {
            $all += $value->order_total;
            if ($nowYear == Carbon::createFromFormat('Y-m-d H:i:s', $value->updated_at)->year) {
                $month = Carbon::createFromFormat('Y-m-d H:i:s', $value->updated_at)->month;
                if ($month == 1)
                    $e1 += $value->order_total;
                if ($month == 2)
                    $e2 += $value->order_total;
                if ($month == 3)
                    $e3 += $value->order_total;
                if ($month == 4)
                    $e4 += $value->order_total;
                if ($month == 5)
                    $e5 += $value->order_total;
                if ($month == 6)
                    $e6 += $value->order_total;
                if ($month == 7)
                    $e7 += $value->order_total;
                if ($month == 8)
                    $e8 += $value->order_total;
                if ($month == 9)
                    $e9 += $value->order_total;
                if ($month == 10)
                    $e10 += $value->order_total;
                if ($month == 11)
                    $e11 += $value->order_total;
                if ($month == 12)
                    $e12 += $value->order_total;
            }
        }

        $settings = Setting::where('param', '=', "default_currencies")->get();
        $orders = Order::where('is_complete', true)->with('user')->orderBy('updated_at', 'desc')->limit(10)->get();

        $iusers = User::all();
        foreach ($orders as $orderItem) {
            $orderItem->timeago = Util::timeago($orderItem->updated_at);
        }
        // dd([
        //     'userscount' => $usersCount, 'orderscount' => $ordersCount,
        //     'earning' => $all, 'currency' => "ETB",
        //     'iorders' => $orders, 'iusers' => $iusers,
        //     'e1' => $e1, 'e2' => $e2, 'e3' => $e3, 'e4' => $e4, 'e5' => $e5, 'e6' => $e6,
        //     'e7' => $e7, 'e8' => $e8, 'e9' => $e9, 'e10' => $e10, 'e11' => $e11, 'e12' => $e12,
        // ]);

        return view('pages.admin.dashboard.dashboard', [
            'userscount' => $usersCount, 'orderscount' => $ordersCount,
            'earning' => Util::makePrice($all), 'currency' => "ETB",
            'iorders' => $orders, 'iusers' => $iusers,
            'e1' => $e1, 'e2' => $e2, 'e3' => $e3, 'e4' => $e4, 'e5' => $e5, 'e6' => $e6,
            'e7' => $e7, 'e8' => $e8, 'e9' => $e9, 'e10' => $e10, 'e11' => $e11, 'e12' => $e12,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
