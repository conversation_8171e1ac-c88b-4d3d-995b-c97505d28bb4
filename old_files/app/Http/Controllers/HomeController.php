<?php

namespace App\Http\Controllers;

use App\Models\Advert;
use App\Models\Branch;
use App\Models\BusinessCalculator;
use App\Models\Company;
use App\Models\Image;
use App\Models\Product;
use App\Models\Setting;
use App\Models\Util;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HomeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $referalCode = null;
        if ($request->has('referalCode')) {
            $referalCode = $request->get('referalCode');
        }

        return HomeController::getPage(
            $request,
            1,
            true,
            0,
            0,
            ***********,
            0,
            '',
            true,
            $referalCode
        );
    }

    /**
     * search - shows search page
     */
    public function search(Request $request)
    {
        $referalCode = null;
        if ($request->has('referalCode')) {
            $referalCode = $request->get('referalCode');
        }

        $search = $request->input('search') ?: "";
        return HomeController::getPage(
            $request,
            1,
            true,
            0,
            0,
            ***********,
            0,
            $search,
            false,
            $referalCode
        );
    }

    /**
     * productsGoPage - used for pagination
     *
     * Return: paginated data of products
     */
    public function productsGetPage(Request $request)
    {
        $home = $request->input('home', true) ?: true;
        $page = $request->input('page') ?: 1;
        $sort = $request->input('sort') ?: 0;
        $productMinPrice = (int) $request->input('productMinPrice') ?: null;
        $productMaxPrice = (int) $request->input('productMaxPrice') ?: null;
        $cat = (int) $request->input('cat');
        $search = $request->input('search') ?: "";
        return HomeController::getPage(
            $request,
            $page,
            false,
            $sort,
            $productMinPrice,
            $productMaxPrice,
            $cat,
            $search,
            $home
        );
    }

    /**
     * getPage - fetches products with the specified parameters
     *
     * Return: if view is different from 0 returns view else returns response data
     */
    public static function getPage(
        $request,
        $page,
        $view,
        $sort,
        $productMinPrice,
        $productMaxPrice,
        $cat,
        $search,
        $home,
        $referalCode = null
    ) {
        // Order by
        $orderby = "";
        if ($sort == 1) // by date
            $orderby = "products.updated_at DESC";
        if ($sort == 2) // by low price top
            $orderby = "CASE WHEN products.discount_price!=0.00 THEN products.discount_price ELSE products.price END ASC";
        if ($sort == 3) // by high price top
            $orderby = "CASE WHEN products.discount_price!=0.00 THEN products.discount_price ELSE products.price END DESC";

        // page offset
        $offset = (($page - 1) * 12);

        // reset price range
        if ($productMinPrice > 0 && $productMaxPrice === 0) {
            $productMinPrice = 0;
            $productMaxPrice = ***********;
        }

        // All Products
        $data = Product::filteredProducts($request, $orderby, $productMinPrice, $productMaxPrice, $cat, $search);
        $products = $data['products'];

        // min - max
        $minmax = $data['minmax'];

        $count = count($products->get());
        // Not effiecient for pagination
        // $products = $products->limit(12)->offset($offset)->get();
        $products = $products->take(12)->skip($offset)->get();

        // min - max price
        $min = ***********;
        $max = 0;
        if (!empty($minmax)) {
            $min = $minmax[0]->minPrice;
            $max = $minmax[0]->maxPrice;
        }
        if ($min == '') $min = 0;
        if ($max == '') $max = 0;

        // fill with images and other datas
        $products = Product::fillProducts($products);

        // count
        $count_current = count($products);

        // number of pages for pagination
        $t = ceil($count / 12);

        $resultsPerPage = 12; // Number of results to show per page
        $totalResults = $data['count']; // Total number of results
        $currentPage = $page; // Current page number

        // Calculate the starting and ending indices of the results to display
        $startIndex = ($currentPage - 1) * $resultsPerPage + 1;
        $endIndex = min($startIndex + $resultsPerPage - 1, $totalResults);
        if ($count_current == 0) $startIndex = 0;

        // Banner
        $banner = Advert::whereIn('ad_type', ['banner', 'external link'])->where('visible', 1)->with(['image'])->get();

        // Featured products
        $featured = Product::featured();

        // Business calculator
        $bs = BusinessCalculator::where('visible', 1)->get();

        // Market's name
        // $marketName = "";

        $company = Company::with(['image'])->first();

        // Market's documents like fee, tax, phone, address, etc...
        // $docs = Docs::get($market);
        if ($view) {
            if ($home) {
                // Home page
                $ret = [
                    'cat' => $cat,
                    'products' => $products,
                    'featured' => $featured,
                    'count' => $count,
                    'page' => $page,
                    'pages' => (int)$t,
                    'min' => $min,
                    'max' => $max,
                    'banner' => $banner,
                    'results' => $totalResults,
                    'start' => $startIndex,
                    'end' => $endIndex,
                    'business_calculator' => $bs,
                    // 'title' => $marketName, // "Market's name"
                    // 'market' => $market,
                    // 'marketName' => $marketName,
                    // 'docs' => $docs
                ];
                if (!is_null($company)) {
                    $ret['title'] = $company->name ?: '';
                    if (!is_null($company->image)) {
                        Image::updateImageFile($company->image);
                        $ret['logo'] = $company->image->image_path;
                    }
                }
                return view('pages.customer.home.home', $ret)->with(['referalCode' => $referalCode]);
            } else {
                // Search page
                $ret = [
                    'cat' => $cat,
                    'search' => $search,
                    'products' => $products,
                    'featured' => $featured,
                    'count' => $count,
                    'page' => $page,
                    'pages' => (int)$t,
                    'min' => $min,
                    'max' => $max,
                    'results' => $totalResults,
                    'start' => $startIndex,
                    'end' => $endIndex,
                    'business_calculator' => $bs,
                    // 'title' => $marketName, // "Market's name"
                    // 'market' => $market,
                    // 'marketName' => $marketName,
                    // 'docs' => $docs
                ];
                // dd($products);
                return view('pages.customer.search.search', $ret)->with(['referalCode' => $referalCode]);
            }
        } else {
            // changing title for search pages
            $title = 'Seach Results for "' . $search . '"';
            return response()->json([
                'cat' => $cat,
                'products' => $products,
                'featured' => $featured,
                'count' => $count,
                'page' => $page,
                'pages' => (int)$t,
                'min' => $min,
                'max' => $max,
                'title' => $title,
                'results' => $totalResults,
                'start' => $startIndex,
                'end' => $endIndex,
                'business_calculator' => $bs,
            ], 200);
        }
    }

    /**
     * details - shows product's info
     *
     * Return: view
     */
    public function details($id)
    {
        $data = Product::getMainProduct($id);

        if (!is_null($data)) {
            return view('pages.customer.details.details', [
                'data' => $data,
                'title' => $data['name'],
            ]);
        }

        return abort(404);
    }

    /**
     * productsInfo - fetches product's info
     *
     * Return: product's info
     */
    public function productsInfo(Request $request)
    {
        $view = $request->input('view', 0);

        if ($view == 0) {
            // Check if shop is not open
            // $shopOpen = HomeController::shopOpenCloseTime2(0);
            // if ($shopOpen == 2) {
            //     return response()->json([
            //         "error" => 1,
            //         "text" => "We are not open"
            //     ]);
            // }
            // else if ($shopOpen == 3) {
            //     return response()->json([
            //         "error" => 1,
            //         "text" => "We are closed"
            //     ]);
            // }
        }

        $id = $request->input('id', 0);
        $branch_id = $request->input('branch_id', 0);
        $variant_id = $request->input('variant_id', null);
        if ($variant_id == 0) $variant_id = null;
        $cat = $request->input('cat', 1);

        if ($variant_id) {
            Log::info('fetching product with variant_id: ' . $variant_id);
            return response()->json([
                'error' => false,
                'data' => Product::getBranchProductVariant($id, $branch_id, $variant_id, $cat),
            ], 200);
        }

        if ($branch_id) {
            Log::info('fetching product with branch_id: ' . $id . ' - ' . $branch_id . ' - ' . $cat);
            return response()->json([
                'error' => false,
                'data' => Product::getBranchProduct($id, $branch_id, $cat),
            ], 200);
        }

        return response()->json([
            'error' => false,
            'data' => Product::getProduct($id, $cat),
        ], 200);
    }

    /**
     * shopOenCloseTime - request to check if a shop is open by specified time by vendor
     *
     * Return: response false if it's open else true if it's closed.
     */
    public function shopOpenCloseTime(Request $request)
    {
        $requestStatus = $request->input('status') == 1 ? 1 : 0;
        return HomeController::shopOpenCloseTime2($requestStatus);
    }

    public function productsBranchCheck(Request $request)
    {
        $product_id = $request->input('product_id') ?: 0;
        $branch_id = $request->input('branch_id') ?: 0;
        $variant_id = $request->input('variant_id') ?: 0;

        if ($branch_id == 0) {
            return response()->json([
                'error' => true,
                'data' => 'product not found'
            ], 200);
        }

        if ($variant_id) {
            $branch = Branch::whereHas('branchProducts', function ($query) use ($branch_id, $variant_id) {
                // Apply the where condition on the variant table
                $query->where('branch_id', $branch_id)->where('product_variant_id', $variant_id);
            })
                ->with(['branchProducts' => function ($query) use ($branch_id, $product_id) {
                    $query->where('branch_id', $branch_id)->where('product_id', $product_id);
                }])
                ->where('visible', true)
                ->first();
        } else {
            $branch = Branch::whereHas('branchProducts', function ($query) use ($branch_id, $product_id) {
                // Apply the where condition on the variant table
                $query->where('branch_id', $branch_id)->where('product_id', $product_id);
            })
                ->with(['branchProducts' => function ($query) use ($branch_id, $product_id) {
                    $query->where('branch_id', $branch_id)->where('product_id', $product_id);
                }])
                ->where('visible', true)
                ->first();
        }

        if (!$branch) {
            return response()->json([
                'error' => '1',
                'data' => 'product not found'
            ], 200);
        }

        $branch->product = Product::find($product_id);
        $branch = $branch->toArray();
        $branch['branch_products'] = $branch['branch_products'][0];

        return response()->json([
            'error' => '0',
            'data' => $branch,
        ], 200);
    }

    // Get market branches
    public function addresses(Request $request)
    {
        $method = $request->input('method');
        $userLat = $request->input('user_lat');
        $userLng = $request->input('user_lng');
        // vendor branches
        $branches = DB::table('branches')
            ->where('branches.lat', '!=', '')
            ->where('branches.lng', '!=', '')
            ->where('visible', 1)
            ->get();
        $branches = DB::table('branches')
            ->where('branches.lat', '!=', '')
            ->where('branches.lng', '!=', '')
            ->where('visible', 1)
            ->get();
        if (count($branches) != 0) {
            if ($method == 'deliveryBranchMethod') {
                foreach ($branches as $branch) {
                    $branchLat = $branch->lat;
                    $branchLng = $branch->lng;
                    $unit = 'km';
                    $distance = Util::getDistanceFromLatLonInKm((float)$branchLat, (float)$branchLng, (float)$userLat, (float)$userLng, $unit);
                    $branch->distance = $distance;
                }
            }
            return response()->json([
                'error' => '0',
                'data' => $branches
            ], 200);
        } else {
            return response()->json([
                "error" => 1,
                "text" => "Delivery will be available shortly. Please use curbside pickup for now."
            ]);
        }
    }

    /**
     * shopOenCloseTime2 - checks if a shop is open by specified time by vendor
     * @requestStatus - a request status or a function call variable
     *
     * Return: response false if it's open else true if it's closed.
     */
    public function shopOpenCloseTime2($requestStatus)
    {
        // open close time status
        $status = 1;
        // Default time zone
        date_default_timezone_set("Africa/Nairobi");
        // Vendor Shop
        $company = DB::table('companies')->first();
        // Current time
        $curentTime =  getdate(date("U"))[0];
        // For Monday
        if (date("N") == 1 && ($company->openTimeMonday != "" || $company->closeTimeMonday != "")) {
            // Open Time
            if ($company->openTimeMonday != "" && $curentTime < strtotime($company->openTimeMonday)) {
                $status = 2;
            }
            // Close Time
            if ($company->closeTimeMonday != "" && $curentTime > strtotime($company->closeTimeMonday)) {
                $status = 3;
            }
        }
        // For Tuesday
        if (date("N") == 2 && ($company->openTimeTuesday != "" || $company->closeTimeTuesday != "")) {
            // Open Time
            if ($company->openTimeTuesday != "" && $curentTime < strtotime($company->openTimeTuesday)) {
                $status = 2;
            }
            // Close Time
            if ($company->closeTimeTuesday != "" && $curentTime > strtotime($company->closeTimeTuesday)) {
                $status = 3;
            }
        }
        // For Wednesday
        if (date("N") == 3 && ($company->openTimeWednesday != "" || $company->closeTimeWednesday != "")) {
            // Open Time
            if ($company->openTimeWednesday != "" && $curentTime < strtotime($company->openTimeWednesday)) {
                $status = 2;
            }
            // Close Time
            if ($company->closeTimeWednesday != "" && $curentTime > strtotime($company->closeTimeWednesday)) {
                $status = 3;
            }
        }
        // For Thursday
        if (date("N") == 4 && ($company->openTimeThursday != "" || $company->closeTimeThursday != "")) {
            // Open Time
            if ($company->openTimeThursday != "" && $curentTime < strtotime($company->openTimeThursday)) {
                $status = 2;
            }
            // Close Time
            if ($company->closeTimeThursday != "" && $curentTime > strtotime($company->closeTimeThursday)) {
                $status = 3;
            }
        }
        // For Friday
        if (date("N") == 5 && ($company->openTimeFriday != "" || $company->closeTimeFriday != "")) {
            // Open Time
            if ($company->openTimeFriday != "" && $curentTime < strtotime($company->openTimeFriday)) {
                $status = 2;
            }
            // Close Time
            if ($company->closeTimeFriday != "" && $curentTime > strtotime($company->closeTimeFriday)) {
                $status = 3;
            }
        }
        // For Saturday
        if (date("N") == 6 && ($company->openTimeSaturday != "" || $company->closeTimeSaturday != "")) {
            // Open Time
            if ($company->openTimeSaturday != "" && $curentTime < strtotime($company->openTimeSaturday)) {
                $status = 2;
            }
            // Close Time
            if ($company->closeTimeSaturday != "" && $curentTime > strtotime($company->closeTimeSaturday)) {
                $status = 3;
            }
        }
        // For Sunday
        if (date("N") == 7 && ($company->openTimeSunday != "" || $company->closeTimeSunday != "")) {
            // Open Time
            if ($company->openTimeSunday != "" && $curentTime < strtotime($company->openTimeSunday)) {
                $status = 2;
            }
            // Close Time
            if ($company->closeTimeSunday != "" && $curentTime > strtotime($company->closeTimeSunday)) {
                $status = 3;
            }
        }

        // if it's a request
        if ($requestStatus == 1) {
            // Shop not open
            if ($status == 2) {
                return response()->json(["error" => 0, "status" => $status, "text" => "We are not open"]);
            }
            // Shop is closed
            else if ($status == 3) {
                return response()->json(["error" => 0, "status" => $status, "text" => "We are closed"]);
            }
        }

        return $status;
    }

    public function getAboutUs()
    {
        $data = Setting::legalsInfo('about');
        if ($data['about_visible'] == 1) {
            return HomeController::showLegalsView($data['about'], 'About Us');
        }

        return abort(404);
    }

    public function getPrivacy()
    {
        $data = Setting::legalsInfo('privacy');
        if ($data['privacy_visible'] == 1) {
            return HomeController::showLegalsView($data['privacy'], 'Privacy Policy');
        }

        return abort(404);
    }

    public function getTerms()
    {
        $data = Setting::legalsInfo('terms');
        if ($data['terms_visible'] == 1) {
            return HomeController::showLegalsView($data['terms'], 'Terms & Condition');
        }

        return abort(404);
    }

    public function getDelivery()
    {
        $data = Setting::legalsInfo('delivery');
        if ($data['delivery_visible'] == 1) {
            return HomeController::showLegalsView($data['delivery'], 'Delivery Info');
        }

        return abort(404);
    }

    public function getRefund()
    {
        $data = Setting::legalsInfo('refund');
        if ($data['refund_visible'] == 1) {
            return HomeController::showLegalsView($data['refund'], 'Refund Policy');
        }

        return abort(404);
    }

    public function showLegalsView($data, $title)
    {
        return view('pages.customer.legals.legals', [
            'title' => $title,
            'data' => $data,
        ]);
    }
}
