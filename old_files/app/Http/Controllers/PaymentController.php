<?php

namespace App\Http\Controllers;

use App\Models\Image;
use App\Models\Payment;
use App\Models\Util;
use App\Traits\PaginateSortSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Illuminate\Support\Facades\Validator;

class PaymentController extends Controller
{
    use PaginateSortSearch;

    // TODO: add authentication
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('pages.admin.payment-method.payment-method');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => ['string'],
            'name' => ['required'],
            'api_key' => ['required'],
            'image' => ['required', 'integer'],
            'visible' => ['required', 'boolean']
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $editId = $request->input('id');
        $is_apiKeyRegistered = Payment::where('api_key', $request->input('api_key'))->first();

        if (!is_null($is_apiKeyRegistered) && $editId == "0") {
            return response()->json(['error' => true, 'msg' => 'API key is already registered'], 400);
        }

        $name = $request->input('name');
        $api_key = $request->input('api_key');
        $image = $request->input('image');
        $visible = $request->input('visible', 0);

        $values = array(
            'name' => $name,
            'api_key' => $api_key,
            'image_id' => $image,
            'visible' => $visible
        );

        if ($editId != "0") {
            Payment::where('id', $editId)
                ->update($values);
            $data = Payment::find($editId);
        } else {
            $data = Payment::create($values);
        };

        return PaymentController::edit($data);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request)
    {
        $datas = Payment::query();
        $datas = $this->run($request, $datas, ['name', 'ad_type'], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data'];
        foreach ($datas as $data) {
            Image::updateImageFile($data->image);
            // if ($data->image) {
            //     $domainName = FacadesRequest::root();
            //     $data->image->image_path = $domainName . '/' . $data->image->image_path;
            // }
        }
        $datas = $datas->toArray();
        $datas['data'] = array_map(function ($data) {
            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);

        return response()->json(['error' => false, 'data' => $datas], 200);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Payment $payment)
    {
        if (is_null($payment)) {
            return response()->json(['error' => false, 'msg' => 'Payment method not found'], 404);
        }

        $payment->timeago = Util::timeago($payment->updated_at);
        $payment->updated_at2 = Util::formatDate($payment->updated_at);
        $payment->ad_type_id = Util::getBannerVal($payment->ad_type);
        Image::updateImageFile($payment->image);

        return response()->json([
            'error' => false,
            'data' => $payment
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function delete(Payment $payment)
    {
        $payment->delete();

        return response()->json(['error' => false]);
    }
}
