<?php

namespace App\Http\Controllers;

use App\Models\Image;
use App\Models\ImageUpload;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ImageUploadController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function storeAdmin(Request $request)
    {
        // if (!Auth::check())
        //     return redirect()->route('/');

        $requestValidated = Validator::make($request->all(), [
            'image' => ['required', 'file', 'image', 'mimes:jpeg,jpg,png,gif,svg', 'max:2048'],
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        $file = $request->file('image');
        $extenstion = $file->getClientOriginalExtension();
        $countExtenstion = strlen($extenstion);
        $fileName = substr($file->getClientOriginalName(), 0, 25);
        $countFileName = strlen(substr($file->getClientOriginalName(), 0, 25));
        if (substr($fileName, (-1 * $countExtenstion))) {
            $fileName = substr($file->getClientOriginalName(), 0, $countFileName - ($countExtenstion));
            if (substr($fileName, (-1) == '.')) {
                $fileName = substr($file->getClientOriginalName(), 0, $countFileName - ($countExtenstion + 1));
            }
        }

        $fileName = Carbon::now()->format('d_m_Y') . '-' . $fileName . '.' . $extenstion;
        $path = $file->storeAs('admin', $fileName, 'public');
        $path = 'storage/' . $path;
        $is_added = Image::where('');
        $image = new Image();
        $image->image_path = $path;
        $image->save();
        $image->makeHidden(['created_at', 'updated_at']);

        return response()->json(['error' => false, 'data' => $image], 201);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function storeCustomer(Request $request)
    {
        // if (!Auth::check())
        //     return redirect()->route('/');

        $requestValidated = Validator::make($request->all(), [
            'image' => ['required', 'file', 'image', 'mimes:jpeg,jpg,png,gif,svg', 'max:2048'],
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        $file = $request->file('image');
        $extenstion = $file->getClientOriginalExtension();
        $countExtenstion = strlen($extenstion);
        $fileName = substr($file->getClientOriginalName(), 0, 25);
        $countFileName = strlen(substr($file->getClientOriginalName(), 0, 25));
        if (substr($fileName, (-1 * $countExtenstion))) {
            $fileName = substr($file->getClientOriginalName(), 0, $countFileName - ($countExtenstion));
            if (substr($fileName, (-1) == '.')) {
                $fileName = substr($file->getClientOriginalName(), 0, $countFileName - ($countExtenstion + 1));
            }
        }
        $fileName = Carbon::now()->format('d_m_Y') . '-' . $fileName . '.' . $extenstion;
        $file->storeAs('customer', $fileName, 'public');
        dd($fileName);


        // $image->move(public_path('images'), $imageName);
        // $imageUpload = new ImageUpload();
        // $imageUpload->filename = $imageName;
        // $imageUpload->vendor = Auth::user()->vendor;
        // $imageUpload->save();
        // return response()->json(['filename' => $imageUpload->filename, 'id' => $imageUpload->id, 'date' => $imageUpload->updated_at->format('Y-m-d H:i:s')]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ImageUpload $imageUpload)
    {
        // if (!Auth::check())
        //     return redirect()->route('/');

        dd($imageUpload);
        // ImageUpload::where('filename',$filename)->delete();
        // $path=public_path().'/images/'.$filename;
        // if (file_exists($path)) {
        //     unlink($path);
        // }

        // return response()->json(['filename'=>$filename]);
    }
}
