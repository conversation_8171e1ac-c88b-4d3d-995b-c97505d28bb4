<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use App\Models\User;
use App\Models\Util;
use App\Traits\PaginateSortSearch;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Illuminate\Support\Facades\Validator;

class ChatController extends Controller
{
    use PaginateSortSearch;

    // TODO: add authentication
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('pages.admin.chat.chat');
    }

    /**
     * Display a listing of the resource.
     */
    public function getNewMsgsCount()
    {
        $count = count(Chat::where('read', 0)->where('to_user', Auth::user()->id)->with("user")->get());
        return response()->json([
            'error' => false,
            'count' => $count
        ], 200);
    }

    public function getChatMessagesNewCount(Request $request)
    {
        $id = Auth::user()->id;
        $ordercount = count(DB::select("SELECT * FROM orders WHERE is_complete=1 AND view=0"));
        return response()->json([
            'error' => false,
            'count' => count(Chat::where('read', 0)->where('to_user', $id)->rightJoin("users", "users.id", "chats.from_user")->get()),
            'orders' => $ordercount,
        ], 200);
    }

    public function getUserMsgs(Request $request)
    {
        $from_user = $request->input('user_id');
        return ChatController::getMsgs($from_user);
    }

    public function getMsgs($from_user)
    {
        $to_user = Auth::user()->id;

        $msg = DB::select("SELECT * FROM (
                (SELECT chats.*, 'customer' as author
                FROM chats
                WHERE from_user=$from_user AND to_user=$to_user ORDER BY created_at ASC)
                UNION
                (SELECT chats.*, 'vendor' as author FROM chats
                WHERE from_user=$to_user AND to_user=$from_user ORDER BY created_at ASC)) AS i
                ORDER BY created_at ASC
                ");
        foreach ($msg as &$value) {
            $value->created_at2 = Util::formatTime($value->created_at);
            $value->created_at3 = Util::formatMonthYear($value->created_at);
        }
        $user = User::where('id', $from_user)->first();
        $user->makeHidden(['email_verified_at', 'has_purchased_product', 'identification_image_id', 'phone_verified_at', 'referral_link', 'created_at', 'updated_at']);

        $values = array(
            'read' => 1
        );
        Chat::where('from_user', $from_user)
            ->where('to_user', $to_user)
            ->update($values);

        $response = [
            'error' => false,
            'user' => $user,
            'messages' => $msg,
            'unread' => count(Chat::where('read', 0)
                ->where('to_user', $to_user)
                ->rightJoin("users", "users.id", "chats.from_user")
                ->get()),
        ];
        return response()->json($response, 200);
    }

    public function sendNewMsg(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'user_id' => ['required', 'string']
        ]);

        if ($requestValidated->fails()) {
            return response()->json(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        $to_user = $request->input('user_id');
        $from_user = Auth::user()->id;
        $msg = $request->input('msg');
        return ChatController::sendNewMsg2($from_user, $to_user, $msg);
    }

    public function sendNewMsg2($from_user, $to_user, $msg)
    {
        $values = array(
            'to_user' => $to_user,
            'from_user' => $from_user,
            'msg' => $msg,
            'delivered' => 0,
            'read' => 0,
        );
        Chat::create($values);

        //
        // Send Notifications to user
        //
        // $myRequest = new \Illuminate\Http\Request();
        // $myRequest->setMethod('POST');
        // $myRequest->request->add(['user' => $to_user]);
        // $myRequest->request->add(['chat' => 'true']);
        // $myRequest->request->add(['title' => Lang::get(477)]); // Chat Message
        // $myRequest->request->add(['text' => $text]);
        // $defaultImage = DB::table('settings')->where('param', '=', "notify_image")->first()->value;
        // $myRequest->request->add(['imageid' => $defaultImage]);
        // MessagingController::sendNotify($myRequest);

        return ChatController::getMsgs($to_user);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function get_messages($read = false)
    {
        // User id
        $to_user = Auth::user()->id;
        // Company user id
        $from_user = 1;

        // Customer user
        $customer = Auth::user();
        // Company user
        $company = User::where('id', $from_user)->first();

        // get all messages
        // customer msgs
        $msgs = Chat::select('chats.*', DB::raw("'company' as sender"))
            ->where('from_user', $from_user)
            ->where('to_user', $to_user)
            ->orderBy('created_at', 'ASC');

        // company msgs
        $msgs = $msgs->union(function ($query) use ($from_user, $to_user) {
            $query->select('chats.*', DB::raw("'customer' as sender"))
                ->from('chats')
                ->where('from_user', $to_user)
                ->where('to_user', $from_user);
        });

        $msgs = $msgs->orderBy('created_at', 'ASC')->get();

        foreach ($msgs as $msg) {
            $msg->created_at2 = Carbon::parse($msg->created_at)->format('M d, Y');
        }

        if ($read == true) {
            $values = array(
                'read' => true,
                'updated_at' => new \DateTime()
            );

            Chat::where('from_user', $from_user)
                ->where('to_user', $to_user)
                ->update($values);
        }

        return response()->json([
            'error' => false,
            'msgs' => $msgs,
            'customer' => $customer,
            'company' => $company,
        ], 200);
    }

    public function new_messages()
    {
        return ChatController::get_messages(true);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function send_msg(Request $request)
    {
        $from_user = Auth::user()->id;
        $text = $request->input('text');
        return ChatController::new_msg_send($from_user, $text);
    }

    /**
     * Update the specified resource in storage.
     */
    public function new_msg_send($from_user, $text)
    {
        if (!Auth::check())
            return response()->json(['error' => "1",], 200);

        $values = array(
            'to_user' => 1, // admin
            'from_user' => $from_user,
            'msg' => $text,
            'delivered' => false,
            'read' => false,
        );

        Chat::create($values);

        //
        // Send Notifications to user
        //
        // $myRequest = new \Illuminate\Http\Request();
        // $myRequest->setMethod('POST');
        // $myRequest->request->add(['user' => $to_user]);
        // $myRequest->request->add(['chat' => 'true']);
        // $myRequest->request->add(['title' => Lang::get(151)]); // Chat Message
        // $myRequest->request->add(['text' => $text]);
        // $defaultImage = DB::table('settings')->where('param', '=', "notify_image")->first()->value;
        // $myRequest->request->add(['imageid' => $defaultImage]);
        // MessagingController::sendNotify($myRequest);

        return ChatController::get_messages();
    }

    // Chat users
    public function getUsers(Request $request)
    {
        // $page = $request->get('page') ?: 1;
        // $search = $request->get('search') ?: '';
        // $offset = (($page - 1) * 10);
        // if (Auth::user()->id == 0) {
        //     $users = User::where([["users.id", '!=', Auth::id()]])
        //         ->where('users.name', 'like', '%' . $search . '%')
        //         // ->leftjoin("image_uploads", 'image_uploads.id', '=', 'users.imageid')
        //         // , 'image_uploads.filename as image'
        //         ->select('users.id', 'users.name')
        //         ->orderBy('users.updated_at')
        //         ->offset($offset)
        //         ->limit(10)
        //         ->get();
        //     $count = count(
        //         User::where([["users.id", '!=', Auth::id()]])
        //             ->where('users.name', 'like', '%' . $search . '%')
        //             // ->leftjoin("image_uploads", 'image_uploads.id', '=', 'users.imageid')
        //             // , 'image_uploads.filename as image'
        //             ->select('users.id', 'users.name')
        //             ->get()
        //     );
        //     $pages = ceil($count / 10);
        // }

        // if (Auth::user()->id != 0) {
        //     $users = User::where([
        //         ["users.id", '!=', Auth::id()],
        //     ])
        //         ->where('users.name', 'like', '%' . $search . '%')
        //         // ->leftjoin("image_uploads", 'image_uploads.id', '=', 'users.imageid')
        //         // , 'image_uploads.filename as image'
        //         ->select('users.id', 'users.name')
        //         ->orderBy('users.updated_at')
        //         ->offset($offset)
        //         ->limit(10)
        //         ->get();

        //     $count = count(
        //         User::where([
        //             ["users.id", '!=', Auth::id()],
        //         ])
        //             ->where('users.name', 'like', '%' . $search . '%')
        //             // ->leftjoin("image_uploads", 'image_uploads.id', '=', 'users.imageid')
        //             // , 'image_uploads.filename as image'
        //             ->select('users.id', 'users.name')
        //             ->get()
        //     );
        //     $pages = ceil($count / 10);
        // }

        // $usersData = array();
        // foreach ($datas['data'] as &$data) {
        //     // all messages
        //     $data->messages = 0;
        //     foreach ($all as &$data2) {
        //         if ($data->id == $data2->from_user)
        //             $data->messages = $data2->result;
        //     }

        //     // unread messages
        //     $data->unread = 0;
        //     foreach ($unreadUserMessages as &$data2) {
        //         if ($data->id == $data2->from_user)
        //             $data->unread = $data2->result;
        //     }
        //     $usersData[] = $data;
        // }

        // usort($usersData, function ($a, $b) {
        //     if ($a->unread != $b->unread) return $a->unread < $b->unread ? 1 : -1;
        //     if ($a->messages == $b->messages) return 0;
        //     return $a->messages < $b->messages ? 1 : -1;
        // });

        $datas = User::query();
        $datas = $datas->where([["users.id", '!=', Auth::id()]]);
        $datas = $this->run($request, $datas, ['name', 'email', 'phone'], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data'];
        foreach ($datas as $data) {
            $data->image;
            $domainName = FacadesRequest::root();
            if ($data->image) {
                $data->image->image_path = $domainName . '/' . $data->image->image_path;
            } else {
                $data->default_image = $domainName . '/img/user-circle.png';
            }
            $data->makeHidden(['email_verified_at', 'has_purchased_product', 'identification_image_id', 'phone_verified_at', 'referral_link', 'created_at', 'updated_at']);
        }
        $datas = $datas->toArray();

        $unreadUserMessages = Chat::where('to_user', '=', Auth::id())
            ->where('read', 0)
            ->selectRaw('from_user, count(*) as result')
            ->groupBy('from_user')
            ->get();
        $all = Chat::where('to_user', '=', Auth::id())
            ->selectRaw('from_user, count(*) as result')
            ->groupBy('from_user')
            ->get();

        $datas['data'] = array_map(function ($data) use ($all, $unreadUserMessages) {
            // all messages
            $data['messages'] = 0;
            foreach ($all as &$data2) {
                if ($data['id'] == $data2->from_user)
                    $data['messages'] = $data2->result;
            }

            // unread messages
            $data['unread'] = 0;
            foreach ($unreadUserMessages as &$data2) {
                if ($data['id'] == $data2->from_user)
                    $data['unread'] = $data2->result;
            }
            return $data;
        }, $datas['data']);

        $usersData = $datas['data'];

        // Sort
        usort($usersData, function ($a, $b) {
            if ($a['unread'] != $b['unread']) return $a['unread'] < $b['unread'] ? 1 : -1;
            if ($a['messages'] == $b['messages']) return 0;
            return $a['messages'] < $b['messages'] ? 1 : -1;
        });

        $pages = ceil($datas['total'] / $datas['per_page']);
        return response()->json([
            "error" => false,
            "users" => $usersData,
            "pages" => $pages,
            "page" => $datas["current_page"]
        ], 200);
    }
}
