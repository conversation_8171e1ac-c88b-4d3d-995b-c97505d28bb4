<?php

namespace App\Http\Controllers;

use App\Models\Referral;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AccountController extends Controller
{
    public function changePassword(Request $request)
    {
        if (!Auth::check())
            return response()->json(['error' => true], 401);

        $oldPassword = $request->input('oldPassword') ?: "";
        $newPassword = $request->input('newPassword') ?: "";

        // Incorrect current password
        if (!Hash::check($oldPassword, Auth::user()->password)) {
            return response()->json(['error' => true, 'msg' => 'Please check your current password; it seems to be incorrect.'], 422);
        }

        $requestValidated = Validator::make($request->all(), [
            'newPassword' => ['required', 'min:8', 'confirmed']
        ]);

        if ($requestValidated->fails()) {
            return response()->json(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        // Incorrect current password
        if (Hash::check($newPassword, Auth::user()->password)) {
            return response()->json(['error' => true, 'msg' => 'New password is the same as old password'], 422);
        }

        $values = array(
            'password' => bcrypt($newPassword)
        );

        // Auth::user()->password = bcrypt($newPassword);
        // Auth::user()->save();
        User::where('id', Auth::user()->id)->update($values);

        return response()->json(['error' => '0']);
    }

    public function generateReferal()
    {
        if (!Auth::check())
            return response()->json(['error' => true], 401);

        $link = Referral::generate();

        return response()->json(['error' => false, 'data' => $link]);
    }

    public function updateTwoFA(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'two_fa' => ['boolean']
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        $values = array(
            '2fa' => $request->input('two_fa', false),
        );

        $user = User::where('id', Auth::user()->id)->update($values);

        return response()->json(['error' => false, 'data' => User::where('id', Auth::user()->id)->first()['2fa']]);
    }
}
