<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Product;
use App\Models\Util;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

class OrderInfoController extends Controller
{
    public function load(Request $request)
    {
        if (!Auth::check())
            return redirect()->route('login');

        $id = (int) Route::current()->parameter('id') ?: "";

        // order
        $order = Order::where('id', $id)->where('user_id', Auth::user()->id)->where('is_complete', 1)->with('orderDetails')->orderBy('id', 'DESC')->select('orders.*', DB::raw('(@row_number:=@row_number + 1) AS No'))->first();
        if (is_null($order)) {
            return redirect()->back();
        }

        // order details
        $ordersdata = $order->orderDetails;

        // fee, tax, subtotal, total
        $orderInfo = Util::getOrderDetailsInfo($order, $ordersdata);

        $featured = Product::featured();

        return view('pages.customer.order_details.order-details', [
            'title' => 'Order details',
            'order' => $order,
            'couponCode' => $order->couponName,
            'ordersdetails' =>  $ordersdata,
            'subtotal' => $orderInfo["subtotal"],
            'subtotalAll' => $orderInfo["subtotalAll"],
            'fee' => $orderInfo["fee"],
            'tax' => $orderInfo["tax"],
            'total' => $orderInfo["total"],
            'title' => 'Order details',
            'featured' => $featured
        ]);
    }
}
