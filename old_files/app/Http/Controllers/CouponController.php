<?php

namespace App\Http\Controllers;

use App\Models\Coupon;
use App\Models\Util;
use App\Traits\PaginateSortSearch;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CouponController extends Controller
{
    use PaginateSortSearch;

    // TODO: add authentication
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('pages.admin.coupon.coupon');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => ['string'],
            'name' => ['required'],
            'start_date' => ['required'],
            'end_date' => ['required'],
            'discount' => ['required'],
            'min_amount' => ['required'],
            'percent' => ['integer', 'boolean'],
            'desc' => ['required', 'string'],
            'visible' => ['required', 'boolean']
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $editId = $request->input('id');
        $name = $request->input('name');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $discount = $request->input('discount');
        $min_amount = $request->input('min_amount');
        $desc = $request->input('desc');
        $percent = $request->input('percent', 0);
        $visible = $request->input('visible', 0);

        // Format date and time
        $startDate = Carbon::createFromFormat('Y-m-d\TH:i', $startDate);
        $endDate = Carbon::createFromFormat('Y-m-d\TH:i', $endDate);
        $now = Carbon::now();
        if ($startDate >= $endDate)
            return response()->json(['error' => true, 'msg' => 'Start date can\'t be greater than end date']);
        if ($endDate <= $now)
            return response()->json(['error' => true, 'msg' => 'End date can\'t be greater than current date']);

        $values = array(
            'name' => $name,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'discount' => $discount,
            'min_amount' => $min_amount,
            'desc' => $desc,
            'percent' => $percent,
            'visible' => $visible
        );

        if ($editId != "0") {
            Coupon::where('id', $editId)
                ->update($values);
            $data = Coupon::find($editId);
        } else {
            $data = Coupon::create($values);
        };

        return CouponController::edit($data);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request)
    {
        $datas = Coupon::query();
        $datas = $this->run($request, $datas, ['name', 'ad_type'], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data']->toArray();
        $datas['data'] = array_map(function ($data) {
            $data['start_date'] = Util::formatDate($data['start_date']);
            $data['end_date'] = Util::formatDate($data['end_date']);
            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);

        return response()->json(['error' => false, 'data' => $datas], 200);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Coupon $coupon)
    {
        if (is_null($coupon)) {
            return response()->json(['error' => false, 'msg' => 'Coupon not found'], 404);
        }

        $coupon->timeago = Util::timeago($coupon->updated_at);
        $coupon->updated_at2 = Util::formatDate($coupon->updated_at);
        $coupon->ad_type_id = Util::getBannerVal($coupon->ad_type);

        return response()->json([
            'error' => false,
            'data' => $coupon
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function delete(Coupon $coupon)
    {
        $coupon->delete();

        return response()->json(['error' => false]);
    }
}
