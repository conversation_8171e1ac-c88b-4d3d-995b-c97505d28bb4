<?php

namespace App\Http\Controllers;

use App\Models\Util;
use App\Models\Branch;
use App\Models\Product;
use App\Models\ImageUpload;
use Illuminate\Http\Request;
use App\Models\BranchProduct;
use App\Models\ProductBusinessCalculator;
use App\Models\ProductVariant;
use App\Models\RecommendedProduct;
use App\Traits\PaginateSortSearch;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ProductController extends Controller
{
    use PaginateSortSearch;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // $products = Product::offset(0)->limit(15)->get();
        $branches = Branch::all();
        $products = ProductController::getProducts();
        return view('pages.admin.product.product', [
            'branches' => $branches,
            'products' => $products['data']->toArray()
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => ['required'],
            'name' => ['required'],
            'price' => ['required'],
            'desc' => ['required'],
            'published' => ['required'],
            'branchData' => ['array'],
            'cacheRProducts' => ['array'],
            'cacheVariant' => ['array'],
            'cachePrBusCalculator' => ['array'],
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $editId = $request->input('id');

        if (!$editId) {
            $validData = Validator::make($request->all(), [
                'branchData' => ['required']
            ]);

            if ($validData->fails()) {
                return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
            }
        }

        $branchDatas = $request->get('branchData');
        if ($request->has('branchData')) {
            // Check address field
            if (count($branchDatas) == 0) {
                return response()->json(['error' => true, 'msg' => 'Please add address'], 400);
            }

            foreach ($branchDatas as $branchData) {
                if (!Branch::find($branchData['branch']['id'])) {
                    return response()->json(['error' => true, 'msg' => $branchData['branch'] . ' not found'], 400);
                }
            }
        }

        $name = $request->input('name') ?: "";
        $price = $request->input('price') ?: 0.0;
        $discPrice = $request->input('discPrice') ?: null;
        $desc = $request->input('desc') ?: "";
        $videoLink = $request->input('videoLink') ?: "";
        $images = $request->input('images') ?: "";
        $featured = $request->input('featured', 0);
        $published = $request->input('published', 1);
        $exited = $request->input('exited', 0);

        $values = array(
            'name' => $name,
            'price' => $price,
            'discount_price' => $discPrice,
            'desc' => $desc,
            'video_link' => $videoLink,
            'featured' => $featured,
            'soon_instock' => $exited,
            'visible' => $published,
        );
        if ($editId != "0") {
            Product::where('id', $editId)
                ->update($values);
            $product = Product::find($editId);
        } else {
            $product = Product::create($values);
        };

        // images
        ImageUpload::where('product_id', $product->id)->delete();
        if ($images) {
            foreach ($images as $image) {
                ImageUpload::create([
                    'image_id' => $image['id'],
                    'product_id' => $product->id
                ]);
            }
        }

        if ($request->has('branchData')) {
            foreach ($branchDatas as $branchData) {
                $branchId = $branchData['branch']['id'];
                $branchSave = array(
                    'product_id' => $product->id,
                    'branch_id' => (int) $branchId,
                    'minquantity' => (int) $branchData['minquantity'],
                    'maxquantity' => (int) $branchData['maxquantity'],
                    'quantity' => (int) $branchData['quantity'],
                );
                $branchProductData = BranchProduct::where('product_id', $product->id)->where('branch_id', $branchId)->first();
                if (is_null($branchProductData)) {
                    BranchProduct::create($branchSave);
                } else {
                    BranchProduct::where('product_id', $product->id)
                        ->where('branch_id', $branchId)
                        ->update($branchSave);
                }
            };
        }

        $cacheRProducts = $request->input('cacheRProducts');
        if (!is_null($cacheRProducts)) {
            foreach ($cacheRProducts as &$value) {
                ProductController::storeRProduct2($product->id, $value['rproduct']['id']);
            }
        }

        $cacheVariant = $request->input('cacheVariant');
        if (!is_null($cacheVariant)) {
            foreach ($cacheVariant as &$value) {
                ProductController::productVariantsAdd2(
                    $product->id,
                    0,
                    $value['name'],
                    $value['desc'],
                    $value['cprice'],
                    $value['cdprice'],
                    $value['imageid'],
                    $value['branchData'],
                );
            }
        }

        $cacheProductBusiness = $request->input('cachePrBusCalculator', null);
        if (!is_null($cacheProductBusiness)) {
            foreach ($cacheProductBusiness as &$value) {
                ProductController::storeBusinessCalculator2(
                    null,
                    $value['name'],
                    $value['amount_of_items'],
                    $value['rental_yield'],
                    $value['property_appreciation'],
                    $product->id,
                    $value['from_amount'],
                    $value['to_amount'],
                    $value['discount_type'],
                    $value['discount_amount'],
                    $value['incentive'],
                    $value['point'],
                    $value['percent'],
                    $value['visible'],
                );
            }
        }

        return ProductController::edit($product);
    }

    /**
     * Display resources.
     */
    public function show(Request $request)
    {
        // $search = $request->input('search') ?: "";
        // $cat = $request->input('cat', "0");
        // $page = (int) $request->input('page') ?: 1;
        // $count = $request->input('count', 15);
        // $sortBy = $request->input('sortBy') ?: "id";
        // $sortAscDesc = $request->input('sortAscDesc') ?: "asc";
        // $sortPublished = $request->input('sortPublished', "1");
        // $sortUnPublished = $request->input('sortUnPublished', "1");
        // $sortFeatured = $request->input('sortFeatured', "0");

        $datas = Product::query();
        $datas = $this->run($request, $datas, [
            'name',
            'price',
            'discount_price',
            'desc',
            'video_link',
        ], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data'];
        foreach ($datas as $data) {
            $data->images;
            $data->image = count($data->images) ? $data->images[0] : '';
            $data->price2 = Util::makePrice($data->price);
            $data->discount_price2 = Util::makePrice($data->discount_price);
            if (is_null($data->discount_price)) {
                $data->discount_price2 = '-';
            }
            foreach ($data->branchProducts as $branchProduct) {
                $branchProduct->branch;
            }
        }
        $datas = $datas->toArray();
        $datas['data'] = array_map(function ($data) {
            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);


        // $data = ProductController::getProducts($search, $cat, $page, $count, $sortBy, $sortAscDesc, $sortPublished, $sortUnPublished, $sortFeatured);

        return response()->json(['error' => false, 'data' => $datas], 200);
    }

    /**
     * Display resources.
     */
    public function productAddressShow(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => ['required', 'numeric']
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $datas = BranchProduct::query()->where('product_id', $request->input('id'))->whereNull('product_variant_id');
        $datas = $this->run($request, $datas, [], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data'];
        foreach ($datas as $data) {
            $data->branch;
        }
        $datas = $datas->toArray();
        $datas['data'] = array_map(function ($data) {
            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);

        return response()->json(['error' => false, 'data' => $datas], 200);
    }

    /**
     * Display resources.
     */
    public static function getProducts(
        $search = "",
        $cat = 0,
        $page = 1,
        $count = 15,
        $sortBy = "id",
        $sortAscDesc = "asc",
        $sortPublished = "1",
        $sortUnPublished = "1",
        $sortFeatured = "0",
    ) {
        $offset = ($page - 1) * $count;
        $searchFeature = '';
        if ($sortFeatured == '1') {
            $searchFeature = "featured = 1";
        }

        $searchVisible = "";
        if ($sortPublished != '1' || $sortUnPublished != '1') {
            if ($sortPublished == '1')
                $searchVisible = "products.visible = 1";
            if ($sortUnPublished == '1')
                $searchVisible = "products.visible = 0";
        }
        if ($sortPublished == '0' && $sortUnPublished == '0')
            $searchVisible = "products.visible = 0";

        $query = Product::where('name', 'LIKE', '%' . $search . '%');
        if ($searchVisible) {
            $query = $query->whereRaw($searchVisible);
        }
        if ($searchFeature) {
            $query = $query->whereRaw($searchFeature);
        }
        $total = count($query->get());
        $datas = $query->orderBy($sortBy, $sortAscDesc)
            ->limit($count)
            ->offset($offset)
            ->get();

        foreach ($datas as $data) {
            $data->timeago = Util::timeago($data->updated_at);
            $data->updated_at2 = Util::formatDate($data->updated_at);
            $data->images;
            $data->image = count($data->images) ? $data->images[0] : '';
            // if (count($data->images) < 0) {
            //     $image = new Objecj();
            //     $data['images'] = new Object();
            //     'storage/noimage.jpg';
            // }
            $data->price2 = Util::makePrice($data->price);
            $data->discount_price2 = Util::makePrice($data->discount_price);
            if (is_null($data->discount_price)) {
                $data->discount_price2 = '-';
            }

            foreach ($data->branchProducts as $branchProduct) {
                $branchProduct->branch;
            }
        }

        $t = ceil($total / $count);

        return [
            'error' => 0,
            'data' => $datas,
            'page' => $page,
            'pages' => $t,
            'total' => $total
        ];
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        if (is_null($product)) {
            return response()->json(['error' => false, 'msg' => 'Product not found'], 404);
        }

        $product->timeago = Util::timeago($product->updated_at);
        $product->updated_at2 = Util::formatDate($product->updated_at);
        foreach ($product->images as $image) {
            $size = file_exists(public_path($image->image_path)) ? filesize($image->image_path) : "-";
            $image->size = $size;
        }
        $product->image_files = $product->images;
        $product->branches;
        $product->branchProducts;
        foreach ($product->branchProducts as $branchProduct) {
            $branchProduct->branch;
        }
        $product->variants;
        foreach ($product->variants as $variant) {
            $variant->timeago = Util::timeago($variant->updated_at);
            $variant->updated_at2 = Util::formatDate($variant->updated_at);
            $variant->branchProducts;
            foreach ($variant->branchProducts as $prVariantBranch) {
                $prVariantBranch->branch;
            }
        }
        $product->recommended;
        foreach ($product->recommended as $rProduct) {
            $rProduct->rproduct;
        }
        // // Product business calculator
        // $request = Request();
        // $product->business_calculator = ProductController::getProductBusinessCalculator2($request, $product->id);

        $product = Product::fillProduct($product);

        return response()->json([
            'error' => 0,
            'data' => $product
        ], 200);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function editBranchProduct(BranchProduct $branchProduct)
    {
        if (is_null($branchProduct)) {
            return response()->json(['error' => false, 'msg' => 'Address not found'], 404);
        }

        $branchProduct->timeago = Util::timeago($branchProduct->updated_at);
        $branchProduct->branch;
        $branchProduct->updated_at2 = Util::formatDate($branchProduct->updated_at);

        return response()->json([
            'error' => 0,
            'data' => $branchProduct,
        ], 200);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function updateBranchProduct(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => ['required'],
            'quantity' => 'required|numeric',
            'minquantity' => 'required|numeric',
            'maxquantity' => 'required|numeric',
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $editId = $request->input('id');

        if (!BranchProduct::find($editId)) {
            return response()->json(['error' => true, 'msg' => 'Address not found'], 400);
        }

        $quantity = $request->input('quantity');
        $minquantity = $request->input('minquantity');
        $maxquantity = $request->input('maxquantity');

        $values = array(
            'quantity' => $quantity,
            'minquantity' => $minquantity,
            'maxquantity' => $maxquantity,
        );

        $address = BranchProduct::where('id', $editId)->first();

        $address->update($values);

        return response()->json(['error' => false, 'msg' => 'Address successfully updated'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function delete(Request $request)
    {
        $id = $request->input('id');
        Product::where('id', $id)->delete();

        return response()->json(['error' => 0]);
    }

    public function deleteBranchProduct(BranchProduct $branchProduct)
    {
        if (is_null($branchProduct)) {
            return response()->json(['error' => 1, 'msg' => 'Branch product not found'], 404);
        }

        $status = $branchProduct->delete();
        return response()->json([
            'error' => 0,
            'msg' => 'Deleted Successfully',
            'status' => $status,
        ], 200);
    }

    // Product variant add
    public function storeProductVariant(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => 'numeric',
            'product_id' => 'required|numeric',
            'name' => 'required|string',
            'desc' => 'required|string',
            'price' => 'required|numeric',
            'dprice' => 'numeric',
            'imageid' => 'numeric',
            'branch_products' => 'required|array',
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        return ProductController::productVariantsAdd2(
            $request->input('product_id'),
            (int)$request->input('id'),
            $request->input('name'),
            $request->input('desc'),
            $request->input('price'),
            $request->input('dprice') ?: 0,
            $request->input('imageid'),
            $request->get('branch_products')
        );
    }

    public static function productVariantsAdd2($id, $variantId, $name, $desc, $price, $dprice, $imageid, $branchData)
    {
        $variant_id = 0;
        if ($id != 0) {
            $editVariant = ProductVariant::where('product_id', $id)->where('id', $variantId)->first();
            $data = array(
                'product_id' => $id,
                'name' => $name,
                'desc' => $desc,
                'imageid' => $imageid,
                'price' => $price,
                'dprice' => $dprice
            );
            if (is_null($editVariant)) {
                $variant_id = ProductVariant::create($data);
                // DB::getPdo()->lastInsertId();
                $variantId = $variant_id->id;
            } else {
                $editVariant->update($data);
            }

            if (count($branchData) != 0) {
                // Delete branches
                DB::table('branch_products')->where('product_id', $id)->where('product_variant_id', $variantId)->delete();
                foreach ($branchData as $updates) {
                    $branchSave = array(
                        'product_id' => $id,
                        'product_variant_id' => $variantId,
                        'branch_id' => $updates['branch']['id'],
                        'minquantity' => $updates['minquantity'],
                        'maxquantity' => $updates['maxquantity'],
                        'quantity' => $updates['quantity'],
                        'updated_at' => new \DateTime()
                    );
                    $branchProductData = DB::table('branch_products')
                        ->where('product_id', $id)
                        ->where('product_variant_id', $variantId)
                        ->where('branch_id', $updates['branch']['id'])
                        ->get();
                    if (count($branchProductData) == 0) {
                        $branchSave['created_at'] = new \DateTime();
                        DB::table('branch_products')->insert($branchSave);
                    } else {
                        DB::table('branch_products')
                            ->where('product_id', $id)
                            ->where('product_variant_id', $variantId)
                            ->where('branch_id', $updates['branch']['id'])
                            ->update($branchSave);
                    }
                };
            } else {
                DB::table('branch_products')
                    ->where('product_id', $id)
                    ->where('product_variant_id', $variantId)
                    ->delete();
            }
        } else {
            $image = DB::table("image_uploads")->where("id", $imageid)->first();
            if ($image != null) $image = $image->filename;
            else $image = "noimage.png";
            return response()->json([
                'error' => false,
                'data' => array(
                    'name' => $name,
                    'desc' => $desc,
                    'price' => Util::makePrice($price),
                    'cprice' => $price,
                    'dprice' => Util::makePrice($dprice),
                    'cdprice' => $dprice,
                    'timeago' => " ",
                    'image' => $image,
                    'imageid' => $imageid,
                    'branchData' => $branchData,
                ),
            ]);
        }

        return response()->json([
            'error' => false,
            'data' => ProductController::variantsGet($id),
            'id' => $variantId
        ]);
    }

    public static function variantsGet($id)
    {
        $data = ProductVariant::where('product_id', $id)->with('images')->get();
        foreach ($data as &$value) {
            $value->timeago = Util::timeago($value->updated_at);
            $value->updated_at2 = Util::formatDate($value->updated_at);
            $value->price2 = Util::makePrice($value->price);
            $value->discount_price2 = Util::makePrice($value->discount_price);
            $value->branchProducts;
            foreach ($value->branchProducts as $prVariantBranch) {
                $prVariantBranch->branch;
            }
        }
        return $data;
    }

    public function productVariantsInfo(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => 'required|numeric',
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $id = $request->input('id');
        $variant = ProductVariant::where('id', $id)->first();
        if (is_null($variant)) {
            return response()->json(['error' => true, 'msg' => 'Product variant not found'], 404);
        }
        $variant->price2 = Util::makePrice($variant->price);
        $variant->discount_price2 = Util::makePrice($variant->discount_price);
        $variant->timeago = Util::timeago($variant->updated_at);
        $variant->updated_at2 = Util::formatDate($variant->updated_at);
        $variant->branchProducts;
        foreach ($variant->branchProducts as $prVariantBranch) {
            $prVariantBranch->branch;
        }
        $variant->images;

        // $size = file_exists(public_path($data->image_path)) ? filesize($data->image_path) : "-";
        // $data->images_files = array(
        //     'id' => $data->imageid,
        //     'filename' => $data->image,
        //     'size' => $size
        // );
        return response()->json([
            'error' => false,
            'data' => $variant,
        ]);
    }

    public function deleteProductVariant(ProductVariant $productVariant)
    {
        if (is_null($productVariant)) {
            return response()->json(['error' => 1, 'msg' => 'Product variant not found'], 404);
        }

        $pid = $productVariant->product_id;
        $status = $productVariant->delete();
        return response()->json([
            'error' => false,
            'status' => $status,
            'msg' => 'Removed Successfully',
            'data' => ProductController::variantsGet($pid)
        ], 200);
    }

    public function deleteBranchProductVariant(Request $request)
    {
        $branchid = $request->input('branch_id');
        $productid = $request->input('product_id');
        $variantid = $request->input('variant_id');
        $status = DB::table('branch_products')->where('branch_id', $branchid)->where('product_id', $productid)->where('product_variant_id', $variantid)->delete();
        $editBranches = DB::table('branch_products')
            ->where('branch_products.product_id', $productid)
            ->where('branch_products.variant_id', $variantid)
            ->leftJoin('branches', 'branches.id', '=', 'branch_products.branch_id')
            ->select(
                'branch_products.minquantity',
                'branch_products.maxquantity',
                'branch_products.quantity',
                'branches.id',
                'branches.parent',
                'branches.name'
            )
            ->get();
        return response()->json([
            'error' => false,
            'status' => $status,
            'editBranches' => $editBranches
        ], 200);
    }

    /**
     * Sends product info to added to recommeneded products.
     */
    public function storeRProduct(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => 'numeric',
            'rp' => 'required|numeric'
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        return ProductController::storeRProduct2($request->input('id'), $request->input('rp'));
    }

    /**
     * Adds recommeneded products.
     */
    public static function storeRProduct2($id, $rp)
    {
        $validData = Validator::make(['rp' => $rp], [
            'rp' => 'required|numeric'
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        if ($id != 0) {
            $rproduct = RecommendedProduct::where('product_id', $id)->where('recommended_product_id', $rp)->first();
            if (!is_null($rproduct)) {
                return response()->json([
                    'error' => false,
                    'data' => ProductController::getRProduct($id),
                    'msg' => 'Already added'
                ]);
            }

            // created recommeneded product
            RecommendedProduct::create(array(
                'product_id' => $id,
                'recommended_product_id' => $rp
            ));
        } else {
            $data = Product::where('id', $rp)->with('images')->select('id', 'name', 'price')->first();
            $data = Product::fillProduct($data);
            return response()->json([
                'error' => false,
                'nowrite' => '1',
                'data' => $data
            ]);
        }

        return response()->json([
            'error' => false,
            'data' => ProductController::getRProduct($id),
            'msg' => 'Added Successfully'
        ]);
    }

    public static function getRProduct($id)
    {
        $data = RecommendedProduct::where('product_id', $id)->with(['product.images', 'rproduct.images'])->get();
        $data = RecommendedProduct::fillRProducts($data);
        return $data;
    }

    public function deleteRProduct(RecommendedProduct $rProduct)
    {
        if (is_null($rProduct)) {
            return response()->json(['error' => 1, 'msg' => 'Recommeneded product not found'], 404);
        }

        $pid = $rProduct->product_id;
        $status = $rProduct->delete();
        return response()->json([
            'error' => false,
            'status' => $status,
            'msg' => 'Removed Successfully',
            'data' => ProductController::getRProduct($pid)
        ], 200);
    }

    // Product business calculator
    public function storeBusinessCalculator(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => 'required|numeric',
            'name' => 'required|string|unique:product_business_calculators,name,' . $request->input('id'),
            'amount_of_items' => ['required', 'numeric'],
            'rental_yield' => ['required', 'numeric'],
            'property_appreciation' => ['required', 'numeric'],
            'product_id' => 'required|numeric',
            'from_amount' => ['required', 'numeric', 'gt:0'],
            'to_amount' => ['required', 'numeric', 'gt:0'],
            'discount_amount' => ['numeric', 'gt:0'],
            'discount_type' => ['numeric', 'gt:0'],
            'incentive' => ['string'],
            'point' => ['numeric', 'gt:0'],
            'percent' => ['required', 'boolean'],
            'visible' => ['required', 'boolean']
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        return ProductController::storeBusinessCalculator2(
            $request->input('id'),
            $request->input('name'),
            $request->input('amount_of_items'),
            $request->input('rental_yield'),
            $request->input('property_appreciation'),
            $request->input('product_id'),
            $request->input('from_amount'),
            $request->input('to_amount'),
            $request->input('discount_type', null),
            $request->input('discount_amount', null),
            $request->input('incentive', null),
            $request->input('point', null),
            $request->input('percent', 0),
            $request->input('visible', 0)
        );
    }

    public static function storeBusinessCalculator2(
        $id = null,
        $name,
        $amountOfItems,
        $rentalYield,
        $propertyAppreciation,
        $productId,
        $fromAmount,
        $toAmount,
        $disType,
        $disAmount,
        $incent,
        $point_,
        $percent_,
        $visible
    ) {
        $editId = $id;
        $prId = $productId;
        $discountType = $disType;
        $discountAmount = null;
        $incentive = null;
        $point = null;
        $percent = 0;

        // In discount amount
        if ($discountType == '1') {
            $discountAmount = $disAmount;
            $percent = $percent_;
            // if (is_null($discountAmount)) {
            //     return response()->json(['error' => true, 'msg' => 'Discount amount must not be empty'], 400);
            // }
        }

        // In incentive
        if ($discountType == '2') {
            $incentive = $incent;
            if (is_null($incentive)) {
                return response()->json(['error' => true, 'msg' => 'Incentive must not be empty'], 400);
            }
        }

        // In points
        if ($discountType == '3') {
            $point = $point_;
            if (is_null($point)) {
                return response()->json(['error' => true, 'msg' => 'Point must not be empty'], 400);
            }
        }

        $values = array(
            'name' => $name,
            'amount_of_items' => $amountOfItems,
            'rental_yield' => $rentalYield,
            'property_appreciation' => $propertyAppreciation,
            'product_id' => $prId,
            'from_amount' => $fromAmount,
            'to_amount' => $toAmount,
            'discount_amount' => $discountAmount,
            'incentive' => $incentive,
            'point' => $point,
            'percent' => $percent,
            'visible' => $visible,
        );

        $msg = 'Data created successfully';
        if (!is_null($editId) && $editId != "0") {
            ProductBusinessCalculator::where('id', $editId)
                ->update($values);
            $data = ProductBusinessCalculator::find($editId);
            $msg = 'Data updated successfully';
        } else {
            $data = ProductBusinessCalculator::create($values);
        };

        return response()->json(['error' => false, 'data' => ProductController::showBusinessCalculator2($data['id']), 'msg' => $msg], 200);
    }

    public function showBusinessCalculator(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'id' => 'required|numeric',
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $id = $request->input('id');
        return response()->json(['error' => false, 'data' => ProductController::showBusinessCalculator2($id)], 200);
    }

    public static function showBusinessCalculator2($id)
    {
        $data = ProductBusinessCalculator::where('id', $id)->first();
        $data->timeago = Util::timeago($data->updated_at);
        $data->updated_at2 = Util::formatDate($data->updated_at);
        // foreach ($data as &$value) {
        // }
        return $data;
    }

    public function getProductBusinessCalculator(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'prId' => 'required|numeric',
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $prId = $request->input('prId');

        return response()->json([
            'error' => false,
            'data' => ProductController::getProductBusinessCalculator2($request, $prId),
        ]);
    }

    public function getProductBusinessCalculator2($request, $prId)
    {
        $datas = ProductBusinessCalculator::query()->where('product_id', $prId);
        $datas = $this->run($request, $datas, [], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data'];
        $datas = $datas->toArray();
        $datas['data'] = array_map(function ($data) {
            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);

        return $datas;
    }

    public function deleteBusinessCalculator(ProductBusinessCalculator $prBusinessCalculator, Request $request)
    {
        if (is_null($prBusinessCalculator)) {
            return response()->json(['error' => 1, 'msg' => 'Product variant not found'], 404);
        }

        $prId = $prBusinessCalculator->product_id;
        $status = $prBusinessCalculator->delete();
        return response()->json([
            'error' => false,
            'status' => $status,
            'msg' => 'Removed Successfully',
            'data' => ProductController::getProductBusinessCalculator2($request, $prId)
        ], 200);
    }
}
