<?php

namespace App\Http\Controllers;

use App\Models\WalletLog;
use Illuminate\Http\Request;

class WalletLogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(WalletLog $walletLog)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(WalletLog $walletLog)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, WalletLog $walletLog)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(WalletLog $walletLog)
    {
        //
    }
}
