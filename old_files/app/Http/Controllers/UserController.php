<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Util;
use App\Traits\PaginateSortSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    use PaginateSortSearch;

    // TODO: add authentication
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('pages.admin.user.user');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'id' => ['string'],
            'name' => ['required', 'string', 'regex:/^\w+\s\w+$/', 'min:3'],
            'email' => ['email'],
            'phone' => ['string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'password' => ['min:8', 'confirmed'],
            'identification_img' => ['required', 'integer'],
            'role' => ['integer', 'required'],
            'active' => ['boolean'],
            'twofa' => ['boolean'],
        ]);

        if ($requestValidated->fails()) {
            return response()->json(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        $editId = $request->input('id');
        if ($request->has('email')) {
            $is_emailRegistered = User::where('email', $request->input('email'))->first();

            if (!is_null($is_emailRegistered) && $editId == "0") {
                return response()->json(['error' => true, 'msg' => 'The email has already been taken.'], 400);
            }
        }

        if ($request->has('phone')) {
            $is_phoneRegistered = User::where('phone', $request->input('phone'))->first();

            if (!is_null($is_phoneRegistered) && $editId == "0") {
                return response()->json(['error' => true, 'msg' => 'The phone number has already been taken.'], 400);
            }
        }

        $name = $request->input('name');
        $email = $request->input('email');
        $phone = $request->input('phone');
        $role = $request->input('role');

        $values = array(
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'role_id' => $role,
            'active' => $request->input('active', 1),
            '2fa' => $request->input('twofa', 0),
        );

        if ($editId != "0") {
            if ($request->has('password')) {
                $values['password'] = Hash::make($request->get('password'));
            }

            if ($request->has('identification_img') && $request->get('identification_img') > 0) {
                $values['identification_image_id'] = $request->get('identification_img');
            }

            $user = User::findOrFail($editId);
            if (is_null($user)) {
                return response()->json(['error' => false, 'msg' => 'User not found'], 404);
            }

            User::where('id', $editId)
                ->update($values);
            $data = User::find($editId);
        } else {
            if (!$request->has('password')) {
                return response()->json(['error' => false, 'msg' => 'Password is required'], 400);
            }
            if (!$request->has('identification_img')) {
                return response()->json(['error' => false, 'msg' => 'Identification image is required'], 400);
            }

            $values['password'] = Hash::make($request->get('password'));
            $values['identification_image_id'] = $request->get('identification_img') == 0 ? null : $request->get('identification_img');

            $data = User::create($values);
        };

        return UserController::edit($data);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request)
    {
        $datas = User::query();
        $datas = $this->run($request, $datas, ['name', 'email', 'phone'], []);
        if ($datas['error'] == true) {
            return response()->json($datas, 400);
        }
        $datas = $datas['data'];
        foreach ($datas as $data) {
            $data->role;
            $data->image;
            if ($data->image) {
                $domainName = FacadesRequest::root();
                $data->image->image_path = $domainName . '/' . $data->image->image_path;
            }
        }
        $datas = $datas->toArray();
        $datas['data'] = array_map(function ($data) {
            if ($data['phone']) $data['phone'] = '+' . $data['phone'];
            else $data['phone'] = '-';

            $data['timeago'] = Util::timeago($data['updated_at']);
            $data['updated_at2'] = Util::formatDate($data['updated_at']);
            return $data;
        }, $datas['data']);

        return response()->json(['error' => false, 'data' => $datas], 200);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        if (is_null($user)) {
            return response()->json(['error' => false, 'msg' => 'User not found'], 404);
        }

        $user->timeago = Util::timeago($user->updated_at);
        $user->updated_at2 = Util::formatDate($user->updated_at);
        $user->image;
        $user->role;
        if ($user->image) {
            $domainName = FacadesRequest::root();
            $user->image->image_path = $domainName . '/' . $user->image->image_path;
        }

        return response()->json([
            'error' => false,
            'data' => $user
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function delete(User $user)
    {
        $user->delete();

        return response()->json(['error' => false]);
    }
}
