<?php

namespace App\Http\Controllers;

use App\Models\Image;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ImageController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function storeAdmin(Request $request)
    {
        // if (!Auth::check())
        //     return redirect()->route('/');

        $requestValidated = Validator::make($request->all(), [
            'file' => ['required', 'file', 'image', 'mimes:jpeg,jpg,png,gif,svg', 'max:2048'],
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => 1, 'msg' => $requestValidated->errors()], 400);
        }

        $file = $request->file('file');

        // upload or get image
        $uploaded = ImageController::setImage($file, 'admin');
        return response()->json(['error' => 0, 'data' => $uploaded['data']], $uploaded['status']);
    }

    public function storeCustomer(Request $request)
    {
        // if (!Auth::check())
        //     return redirect()->route('/');

        $requestValidated = Validator::make($request->all(), [
            'file' => ['required', 'file', 'image', 'mimes:jpeg,jpg,png,gif,svg', 'max:2048'],
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => 1, 'msg' => $requestValidated->errors()], 400);
        }

        $file = $request->file('file');

        // upload or get image
        $uploaded = ImageController::setImage($file, 'customer');
        return response()->json(['error' => 0, 'data' => $uploaded['data']], $uploaded['status']);
    }

    public static function setImage($file, $path)
    {
        $extenstion = $file->getClientOriginalExtension();
        $countExtenstion = strlen($extenstion);
        $fileName = substr($file->getClientOriginalName(), 0, 30);
        $countFileName = strlen(substr($file->getClientOriginalName(), 0, 30));
        if (substr($fileName, (-1 * $countExtenstion))) {
            $fileName = substr($file->getClientOriginalName(), 0, $countFileName - ($countExtenstion));
            if (substr($fileName, (-1) == '.')) {
                $fileName = substr($file->getClientOriginalName(), 0, $countFileName - ($countExtenstion + 1));
            }
        }
        $fileName = strtolower($fileName);
        $fileName = str_replace(" ", "_", $fileName);
        $fileName = $fileName . '_' . Carbon::now()->format('Y-m-d') . '.' . $extenstion;

        // path -> 'storage/' . $path . '/' . $fileName
        $data = Image::where('image_path', 'storage/' . $path . '/' . $fileName)->first();
        $status = is_null($data) ? 201 : 200;
        if (is_null($data)) {
            $imagePath = $file->storeAs($path, $fileName, 'public');
            $imagePath = 'storage/' . $imagePath;
            $data = new Image();
            $data->image_path = $imagePath;
            $data->save();
        } else {
            $imagePath = $file->storeAs($path, $fileName, 'public');
        }
        $data->makeHidden(['created_at', 'updated_at']);
        return [
            'data' => $data,
            'status' => $status
        ];
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Image $image)
    {
        // if (!Auth::check())
        //     return redirect()->route('/');

        if (is_null($image)) {
            return response()->json(['error' => 1, 'msg' => 'Image not found'], 404);
        }

        $path = public_path() . '/' . $image->image_path;
        if (file_exists($path)) {
            $image->delete();
            unlink($path);
            return response()->noContent();
        }

        return response()->json(['error' => 1, 'msg' => 'File not found'], 404);
    }
}
