<?php

namespace App\Http\Controllers;

use App\Models\OtpSession;
use App\Models\Referral;
use App\Models\User;
use App\Models\Wallet;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    public function index()
    {
        if (Auth::check()) {
            return redirect()->route('home');
        }

        $page = "login";
        $breadcrumb = 'Login';

        return view('pages.customer.auth.login', [
            'title' => $breadcrumb,
            'page' => $page
        ]);
    }

    public function Login2FAView()
    {
        if (Auth::check()) {
            return redirect()->route('home');
        }

        $session = Session::get('otp_session');
        if (!$session) {
            return redirect()->route('login');
        }

        $page = "2FA Login";
        $breadcrumb = '2FA Login';

        return view('pages.customer.auth.login-2FA', [
            'title' => $breadcrumb,
            'page' => $page
        ]);
    }

    public function account()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $page = "account";
        $breadcrumb = 'My Account';

        return view('pages.customer.account.account', [
            'title' => $breadcrumb,
            'page' => $page
        ]);
    }

    public function profile()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $page = "profile";
        $breadcrumb = 'Profile';

        return view('pages.customer.profile.profile', [
            'title' => $breadcrumb,
            'page' => $page
        ]);
    }

    public function register()
    {
        return view('pages.customer.auth.signup', [
            'title' => 'Register'
        ]);
    }

    public function sendOtpView()
    {
        $session = Session::get('user_set');
        if (!$session || $session == 0) {
            return redirect()->route('register');
        }

        return view('pages.customer.auth.send-otp', [
            'title' => 'Choose OTP channel'
        ]);
    }

    public function verifyOtpView()
    {
        $session = Session::get('otp_session');
        if (!$session || $session == 0) {
            return redirect()->route('sendOtp');
        }

        return view('pages.customer.auth.verify-otp', [
            'title' => 'OTP Verification'
        ]);
    }

    public function forgotPasswordView()
    {
        return view('pages.customer.auth.forgot-password', [
            'title' => 'Forgot Password'
        ]);
    }

    public function resetPasswordView()
    {
        $session = Session::get('forgot_otp_session');
        if (!$session) {
            return redirect()->route('forgotPassword');
        }

        return view('pages.customer.auth.reset-password', [
            'title' => 'Reset Password'
        ]);
    }

    /**
     * Display a listing of the resource.
     */
    public function customerSignup(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'first_name' => ['required', 'string', 'min:3'],
            'last_name' => ['required', 'string', 'min:3'],
            'email' => ['required', 'email', 'unique:users'],
            'phone' => ['required', 'string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'password' => ['required', 'min:8', 'confirmed'],
            'identification_img' => ['required', 'integer'],
        ]);

        if ($requestValidated->fails()) {
            return response()->json(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }


        // TODO: check identification img is uploaded
        $name = $request->get('first_name') . ' ' . $request->get('last_name');
        $email = $request->get('email');
        $phone = '251' . substr($request->get('phone'), -9);
        $password = $request->get('password');
        $identification_img = $request->get('identification_img');
        $purchased_product = false;

        // Check phone duplicate
        $is_phoneRegistered = User::where('phone', $phone)->first();
        if (!is_null($is_phoneRegistered)) {
            return response()->json(['error' => true, 'msg' => 'Phone number has already been registered'], 409);
        }

        // Check email duplicate
        $is_emailRegistered = User::where('email', $email)->first();
        if (!is_null($is_emailRegistered)) {
            return response()->json(['error' => true, 'msg' => 'Email has already been registered'], 409);
        }

        // Data
        $validatedData = array(
            'name' => $name,
            'role_id' => 2,  // 'customer'
            'email' => $email,
            'phone' => $phone,
            'active' => 1,
            '2fa' => 1,
            'phone_verified_at' => Carbon::now(),
            'password' => Hash::make($password),
            'identification_image_id' => $identification_img,
            'has_purchased_product' => $purchased_product,
            'profession' => $request->get('profession', ''),
        );

        $user = User::create($validatedData);
        Wallet::create([
            'user_id' => $user->id,
            'balance' => 0,
            'status' => 'Active'
        ]);

        // Referred user
        if ($request->has('referalCode')) {
            $referalCode = $request->get('referalCode');
            $referredUser = User::where('referral_link', $referalCode)->first();
            if (!is_null($referredUser)) {
                $newData = array(
                    'referrer_user_id' => $referredUser->id,
                    'referred_user_id' => $user->id,
                    'referral_status' => 'Complete',
                );
                Referral::create($newData);
            }
        }

        if ($user) {
            return AuthController::customerSignupLogin($request);
        }

        return response()->json(['error' => true, 'msg' => 'Something went wrong'], 201);
    }

    public function checkUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => ['required', 'string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'email' => ['required', 'email', 'unique:users'],
            'first_name' => ['required', 'string', 'min:3'],
            'last_name' => ['required', 'string', 'min:3'],
            // 'name' => ['required', 'string', 'regex:/^\w+\s\w+$/', 'min:3'],
            'password' => ['required', 'min:8', 'confirmed'],
            'identification_img' => ['required', 'integer'],
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $email = $request->get('email');
        $phone = '251' . substr($request->get('phone'), -9);
        // Check phone duplicate
        $is_phoneRegistered = User::where('phone', $phone)->first();
        if (!is_null($is_phoneRegistered)) {
            return response()->json(['error' => true, 'msg' => 'Phone number has already been registered'], 409);
        }

        // Check email duplicate
        $is_emailRegistered = User::where('email', $email)->first();
        if (!is_null($is_emailRegistered)) {
            return response()->json(['error' => true, 'msg' => 'Email has already been registered'], 409);
        }

        Session::put('user_set', 1);

        return response()->json(["error" => false], 200);
    }

    public function sendOtp(Request $request, $fromLogin = false)
    {
        $validator = Validator::make($request->all(), [
            'phone' => ['sometimes', 'string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            // 'unique:users'
            'email' => ['sometimes', 'email'],
            'otp_channel' => ['required'],
            // 'required_without:phone,email',
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $email = $request->get('email');
        $phone = '251' . substr($request->get('phone'), -9);

        $subject = '2FA Authentication';
        $view = 'emails.2fa-otp';

        if (!$fromLogin) {
            // Check phone duplicate
            $is_phoneRegistered = User::where('phone', $phone)->first();
            if (!is_null($is_phoneRegistered)) {
                return response()->json(['error' => true, 'msg' => 'The Phone number has already been taken'], 409);
            }

            // Check email duplicate
            $is_emailRegistered = User::where('email', $email)->first();
            if (!is_null($is_emailRegistered)) {
                return response()->json(['error' => true, 'msg' => 'The Email has already been taken'], 409);
            }

            $subject = 'Account Verification';
            $view = 'emails.otp-verify';
        }

        $uuid = OtpSession::sendOTP($request->get('otp_channel'), $phone, $request->email, $subject, $view);

        if ($uuid['error'] == true) {
            return response()->json(["error" => true, "msg" => $uuid['msg']], 400);
        }

        if ($uuid['error'] == false && is_null($uuid['data'])) {
            return response()->json(["error" => true, "msg" => $uuid['msg']], 400);
        }

        Session::put('otp_session', $uuid['data']);
        return response()->json(["error" => false, "session" => $uuid['data'], "msg" => "OTP successfully sent"], 200);
    }

    public function verifyOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session' => 'required|string|max:255',
            'otp' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $session = Session::get('otp_session');
        if ($request->has('session')) {
            $session = $request->get("session");
        }

        $otpSession = OtpSession::where(['session' => $session, "otp" => $request->otp])->first();

        if (is_null($otpSession))
            return response()->json(["msg" => "Incorrect OTP.", "error" => true], 200);

        // $lapsed = abs($otpSession->expires_at->diffInMinutes(Carbon::now()));
        $lapsed = strtotime($otpSession->expires_at) - strtotime(Carbon::now());

        // if ($lapsed > env("OTP_EXPIRE_MIN"))
        if ($lapsed < 0)
            return response()->json([
                "msg" => "OTP expired",
                "data" => 0,
                "error" => true
            ], 200);

        $phone = $otpSession->phone;
        $otpSession->delete();

        return response()->json(["error" => false, "msg" => "OTP Verified"], 200);
    }

    public function otpLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session' => 'required|string|max:255',
            'email' => ['email'],
            'phone' => ['string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'password' => ['required'],
            'otp' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $session = Session::get('otp_session');
        if ($request->has('session')) {
            $session = $request->get("session");
        }

        $otpSession = OtpSession::where(['session' => $session, "otp" => $request->otp])->first();

        if (is_null($otpSession))
            return response()->json(["msg" => "Incorrect OTP.", "error" => true], 400);

        // $lapsed = abs($otpSession->expires_at->diffInMinutes(Carbon::now()));
        $lapsed = strtotime($otpSession->expires_at) - strtotime(Carbon::now());

        // if ($lapsed > env("OTP_EXPIRE_MIN"))
        if ($lapsed < 0)
            return response()->json(["msg" => "OTP expired.", "error" => true], 400);

        $phone = $otpSession->phone;
        $otpSession->delete();

        return AuthController::customerSignupLogin($request);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function sellerSignup(Request $request)
    {
        // if (!Auth::check()) {
        //     return redirect()->route('home');
        // }

        $requestValidated = Validator::make($request->all(), [
            'trade_license' => 'required',
            'identif' => 'required',
            'requested_product' => 'required',
            'requested_product_desc' => 'required',
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        // TODO: check images is uploaded
        $trade_license_image_id = $request->get('trade_license');
        $tin_number = $request->get('tinnum');
        $id = $request->get('identif');
        $company_profile = $request->get('company_profile');
        $requested_product_image_id = $request->get('requested_product');
        $requested_product_desc = $request->get('requested_product_desc');

        $validatedData = array();
        $validatedData['identification_image_id'] = $id;
        $validatedData['trade_license_image_id'] = $trade_license_image_id;
        $validatedData['requested_product_image_id'] = $requested_product_image_id;
        $validatedData['requested_product_desc'] = $requested_product_desc;

        // User
        $user = Auth::user() ? Auth::user()->id : 2;
        $user = User::where('id', $user)->first();
        if ($user) {
            $user->identification_image_id = $id;
            $user->trade_license_image_id = $trade_license_image_id;
            $user->requested_product_image_id = $requested_product_image_id;
            $user->requested_product_desc = $requested_product_desc;
            $user->save();
            // $user->update($validatedData);

            return response()->json(['error' => false, 'msg' => 'Sent successfully'], 201);
        }

        return response()->json(['error' => true, 'msg' => 'Something went wrong'], 201);
    }

    /**
     * 2FA check
     */
    public function customerLogin(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'email' => ['email'],
            'phone' => ['string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'password' => ['required'],
            'remember_me' => ['boolean']
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        $remember_me = $request->get('remember_me') ? true : false;

        // Retrieve the intended URL from the session
        $intendedUrl = Session::get('intended_url');

        // Login with email and password
        if ($request->has('email')) {
            // Get user
            $user = User::where('email', $request->get('email'))->where('2fa', 1)->first();

            // Validate if user is present
            if (!is_null($user) && Hash::check($request->get('password'), $user->password)) {
                // adding otp channel
                $request->request->add(['otp_channel' => 'email']);
                // redirect to otp verification page
                return AuthController::sendOtp($request, true);
            }
            return AuthController::customerSignupLogin($request);
        }

        // Login with phone and password
        if ($request->has('phone')) {
            $validatedPhone = '251' . substr($request->get('phone'), -9);

            // Get user
            $user = User::where('phone', $validatedPhone)->where('2fa', 1)->first();

            // Validate if user is present
            if (!is_null($user) && Hash::check($request->get('password'), $user->password)) {
                // adding otp channel
                $request->request->add(['phone' => $validatedPhone]);
                $request->request->add(['otp_channel' => 'phone']);
                // redirect to otp verification page
                return AuthController::sendOtp($request, true);
            }

            return AuthController::customerSignupLogin($request);
        }

        return response()->json(['error' => true, 'msg' => 'These credentials do not match'], 401);
    }

    public function customerSignupLogin(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'email' => ['email'],
            'phone' => ['string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'password' => ['required'],
            'remember_me' => ['boolean']
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        $remember_me = $request->get('remember_me') ? true : false;

        // Retrieve the intended URL from the session
        $intendedUrl = Session::get('intended_url');

        // Login with email and password
        if ($request->has('email')) {
            if (auth()->attempt(array('email' => $request->get('email'), 'password' => $request->get('password')), $remember_me)) {
                if ($request->ajax()) {
                    $response = ['error' => false, 'msg' => 'Logged in successfully.'];
                    if ($intendedUrl) {
                        $response['redirect_url'] = $intendedUrl;
                        Session::remove('intended_url');
                    }
                    return response()->json($response, 200);
                }
                // Redirect the user to the intended URL if it exists, or a default page if not
                return redirect()->intended($intendedUrl ?? route('home'));
            }
        }

        // Login with phone and password
        if ($request->has('phone')) {
            $validatedPhone = '251' . substr($request->get('phone'), -9);
            if (auth()->attempt(array('phone' => $validatedPhone, 'password' => $request->get('password')), $remember_me)) {
                if ($request->ajax()) {
                    $response = ['error' => false, 'msg' => 'Logged in successfully.'];
                    if ($intendedUrl) {
                        $response['redirect_url'] = $intendedUrl;
                        Session::remove('intended_url');
                    }
                    return response()->json($response, 200);
                }
                // Redirect the user to the intended URL if it exists, or a default page if not
                return redirect()->intended($intendedUrl ?? route('home'));
            }
        }

        return response()->json(['error' => true, 'msg' => 'These credentials do not match'], 401);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function adminLogin(Request $request)
    {
        $requestValidated = Validator::make($request->all(), [
            'email' => ['email'],
            'phone' => ['string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'password' => ['required', 'min:8'],
            'remember_me' => ['boolean']
        ]);

        if ($requestValidated->fails()) {
            return response(['error' => true, 'msg' => $requestValidated->errors()], 400);
        }

        $remember_me = $request->get('remember_me') ? true : false;

        // Retrieve the intended URL from the session
        $intendedUrl = Session::get('intended_url');

        // TODO: redirect to landing page
        // Login with email and password
        if ($request->has('email')) {
            $isCustomer = User::where('email', $request->get('email'))->where('role_id', '=', 2)->first();
            if ($isCustomer) {
                return response()->json([
                    'error' => true,
                    'redirect_url' => route('home'),
                ]);
            }

            if (auth()->attempt(array('email' => $request->get('email'), 'password' => $request->get('password')), $remember_me)) {
                // return redirect()->route('dashboard');
                $response = ['error' => false, 'msg' => 'Logged in successfully.'];
                if ($intendedUrl) {
                    $response['redirect_url'] = $intendedUrl;
                    Session::remove('intended_url');
                }
                return response()->json($response, 200);
            }
        }

        // Login with phone and password
        if ($request->has('phone')) {
            $validatedPhone = '251' . substr($request->get('phone'), -9);
            $isCustomer = User::where('phone', $validatedPhone)->where('role_id', '=', 2)->first();
            if ($isCustomer) {
                return response()->json([
                    'error' => true,
                    'redirect_url' => route('home'),
                ]);
            }

            if (auth()->attempt(array('phone' => $validatedPhone, 'password' => $request->get('password')), $remember_me)) {
                // return redirect()->route('dashboard');
                $response = ['error' => false, 'msg' => 'Logged in successfully.'];
                if ($intendedUrl) {
                    $response['redirect_url'] = $intendedUrl;
                    Session::remove('intended_url');
                }
                return response()->json($response, 200);
            }
        }

        return response()->json(['error' => true, 'msg' => 'These credentials do not match'], 401);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = Auth::user() ? Auth::user()->id : 2;
        $user = User::findOrFail($user);
        return response()->json(['error' => false, 'data' => $user], 200);
    }

    public function sendForgotOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => ['sometimes', 'string', 'regex:/(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/', 'min:9', 'max:12'],
            'email' => ['sometimes', 'email'],
            'otp_channel' => ['required']
        ]);

        if ($validator->fails()) {
            return response()->json(["error" => true, "msg" => $validator->errors()], 400);
        }

        $session = Session::get('forgot_otp_session');
        $otpSession = OtpSession::where(['session' => $session])->first();

        if (is_null($otpSession))
            return response()->json(["msg" => "Incorrect OTP.", "error" => true], 400);

        $lapsed = strtotime($otpSession->expires_at) - strtotime(Carbon::now());
        // $lapsed = abs($otpSession->expires_at->diffInMinutes(Carbon::now()));
        if ($lapsed > 0)
            return response()->json(["error" => false, "msg" => "OTP already sent"], 200);

        // if ($lapsed > env("OTP_EXPIRE_MIN"))
        if ($lapsed < 0)
            return response()->json(["msg" => "OTP expired", "error" => true], 400);

        $email = $request->get('email');
        $phone = '251' . substr($request->get('phone'), -9);
        if ($request->get('otp_channel') == 'phone') {
            // Check phone duplicate
            $is_phoneRegistered = User::where('phone', $phone)->where('active', 1)->first();
            if (is_null($is_phoneRegistered)) {
                return response()->json(['error' => true, 'msg' => 'User not found'], 404);
            }
        } else if ($request->get('otp_channel') == 'email') {
            // Check email duplicate
            $is_emailRegistered = User::where('email', $email)->where('active', 1)->first();
            if (is_null($is_emailRegistered)) {
                return response()->json(['error' => true, 'msg' => 'User not found'], 404);
            }
        } else {
            return response()->json(['error' => true, 'msg' => 'User not found'], 404);
        }

        $subject = 'Reset Password';
        $view = 'emails.reset-pwd';
        // $uuid = OtpSession::resetPwd($request->get('otp_channel'), $phone, $request->email);
        $uuid = OtpSession::sendOTP($request->get('otp_channel'), $phone, $request->email, $subject, $view);

        if ($uuid['error'] == true) {
            return response()->json(["error" => true, "msg" => $uuid['msg']], 400);
        }

        if ($uuid['error'] == false && is_null($uuid['data'])) {
            return response()->json(["error" => true, "msg" => $uuid['msg']], 400);
        }

        Session::put('forgot_otp_session', $uuid['data']);
        return response()->json(["error" => false, "session" => $uuid['data'], "msg" => "OTP sent successfully"], 200);
    }

    /**
     * Display a listing of the resource.
     */
    public function sendResetPwd(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session' => 'required|string|max:255',
            'otp' => 'required|integer',
            'password' => ['required', 'min:8', 'confirmed'],
        ]);

        if ($validator->fails()) {
            return response()->json(["msg" => $validator->errors(), "error" => true], 400);
        }

        $session = Session::get('forgot_otp_session');
        if ($request->has('session')) {
            $session = $request->get("session");
        }

        $otpSession = OtpSession::where(['session' => $session, "otp" => $request->otp])->first();

        if (is_null($otpSession))
            return response()->json(["msg" => "Incorrect OTP", "error" => true], 400);

        $lapsed = strtotime($otpSession->expires_at) - strtotime(Carbon::now());
        // $lapsed = abs($otpSession->expires_at->diffInMinutes(Carbon::now()));
        if ($lapsed < 0)
            return response()->json(["msg" => "OTP expired", "error" => true], 400);

        $phone_email = $otpSession->phone;
        $otpSession->delete();

        // if ($lapsed > env("OTP_EXPIRE_MIN"))
        $password = $request->get('password');

        // Check phone duplicate
        $user = User::where('phone', $phone_email)
            ->orWhere('email', $phone_email)
            ->update(
                array(
                    'password' => Hash::make($password),
                )
            );

        if ($user) {
            return response()->json(['error' => false, 'msg' => 'Password Updated Successfully!'], 200);
        }

        return response()->json(['error' => true, 'msg' => 'Something went wrong'], 201);
    }

    public function logout(Request $request)
    {
        Auth::logout();
        return redirect()->route('home');
    }
}
