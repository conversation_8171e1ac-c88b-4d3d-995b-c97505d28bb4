<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Image;
use App\Models\Setting;
use App\Models\Util;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class CompanyController extends Controller
{
    // TODO: add authentication
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $company = Company::with('image')->first();
        // Update image path
        if (!is_null($company)) Image::updateImageFile($company->image);
        $legals = Setting::legalsInfo();
        $links = Setting::socialMediasInfo();
        return view('pages.admin.setting.setting', [
            'data' => [
                'company' => $company,
                ...$legals,
                ...$links
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validData = Validator::make($request->all(), [
            'name' => ['required', 'string'],
            'phone' => ['required', 'string'],
            'mobile_phone' => ['required', 'string'],
            'address' => ['string'],
            'lat' => ['numeric'],
            'lng' => ['numeric'],
            'min_purchase_amount' => ['decimal:2'],
            'min_discount_amount' => ['decimal:2'],
            'discount_amount' => ['decimal:2'],
            'tax' => ['numeric'],
            'point_amount' => ['decimal:2'],
            'fee' => ['decimal:2'],
            'percent' => ['boolean'],
            'per_km' => ['boolean'],
            'flat_rate' => ['boolean'],
            'image' => ['integer'],
            'about' => ['string', 'min:10'],
            'terms' => ['string', 'min:10'],
            'privacy' => ['string', 'min:10'],
            'delivery' => ['string', 'min:10'],
            'refund' => ['string', 'min:10'],
            'fb_link' => ['url', 'min:5'],
            'fb_visible' => ['boolean'],
            'yt_link' => ['url', 'min:5'],
            'yt_visible' => ['boolean'],
            'insta_link' => ['url', 'min:5'],
            'insta_visible' => ['boolean'],
            'tiktok_link' => ['url', 'min:5'],
            'tiktok_visible' => ['boolean'],
            'telegram_link' => ['url', 'min:5'],
            'telegram_visible' => ['boolean'],
        ]);

        if ($validData->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        // Time validation
        $timeValidator1 = Validator::make($request->only(
            'openTimeMonday',
            'closeTimeMonday',
            'openTimeTuesday',
            'closeTimeTuesday',
            'openTimeWednesday',
            'closeTimeWednesday',
            'openTimeThursday',
            'closeTimeThursday',
            'openTimeFriday',
            'closeTimeFriday',
            'openTimeSaturday',
            'closeTimeSaturday',
            'openTimeSunday',
            'closeTimeSunday'
        ), [
            'openTimeMonday' => ['date_format:H:i'],
            'closeTimeMonday' => ['date_format:H:i'],
            'openTimeTuesday' => ['date_format:H:i'],
            'closeTimeTuesday' => ['date_format:H:i'],
            'openTimeWednesday' => ['date_format:H:i'],
            'closeTimeWednesday' => ['date_format:H:i'],
            'openTimeThursday' => ['date_format:H:i'],
            'closeTimeThursday' => ['date_format:H:i'],
            'openTimeFriday' => ['date_format:H:i'],
            'closeTimeFriday' => ['date_format:H:i'],
            'openTimeSaturday' => ['date_format:H:i'],
            'closeTimeSaturday' => ['date_format:H:i'],
            'openTimeSunday' => ['date_format:H:i'],
            'closeTimeSunday' => ['date_format:H:i']
        ]);

        $timeValidator2 = Validator::make($request->only(
            'openTimeMonday',
            'closeTimeMonday',
            'openTimeTuesday',
            'closeTimeTuesday',
            'openTimeWednesday',
            'closeTimeWednesday',
            'openTimeThursday',
            'closeTimeThursday',
            'openTimeFriday',
            'closeTimeFriday',
            'openTimeSaturday',
            'closeTimeSaturday',
            'openTimeSunday',
            'closeTimeSunday'
        ), [
            'openTimeMonday' => ['date_format:H:i:s'],
            'closeTimeMonday' => ['date_format:H:i:s'],
            'openTimeTuesday' => ['date_format:H:i:s'],
            'closeTimeTuesday' => ['date_format:H:i:s'],
            'openTimeWednesday' => ['date_format:H:i:s'],
            'closeTimeWednesday' => ['date_format:H:i:s'],
            'openTimeThursday' => ['date_format:H:i:s'],
            'closeTimeThursday' => ['date_format:H:i:s'],
            'openTimeFriday' => ['date_format:H:i:s'],
            'closeTimeFriday' => ['date_format:H:i:s'],
            'openTimeSaturday' => ['date_format:H:i:s'],
            'closeTimeSaturday' => ['date_format:H:i:s'],
            'openTimeSunday' => ['date_format:H:i:s'],
            'closeTimeSunday' => ['date_format:H:i:s']
        ]);

        if ($timeValidator1->fails() && $timeValidator2->fails()) {
            return response()->json(['error' => true, 'msg' => $validData->errors()], 400);
        }

        $name = $request->input('name');
        $phone = $request->input('phone', "");
        $mobile_phone = $request->input('mobile_phone', "");
        $address = $request->input('address', "");
        $lat = $request->input('lat', "");
        $lng = $request->input('lng', "");
        $min_purchase_amount = $request->input('min_purchase_amount', 0);
        $min_discount_amount = $request->input('min_discount_amount', 0);
        $discount_amount = $request->input('discount_amount', 0);
        $tax = $request->input('tax', 0);
        $fee = $request->input('fee', 0);
        $percent = $request->input('percent', 0);
        $per_km = $request->input('per_km', 0);
        $flat_rate = $request->input('flat_rate', 0);
        $image = $request->input('image');

        // Check image
        if (is_null(Image::find($image))) {
            return response()->json(['error' => true, 'msg' => 'Please upload logo'], 400);
        }

        $values = array(
            'name' => $name,
            'phone' => $phone,
            'mobile_phone' => $mobile_phone,
            'address' => $address,
            'lat' => $lat,
            'lng' => $lng,
            'min_purchase_amount' => $min_purchase_amount,
            'min_discount_amount' => $min_discount_amount,
            'discount_amount' => $discount_amount,
            'tax' => $tax,
            'fee' => $fee,
            'percent' => $percent,
            'per_km' => $per_km,
            'flat_rate' => $flat_rate,
            'image_id' => $image,
        );

        if ($request->has('openTimeMonday')) {
            $values['openTimeMonday'] = $request->get('openTimeMonday');
        }
        if ($request->has('closeTimeMonday')) {
            $values['closeTimeMonday'] = $request->get('closeTimeMonday');
        }

        if ($request->has('openTimeTuesday')) {
            $values['openTimeTuesday'] = $request->get('openTimeTuesday');
        }
        if ($request->has('closeTimeTuesday')) {
            $values['closeTimeTuesday'] = $request->get('closeTimeTuesday');
        }

        if ($request->has('openTimeWednesday')) {
            $values['openTimeWednesday'] = $request->get('openTimeWednesday');
        }
        if ($request->has('closeTimeWednesday')) {
            $values['closeTimeWednesday'] = $request->get('closeTimeWednesday');
        }

        if ($request->has('openTimeThursday')) {
            $values['openTimeThursday'] = $request->get('openTimeThursday');
        }
        if ($request->has('closeTimeThursday')) {
            $values['closeTimeThursday'] = $request->get('closeTimeThursday');
        }

        if ($request->has('openTimeFriday')) {
            $values['openTimeFriday'] = $request->get('openTimeFriday');
        }
        if ($request->has('closeTimeFriday')) {
            $values['closeTimeFriday'] = $request->get('closeTimeFriday');
        }

        if ($request->has('openTimeSaturday')) {
            $values['openTimeSaturday'] = $request->get('openTimeSaturday');
        }
        if ($request->has('closeTimeSaturday')) {
            $values['closeTimeSaturday'] = $request->get('closeTimeSaturday');
        }

        if ($request->has('openTimeSunday')) {
            $values['openTimeSunday'] = $request->get('openTimeSunday');
        }
        if ($request->has('closeTimeSunday')) {
            $values['closeTimeSunday'] = $request->get('closeTimeSunday');
        }

        Company::where('id', 1)->update($values);
        $data = Company::first();

        // 1 Point amount in ETB
        if ($request->has('point_amount')) {
            $pointAmount = array();
            $pointAmount["value"]  = $request->input('point_amount');
            if (Setting::where('param', 'point_amount')->exists()) {
                if (Setting::where('param', 'point_amount')->first()->value != $pointAmount["value"]) {
                    Setting::where('param', 'point_amount')->update($pointAmount);
                }
            } else {
                $pointAmount["param"]  = "point_amount";
                Setting::create($pointAmount);
            }
        }

        // about_us
        if ($request->has('about')) {
            $aboutUs = array();
            $aboutUs["value"]  = $request->input('about');
            if (Setting::where('param', 'about')->exists()) {
                if (Setting::where('param', 'about')->first()->value != $aboutUs["value"]) {
                    Setting::where('param', 'about')->update($aboutUs);
                }
            } else {
                $aboutUs["param"]  = "about";
                Setting::create($aboutUs);
            }

            $aboutUs = array();
            $aboutUs["value"]  = $request->input('about_visible');
            if (Setting::where('param', 'about_visible')->exists()) {
                if (Setting::where('param', 'about_visible')->first()->value != $aboutUs["value"]) {
                    Setting::where('param', 'about_visible')->update($aboutUs);
                }
            } else {
                $aboutUs["param"]  = "about_visible";
                Setting::create($aboutUs);
            }
        }

        // terms_condition
        if ($request->has('terms')) {
            $tc = array();
            $tc["value"]  = $request->input('terms');
            if (Setting::where('param', 'terms')->exists()) {
                if (Setting::where('param', 'terms')->first()->value != $tc["value"]) {
                    Setting::where('param', 'terms')->update($tc);
                }
            } else {
                $tc["param"]  = "terms";
                Setting::create($tc);
            }

            $tc = array();
            $tc["value"]  = $request->input('terms_visible');
            if (Setting::where('param', 'terms_visible')->exists()) {
                if (Setting::where('param', 'terms_visible')->first()->value != $tc["value"]) {
                    Setting::where('param', 'terms_visible')->update($tc);
                }
            } else {
                $tc["param"]  = "terms_visible";
                Setting::create($tc);
            }
        }

        // privacy_policy
        if ($request->has('privacy')) {
            $pp = array();
            $pp["value"]  = $request->input('privacy');
            if (Setting::where('param', 'privacy')->exists()) {
                if (Setting::where('param', 'privacy')->first()->value != $pp["value"]) {
                    Setting::where('param', 'privacy')->update($pp);
                }
            } else {
                $pp["param"]  = "privacy";
                Setting::create($pp);
            }

            $pp = array();
            $pp["value"]  = $request->input('privacy_visible');
            if (Setting::where('param', 'privacy_visible')->exists()) {
                if (Setting::where('param', 'privacy_visible')->first()->value != $pp["value"]) {
                    Setting::where('param', 'privacy_visible')->update($pp);
                }
            } else {
                $pp["param"]  = "privacy_visible";
                Setting::create($pp);
            }
        }
        // delivery_info
        if ($request->has('delivery')) {
            $dinfo = array();
            $dinfo["value"]  = $request->input('delivery');
            if (Setting::where('param', 'delivery')->exists()) {
                if (Setting::where('param', 'delivery')->first()->value != $dinfo["value"]) {
                    Setting::where('param', 'delivery')->update($dinfo);
                }
            } else {
                $dinfo["param"]  = "delivery";
                Setting::create($dinfo);
            }

            $dinfo = array();
            $dinfo["value"]  = $request->input('delivery_visible');
            if (Setting::where('param', 'delivery_visible')->exists()) {
                if (Setting::where('param', 'delivery_visible')->first()->value != $dinfo["value"]) {
                    Setting::where('param', 'delivery_visible')->update($dinfo);
                }
            } else {
                $dinfo["param"]  = "delivery_visible";
                Setting::create($dinfo);
            }
        }
        // refund_policy
        if ($request->has('refund')) {
            $rp = array();
            $rp["value"]  = $request->input('refund');
            if (Setting::where('param', 'refund')->exists()) {
                if (Setting::where('param', 'refund')->first()->value != $rp["value"]) {
                    Setting::where('param', 'refund')->update($rp);
                }
            } else {
                $rp["param"]  = "refund";
                Setting::create($rp);
            }

            $rp = array();
            $rp["value"]  = $request->input('refund_visible');
            if (Setting::where('param', 'refund_visible')->exists()) {
                if (Setting::where('param', 'refund_visible')->first()->value != $rp["value"]) {
                    Setting::where('param', 'refund_visible')->update($rp);
                }
            } else {
                $rp["param"]  = "refund_visible";
                Setting::create($rp);
            }
        }

        // Social media links
        // facebook
        if ($request->has('fb_link')) {
            $facebook = array();
            $facebook["value"]  = $request->input('fb_link');
            if (Setting::where('param', 'facebook_link')->exists()) {
                if (Setting::where('param', 'facebook_link')->first()->value != $facebook["value"]) {
                    Setting::where('param', 'facebook_link')->update($facebook);
                }
            } else {
                $facebook["param"]  = "facebook_link";
                Setting::create($facebook);
            }

            $facebook = array();
            $facebook["value"]  = $request->input('fb_visible');
            if (Setting::where('param', 'facebook_visible')->exists()) {
                if (Setting::where('param', 'facebook_visible')->first()->value != $facebook["value"]) {
                    Setting::where('param', 'facebook_visible')->update($facebook);
                }
            } else {
                $facebook["param"]  = "facebook_visible";
                Setting::create($facebook);
            }
        }

        // youtube
        if ($request->has('yt_link')) {
            $yt = array();
            $yt["value"]  = $request->input('yt_link');
            if (Setting::where('param', 'youtube_link')->exists()) {
                if (Setting::where('param', 'youtube_link')->first()->value != $yt["value"]) {
                    Setting::where('param', 'youtube_link')->update($yt);
                }
            } else {
                $yt["param"]  = "youtube_link";
                Setting::create($yt);
            }

            $yt = array();
            $yt["value"]  = $request->input('yt_visible');
            if (Setting::where('param', 'youtube_visible')->exists()) {
                if (Setting::where('param', 'youtube_visible')->first()->value != $yt["value"]) {
                    Setting::where('param', 'youtube_visible')->update($yt);
                }
            } else {
                $yt["param"]  = "youtube_visible";
                Setting::create($yt);
            }
        }

        // instagram
        if ($request->has('insta_link')) {
            $insta = array();
            $insta["value"]  = $request->input('insta_link');
            if (Setting::where('param', 'instagram_link')->exists()) {
                if (Setting::where('param', 'instagram_link')->first()->value != $insta["value"]) {
                    Setting::where('param', 'instagram_link')->update($insta);
                }
            } else {
                $insta["param"]  = "instagram_link";
                Setting::create($insta);
            }

            $insta = array();
            $insta["value"]  = $request->input('insta_visible');
            if (Setting::where('param', 'instagram_visible')->exists()) {
                if (Setting::where('param', 'instagram_visible')->first()->value != $insta["value"]) {
                    Setting::where('param', 'instagram_visible')->update($insta);
                }
            } else {
                $insta["param"]  = "instagram_visible";
                Setting::create($insta);
            }
        }

        // tiktok
        if ($request->has('tiktok_link')) {
            $tik = array();
            $tik["value"]  = $request->input('tiktok_link');
            if (Setting::where('param', 'tiktok_link')->exists()) {
                if (Setting::where('param', 'tiktok_link')->first()->value != $tik["value"]) {
                    Setting::where('param', 'tiktok_link')->update($tik);
                }
            } else {
                $tik["param"]  = "tiktok_link";
                Setting::create($tik);
            }

            $tik = array();
            $tik["value"]  = $request->input('tiktok_visible');
            if (Setting::where('param', 'tiktok_visible')->exists()) {
                if (Setting::where('param', 'tiktok_visible')->first()->value != $tik["value"]) {
                    Setting::where('param', 'tiktok_visible')->update($tik);
                }
            } else {
                $tik["param"]  = "tiktok_visible";
                Setting::create($tik);
            }
        }

        // telegram
        if ($request->has('telegram_link')) {
            $tele = array();
            $tele["value"]  = $request->input('telegram_link');
            if (Setting::where('param', 'telegram_link')->exists()) {
                if (Setting::where('param', 'telegram_link')->first()->value != $tele["value"]) {
                    Setting::where('param', 'telegram_link')->update($tele);
                }
            } else {
                $tele["param"]  = "telegram_link";
                Setting::create($tele);
            }

            $tele = array();
            $tele["value"]  = $request->input('telegram_visible');
            if (Setting::where('param', 'telegram_visible')->exists()) {
                if (Setting::where('param', 'telegram_visible')->first()->value != $tele["value"]) {
                    Setting::where('param', 'telegram_visible')->update($tele);
                }
            } else {
                $tele["param"]  = "telegram_visible";
                Setting::create($tele);
            }
        }

        return CompanyController::edit($data);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Company $company)
    {
        if (is_null($company)) {
            return response()->json(['error' => false, 'msg' => 'Company not found'], 404);
        }

        Image::updateImageFile($company->image);
        $legals = Setting::legalsInfo();
        $links = Setting::socialMediasInfo();
        return response()->json([
            'error' => false,
            'data' => [
                'company' => $company,
                ...$legals,
                ...$links
            ],
        ], 200);
    }
}
