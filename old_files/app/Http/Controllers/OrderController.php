<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\BranchProduct;
use App\Models\Company;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Payment;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Referral;
use App\Models\ReferralLog;
use App\Models\Setting;
use App\Models\User;
use App\Models\Util;
use App\Models\Wallet;
use App\Models\WalletLog;
use App\Models\Address;
use App\Services\Telebirr\TelebirrH5C2B\TelebirrH5C2BPay;
use Carbon\Carbon;
use DateTime;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request as Psr7Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Throwable;

class OrderController extends Controller
{
    private $webhook = "https://webhook.site/0a99fb0c-195b-4663-bff7-b2e86996ba1d";

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!Auth::check()) {
            // Store the intended URL in the session
            Session::put('checkout_intended_url', url()->current());
            return redirect()->route('login');
        }

        return view('pages.customer.cart.cart', [
            'title' => 'Cart'
        ]);
    }

    public function getCart(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['error' => true], 401);
        }

        $orderdetails = null;
        $fee = 0;
        $tax = 0;
        $perkm = 0;
        $percent = 0;
        $order = null;
        $companyName = "";
        $minAmount = 0;
        $flatrate = 0;
        $id = Auth::user()->id;
        // basket
        $order = Order::where('user_id', $id)
            ->where('is_complete', 0)
            ->first();
        if (!is_null($order)) {
            $orderdetails = OrderDetail::where('order_id', $order->id)->get();
            $rest = Company::first();
            $orderdetails = $orderdetails->toArray();

            foreach ($orderdetails as $key => &$value) {
                if (is_null($value['product_variant_id'])) {
                    $product = Product::getBranchProduct($value['product_id'], $value['branch_id']);
                    if (!is_null($product)) {
                        $value['product'] = $product;
                        $value['business_calculator'] = $product["business_calculator"];
                    } else {
                        unset($orderdetails[$key]); // Remove the item
                        continue;
                    }
                } else {
                    $variant = Product::getBranchProductVariant($value['product_id'], $value['branch_id'], $value['product_variant_id']);
                    if (!is_null($variant)) {
                        $value['product'] = $variant;
                        $value['business_calculator'] = $variant["business_calculator"];
                    } else {
                        unset($orderdetails[$key]); // Remove the item
                        continue;
                    }
                }

                // Branch
                if ($value['product_variant_id']) {
                    $branchProduct = Product::getBranchProductVariant($value['product_id'], $value['branch_id'], $value['product_variant_id']);
                    if (!is_null($branchProduct)) {
                        $branchProduct = Product::getBranchProductVariant($value['product_id'], $value['branch_id'], $value['product_variant_id'])['branch_products'];
                    }
                } else {
                    $branchProduct = Product::getBranchProduct($value['product_id'], $value['branch_id']);
                    if (!is_null($branchProduct)) {
                        $branchProduct = Product::getBranchProduct($value['product_id'], $value['branch_id'])['branch_products'];
                    }
                }
                $value['branchname'] = $branchProduct;
                $value['quantity'] = $branchProduct;
                if ($branchProduct) {
                    $value['branchname'] = $branchProduct[0]['branch']['name'];
                    $value['quantity'] = $branchProduct[0]['quantity'];
                }
            }

            if (!is_null($rest)) {
                $tax = $rest->tax;
                $fee = $rest->fee;
                $percent = $rest->percent;
                $companyName = $rest->name;
                $perkm = $rest->perkm;
                $minAmount = $rest->minAmount;
                $flatrate = $rest->flatrate;
            }
        }

        $currencies = Setting::where('param', '=', "default_currencies")->first();

        $response = [
            'error' => false,
            'currency' => $currencies,
            'order' => $order,
            'orderdetails' => $orderdetails,
            'fee' => $fee,
            'tax' => $tax,
            'percent' => $percent,
            'perkm' => $perkm,
            'flatrate' => $flatrate,
            'companyName' => $companyName,
            'minAmount' => $minAmount,
        ];
        return response()->json($response, 200);
    }

    public function addToCart(Request $request)
    {
        if (!Auth::check()) {
            return response()->json($response = ['error' => true], 401);
        }

        $user = Auth::user();
        $order = Order::where('user_id', $user->id)->where('is_complete', 0)->first();
        $orderid = 0;
        $send = $request->input('send');

        // Products
        $data = $request->input('data');

        // Payment method
        $method = $request->input('method');
        // if ($method == "mpesa_superapp") $method = "mpesa_safaricom_superapp";
        // if ($method == "mpesa_webpay") $method = "mpesa_safaricom_webpay";

        $pickupAddress = $request->input('user_address');
        $dropoffAddress = $request->input('company_address');
        $phone = "251" . substr($request->input('phone'), -9);
        $total = $request->input('total') ?: 0;
        $couponName = $request->input('couponName') ?: "";
        $curbsidePickup = $request->input('curbsidePickup') == 'true' ? 1 : 0;
        $is_telebirr = $method == 2 || $method == 3 || $method == 4 || $method == 5;

        // Company
        $companyObj = Company::first();
        if ($companyObj->perkm != '1')
            $fee = $companyObj->fee;
        $percent = !is_null($companyObj->percent) ? $companyObj->percent : 0;
        $tax = !is_null($companyObj->tax) ? $companyObj->tax : 0;
        $perkm = !is_null($companyObj->perkm) ? $companyObj->perkm : 0;
        $flatrate = !is_null($companyObj->flatrate) ? $companyObj->flatrate : 0;
        if ($curbsidePickup == "true") {
            $fee = "0";
        }
        // Delivery locations
        $deliveryLocations = array();
        $deliveryLocations["sourceLocation"] = $request->input('sourceLocation') ?: [];
        $deliveryLocations["destinationLocation"] = $request->input('destinationLocation') ?: [];

        // Wallet
        $walletAmount = $request->input('walletAmount');
        $referralPoints = $request->input('referralPoints');

        // orders with coupon and delivery fee
        $actual_orders = OrderController::calc_order($data, $curbsidePickup, $couponName, $deliveryLocations, $walletAmount, $referralPoints);
        if ($actual_orders instanceof JsonResponse) {
            return $actual_orders;
        }

        $subtotal = $actual_orders ? $actual_orders["subtotal"] : 0;
        $orderTotalSum = $actual_orders ? $actual_orders["total"] : 0;
        $fee = $actual_orders ? $actual_orders["fee"] : null;

        // TODO: get total fee => $feeTotal
        $feeTotal = 0;
        $tax = $actual_orders ? $actual_orders["tax"] : null;
        $discountAmount = $actual_orders ? $actual_orders["discountAmount"] : 0;
        $minDiscountAmount = $actual_orders ? $actual_orders["minDiscountAmount"] : 0;
        $couponDiscount = $actual_orders ? $actual_orders["couponDiscount"] : 0;
        $couponMinAmount = $actual_orders ? $actual_orders["couponMinAmount"] : 0;

        if ($order == null) {
            $values = array(
                'user_id' => $user->id,
                'order_status' => null, // enum('Order Received','Preparing','Ready','On the Way','Delivered','Canceled')
                'order_subtotal' => $subtotal,
                'order_total' => $orderTotalSum,
                'payment_id' => $method,
                'phone' => $phone,
                'curbside_pickup' => $curbsidePickup,
                'dropoff_address_id' => $dropoffAddress,
                'coupon_name' => $couponName,
                'coupon_discount_amount' => $couponDiscount,
                'coupon_min_discount_amount' => $couponMinAmount,
                'discount_amount' => $discountAmount,
                'min_discount_amount' => $minDiscountAmount,
                'order_tax' => $tax,
                'order_fee' => $fee,
                'order_fee_amount' => $feeTotal,
                'per_km' => $perkm,
                'flat_rate' => $flatrate,
                'percent' => $percent,
                'send' => $send == 1 ? 1 : 0,
                'is_complete' => (!$is_telebirr && $send == 1) ? 1 : 0,
                'order_comment' => '',
                "total_investment_discount_amount" => $actual_orders["totalInvestmentDiscount"],
                "total_investment_incentive" => count($actual_orders["totalInvestmentIncentive"]) > 0 ? implode(',', $actual_orders["totalInvestmentIncentive"]) : null,
                "total_investment_point" => $actual_orders["totalInvestmentPoint"],
            );
            Order::create($values);
            $orderid = DB::getPdo()->lastInsertId();
        } else {
            $orderid = $order->id;
            $values = array(
                'user_id' => $user->id,
                'order_status' => null, // enum('Order Received','Preparing','Ready','On the Way','Delivered','Canceled')
                'order_subtotal' => $subtotal,
                'order_total' => $orderTotalSum,
                'payment_id' => $method,
                'phone' => $phone,
                'curbside_pickup' => $curbsidePickup,
                'dropoff_address_id' => $dropoffAddress,
                'coupon_name' => $couponName,
                'coupon_discount_amount' => $couponDiscount,
                'coupon_min_discount_amount' => $couponMinAmount,
                'discount_amount' => $discountAmount,
                'min_discount_amount' => $minDiscountAmount,
                'order_tax' => $tax,
                'order_fee' => $fee,
                'order_fee_amount' => $feeTotal,
                'per_km' => $perkm,
                'flat_rate' => $flatrate,
                'percent' => $percent,
                'send' => $send == 1 ? 1 : 0,
                'is_complete' => (!$is_telebirr && $send == 1) ? 1 : 0,
                'order_comment' => '',
                "total_investment_discount_amount" => $actual_orders["totalInvestmentDiscount"],
                "total_investment_incentive" => count($actual_orders["totalInvestmentIncentive"]) > 0 ? implode(',', $actual_orders["totalInvestmentIncentive"]) : null,
                "total_investment_point" => $actual_orders["totalInvestmentPoint"],
            );
            Order::where('id', $orderid)
                ->update($values);
        }

        OrderDetail::where('order_id', $orderid)->delete();

        $data = $actual_orders ? $actual_orders["orders"] : [];
        if (!empty($data)) {
            foreach ($data as &$value) {
                $product = $value['title'];
                $count = (int) $value['count'];
                if ($value['discountPrice'] != 0)
                    $productprice = $value['discountPrice'];
                else
                    $productprice = $value['price'];

                $productprice = (float)$productprice;
                // for stock items
                $productid = $value['id'];
                $variantid = $value['variantId'] == "0" ? null : $value['variantId'];
                $branchid = $value['branchId'];
                $image = $value['images'][0]['image_path'];
                $total = $count * $productprice;
                $values = array(
                    'order_id' => $orderid,
                    'product_id' => $productid,
                    'product_variant_id' => $variantid,
                    'branch_id' => $branchid,
                    'name' => $product,
                    'count' => $count,
                    'price' => $productprice,
                    'total' => $total,
                    'image' => $image,
                    "investment_discount_amount" => $value["investment_discount_amount"],
                    "investment_incentive" => $value["investment_incentive"],
                    "investment_point" => $value["investment_point"],
                );
                OrderDetail::create($values);
                $orderDetailId = DB::getPdo()->lastInsertId();
            }
        }

        $walletlog = WalletLog::where('user_id', $user->id)->where('order_id', $orderid)->first();
        if (!is_null($actual_orders)) {
            if (array_key_exists("walletAmount", $actual_orders) && $actual_orders["walletAmount"]) {
                if ($walletlog) {
                    if ($actual_orders["walletAmount"] && $walletlog != $actual_orders["walletAmount"] && $send == '0') {
                        $walletlog
                            ->update([
                                'amount' => $actual_orders["walletAmount"],
                            ]);
                    } else if (!$actual_orders["walletAmount"] && $send == '0') {
                        $walletlog->delete();
                    }
                } else {
                    WalletLog::create([
                        'user_id' => $user->id,
                        'action' => 'Payment',
                        'amount' => $actual_orders["walletAmount"],
                        'order_id' => $orderid,
                        'status' => 'Pending',
                    ]);
                }
            }
        }

        $referralLog = ReferralLog::where('user_id', $user->id)->where('order_id', $orderid)->where('status', 'Pending')->first();
        if ($actual_orders && $actual_orders["referralPoint"]) {
            if ($referralLog) {
                if ($actual_orders["referralPoint"] && $referralLog != $actual_orders["referralPoint"] && $send == '0') {
                    $referralLog
                        ->update([
                            'amount' => $actual_orders["referralPoint"],
                        ]);
                } else if (!$actual_orders["referralPoint"] && $send == '0') {
                    $referralLog->delete();
                }
            } else {
                ReferralLog::create([
                    'user_id' => $user->id,
                    'action' => 'Payment',
                    'amount' => $actual_orders["referralPoint"],
                    'order_id' => $orderid,
                    'status' => 'Pending',
                ]);
            }
        }

        // Make order notification
        if ($send == '1' && ($is_telebirr ? false : true)) {
            // Quantity decrease from stock items
            if ($data != null) {
                // Deduct qunatity
                // foreach ($data as &$value) {
                //     $count = $value['count'];
                //     $productid = $value['id'];
                //     $branchid = $value['branchId'];
                //     BranchProduct::where('product_id', $productid)
                //         ->where('branch_id', $branchid)
                //         ->update([
                //             'quantity' => DB::raw('quantity - ' . $count),
                //         ]);
                // }

                $walletlog;
                if ($actual_orders && $walletlog) {
                    if ($actual_orders["walletAmount"]) {
                        $walletlog
                            ->update([
                                'amount' => $actual_orders["walletAmount"],
                                'status' => 'Completed',
                            ]);
                        $userWallet = Wallet::where('user_id', $walletlog->user_id)->first();
                        $userWallet->update([
                            'balance' => $actual_orders['walletRemainingAmount'],
                        ]);
                    } else {
                        $walletlog->delete();
                    }
                }

                $referralLog;
                if ($referralLog) {
                    if ($actual_orders && $actual_orders["referralPoint"]) {
                        $referralLog
                            ->update([
                                'amount' => $actual_orders["referralPoint"],
                                'status' => 'Completed',
                            ]);
                        $referral = User::where('id', $referralLog->user_id)->first();
                        $referral->update([
                            'referral_points' => $actual_orders['referralPointsRemaining'],
                        ]);
                    } else {
                        $referralLog->delete();
                    }
                }

                // user first purchase and referal award money
                OrderController::userPurchased($user, $orderTotalSum);
            }
        }

        // Make payment
        $url = '';
        $msg = '';
        $callBackURL = "";
        $vendor_name = Company::first()->name;
        if ($is_telebirr) {
            // Payment method 2 == telebirr
            if ($method == 2) {
                // Checking if first digit of phone number is correct telecom phone
                if ((int) substr(substr($phone, -9), 0, 1) != 9)
                    return response()->json([
                        'error' => '1',
                        'text' => "Please use Ethio Telecom's phone number.",
                        'msg' => "Please use Ethio Telecom's phone number."
                    ], 200);

                $apiKey = Payment::where('id', 2)->first();
                if (!is_null($apiKey)) {
                    // $notifyUrl = URL::signedRoute("tele_tx_notify", ['transaction_id' => $orderid]);
                    // $response =  Http::post(
                    //     env("TELEBIRR_CHECKOUT"),
                    //     [
                    //         "test" => 0,
                    //         "subject" => $vendor_name . ' order ' . $orderid,
                    //         "amount" => (string)$actual_orders["total"],
                    //         "payload" => $vendor_name . ' order ' . $orderid,
                    //         "apikey" => $apiKey->api_key,
                    //         "transaction_id" => (string) $orderid,
                    //         "mobile" => $phone, // substr(Auth::user()->phone, -9)
                    //         "success_url" => URL::to("/payment-complete?pid=$orderid"),
                    //         "return_url" => $notifyUrl
                    //     ]
                    // );
                    // if ($response->successful()) {
                    //     $data = $response->object();
                    //     if (!$data->error) {
                    //         $url = $response->object()->checkout_link;
                    //         $msg = "Redirecting to telebirr...";
                    //     }
                    //     // if (!property_exists($response->object(), 'error')) {
                    //     // }
                    //     // $url = $response["data"]["toPayUrl"];
                    // } else if ($response->failed()) {
                    //     // Reset order info
                    //     OrderController::resetOrderInfo($orderid);
                    //     return response()->json([
                    //         'error' => '1',
                    //         'text' => 'Something went wrong'
                    //     ], 200);
                    // }

                    $test = 0;
                    $uuid = substr(Str::uuid()->toString(), 0, 8);
                    $transactionsId = $uuid . "0A0" . $orderid;
                    // $transactionsId = $orderid;
                    $vendor_name = str_replace("|", " ", $vendor_name);
                    $payload = [
                        "subject" => "{$vendor_name} order {$orderid}",
                        "amount" => (string)$actual_orders["total"],
                        "payload" => "{$vendor_name} order {$orderid}",
                        "transaction_id" => (string) $transactionsId,
                        "mobile" => $phone,
                        // "notifyUrl" => URL::signedRoute("tele_tx_notify", ['transaction_id' => $orderid]),
                        // "notifyUrl" => URL::signedRoute("tele_tx_notify", ['transaction_id' => $transactionsId]),
                        "notifyUrl" => URL::to("/tele_tx_notify?transaction_id=$transactionsId"),
                        "returnUrl" => URL::to("/payment-complete/$orderid"),
                    ];
                    $timestamp = (string) Carbon::now()->timestamp;
                    $nonce = Str::uuid()->toString();
                    $payload = array_merge($payload, ["nonce" => $nonce, "timestamp" => $timestamp]);
                    $newRequest = new Request($payload);
                    $makeOrder = new TelebirrH5C2BPay();
                    $makeOrder = $makeOrder->makeOrder(
                        $test ? env('TELEBIRR_BASE_URL') : env('TELEBIRR_PROD_BASE_URL'),
                        $newRequest
                    );
                    if ($makeOrder["error"]) {
                        // Reset order info
                        OrderController::resetOrderInfo($orderid);
                        return response()->json([
                            'error' => '1',
                            'text' => 'Something went wrong'
                        ], 200);
                    }
                    $msg = "Redirecting to telebirr...";
                    $url = $makeOrder["checkout_link"];
                } else {
                    // Reset order info
                    OrderController::resetOrderInfo($orderid);
                    return response()->json([
                        'error' => '1',
                        'text' => 'Telebirr payment will be available shortly. Please use Cash on Delivery for now.'
                    ], 200);
                }
            }

            // If no payurl return send status to 0
            if ($url == null) {
                // Reset order info
                OrderController::resetOrderInfo($orderid);
                $msg = "Something went wrong";
            }
        }

        $response = [
            'error' => "0",
            'data' => $data,
            'orderid' => $orderid,
            'userEmail' => $user ? $user->email : "",
            'fee' => $fee,
            'payurl' => $url,
            'percent' => $percent,
            'amount' => $actual_orders["total"],
            'text' => $msg,
            'info' => $vendor_name . ' order ' . $orderid
        ];

        if ($actual_orders) {
            $response['tax'] = $actual_orders['tax'];
            $response['subtotal'] = $actual_orders['subtotal'];
            $response['total'] = $actual_orders['total'];
            $response['totalInvestmentDiscount'] = $actual_orders['totalInvestmentDiscount'];
            $response['totalInvestmentIncentive'] = $actual_orders['totalInvestmentIncentive'];
            $response['totalInvestmentPoint'] = $actual_orders['totalInvestmentPoint'];
            $response['tax2'] = $actual_orders['tax2'];
            $response['subtotal2'] = $actual_orders['subtotal2'];
            $response['total2'] = $actual_orders['total2'];

            if ($request->has('walletAmount')) {
                $response['walletAmount'] = (float) $actual_orders['walletAmount'];
                $response['walletRemainingAmount'] = $actual_orders['walletRemainingAmount'];
            }

            if ($request->has('referralPoints')) {
                $response['referralPoint'] = (float) $actual_orders['referralPoint'];
                $response['referralPointAmount'] = Referral::convertMoney($actual_orders['referralPoint']);
                $response['referralPointsRemaining'] = $actual_orders['referralPointsRemaining'];
            }

            if ($request->has('couponName') && $actual_orders['couponFound']) {
                $response['coupon'] = $actual_orders['coupon'];
                $response['couponFound'] = $actual_orders['couponFound'];
                $response['couponDiscount'] = $actual_orders['couponDiscount'];
                $response['couponMinAmount'] = $actual_orders['couponMinAmount'];
                $response['couponMinAmount2'] = Util::makePrice($actual_orders['couponMinAmount']);
                $response['couponMsg'] = $actual_orders['couponMsg'];
            }
        }

        // mpesa shortcode
        if ($method == "mpesa_safaricom_superapp") $response["mpesa_shortcode"] = "2227";

        if ($method == "mpesa_safaricom_webpay") $response["callBack"] = $callBackURL;

        return response()->json($response, 200);
    }

    public function make_request($method, $url, $params = [], $headers = [], $body = [])
    {
        $client = new Client();

        // $client->request('GET', '/get', ['auth' => ['username', 'password']]);
        $response = $client->request($method, $url, array_merge(['http_errors' => false], $params), $headers);

        // Check for success response
        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getBody(), true);

            return [
                "error" => false,
                "data" => $data
            ];
        }

        return [
            "error" => true,
            "data" => []
        ];
    }

    // Reset telebirr failed checkouts
    public function resetOrderInfo($orderId)
    {
        if (!Auth::check())
            return;
        $values = array(
            'send' => 0,
            'is_complete' => 0,
        );
        Order::find($orderId)
            ->update($values);
    }

    // Non stock Order Delivery fee
    public function branchFee(Request $request)
    {
        $method = $request->input('method');
        $lat = $request->input('user_lat');
        $lng = $request->input('user_lng');
        $branchId = $request->input('branchId');
        $orderData = $request->input('data');
        $orderProductBranch = Branch::select('id', 'name', 'address', 'lat', 'lng')->where('id', $branchId)->get();
        // Market Delivery fee
        $company = Company::first();
        $fee = 0;
        $marketTax = 0;
        if (!is_null($company)) {
            $fee = $company->fee;
            $marketTax = $company->tax;
        }
        if ($method == 'deliveryBranchMethod') {
            // Non stock items branch order
            foreach ($orderProductBranch as $branchOrder) {
                $branchLat = $branchOrder->lat;
                $branchLng = $branchOrder->lng;
                $unit = 'km';
                $distance = Util::getDistanceFromLatLonInKm((float)$branchLat, (float)$branchLng, (float)$lat, (float)$lng, $unit);
                $branchOrder->deliveryDistance = $distance;
                $deliveryFee = (float)$distance; // (float)$fee *
                $branchOrder->deliveryFee = $deliveryFee;
            }
        } else {
            foreach ($orderProductBranch as $branchOrder) {
                $branchLat = $branchOrder->lat;
                $branchLng = $branchOrder->lng;
                $unit = 'km';
                $distance = '0 km';
                $branchOrder->deliveryDistance = $distance;
                $deliveryFee = (float)$distance; // (float)$fee *
                $branchOrder->deliveryFee = $deliveryFee;
            }
        }
        // If there are other branch stock items multi branch order
        $otherBranchOrder = collect();
        foreach ($orderData as $order) {
            if (!collect($otherBranchOrder)->contains($order['branchId'])) {
                $otherBranchOrder[] = $order['branchId'];
            }
        }
        if (count($otherBranchOrder) != 0) {
            $otherBranch = Branch::select('id', 'name', 'address', 'lat', 'lng')->whereIn('id', $otherBranchOrder)->get();
            foreach ($otherBranch as $branch) {
                $branchLat = $branch->lat;
                $branchLng = $branch->lng;
                $unit = 'km';
                $distance = Util::getDistanceFromLatLonInKm((float)$branchLat, (float)$branchLng, (float)$lat, (float)$lng, $unit);
                $branch->deliveryDistance = $distance;
                $deliveryFee = (float)$distance; // (float)$fee *
                $branch->deliveryFee = $deliveryFee;
            }
            $orderProductBranch = $orderProductBranch->merge($otherBranch);
        }
        foreach ($orderProductBranch as $branches) {
            $branch_cart_items = array();
            foreach ($orderData as $orderItem) {
                if ($orderItem['branchId'] == $branches->id) {
                    $branch_cart_items[] = $orderItem['title'];
                }
            }
            $branches->cart_items = implode(", ", $branch_cart_items);
            $branches->cart_items_count = count($branch_cart_items);
        }
        return response()->json([
            'error' => 0,
            'data' => $orderProductBranch,
            'market_tax' => $marketTax
        ]);
    }

    /**
     * checkOrder - checks if order status is updated
     * @orderId: order id
     */
    static public function checkOrder($orderId)
    {
        $order = Order::where('id', $orderId)->first();
        if ($order != null) {
            $user = User::where('id', $order->user->id)->first();
            $orderTotalSum = $order->order_total;

            // user first purchase and referal award money
            if ($user) OrderController::userPurchased($user, $orderTotalSum);

            if ($order->send == 1 && $order->is_complete == 1 && $order->view == 1) return 1;
        }
        return 0;
    }

    /**
     * calc_order - calculates total price of orders and
     * sets the correct price to product.
     * @orders: array of objects of orders
     *
     * Return: total amount with their actual prices.
     */
    public function calc_order($orders, $curbsidePickup, $coupon_code, $deliveryLocations, $walletAmount = 0, $referalPoint = 0)
    {
        if ($orders && count($orders) > 0) {
            // adding correct data to order
            $orders = array_map(function ($orderItem) {
                // check if it's a stock item or not to get the correct price according to id, variant id and branch id
                // check if it's a product's variant or not
                if ($orderItem["variantId"] != 0) {
                    $actualProduct = Product::getBranchProductVariant($orderItem["id"], $orderItem["branchId"], $orderItem["variantId"]);
                    $orderItem["price"] = $actualProduct['price'];
                    $orderItem["discount_price"] = $actualProduct['discount_price'];
                    $orderItem["images"] = $actualProduct['images'];
                    $orderItem["name"] = $actualProduct['name'];
                    // if ($orderItem["count"] >= $actualProduct['branch_products'][0]['quantity']) {
                    //     $orderItem["count"] = $actualProduct['branch_products'][0]['quantity'];
                    //     response()->json([
                    //         'error' => '2',
                    //         'text' => $orderItem["name"] . 'count updated'
                    //     ]);
                    // }
                    $orderItem["quantity"] = $actualProduct['branch_products'][0]['quantity'];
                    $orderItem["minquantity"] = $actualProduct['branch_products'][0]['minquantity'];
                    $orderItem["maxquantity"] = $actualProduct['branch_products'][0]['maxquantity'];
                }
                // for the product(not variant)
                else {
                    $actualProduct = Product::getBranchProduct($orderItem["id"], $orderItem["branchId"]);
                    // if (is_null($actualProduct)) dd($orderItem["id"], $orderItem["branchId"]);
                    // if (!array_key_exists("price", $actualProduct)) dd($orderItem, $actualProduct);
                    $orderItem["price"] = $actualProduct['price'];
                    $orderItem["discount_price"] = $actualProduct['discount_price'];
                    $orderItem["images"] = $actualProduct['images'];
                    $orderItem["name"] = $actualProduct['name'];
                    if ($orderItem["count"] >= $actualProduct['branch_products'][0]['quantity']) {
                        $orderItem["count"] = $actualProduct['branch_products'][0]['quantity'];
                        response()->json([
                            'error' => '2',
                            'text' => $orderItem["name"] . 'count updated'
                        ]);
                    }
                    $orderItem["quantity"] = $actualProduct['branch_products'][0]['quantity'];
                    $orderItem["minquantity"] = $actualProduct['branch_products'][0]['minquantity'];
                    $orderItem["maxquantity"] = $actualProduct['branch_products'][0]['maxquantity'];
                }
                $orderItem["investment_discount_amount"] = null;
                $orderItem["investment_incentive"] = null;
                $orderItem["investment_point"] = null;

                return $orderItem;
            }, $orders);

            // Check count exceeding stock quantity
            foreach ($orders as $key => $orderItem) {
                $orderItem["count"] = (int) $orderItem["count"];
                $orderItem["branchId"] = (int) $orderItem["branchId"];
                $orderItem["id"] = (int) $orderItem["id"];
                if ($orderItem["count"] < $orderItem["minquantity"]) {
                    return response()->json([
                        'error' => true,
                        'msg' => $orderItem["title"] . ' minimum purchase count is ' . $orderItem["minquantity"]
                    ]);
                }
                if ($orderItem["count"] > $orderItem["maxquantity"]) {
                    return response()->json([
                        'error' => true,
                        'msg' => $orderItem["title"] . ' has reached maximum purchase limit'
                    ]);
                }
                if ($orderItem["count"] > $orderItem["quantity"]) {
                    return response()->json([
                        'error' => true,
                        'msg' => $orderItem["title"] . ' count limit reached. Please adjust the count and add again!'
                        // 'msg' => $orderItem["title"] . 'quantity exceeded'
                    ]);
                }
            }

            // subtotal
            // adding correct data to order to
            $subtotal = 0;
            $subtotal2 = 0;
            $totalInvestmentDiscount = 0;
            $totalInvestmentIncentive = array();
            $totalInvestmentPoint = 0;
            foreach ($orders as $key => &$orderItem) {
                $price = 0;
                $orderItem["investment_discount_amount"] = null;
                $orderItem["investment_incentive"] = null;
                $orderItem["investment_point"] = null;

                if ($orderItem["discountPrice"])
                    $price = (float)str_replace(',', '', $orderItem["discountPrice"]);
                else
                    $price = (float)str_replace(',', '', $orderItem["price"]);

                $price = $price * (int)$orderItem['count'];
                $subtotal += $price;

                // TODO: discount logic
                if (array_key_exists("businessCalc", $orderItem) && $orderItem["businessCalc"]) {
                    $busiClacs = $orderItem["businessCalc"];
                    foreach ($busiClacs as $busiClac) {
                        $discounted = 0;
                        if ($price >= $busiClac["from_amount"] && $price <= $busiClac["to_amount"]) {
                            if ($busiClac["discount_amount"]) {
                                if ($busiClac["percent"] == 1) {
                                    $discounted = $price * (float)($busiClac["discount_amount"]) / 100;
                                } else {
                                    $discounted = (float)($busiClac["discount_amount"]);
                                }
                                $totalInvestmentDiscount += $discounted;

                                // Discount
                                $orderItem["investment_discount_amount"] = $discounted;
                            } else if ($busiClac["incentive"]) {
                                // Incentive
                                $orderItem["investment_incentive"] = $busiClac["incentive"];
                                array_push($totalInvestmentIncentive, $busiClac["incentive"]);
                            } else if ($busiClac["point"]) {
                                // Point
                                $orderItem["investment_point"] = $busiClac["point"];
                                $totalInvestmentPoint += $busiClac["point"];
                            }
                        }
                    }
                }
            }

            // company pricing on tax, delivery fee & coupon code
            $company = Company::first();
            // company tax
            $Mtax = 0;
            $tax2 = 0;
            if ($company->tax) {
                $Mtax = Util::percent_calculate($company->tax, 1);
            }
            $tax = $Mtax * $subtotal;
            if ($totalInvestmentDiscount != 0) {
                $tax2 = $Mtax;
                $subtotal2 = $subtotal - $totalInvestmentDiscount;
                $tax2 = $tax2 * $subtotal2;
            }
            // delivery fee
            $fee = null;
            // if it's not curbside pickup
            // TODO: make 0
            if ($curbsidePickup == 0) {
                // Delivery fee
                $fee = (float)$company->fee;
                // company delivery fee by percent
                $percent = $company->percent;
                // company delivery fee by distance in km
                $perkm = $company->perkm;
                // company delivery fee by given price(flate rate)
                $flatrate = $company->flatrate;
                // update fee if fee is by percent
                $fee = Util::percent_calculate($fee, $percent);
                // update fee if fee is by distance
                $distance = 0;
                $branchLocation = $deliveryLocations["sourceLocation"];
                $customerLocation = $deliveryLocations["destinationLocation"];
                $unit = 'km';
                foreach ($branchLocation as $key => $branch) {
                    $distance += (float)Util::getDistanceFromLatLonInKm((float)$branch["lat"], (float)$branch["lng"], (float)$customerLocation["lat"], (float)$customerLocation["lng"], $unit);
                }
                if ($perkm == 1) {
                    // $distance = Util::getDistanceFromLatLonInKm((float)$branchLocation["lat"], (float)$branchLocation["lng"], (float)$customerLocation["lat"], (float)$customerLocation["lng"], $unit);
                    $fee = (float)$fee * (float)$distance;
                } else if ($percent == 1) {
                    $fee = $subtotal * (float)$fee;
                }
            }

            // total with tax added
            $total = $subtotal + $tax + ($fee == null ? 0 : $fee);
            $total2 = 0;
            if ($totalInvestmentDiscount != 0) {
                $total2 = $subtotal2 + $tax2 + ($fee == null ? 0 : $fee);
            }

            // coupon price
            // check if the coupon is correct
            $coupon = Coupon::where('coupons.name', $coupon_code)
                ->where('coupons.visible', 1)
                ->first();
            $couponDiscount = 0;
            $couponMinAmount = 0;
            $couponFound = 0;
            $couponMsg = 0;
            if ($coupon != NULL) {
                $couponFound = 1;
                $currentDate = Carbon::now();
                // DateTime::createFromFormat('Y-m-d\TH:i', $coupon->start_date)
                $couponStartDate = Carbon::createFromFormat('Y-m-d H:i:s', $coupon->start_date);
                // DateTime::createFromFormat('Y-m-d\TH:i', $coupon->end_date)
                $couponExpiryDate = Carbon::createFromFormat('Y-m-d H:i:s', $coupon->end_date);
                $couponDiscountPrice = (float) $coupon->discount;
                $couponMinimunAmount = (float) $coupon->min_amount;
                $couponInpercents = $coupon->percent;
                // check if the coupon has expired
                if ($currentDate > $couponExpiryDate) {
                    // coupon has expired
                    $couponMsg = 'Coupon have already expired on ' . $couponExpiryDate->format('h:i:s A M d, Y');
                    // return response()->json(['error' => true, 'msg' => 'Coupon have already expired on ' . $couponExpiryDate->format('h:i:s A M d, Y')]);
                }
                // check if the coupon code has started
                else if ($currentDate < $couponStartDate) {
                    // coupon has not started yet
                    $couponMsg = 'Coupon is not active until ' . $couponStartDate->format('h:i:s A M d, Y');
                    // return response()->json(['error' => true, 'msg' => 'Coupon is not active until ' . $couponStartDate->format('h:i:s A M d, Y')]);
                }
                $couponMinAmount = $couponMinimunAmount;
                if ($total2) {
                    // check if the total amount is greater than the coupon minimun amount
                    if ($total2 >= $couponMinimunAmount) {
                        // apply coupon
                        // check if coupon discount is in percent or not and apply to the amount
                        $couponDiscountPrice = Util::percent_calculate($couponDiscountPrice, $couponInpercents);
                        $couponDiscount = $couponDiscountPrice;
                        if ($couponInpercents == 1) {
                            $couponDiscount = $total2 * $couponDiscountPrice;
                            $total2 = $total2 - $couponDiscount;
                        } else {
                            $total2 -= $couponDiscountPrice;
                        }
                    } else {
                        $couponMsg = 'Mnimum amount must be at least ' . $couponMinimunAmount . ' for the coupon to work';
                        // return response()->json(['error' => true, 'msg' => 'Mnimum amount must be at least ' . $couponMinimunAmount . ' for the coupon to work']);
                    }
                } else {
                    // check if the total amount is greater than the coupon minimun amount
                    if ($total >= $couponMinimunAmount) {
                        // apply coupon
                        // check if coupon discount is in percent or not and apply to the amount
                        $couponDiscountPrice = Util::percent_calculate($couponDiscountPrice, $couponInpercents);
                        $couponDiscount = $couponDiscountPrice;
                        if ($couponInpercents == 1) {
                            $couponDiscount = $total * $couponDiscountPrice;
                            $total = $total - $couponDiscount;
                        } else {
                            $total -= $couponDiscountPrice;
                        }
                    } else {
                        $couponMsg = 'Mnimum amount must be at least ' . $couponMinimunAmount . ' for the coupon to work';
                        // return response()->json(['error' => true, 'msg' => 'Mnimum amount must be at least ' . $couponMinimunAmount . ' for the coupon to work']);
                    }
                }
            }

            // minimum discount amount and discount amount
            $minDiscountAmount = (float)$company->minDiscountAmount;
            $discountAmount = 0;

            if ($total2) {
                if ($minDiscountAmount > 0 && $total2 > $minDiscountAmount) {
                    $minDiscountAmount2 = Util::makePrice($company->minDiscountAmount);
                    $discountAmount = $company->discountAmount;
                    // update fee if fee is by percent
                    $discountAmount = Util::percent_calculate($discountAmount, 1);
                    $discountAmount = $total2 * $discountAmount;
                    $total2 = $total2 - $discountAmount;
                }
            } else {
                if ($minDiscountAmount > 0 && $total > $minDiscountAmount) {
                    $minDiscountAmount2 = Util::makePrice($company->minDiscountAmount);
                    $discountAmount = $company->discountAmount;
                    // update fee if fee is by percent
                    $discountAmount = Util::percent_calculate($discountAmount, 1);
                    $discountAmount = $total * $discountAmount;
                    $total = $total - $discountAmount;
                }
            }

            // Wallet, ammount check, deduct
            $remainingAmount = null;
            $wallet = Wallet::where('user_id', Auth::user()->id)->where('status', 'Active')->first();
            if (!is_null($wallet)) {
                $remainingAmount = $wallet->balance;
            }
            $orderId = Order::where('user_id', Auth::user()->id)->where('is_complete', 0)->first();
            $walletLog = null;
            if (!is_null($orderId)) $walletLog = WalletLog::where('user_id', Auth::user()->id)->where('order_id', $orderId->id)->where('status', 'Pending')->first();
            if ($walletAmount || !is_null($walletLog)) {
                if (!is_null($wallet)) {
                    if (!is_null($walletLog) && $wallet->balance >= $walletLog->amount) {
                        if ($total2) {
                            $total2 = $total2 - $walletLog->amount;
                        } else {
                            $total = $total - $walletLog->amount;
                        }
                        $remainingAmount = $wallet->balance - $walletLog->amount;
                        $walletAmount = $walletLog->amount;
                    } else if ($wallet->balance >= $walletAmount) {
                        if ($total2) {
                            $total2 = $total2 - $walletAmount;
                        } else {
                            $total = $total - $walletAmount;
                        }
                        $remainingAmount = $wallet->balance - $walletAmount;
                    } else {
                        return response()->json(['error' => true, 'msg' => 'You don\'t have enough amount.']);
                    }
                } else {
                    return response()->json(['error' => true, 'msg' => 'Wallet not found']);
                }
            }

            // Referral points, point convert(convertPoints), deduct
            $remainingPoints = null;
            $referralLog = null;
            if (!is_null($orderId)) $referralLog = ReferralLog::where('user_id', Auth::user()->id)->where('order_id', $orderId->id)->where('status', 'Pending')->first();
            $referral = User::where('id', Auth::user()->id)->first();
            if (!is_null($referral)) {
                $remainingPoints = $referral->referral_points;
            }
            if ($referalPoint || !is_null($referralLog)) {
                if (!is_null($referral)) {
                    $balance = Referral::convertPoints($referral->referral_points);
                    // if (!is_null($referralLog) && $balance >= $referralLog->amount) {
                    //     $total = $total - $referralLog->amount;
                    //     $remainingPoints = Referral::convertMoney($balance) - $referralLog->amount;
                    //     $referalPoint = $referralLog->amount;
                    // } else
                    if ($balance >= $referalPoint) {
                        if ($total2) {
                            $total2 = $total2 - Referral::convertMoney($referalPoint);
                        } else {
                            $total = $total - Referral::convertMoney($referalPoint);
                        }
                        $remainingPoints = $referral->referral_points - $referalPoint;
                    } else {
                        return response()->json(['error' => true, 'msg' => 'You don\'t have enough points.']);
                    }
                } else {
                    return response()->json(['error' => true, 'msg' => 'No referral points found']);
                }
            }

            // real datas of orders(price, discountprice, subtotal, total, tax, fee)
            $realData = array(
                "subtotal" => $subtotal,
                "subtotal2" => $subtotal2,
                "total" => $total,
                "total2" => $total2,
                "discountAmount" => $company->discountAmount,
                "minDiscountAmount" => $company->minDiscountAmount,
                "tax" => $company->tax,
                "tax2" => $tax2,
                "fee" => $fee,
                "coupon" => $coupon_code,
                "couponFound" => $couponFound,
                "couponDiscount" => $couponDiscount,
                "couponMinAmount" => $couponMinAmount,
                "couponMsg" => $couponMsg,
                "totalInvestmentDiscount" => $totalInvestmentDiscount,
                "totalInvestmentIncentive" => $totalInvestmentIncentive,
                "totalInvestmentPoint" => $totalInvestmentPoint,
                "orders" => $orders,
                "walletAmount" => $walletAmount,
                "walletRemainingAmount" => $remainingAmount,
                "referralPoint" => $referalPoint,
                "referralPointsRemaining" => $remainingPoints
            );

            return $realData;
        }
    }

    /**
     * removeFromCart - removes item from cart
     * @request: contains productid, branchid and variantId
     */
    public function removeFromCart(Request $request)
    {
        if (!Auth::check()) {
            return;
        }

        $productId = $request->input('productid');
        $branchId = $request->input('branchid');
        $variantid = $request->input('variantid');
        // Latest order of user
        $orderid = Order::where('user_id', Auth::user()->id)->latest('updated_at')->first()->id;
        OrderDetail::where('order_id', $orderid)
            ->where('product_id', $productId)
            ->where('product_variant_id', $variantid)
            ->where('branch_id', $branchId)
            ->delete();
    }

    /**
     * userPurchased - checks if the user is first time purchasing.
     * If it is it will get the referral code if it's a referred user
     * else it sets first time purchased to true.
     * @user: the user to check
     */
    static public function userPurchased(User $user, $mount = 0)
    {
        if ($user->has_purchased_product == 0) {
            $referal = Referral::where('referred_user_id', $user->id)->first();
            if (!is_null($referal)) {
                $referal->reward_amount = 100;  // $mount / % of the total
                $referal->save();

                $user = User::where('id', $referal->referrer_user_id)->first();
                $user->referral_points += 100;  // $mount / % of the total
            }

            $user->has_purchased_product = 1;
            $user->save();
        }
    }

    // Superadmin










    /**
     * updateOrder - updates order status
     * @transaction_id - order id
     * @mobile - user's mobile number
     */
    static public function updateOrder($orderid, $mobile)
    {
        $iscomplete = ['is_complete' => true, 'view' => true, 'send' => true, 'updated_at' => new \DateTime()];
        // check if order status is already updated
        $orderStatusUpdate = OrderController::checkOrder($orderid);
        if ($orderStatusUpdate == 1) {
            $response = [
                'error' => false,
                'data' => null,
                'orderid' => $orderid,
            ];
            return $response;
        }
        // Send Notifications to Super Admin
        // $userid = User::where('role', "1")->first();
        // if ($userid != null) {
        //     $myRequest = new \Illuminate\Http\Request();
        //     $myRequest->setMethod('POST');
        //     $myRequest->request->add(['user' => $userid->id]);
        //     $myRequest->request->add(['title' => Lang::get(128)]); // 'New order arrived',
        //     $myRequest->request->add(['text' => Lang::get(129) . $orderid]); // "Order #",
        //     $defaultImage = DB::table('settings')->where('param', '=', "notify_image")->first()->value;
        //     $myRequest->request->add(['imageid' => $defaultImage]);
        //     NotificationController::sendNotify($myRequest);
        // }
        // // Notify vendor
        // $company = Order::where('id', $orderid)->first()->company;
        // $userid = User::where('vendor', $company)->first();
        // if ($userid != null) {
        //     $myRequest = new \Illuminate\Http\Request();
        //     $myRequest->setMethod('POST');
        //     $myRequest->request->add(['user' => $userid->id]);
        //     $myRequest->request->add(['title' => Lang::get(128)]); // 'New order arrived',
        //     $myRequest->request->add(['text' => Lang::get(129) . $orderid]);
        //     $defaultImage = DB::table('settings')->where('param', '=', "notify_image")->first()->value;
        //     $myRequest->request->add(['imageid' => $defaultImage]);
        //     NotificationController::sendNotify($myRequest);
        // }

        // Update order status to complete
        Order::where('id', $orderid)
            ->update($iscomplete);

        $data = OrderDetail::where('order_id', $orderid)->get();

        // Get order
        $order = Order::where('id', $orderid)->first();

        $orderItems = array();
        // Quantity decrease from stock items
        foreach ($data as &$value) {
            $count = $value->count;
            $productid = $value->product_id;
            $branchid = $value->branch_id;
            BranchProduct::where('product_id', $productid)
                ->where('branch_id', $branchid)
                ->update([
                    'quantity' => DB::raw('quantity - ' . $count),
                ]);
            $orderItems[] = $value->product->name . "-" . Util::makePrice($value->total) . "($value->count)";
        }

        // Send notify msg SMS
        $orderItems = implode(", ", $orderItems);
        $geezSMStoken = "tEH3gGjk4ch5y3F5YTEbu7jiUIgo8H98";
        $receiverPhones = ["251953960596"];
        $userId = $order->user_id;
        $dropoffAddress = $order->dropoff_address_id;
        if ($dropoffAddress) {
            $dropoffAddress = Address::where('id', $dropoffAddress)->first()->name;
            $dropoffAddress = "\nClient Address: " . $dropoffAddress;
        }
        $user = User::where('id', $userId)->first();
        $clientPhone = $user->phone;
        $clientName = $user->name;
        foreach ($receiverPhones as $receiverPhone) {
            // Notify admin
            Http::post(
                env('GEEZ_ENDPOINT'),
                [
                    "token" => $geezSMStoken,
                    "phone" => $receiverPhone,
                    "msg" => "New Order $orderid\nOrder Item(s): $orderItems\nClient Name: $clientName\nClient Phone: $clientPhone$dropoffAddress",
                    "shortcode_id" => 7
                ]
            );
        }
        // $clientMsg = "Dear customer, we have received your order (Order ID: $orderid). It is being processed. Our team will contact you shortly.\nThank you for choosing our service!";
        // Notify client
        // $smsClientResponse = Http::post(
        //     env('GEEZ_ENDPOINT'),
        //     [
        //         "token" => $geezSMStoken,
        //         "phone" => $clientPhone,
        //         "msg" => $clientMsg,
        //         "shortcode_id" => 7
        //     ]
        // );
        $response = [
            'error' => false,
            'data' => null,
            'orderid' => $orderid,
        ];
        return $response;
    }
}
