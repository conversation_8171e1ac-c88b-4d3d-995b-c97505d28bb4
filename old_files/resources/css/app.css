/* /* @tailwind base;
@tailwind components;
@tailwind utilities; */


/* :root {
    --clr-white: #fff;
    --clr-dark: #242424;
    --clr-svg: #1e1d23;

    --screen-md: 768px;
    --screen-lg: 1024px;
    --screen-xlg: 1440px;
}

* {
    margin: 0px;
    padding: 0px;
    box-sizing: border-box;
    font-family: sans-serif;
}

img,
.img-contain {
    width: auto;
    max-width: 100%;
    height: auto;
    max-height: 100%;
    margin: auto;
    text-align: center;
} */

/* Header Navigation Top */
/* .header-nav {
    background-color: #000;
    width: 100%;
    position: sticky;
    top: 0;
    z-index: 998;

    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.mobile-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.menu {
    position: relative;
    width: 25px;
    height: 1.257rem;
}

.menu-icon {
    width: 100%;
    height: 3px;
    background-color: var(--clr-white);
    position: relative;
}

.menu-icon::before {
    content: "";
    width: 60%;
    height: 3px;
    position: absolute;
    background-color: var(--clr-white);
    top: 10px;
    left: 0;
}

.menu-icon::after {
    content: "";
    width: 100%;
    height: 3px;
    position: absolute;
    background-color: var(--clr-white);
    top: 20px;
    left: 0;
}

.logo {
    display: inline-block;
}

.logo a {
    display: inline-block;
}

.phone svg {
    width: 30px;
    height: 30px;
    color: var(--clr-white);
}

.menuNav {
    @apply h-screen fixed top-0 left-0 p-4;
    width: 225px;
    transform: translatex(-250px);
    transition: all 0.3s ease-out;
    background-color: var(--clr-dark);
} */

/* Navigation Sidebar Left */
/* .sidebar-left {
    height: 100%;
    width: 275px;
    margin-left: -300px;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    z-index: 999;
    background-color: #000;
    overflow-x: hidden;
    transition: 0.3s cubic-bezier(0.65, 0.05, 0.36, 1);
}

.sidebar-left__body {
    height: 100%;
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    grid-template-rows: calc(100% - 140px) 140px;
    overflow: hidden;
}

.sidebar-left__top {
    padding: 10px;
    overflow: auto;
}

.sidebar-left__bottom {
    border-top: 1px solid #e5e7eb;
    padding-top: 10px;
}

.sidebar-left__bottom-body {
    padding: 10px;
} */

/* .sidebar-left__footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 275px;
    border-top: 1px solid #000000b3;
    overflow: hidden;
    padding-right: 15px;
    padding-top: 10px;
    padding-bottom: 5px;
    padding-left: 23px;
} */

/* .exit-icon {
    display: flex;
    justify-content: right;
    margin-bottom: 10px;
}

.exit-icon svg {
    width: 30px;
    height: 30px;
    color: var(--clr-white);
    cursor: pointer;
    display: inline-block;
    user-select: none;
    transition-duration: .2s;
    transition-timing-function: ease-out;
    transition-property: transform;
}

.exit-icon:hover svg {
    transform: rotate(90deg);
}

.nav-link-list {
    overflow: auto;
}

.nav-link {
    display: block;
    position: relative;
    list-style: none;
    color: var(--clr-dark);
    overflow: hidden;
}

.nav-link:not(:last-child) {
    border-bottom: 1px solid #f8f9fa1c;
}

.nav-link a {
    display: block;
    color: var(--clr-white);
    text-decoration: none;
    font-size: 15px;
    line-height: 22px;
    display: block;
    transition: 0.3s;
    padding: 8px;
}

.nav-link a:has(~ input) {
    padding-right: 45px;
}

.nav-link a:hover,
.nav-link a:focus {
    color: #FDB913;
}

.nav-link .active a {
    color: #FDB913;
}

.nav-link:has(li.active)>a {
    color: #FDB913;
}

.sub-menu-arrow {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 6px 17px;
}

.sidebar-left .sub-menu-arrow {
    padding: 6px 17px;
    top: 1px;
    right: 8px;
    cursor: pointer;
} */

/* .sub-menu-arrow svg {
    color: var(--clr-dark);
    padding: 3px;
    font-weight: 700;
    transform: rotate(0deg);
    transition: transform .3s linear;
    width: 25px;
    height: 25px;
}

.sidebar-left .sub-menu-arrow svg {
    color: var(--clr-white);
} */

/* Make the dropdown animate. */
/* .dropdown-content {
    min-width: 100%;
    overflow: hidden;
    max-height: 0;
    transition: max-height .4s linear;
}

.sidebar-left .dropdown-content {
    padding-left: 15px;
}

[id^=sub-menu-check] {
    display: none;
}

[id^=dropdown-check] {
    display: none;
}

.cat-menu:has(+ [id^=sub-menu-check]:checked) {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-bottom-color: #fdb913;
    color: #fdb913;
}

.rotatesvg {
    transform: rotate(180deg);
} */

/* The max-height should be set to something that will always be a little bit bigger than the height of the content. */
/* [id^=sub-menu-check]:checked~.dropdown-content {
    max-height: 400px;
    overflow-y: auto;
} */

/* arrow rotate */
/* [id^=sub-menu-check]:checked~.sub-menu-arrow svg {
    transform: rotate(180deg);
} */

/* [id^=sub-menu-check]:checked~.dropdown-content~.sub-menu-arrow {
    background-color: #fdb913;
    border-radius: 8px;
} */

/* .category-dropdown-list-active [id^=sub-menu-check]:checked~.dropdown-content~.sub-menu-arrow svg {
    color: #fff;
} */

/* The max-height should be set to something that will always be a little bit bigger than the height of the content. */
/* [id^=dropdown-check]:checked~.dropdown-content {
    max-height: 400px;
    overflow-y: auto;
} */

/* arrow rotate */
/* [id^=dropdown-check]:checked~.sub-menu-arrow svg {
    transform: rotate(180deg);
} */

/* [id^=dropdown-check]:checked~.dropdown-content~.sub-menu-arrow {
    background-color: #fdb913;
    border-radius: 8px;
} */


/* Main Body */
/* #main-body {
    min-height: 100vh;
}

.category-menu-check svg {
    color: var(--clr-black);
    font-weight: 700;
    transform: rotate(0deg);
    transition: transform .3s ease;
}

[id^=category-menu-check]:checked~.category-menu-check svg {
    transform: rotate(180deg);
}

.category-dropdown-list {
    min-width: 100%;
    overflow: auto;
    padding: 0px;
    max-height: 0;
    transition: all .3s linear;
}

.category-dropdown-list-active {
    max-height: 550px;
    padding: 15px;
}

.subcat {
    padding: 1px;
}

#category-menu-list.category-menu-list-active {
    height: 250px;
    visibility: visible;
    overflow: auto;
}

#account-menu-list {
    min-width: max-content;
    overflow: hidden;
    max-height: 0;
    transition: max-height .65s ease;
}

#account-menu-list.account-menu-list-active {
    max-height: fit-content;
    overflow: auto;
}

#search-bar.search-bar-active {
    opacity: 1;
    visibility: visible;
} */


/* Navigation footer */
/* .footer-navbar {
    background-color: #fff;
    position: fixed;
    bottom: 0;
    width: 100%;
    box-shadow: 0px -1px 3px 0px #b7b8bf;
    z-index: 996;
}

.menu-list {
    display: flex;
}

.menu-list li {
    list-style: none;
}

.menu-list-icon {
    position: relative;
    width: calc(100% - 100px);
    text-align: center;
    padding: 7px 0px;
}

.menu-list-icon:not(:last-child) {
    border-right: 1px solid #e5e5e5;
}

.menu-li {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.menu-li--link {
    position: relative;
}

.menu-li--link svg {
    width: 20px;
    height: 20px;
    color: var(--clr-svg);
}

.bubble {
    border-radius: 50%;
    min-width: 20px;
    min-height: 20px;
    text-align: center;
    position: absolute;
    right: -10px;
    top: -5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
    line-height: 19px;
}

.big-bubble {
    border-radius: 50%;
    min-width: 20px;
    min-height: 20px;
    text-align: center;
    position: absolute;
    top: 10px;
    right: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
    line-height: 19px;
}

.bubble--red {
    background-color: #f00;
    color: #fff;
}

.nav-desc {
    font-weight: 500;
    font-size: 12px;
    line-height: 19px;
    text-transform: capitalize;
}

#search-check {
    display: none;
}

#search-container.show-search-field {
    top: -66px;
}

.active-link {
    @apply bg-[#FDB913];
}

.col-sm-4 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px
} */

/* Product detail image hover */
/* .single-product-img a.big-image-popup,
.single-product-img a.big-image-popup {
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #373737;
    color: #ffffff;
    padding: 10px 15px;
    border-radius: 5px;
    visibility: hidden;
    opacity: 0;
    margin-left: -20px;
    margin-top: -20px;
}

.single-product-img a.big-image-popup:hover,
.single-product-img a.big-image-popup:hover {
    background-color: #FDB913;
}

.single-product-img:hover a.big-image-popup,
.single-product-img:hover a.big-image-popup {
    visibility: visible;
    opacity: 1;
}

.single-product-content-container .product-image-slider {
    -ms-flex-preferred-size: 75%;
    flex-basis: 75%;
}

.single-product-content-container .product-image-slider .single-product-img {
    position: relative;
}

.single-product-content-container .product-image-slider .single-product-img a.big-image-popup {
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #373737;
    color: #ffffff;
    padding: 10px 15px;
    border-radius: 5px;
    visibility: hidden;
    opacity: 0;
    margin-left: -20px;
    margin-top: -20px;
}

.single-product-content-container .product-image-slider .single-product-img a.big-image-popup:hover {
    background-color: #FDB913;
}

.single-product-content-container .product-image-slider .single-product-img:hover a.big-image-popup {
    visibility: visible;
    opacity: 1;
} */

/* Product details page */
/* #productDetails-moreImages.slick-initialized .slick-slide:not(:last-child) {
    margin-right: 10px;
}

#productDetails-moreImages .slick-slide.slick-current.slick-active .single-small-image {
    opacity: 1;
    border-width: 1px;
    border-color: #FDB913;
}

.active-image {
    opacity: 1;
    border-width: 1px;
    border-color: #FDB913;
}

#productDetails-moreImages .slick-slide .single-small-image {
    opacity: 1;
    border-width: 1px;
    border-color: #f1f5f9;
    margin-right: 15px;
    width: 100%;
    border-radius: 12px;
}

#productDetails-moreImages .slick-slide .single-small-image::before {
    border-radius: 12px;
}

.inactive-image {
    opacity: 1;
    border-width: 1px;
    border-color: #f1f5f9;
} */

/* For account page */
/* .active-bg {
    @apply shadow-md;
} */


/* Account Page */
/*-- Tab Content & Pane Fix --*/
/* .tab-content {
    width: 100%;
}

.tab-content .tab-pane {
    display: block;
    height: 0;
    max-width: 100%;
    visibility: hidden;
    overflow: hidden;
    opacity: 0;
    transition: opacity .2s ease-in;
}

.tab-content .tab-pane.active {
    height: auto;
    visibility: visible;
    opacity: 1;
    overflow: visible;
} */

/* Cart checkout page */
/* tbody#addressTable tr:hover {
    background-color: #e9ecef;
} */

/* Home page slider */
/* .slick-list {
    height: 100%;
}

.slick-list .slick-track {
    height: 100%;
}

.slick-list .slick-track .slick-slide>div:first-child {
    height: 100%;
}

/* Update
.slick-list .slick-track .slick-slide>div:first-child>div {
    display: block !important;
}

.slick-button {
    width: 55px;
    height: 25px;
}

.slick-prev {
    position: absolute;
    z-index: 10;
    left: 35%;
    bottom: 50px;
    transform: translateX(-35%) scale(1);
    color: #FDB913;
    transition: color .3s ease-in-out;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: center;
}

.slick-next {
    position: absolute;
    z-index: 10;
    right: 35%;
    bottom: 50px;
    transform: translateX(35%) scale(1);
    color: #FDB913;
    transition: color .3s ease-in-out;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.slick-button:hover {
    color: #eeac00;
} */

/* button[disabled] {
    @apply cursor-not-allowed;
} */

/* @media (min-width: var(--screen-md)) {
    .slick-prev {
        position: absolute;
        z-index: 10;
        /* left: -30px;
        left: 15px;
        /* top: 50%;
        top: 90%;
        transform: translateY(-50%) scale(3);
        color: #FDB913;
        transition: color .3s ease-in-out;
    }

    .slick-prev span {
        display: none;
    }

    .slick-next {
        position: absolute;
        z-index: 10;
        /* right: -30px;
        right: 15px;
        /* top: 50%;
        top: 90%;
        transform: translateY(-50%) scale(3);
        color: #FDB913;
        transition: color .3s ease-in-out;
    }

    .slick-next span {
        display: none;
    }

    .slick-button {
        width: 25px;
        height: 20px;
    }

    .md\:col-span3 {
        grid-column: span 3 / span 3 !important;
    }
}

@media (min-width: var(--screen-lg)) {
    .lg\:col-span3 {
        grid-column: span 3 / span 3 !important;
    }
}

@media (min-width: var(--screen-xlg)) {
    .slick-prev {
        left: -30px;
        top: 50%;
    }

    .slick-prev span {
        display: none;
    }

    .slick-next {
        right: -30px;
        top: 50%;
    }
} */

/* Media query */
/* @media (max-width: 479px) {
    .alert {
        max-width: 100%;
        bottom: 57.5px !important;
    }
}

@media (min-width:576px) {
    .col-sm-4 {
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%
    }
} */

/* Cart Sidebar Right */
/* .sidebar-right {
    height: 100%;
    width: 345px;
    max-width: 80vw;
    margin-right: -345px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 999;
    background-color: #ffffff;
    overflow: hidden;
    transition: 0.3s cubic-bezier(0.65, 0.05, 0.36, 1);

    display: grid;
    grid-template-rows: 60px calc(100% - 60px - 140px) 140px;
}

.sidebar-right.right-show {
    margin-right: 0px;
}

.sidebar-right__header {
    width: 100%;
    padding: 15px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e5e5;
    place-self: flex-start;
}

.sidebar-right__header .sidebar-right__header-left {
    display: inline-block;
    margin-right: auto;
    font-size: 16px;
    font-weight: bold;
    line-height: normal;
    text-transform: uppercase;
}

.sidebar-right__header .sidebar-right__header-right {
    display: inline-block;
    margin-left: auto;
    font-size: 16px;
    line-height: normal;
}

.sidebar-right__header .sidebar-right__header-right svg {
    width: 25px;
    height: 25px;
    cursor: pointer;
    transition-duration: .2s;
    transition-timing-function: ease-out;
    transition-property: transform;
}

.sidebar-right__header .sidebar-right__header-right:hover svg {
    transform: rotate(90deg);
}

.sidebar-right__body {
    width: 100%;
    height: 100%;
    padding: 5px 15px;
    overflow: auto;
}

.sidebar-right__body-item {
    width: 100%;
    height: 75px;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px;
}

.sidebar-right__body-item:not(:last-child) {
    margin-bottom: 4px;
    border-bottom: 1px solid #e5e5e5;
} */

/* Tailwindcss styles */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* table */
.table-responsive {
    @apply grid gap-2 md:gap-0 md:table bg-white w-full overflow-hidden shadow-lg min-h-32;
}

.table-responsive thead tr th {
    @apply bg-[#ced4da] text-black sm:rounded-none p-4 text-left;
}

.table-responsive tbody tr td {
    @apply text-black sm:rounded-none p-4 text-left;
}

.table-responsive tbody tr:nth-child(odd) {
    @apply bg-slate-50;
}

.table-responsive tbody tr:hover {
    @apply bg-slate-100;
}

.table-responsive tbody tr:not(:last-child) {
    @apply mb-2;
}

.property-label {
    @apply min-w-48 text-lg font-bold;
}

.property-desc {
    @apply p-4 bg-gray-100 border border-gray-300 w-full rounded-md;
}
