<div class="row">
    <div class="col-12">
        <div class="card card-user">
            <!-- Tabs -->
            <div class="card-header card-header-danger">
                <div class="nav-tabs-navigation">
                    <div class="nav-tabs-wrapper">
                        <ul class="nav nav-tabs" data-tabs="tabs">
                            <li role="presentation" class="nav-item" id="tabHome">
                                <a href="#home" class="nav-link active" data-toggle="tab">List</a>
                            </li>
                            <li role="presentation" class="nav-item">
                                <a href="#create" class="nav-link" data-toggle="tab">Create</a>
                            </li>
                            <li role="presentation" class="nav-item" id="tabEdit">
                                <a href="#edit" class="nav-link" data-toggle="tab">Edit</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- End Tabs -->

            <!-- Tab List -->
            <div id="myTabContent" class="card-body">
                <div class="tab-content text-left">

                    <!-- Tab View - Listings -->
                    <div role="tabpanel" class="tab-pane active" id="home">
                        <div class="card-header">
                            <h5 class="card-title">Product Listings</h5>
                            <div class="row">
                                <div class="col-12 col-md-4">
                                    <div class="form-group">
                                        <label for="filter">Filter</label>
                                        <div class="row gap-4 col-12">
                                            @include('components.input-field.checkbox', [
                                                'id' => 'visible_search',
                                                'text' => 'Published item',
                                                'initvalue' => 'true',
                                                'callback' => 'onVisibleSearchSelect()',
                                            ]) {{-- Published item --}}
                                            @include('components.input-field.checkbox', [
                                                'id' => 'unvisible_search',
                                                'text' => 'Unpublished item',
                                                'initvalue' => 'true',
                                                'callback' => 'onVisibleSearchSelect()',
                                            ]) {{-- Unpublished item --}}
                                            @include('components.input-field.checkbox', [
                                                'id' => 'featured_search',
                                                'text' => 'Featured item',
                                                'initvalue' => 'false',
                                                'callback' => 'onVisibleSearchSelect()',
                                            ]) {{-- Unpublished item --}}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-4">
                                    @include('components.input-field.text', [
                                        'text' => 'Search',
                                        'label' => 'Search',
                                        'id' => 'element_search',
                                    ])
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Table -->
                            @include('components.table.light-table')
                            <!-- End Table -->
                        </div>
                    </div>
                    <!-- End Tab View - Listings -->

                    <!-- Tab View - Create -->
                    <div role="tabpanel" class="tab-pane" id="create">
                        <div class="card-header">
                            <h5 class="card-title">Create Product</h5>
                        </div>
                        <div class="card-body">
                            <form>
                                <div class="row">
                                    <div class="col-md-6 col-12">
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.text', [
                                                    'label' => 'Name',
                                                    'placeholder' => 'eg: LG TV',
                                                    'desc' => 'Insert Name',
                                                    'id' => 'element_name',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.price', [
                                                    'label' => 'Price',
                                                    'id' => 'price',
                                                    'desc' => 'Insert price',
                                                    'request' => 'true',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.price', [
                                                    'label' => 'Discount Price',
                                                    'id' => 'discount_price',
                                                    'desc' => 'Insert discount price',
                                                    'request' => 'true',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.text-area', [
                                                    'label' => 'Description',
                                                    'placeholder' => '...',
                                                    'desc' => 'Insert Product Description',
                                                    'id' => 'desc',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.text', [
                                                    'label' => 'Video link',
                                                    'placeholder' => '',
                                                    'desc' => 'Insert Video link',
                                                    'id' => 'video_link',
                                                ])
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.images', []) {{-- Image dropbox --}}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        @include('components.input-field.dropdown-with-table', [
                                            'onchange' => '',
                                            'id' => 'product_branch',
                                            'request' => 'true',
                                            'noitem' => 'true',
                                        ])
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        @include('components.buttons.button', [
                                            'label' => 'Save',
                                            'onclick' => 'addVariant();',
                                        ])
                                    </div>
                                </div>
                            </form>

                            <hr>

                            <div class="row">
                                {{-- Product variants --}}
                                <div class="col-md-2">
                                    @include('components.buttons.button', [
                                        'label' => 'Product variants',
                                        'onclick' => 'onProductVariants();',
                                    ]) {{-- Product variants  --}}
                                </div>
                                {{-- Recommended products --}}
                                <div class="col-md-2">
                                    @include('components.buttons.button', [
                                        'label' => 'Recommended products',
                                        'onclick' => 'onRecommendedProducts();',
                                    ]) {{-- Recommended products  --}}
                                </div>
                            </div>

                            <!-- Product Variants -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div id="productVariantsItems" class="col-md-12" hidden
                                        style="border-color: #2aabd2; border-style: dashed; border-width: 3px; padding: 15px;">
                                        <div class="card-header">
                                            <h5 class="card-title">Product Variants</h5>
                                        </div>
                                        <div class="col-md-12">
                                            <table class="table table-striped">
                                                <thead class="text-primary">
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Image</th>
                                                        <th>Price</th>
                                                        <th>Discount Price</th>
                                                        <th>Updated At</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="pv_table_body">
                                                    {{-- product variant body --}}
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    @include('components.input-field.text', [
                                                        'label' => 'Name',
                                                        'text' => 'Insert Name',
                                                        'id' => 'pv_name',
                                                        'request' => 'true',
                                                        'maxlength' => '40',
                                                    ]) {{-- Name - Insert Name --}}
                                                    @include('components.input-field.price', [
                                                        'label' => 'Insert Price',
                                                        'text' => 'Insert Price',
                                                        'id' => 'pv_price',
                                                        'request' => 'true',
                                                    ]) {{-- Price - Insert Price --}}
                                                    @include('components.input-field.price', [
                                                        'label' => 'Discount Price',
                                                        'text' => 'Insert Discount Price',
                                                        'id' => 'pv_discountprice',
                                                        'request' => 'false',
                                                    ]) {{-- Discount Price - Insert Discount Price --}}
                                                </div>

                                                <div class="col-md-6">
                                                    @include('components.input-field.image', [])
                                                </div>
                                            </div>
                                            <div class="row">
                                                {{-- Variant Branch Insert Form --}}
                                                <div class="col-md-12">
                                                    {{-- @if (count($util->getBranches()) != 0)
                                                    @endif --}}
                                                    @include(
                                                        'components.input-field.dropdown-with-table',
                                                        [
                                                            'label' => 'Select Branch',
                                                            'onchange' => '',
                                                            'id' => 'variant_branch',
                                                            'request' => 'true',
                                                            'noitem' => 'true',
                                                        ]
                                                    ) {{-- Select branch --}}
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12">
                                                    @include('components.buttons.button', [
                                                        'label' => 'Save',
                                                        'onclick' => 'addVariant();',
                                                    ]) {{-- Add new variant  --}}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recommended Products -->
                            <div class="row">
                                <div class="col-12">
                                    <div id="recommendedProductsItems" class="col-12" hidden
                                        style="border-color: #2aabd2; border-style: dashed; border-width: 3px; padding: 15px;">
                                        <div class="card-header">
                                            <h5 class="card-title">Recommended Products</h5>
                                        </div>
                                        <div class="col-md-12">
                                            <table class="table table-striped">
                                                <thead class="text-primary">
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Image</th>
                                                        <th>Price</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="rp_table_body">
                                                    {{-- recommened products --}}
                                                </tbody>
                                            </table>
                                            <div class="row align-items-end">
                                                <div class="col-12 col-md-8">
                                                    @include(
                                                        'components.input-field.dropdown-with-image',
                                                        [
                                                            'label' => 'Product',
                                                            'onchange' => '',
                                                            'id' => 'productForList',
                                                            'request' => 'true',
                                                            'noitem' => 'false',
                                                        ]
                                                    ) {{-- Product --}}
                                                </div>
                                                <div class="col-12 col-md-4">
                                                    @include('components.buttons.button', [
                                                        'label' => 'Add product',
                                                        'onclick' => 'addRProduct();',
                                                    ]) {{-- Add product  --}}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Tab View - Create -->

                    <!-- Tab View - Edit -->
                    <div role="tabpanel" class="tab-pane" id="edit">

                    </div>
                    <!-- End Tab View - Edit -->
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let productType = 0;
    let productVariantType = 0;
    {{-- let is_emptyBranch = {!! json_encode($util->getBranches()) !!}.length == 0 ? true : false;  --}}
    let is_emptyBranch = false;

    document.getElementById("featureLimit").style.display = "none";

    // product stock/non-stock type
    $("#pnon_stock, #pstock").click(function() {
        var clicked = $(this).attr('id');
        if (clicked == 'pnon_stock') {
            // If there are no branches
            if (is_emptyBranch) {
                document.getElementById("empty-branch").hidden = true;
            }
            // Non stock product type
            productType = 0;
            $('.element_product_branch').each(function() {
                $(this).find('.quantity').prop('disabled', true);
                $(this).find('.minQuantity').prop('disabled', true);
                $(this).find('.maxQuantity').prop('disabled', true);
            });
            if (document.getElementById(`element_product_branch`)) {
                document.getElementById(`element_product_branch`).hidden = true;
            }
            if (document.getElementById(`element_product_ingredient`)) document.getElementById(
                `element_product_ingredient`).hidden = false;
        }

        if (clicked == 'pstock') {
            // If there are no branches
            if (is_emptyBranch) {
                document.getElementById("empty-branch").hidden = false;
                return;
            }
            // Stock product type
            productType = 1;
            $('.element_product_branch').each(function() {
                $(this).find('.quantity').prop('disabled', false);
                $(this).find('.minQuantity').prop('disabled', false);
                $(this).find('.maxQuantity').prop('disabled', false);
            });
            if (document.getElementById(`element_product_branch`)) {
                document.getElementById(`element_product_branch`).hidden = false;
            }
            if (document.getElementById(`element_product_ingredient`)) document.getElementById(
                `element_product_ingredient`).hidden = true;
            // alse selects stock for product variant
            $("#pvstock").click();
        }
    });
    // product variant stock/non-stock type
    $("#pvnon_stock, #pvstock").click(function() {
        var clicked = $(this).attr('id');
        if (clicked == 'pvnon_stock') {
            // Non stock product variant type
            productVariantType = 0;
            $('.element_variant_branch').each(function() {
                $(this).find('.quantity').prop('disabled', true);
                $(this).find('.minQuantity').prop('disabled', true);
                $(this).find('.maxQuantity').prop('disabled', true);
            });
            if (document.getElementById(`element_variant_branch`)) {
                document.getElementById(`element_variant_branch`).hidden = true;
            }
        }
        if (clicked == 'pvstock') {
            // Stock product variant type
            productVariantType = 1;
            $('.element_variant_branch').each(function() {
                $(this).find('.quantity').prop('disabled', false);
                $(this).find('.minQuantity').prop('disabled', false);
                $(this).find('.maxQuantity').prop('disabled', false);
            });
            if (document.getElementById(`element_variant_branch`)) {
                document.getElementById(`element_variant_branch`).hidden = false;
            }
        }
    });

    // Views
    $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
        var target = $(e.target).attr("href");
        // console.log(target);
        if (target == "#edit") {
            alert('edirrrrrrrt')
            document.getElementById("tabEdit").style.display = "block";
            $('.nav-tabs a[href="#edit"]').tab('show');
            //
            var target = document.getElementById("form");
            document.getElementById('editForm').appendChild(target);
            // document.getElementById("tabEdit").style.display = "none";
        }
        if (target == "#create") {
            // Clear form
            clearForm();
            // Remove current product branches
            $('tbody .branch_element_product_branch').remove();
            // Remove current vairant branches
            $('tbody .branch_element_variant_branch').remove();
            // All branches
            {{-- let allBranches = {!! json_encode($util->getBranches()) !!}; --}}
            let allBranches = [];
            // options list
            let options = [];
            // Branches
            allBranches.forEach(data => {
                options.push(
                    `<option id="${data.id}" value="${data.id}">${data.name}</option>`
                );
            });
            // Branch lists
            let ptr = `
                <tr class="branch branch_element_product_branch">` +
                `<td>` +
                `<select name="branchName" id="branchName" class="branchNames branchNames_product_branch q-form-s show-tick q-radius">` +
                `<option value="0">--Please choose a branch--</option>` +
                `${options}` +
                `</select>` +
                `</td>` +
                `<td><input type="number" name="quantity" id="quantity" class="quantity q-form"></td>` +
                `<td><input type="number" name="minQuantity" id="minQuantity" class="minQuantity q-form"></td>` +
                `<td><input type="number" name="maxQuantity" id="maxQuantity" class="maxQuantity q-form"></td>` +
                `<td><button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button></td>` +
                `</tr>`;
            let vtr = `
                <tr class="branch branch_element_variant_branch">` +
                `<td>` +
                `<select name="branchName" id="branchName" class="branchNames branchNames_variant_branch q-form-s show-tick q-radius">` +
                `<option value="0">--Please choose a branch--</option>` +
                `${options}` +
                `</select>` +
                `</td>` +
                `<td><input type="number" name="quantity" id="quantity" class="quantity q-form"></td>` +
                `<td><input type="number" name="minQuantity" id="minQuantity" class="minQuantity q-form"></td>` +
                `<td><input type="number" name="maxQuantity" id="maxQuantity" class="maxQuantity q-form"></td>` +
                `<td><button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button></td>` +
                `</tr>`;
            // For product
            $('tbody#branchBody_product_branch').append(ptr);
            // For variant
            $('tbody#branchBody_variant_branch').append(vtr);

            // Feature products count
            {{-- let featuresCount = {!! json_encode($product->featuredProducts()) !!}; --}}
            let featuresCount = [];
            if (featuresCount == 3) {
                document.getElementById("featureLimit").style.display = "flex";
                onSetCheck_featured(0);
            } else
                document.getElementById("featureLimit").style.display = "none";

            document.getElementById('createForm').appendChild(document.getElementById("form"));
        }
        if (target == "#home")
            clearForm();
    });

    let cacheRProducts = [];
    // $('.show-tick').selectpicker('refresh'); OLD

    function addRProduct() {
        var rp = $('select[id=productForList]').val();
        if (rp == 0)
            return;
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ url('rProductAdd') }}',
            data: {
                id: editId,
                rp: rp,
            },
            success: function(data) {
                // console.log("addRProduct");
                // console.log(data);
                if (data.error != "0" || data.data == null)
                    return showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                        ""); // Something went wrong
                if (editId == 0) {
                    if (data.data.length != 0) {
                        // Checking whether the new item exists
                        const newItemCheck = (element) => element.id == rp;
                        if (!cacheRProducts.some(newItemCheck)) {
                            cacheRProducts.push({
                                id: rp,
                                name: data.data[0].name,
                                image: data.data[0].image,
                                price: data.data[0].price,
                            });
                        } else {
                            return showNotification("bg-teal", "Already added", "top", "right");
                        }
                        // console.log("cacheRProducts");
                        // console.log(cacheRProducts);
                        loadAllRProducts(cacheRProducts);
                        return;
                    }
                }
                loadAllRProducts(data.data);
            },
            error: function(e) {
                showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                    ""); // Something went wrong
                // console.log(e);
            }
        });
    }

    function deleteRProducts(id) {
        swal({
            title: "Are you sure?",
            text: "You will not be able to recover this item!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, delete it!",
            cancelButtonText: "No, cancel please!",
            closeOnConfirm: true,
            closeOnCancel: true
        }, function(isConfirm) {
            if (isConfirm) {
                $.ajax({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                    },
                    type: 'POST',
                    url: '{{ url('rProductsDelete') }}',
                    data: {
                        id: id,
                        parent: editId,
                    },
                    success: function(data) {
                        // console.log(data);
                        if (data.error != "0" || data.data == null) {
                            if (data.error == "1")
                                return showNotification("bg-red", data.text, "bottom", "center", "",
                                    ""); // demo mode
                            return showNotification("bg-red", "Something went wrong", "bottom",
                                "center", "", ""); // Something went wrong
                        }
                        // Removing duplicate id
                        if (editId == 0) {
                            for (var i = cacheRProducts.length; i--;) {
                                if (cacheRProducts[i].id == id) {
                                    cacheRProducts.splice(i, 1);
                                }
                            }
                            loadAllRProducts(data.data);
                            return;
                        }
                        loadAllRProducts(data.data);
                    },
                    error: function(e) {
                        showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                            ""); // Something went wrong
                        // console.log(e);
                    }
                });
            } else {

            }
        });
    }

    function loadAllRProducts(data) {
        var text = "";
        data.forEach(function(item, i, arr) {
            text += `
                <tr>
                    <td>${item.name}</td>
                    <td><img src="images/${item.image}" height="100px" ></td>
                    <td>${item.price}</td>
                    <td>
                        <button type="button" class="q-btn-all q-color-alert waves-effect" onclick="deleteRProducts('${item.id}')">
                            <div>Delete</div> {{-- Delete --}}
                        </button>
                    </td>
                </tr>
                `
        });
        document.getElementById("rp_table_body").innerHTML = text;
    }

    let productVariants = false;

    function onProductVariants() {
        productVariants = !productVariants;
        if (productVariants)
            document.getElementById("productVariantsItems").hidden = false;
        else
            document.getElementById("productVariantsItems").hidden = true;
    }

    var recommendedProducts = false;

    function onRecommendedProducts() {
        recommendedProducts = !recommendedProducts;
        if (recommendedProducts)
            document.getElementById("recommendedProductsItems").hidden = false;
        else
            document.getElementById("recommendedProductsItems").hidden = true;
    }

    let cacheVariant = [];

    function addVariant() {
        var pv_name = document.getElementById("pv_name").value;
        var pv_price = document.getElementById("pv_price").value;
        if (pv_name === "")
            return showNotification("bg-red", "The Name field is required", "bottom", "center", "",
                ""); // The Name field is required.
        if (pv_price == "")
            return showNotification("bg-red", "The Price field is required", "bottom", "center", "",
                ""); // The Price field is required.
        // Stock Item
        if (productVariantType)
            checkDublicateBranch(1, 'variant_branch');
        if (dublicateBranch) {
            return;
        }
        // product variant branch
        let variantBranchData = [];
        // check product variant branch field error state
        let checkVariant = 1;
        // Stock product variant type
        if (productVariantType == 1) {
            $('.branch_element_variant_branch').each(function() {
                // $(this).find('.branchNames').val() ? $(this).find('.branchNames').val() : $(this).find('.branchNames option').val();
                var branch_id = $(this).find('.branchNames :selected').attr("value");
                var quantity = $(this).find('.quantity').val() ?? 0;
                var minQuantity = $(this).find('.minQuantity').val() ?? 0;
                var maxQuantity = $(this).find('.maxQuantity').val() ?? 0;
                if (branch_id && quantity && minQuantity) {
                    variantBranchData.push({
                        "branch_id": parseInt(branch_id),
                        "quantity": parseInt(quantity),
                        "minQuantity": parseInt(minQuantity),
                        "maxQuantity": parseInt(maxQuantity)
                    });
                    $(this).css("outline", "0px solid #fff");
                    $(this).css("border-radius", "0px");
                } else {
                    checkVariant = 0;
                    $(this).css("outline", "medium solid #ff674c");
                    $(this).css("border-radius", "4px");
                    if (!branch_id) showNotification("bg-red", "Select branch name", "bottom", "center", "",
                        ""); // Check branch fields
                    if (!quantity) showNotification("bg-red", "Add quantity to \"" + $(this).find(
                            '.branchNames :selected').text() + "\" branch", "bottom", "center", "",
                        ""); // Check branch fields
                    if (!minQuantity) showNotification("bg-red", "Add minimum quantity to  \"" + $(this).find(
                            '.branchNames :selected').text() + "\" branch", "bottom", "center", "",
                        ""); // Check branch fields
                    if (!maxQuantity) showNotification("bg-red", "Add maximum quantity to  \"" + $(this).find(
                            '.branchNames :selected').text() + "\" branch", "bottom", "center", "",
                        ""); // Check branch fields
                    return;
                }
            });
        } else {
            // Non stock product type
            variantBranchData.push({
                "branch_id": 0,
                "quantity": 0,
                "minQuantity": 0,
                "maxQuantity": 0
            });
        }
        if (productVariantType == 1 && variantBranchData.length == 0) {
            return showNotification("bg-red", "The Branch field is required", "bottom", "center", "",
                ""); // The Branch field is required.
        }
        if (!checkVariant)
            return;
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ url('productVariantsAdd') }}',
            data: {
                id: editId,
                variantId: variantId,
                name: pv_name,
                price: pv_price,
                dprice: document.getElementById("pv_discountprice").value,
                imageid: imageid3,
                branchData: variantBranchData,
                variantType: productVariantType
            },
            success: function(data) {
                if (data.error != "0" || data.data == null)
                    return showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                        ""); // Something went wrong
                variantId = 0;
                if (editId == 0) {
                    if (data.data.length != 0) {
                        cacheVariant.push({
                            name: data.data.name,
                            price: data.data.price,
                            cprice: data.data.cprice,
                            dprice: data.data.dprice,
                            cdprice: data.data.cdprice,
                            image: data.data.image,
                            imageid: data.data.imageid,
                            timeago: data.data.timeago,
                            variantType: data.data.variantType,
                            branchData: data.data.branchData
                        });
                        // console.log("cacheVariant");
                        // console.log(cacheVariant);
                        loadAllVariants(cacheVariant);
                        return;
                    }
                }
                loadAllVariants(data.data);
                // Clearing variant form
                document.getElementById("pv_name").value = "";
                document.getElementById("pv_price").value = "";
                document.getElementById("pv_discountprice").value = "";
                document.getElementById(`pvnon_stock`).click();
                resetButtonView('variant_branch', 0);
                editBranches_variant_branch = [];
                clearDropZone3();
                return showNotification("bg-teal", "Data saved", "top", "right");
            },
            error: function(e) {
                showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                    ""); // Something went wrong
                // console.log(e);
            }
        });
    }

    let variantId = 0;
    let editBranches_variant_branch;

    function editVariableItem(id) {
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ url('productVariantsInfo') }}',
            data: {
                id: id,
            },
            success: function(data) {
                // console.log("edit variant response: ", data);
                if (data.error != "0" || data.data == null)
                    return showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                        ""); // Something went wrong
                variantId = data.data.id;
                // branch reset button
                resetButtonView('variant_branch', 1);
                document.getElementById("pv_name").value = data.data.name;
                document.getElementById("pv_price").value = data.data.price;
                if (data.data.dprice != '0.00')
                    document.getElementById("pv_discountprice").value = data.data.dprice;
                addEditImage3(data.data.images_files.id, data.data.images_files.filename);
                productVariantType = data.data.stockItem;
                if (data.data.stockItem == 0) {
                    // Non stock product type
                    document.getElementById(`pvnon_stock`).checked = true;
                    document.getElementById(`element_variant_branch`).hidden = true;
                } else if (data.data.stockItem == 1) {
                    // Stock product type
                    document.getElementById(`pvstock`).checked = true;
                    document.getElementById(`element_variant_branch`).hidden = false;
                }
                let branches = data.data.editBranches;
                // All branches
                {{-- let allBranches = {!! json_encode($util->getBranches()) !!}; --}}
                let allBranches = [];
                let options = [];
                allBranches.forEach(data => {
                    options.push(
                        `<option id="${data.id}" value="${data.id}">${data.name}</option>`
                    );
                });
                $('tbody .branch_element_variant_branch').remove();
                let ptr = '';
                if (branches.length != 0) {
                    editBranches_variant_branch = branches;
                    branches.forEach(data => {
                        let option = '';
                        allBranches.forEach(data1 => {
                            if (data1.id != data.id) {
                                option +=
                                    `<option id="${data1.id}" value="${data1.id}">${data1.name}</option>`;
                            } else {
                                option +=
                                    `<option id="${data.id}" value="${data.id}" selected="selected">${data.name}</option>`;
                            }
                        });
                        ptr = `
                            <tr class="branch branch_element_variant_branch">` +
                            `<td>` +
                            `<select name="branchName" id="branchName" class="branchNames branchNames_variant_branch q-form">` +
                            `<option value="0">--Please choose a branch--</option>` +
                            `${option}` +
                            `</select>` +
                            `</td>` +
                            `<td><input type="number" name="quantity" id="quantity" value="${data.quantity}" class="quantity q-form"></td>` +
                            `<td><input type="number" name="minQuantity" id="minQuantity" value="${data.minquantity}" class="minQuantity q-form"></td>` +
                            `<td><input type="number" name="maxQuantity" id="maxQuantity" value="${data.maxquantity}" class="maxQuantity q-form"></td>` +
                            `<td><button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button></td>` +
                            `</tr>`;
                        $('tbody#branchBody_variant_branch').append(ptr);
                    });
                } else {
                    ptr = `
                        <tr class="branch branch_element_variant_branch">` +
                        `<td>` +
                        `<select name="branchName" id="branchName" class="branchNames branchNames_variant_branch q-form">` +
                        `<option value="0">--Please choose a branch--</option>` +
                        `${options}` +
                        `</select>` +
                        `</td>` +
                        `<td><input type="number" name="quantity" id="quantity" class="quantity q-form"></td>` +
                        `<td><input type="number" name="minQuantity" id="minQuantity" class="minQuantity q-form"></td>` +
                        `<td><input type="number" name="maxQuantity" id="maxQuantity" class="maxQuantity q-form"></td>` +
                        `<td><button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button></td>` +
                        `</tr>`;
                    $('tbody#branchBody_variant_branch').append(ptr);
                }
            },
            error: function(e) {
                showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                    ""); // Something went wrong
                // console.log(e);
            }
        });
    }

    function loadAllVariants(data) {
        var text = "";
        data.forEach(function(item, i, arr) {
            text += `
                <tr>
                    <td>${item.name}</td>
                    <td><img src="images/${item.image}" height="100px" ></td>
                    <td>${item.price}</td>
                    <td>${item.dprice}</td>
                    <td>${item.timeago}</td>
                    <td>
                        <button type="button" class="q-btn-all btn-primary waves-effect" onclick="editVariableItem('${item.id}')">
                            <div>Edit</div> {{-- Edit --}}
                        </button>
                        <button type="button" class="q-btn-all q-color-alert waves-effect" onclick="deleteVariableItem('${item.id}')">
                            <div>Delete</div> {{-- Delete --}}
                        </button>
                    </td>
                </tr>
                `
        });
        document.getElementById("pv_table_body").innerHTML = text;
    }

    function deleteVariableItem(id) {
        swal({
            title: "Are you sure?",
            text: "You will not be able to recover this item!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, delete it!",
            cancelButtonText: "No, cancel please!",
            closeOnConfirm: true,
            closeOnCancel: true
        }, function(isConfirm) {
            if (isConfirm) {
                $.ajax({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                    },
                    type: 'POST',
                    url: '{{ url('productVariantsDelete') }}',
                    data: {
                        id: id,
                        parent: editId,
                    },
                    success: function(data) {
                        // console.log(data);
                        if (data.error != "0" || data.data == null) {
                            if (data.error == "1")
                                return showNotification("bg-red", data.text, "bottom", "center", "",
                                    ""); // demo mode
                            return showNotification("bg-red", "Something went wrong", "bottom",
                                "center", "", ""); // Something went wrong
                        }
                        loadAllVariants(data.data);
                    },
                    error: function(e) {
                        showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                            ""); // Something went wrong
                        // console.log(e);
                    }
                });
            } else {

            }
        });
    }

    let editId = 0;
    let editBranches_product_branch;

    function editItem(id) {
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ url('productGetInfo') }}',
            data: {
                id: id,
            },
            success: function(data) {
                // console.log("Test on edit: ", data);
                if (data.error != "0" || data.data == null)
                    return showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                        ""); // Something went wrong
                document.getElementById("tabEdit").style.display = "block";
                $('.nav-tabs a[href="#edit"]').tab('show');
                //
                var target = document.getElementById("form");
                document.getElementById('editForm').appendChild(target);
                //
                document.getElementById("name").value = data.data.name;
                editId = data.data.id;
                // branch reset button show = true
                resetButtonView('product_branch', 1);
                onSetCheck_visible(data.data.published);
                onSetCheck_featured(data.data.featured);
                if (data.data.featuredFull == "none") {
                    document.getElementById("featureLimit").style.display = "flex";
                } else {
                    document.getElementById("featureLimit").style.display = "none";
                }
                // Only for vendor type 2
                // product ingredient
                let productIngredient = data.data.productIngredient;
                productIngredient.forEach(function(item) {
                    // console.log("its products ingredient: ", item, item.id, item.name);
                    $('#product_ingredient').multiSelect('select', item.name);
                });
                document.getElementById("price").value = data.data.price;
                $('#brand').val(data.data.brand).change();
                // parent
                // nutrition
                $('#nutrition').val(data.data.nutritions).change();
                $('#extras').val(data.data.extras).change();
                // $('.show-tick').selectpicker('refresh');
                // branch
                let branchNames = data.data.editBranchNames;
                let notAddedBranches = data.data.notAddedBranches;
                let branches = data.data.branches;
                // console.log('branchNames', branchNames)
                let lastSelected = '';

                productType = data.data.stockItem;
                if (data.data.stockItem == 0) {
                    // Non stock product type
                    document.getElementById(`pnon_stock`).checked = true;
                    document.getElementById(`element_product_branch`).hidden = true;
                    if (document.getElementById(`element_product_ingredient`)) document.getElementById(
                        `element_product_ingredient`).hidden = false;
                } else if (data.data.stockItem == 1) {
                    // Stock product type
                    document.getElementById(`pstock`).checked = true;
                    document.getElementById(`element_product_branch`).hidden = false;
                    if (document.getElementById(`element_product_ingredient`)) document.getElementById(
                        `element_product_ingredient`).hidden = true;
                }
                // All branches
                {{-- let allBranches = {!! json_encode($util->getBranches()) !!}; --}}
                let allBranches = [];
                let options = [];
                allBranches.forEach(data => {
                    options.push(
                        `<option id="${data.id}" value="${data.id}">${data.name}</option>`
                    );
                });
                $('tbody .branch_element_product_branch').remove();
                let ptr = '';
                // `<option id="0" value="0">--Please choose a branch--</option>` +
                //                         `<option id="${data.id}" value="${data.id}" selected="selected">${data.name}</option>` +
                if (branchNames.length != 0) {
                    editBranches_product_branch = branchNames;
                    branchNames.forEach(data => {
                        let option = '';
                        allBranches.forEach(data1 => {
                            if (data1.id != data.id) {
                                option +=
                                    `<option id="${data1.id}" value="${data1.id}">${data1.name}</option>`;
                            } else {
                                option +=
                                    `<option id="${data.id}" value="${data.id}" selected="selected">${data.name}</option>`;
                            }
                        });
                        ptr = `
                            <tr class="branch branch_element_product_branch">` +
                            `<td>` +
                            `<select name="branchName" id="branchName" class="branchNames branchNames_product_branch q-form">` +
                            `<option value="0">--Please choose a branch--</option>` +
                            `${option}` +
                            `</select>` +
                            `</td>` +
                            `<td><input type="number" name="quantity" id="quantity" value="${data.quantity}" class="quantity q-form"></td>` +
                            `<td><input type="number" name="minQuantity" id="minQuantity" value="${data.minquantity}" class="minQuantity q-form"></td>` +
                            `<td><input type="number" name="maxQuantity" id="maxQuantity" value="${data.maxquantity}" class="maxQuantity q-form"></td>` +
                            `<td><button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button></td>` +
                            `</tr>`;
                        $('tbody#branchBody_product_branch').append(ptr);
                        // document.getElementById('branchNames_product_branch')
                        // selectedIndex
                        // $('.branchNames_product_branch option').val(data.id).change();
                    });

                    // For Object Type
                    // for (const key in notAddedBranches) {
                    //     if (notAddedBranches.hasOwnProperty(key)) {
                    //         var trNotAdded =
                    //             `<option id="${notAddedBranches[key].id}" value="${notAddedBranches[key].id}">${notAddedBranches[key].name}</option>`;
                    //         $('.branchNames_product_branch').append(trNotAdded);
                    //         // $('#branchName').append($('<option>', {
                    //         //     id: notAddedBranches[key].id,
                    //         //     value: notAddedBranches[key].id,
                    //         //     text : notAddedBranches[key].name
                    //         // }));
                    //     }
                    // }

                    // For Array Type
                    // notAddedBranches.forEach(data => {
                    //     var trNotAdded = `<option id="${data.id}" value="${data.id}">${data.name}</option>`;
                    //     $('.branchNames').append(trNotAdded);
                    // });
                } else {
                    ptr = `
                        <tr class="branch branch_element_product_branch">` +
                        `<td>` +
                        `<select name="branchName" id="branchName" class="branchNames branchNames_product_branch q-form">` +
                        `<option value="0">--Please choose a branch--</option>` +
                        `${options}` +
                        `</select>` +
                        `</td>` +
                        `<td><input type="number" name="quantity" id="quantity" class="quantity q-form"></td>` +
                        `<td><input type="number" name="minQuantity" id="minQuantity" class="minQuantity q-form"></td>` +
                        `<td><button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button></td>` +
                        `</tr>`;
                    $('tbody#branchBody_product_branch').append(ptr);
                }
                //
                document.getElementById("discountprice").value = data.data.discountprice;
                // document.getElementById("quantity").value = data.data.quantity;
                // document.getElementById("minquantity").value = data.data.minquantity;
                document.getElementById("ingredients").value = data.data.ingredients;
                document.getElementById("desc").value = data.data.desc;
                document.getElementById("unit").value = data.data.unit;
                document.getElementById("package").value = data.data.packageCount;
                document.getElementById("weight").value = data.data.weight;
                //
                addEditImages(data.data.images_files);
                //
                loadAllVariants(data.variants);
                if (data.variants.length != 0) {
                    productVariants = true;
                    document.getElementById("productVariantsItems").hidden = false;
                }
                loadAllRProducts(data.rp)
                if (data.rp.length != 0) {
                    recommendedProducts = true;
                    document.getElementById("recommendedProductsItems").hidden = false;
                }
                // $('.show-tick').selectpicker('refresh');
            },
            error: function(e) {
                showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                    ""); // Something went wrong
                // console.log(e);
            }
        });
    }

    // Adding each branches value
    $('#onSave').click(function() {
        if (!document.getElementById("name").value)
            return showNotification("bg-red", "The Name field is required", "bottom", "center", "",
                ""); // The Name field is required.
        if (!document.getElementById("price").value)
            return showNotification("bg-red", "The Price field is required", "bottom", "center", "",
                ""); // The Price field is required.
        if ($('select[id=parent]').val() == "0")
            return showNotification("bg-red", "The Category field is required", "bottom", "center", "",
                ""); // The parent field is required.
        // if ($('select[id=brand]').val() == "0")
        //     return showNotification("bg-red", "The Brand field is required", "top", "right");  // The Brand field is required.
        // Stock Item
        if (productType)
            checkDublicateBranch(1, 'product_branch');
        if (dublicateBranch) {
            return;
        }
        // return;

        // add branch value to an array
        let branchData = [];
        // check product variant branch field error state
        let checkProduct = 1;
        if (productType == 1) {
            $('.branch_element_product_branch').each(function() {
                // $(this).find('.branchNames').val() ? $(this).find('.branchNames').val() : $(this).find('.branchNames option').val();
                var branch_id = $(this).find('.branchNames :selected').attr("value");
                var quantity = $(this).find('.quantity').val() ?? 0;
                var minQuantity = $(this).find('.minQuantity').val() ?? 0;
                var maxQuantity = $(this).find('.maxQuantity').val() ?? 0;
                // Stock product type
                if (branch_id && quantity && minQuantity && maxQuantity) {
                    branchData.push({
                        "branch_id": parseInt(branch_id),
                        "quantity": parseInt(quantity),
                        "minQuantity": parseInt(minQuantity),
                        "maxQuantity": parseInt(maxQuantity)
                    });
                } else {
                    checkProduct = 0;
                    $(this).css("outline", "medium solid #ff674c");
                    $(this).css("border-radius", "4px");
                    if (!branch_id) showNotification("bg-red", "Select branch name", "bottom", "center",
                        "", ""); // Check branch fields
                    if (!quantity) showNotification("bg-red", "Add quantity to \"" + $(this).find(
                            '.branchNames :selected').text() + "\" branch", "bottom", "center", "",
                        ""); // Check branch fields
                    if (!minQuantity) showNotification("bg-red", "Add minimum quantity to  \"" + $(this)
                        .find('.branchNames :selected').text() + "\" branch", "bottom", "center",
                        "", ""); // Check branch fields
                    if (!maxQuantity) showNotification("bg-red", "Add maximum quantity to  \"" + $(this)
                        .find('.branchNames :selected').text() + "\" branch", "bottom", "center",
                        "", ""); // Check branch fields
                }

                // if ( branchData.length == 0 ) {
                // } else {
                //     branchData.forEach(value => {
                //         console.log($(this).find('.branchNames').val());
                //         if( String(value.branchName) != String(branch) || String(branch) != null ) {
                //             branchData.push({"branchName":branch, "quantity":quantity, "minQuantity":minQuantity});
                //         }
                //     });
                // }
            });
        } else {
            // Non stock product type
            branchData.push({
                "branch_id": 0,
                "quantity": 0,
                "minQuantity": 0,
                "maxQuantity": 0,
            });
        }
        if (productType == 1 && branchData.length == 0) {
            return showNotification("bg-red", "The Branch field is required", "bottom", "center", "",
                ""); // The Branch field is required.
        }
        if (!checkProduct)
            return;
        onSave(branchData);
    });

    function onSave(branchData) {
        let images = [];
        var imageid = 0;
        for (var i = 0; i < imageArray.length; i++) {
            if (i === 0)
                imageid = imageArray[i].id;
            else
                images.push(imageArray[i].id);
        };
        var non_stock = document.getElementById(`pnon_stock`).checked;
        var stock = !is_emptyBranch ? document.getElementById(`pstock`).checked : null;
        // Only for vendor type 2
        let productIngredientList = null;
        productIngredientList = arrSelected_product_ingredient;
        let data = {
            id: editId,
            name: document.getElementById("name").value,
            image: imageid,
            moreimages: images.toString(),
            published: (visible) ? "1" : "0",
            featured: (featured) ? "1" : "0",
            price: document.getElementById("price").value,
            discPrice: document.getElementById("discountprice").value,
            // quantity: document.getElementById("quantity").value,
            // minQuantity: document.getElementById("minquantity").value,
            unit: document.getElementById("unit").value,
            package: document.getElementById("package").value,
            weight: document.getElementById("weight").value,
            desc: document.getElementById("desc").value,
            ingredients: document.getElementById("ingredients").value,
            // Only for vendor type 2
            productIngredient: productIngredientList,
            extras: $('select[id=extras]').val(),
            nutritions: $('select[id=nutrition]').val(),
            parent: $('select[id=parent]').val(),
            brand: $('select[id=brand]').val(),
            cacheRProducts: cacheRProducts,
            cacheVariant: cacheVariant,
            branchData: branchData,
            productType: productType
        };
        // console.log("send data: ", data);
        // if (!document.getElementById("name").value)
        //     return showNotification("bg-red", "The Name field is required", "top", "right");  // The Name field is required.
        // if (!document.getElementById("price").value)
        //     return showNotification("bg-red", "The Price field is required", "top", "right");  // The Price field is required.
        // if ($('select[id=market]').val() == "0")
        //     return showNotification("bg-red", "The Market field is required", "top", "right");  // The Market field is required.
        // if ($('select[id=parent]').val() == "0")
        //     return showNotification("bg-red", "The Category field is required", "top", "right");  // The parent field is required.
        // if ($('select[id=brand]').val() == "0")
        //     return showNotification("bg-red", "The Brand field is required.", "top", "right");  // The Brand field is required.
        // if (!document.getElementById("quantity").value)
        //     return showNotification("bg-red", "top", "right");  // The Quantity field is required.
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ url('productadd') }}',
            data: data,
            success: function(data) {
                if (data.error == "2") {
                    return showNotification("bg-red", data.text, "bottom", "center", "",
                        ""); // Something went wrong
                }
                if (data.error == "3") {
                    return showNotification("bg-red", data.text, "bottom", "center", "",
                        ""); // Something went wrong
                }
                if (data.error != "0" || data.data == null) {
                    return showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                        ""); // Something went wrong
                }
                if (editId != 0) {
                    paginationGoPage(currentPage);
                } else {
                    var text = buildOneItem(data.data);
                    var text2 = document.getElementById("table_body").innerHTML;
                    document.getElementById("table_body").innerHTML = text + text2;
                }
                $('.nav-tabs a[href="#home"]').tab('show');
                showNotification("bg-teal", "Data saved successfully", "bottom", "center", "",
                    ""); // Data saved
                clearForm();
            },
            error: function(e) {
                showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                    ""); // Something went wrong
                // console.log(e);
            }
        });
    }

    function resetButtonView(id, status) {
        let button = document.getElementById(`resetBranch_${id}`);
        if (button) {
            if (status == 1)
                button.style.display = "inline-block";
            else
                button.style.display = "none";
        }
    }

    let dublicateBranch = false;

    function checkDublicateBranch(noNotify, id) {
        // default
        dublicateBranch = false;
        // let select = $(`#branchTable_${id} .branch`).find(`.branchNames_${id}`);
        let select = document.querySelectorAll(`#branchBody_${id} .branch .branchNames`);
        let values = [];
        // Clear outline
        for (i = 0; i < select.length; i++) {
            let el = select[i].options[select[i].selectedIndex].parentElement.parentElement.parentElement;
            el.style.outline = '';
            el.style.borderRadius = "0px";
        }

        // Checking dublicate branch
        for (i = 0; i < select.length; i++) {
            let el = select[i].options[select[i].selectedIndex].parentElement.parentElement.parentElement;
            // var select = select[i];
            if (values.indexOf(select[i].options[select[i].selectedIndex].id) > -1) {
                // Dublicate found
                dublicateBranch = true;
                el.style.outline = 'medium solid #ff674c';
                el.style.borderRadius = "4px";
                if (noNotify != 0) {
                    showNotification("bg-red",
                        "Duplicate branch form entry found. Please remove dublicate branch name: <b>" + select[i]
                        .options[select[i].selectedIndex].text.replace(/^\s+|\s+$/gm, '') + '</b>', "bottom",
                        "center",
                        "", "");
                }
                break;
            } else {
                values.push(select[i].options[select[i].selectedIndex].id);
            }
        };
    }

    // Clear forms
    function clearForm() {
        editId = 0;
        // product
        document.getElementById(`pnon_stock`).click();
        document.getElementById("name").value = "";
        onSetCheck_visible(true);
        document.getElementById("price").value = "";
        document.getElementById("discountprice").value = "";
        document.getElementById("desc").value = "";
        document.getElementById("unit").value = "";
        document.getElementById("package").value = "";
        document.getElementById("weight").value = "";
        document.getElementById("ingredients").value = "";
        $('#parent').val(0).change();
        document.querySelector('#parent').selectedIndex = "0";
        $('#brand').val(0).change();
        document.querySelector('#brand').selectedIndex = "0";
        $('#nutrition').val(0).change();
        $('#extras').val(0).change();
        resetButtonView('product_branch', 0);
        editBranches_product_branch = [];
        // Only for vendor type 2
        arrSelected_product_ingredient = [];
        $('#product_ingredient').multiSelect('deselect_all');
        clearDropZone();

        // product variant
        productVariants = false;
        document.getElementById("productVariantsItems").hidden = true;
        document.getElementById("pv_table_body").innerHTML = "";
        document.getElementById("pv_name").value = "";
        document.getElementById("pv_price").value = "";
        document.getElementById("pv_discountprice").value = "";
        document.getElementById(`pvnon_stock`).click();
        cacheVariant = [];
        resetButtonView('variant_branch', 0);
        editBranches_variant_branch = [];
        arrSelected_variant_ingredient = [];
        clearDropZone3();

        // recommended product
        document.getElementById("rp_table_body").innerHTML = "";
        recommendedProducts = false;
        document.getElementById("recommendedProductsItems").hidden = true;
        cacheRProducts = [];

        //
        $('.branchNames').val('0').change();
        $('#quantity').val("").change();
        $('#minQuantity').val("").change();
        $('#maxQuantity').val("").change();
        // document.getElementById("quantity").value = "";
        // document.getElementById("minquantity").value = "";
    }
</script>
