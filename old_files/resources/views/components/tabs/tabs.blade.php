<div class="relative w-full">
    {{-- Gradient indicators for scroll --}}
    <div
        class="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-white to-transparent pointer-events-none z-10 sm:hidden">
    </div>
    <div
        class="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-white to-transparent pointer-events-none z-10 sm:hidden">
    </div>

    <div class="w-full overflow-x-auto scrollbar-hide pb-2 -mb-2">
        <div
            class="w-full flex justify-start items-center smmd:grid smmd:grid-cols-3 text-sm lg:text-base mx-auto gap-1 md:gap-2 bg-white border px-2 py-4 smmd:py-3 smmd:px-6 lg:py-3 lg:px-8 rounded-3xl smmd:rounded-[42px] lg:rounded-[42px] shadow-md">
            <button data-cat="1"
                class="w-full font-semibold mx-auto py-2 px-4 smmd:px-8 lg:py-4 lg:px-14 rounded-3xl transition-all duration-200 ease-in-out capitalize theme-text-color theme-active-box f-t">
                Available
            </button>
            <button data-cat="2"
                class="w-full font-semibold mx-auto py-2 px-4 smmd:px-8 lg:py-4 lg:px-14 rounded-3xl transition-all duration-200 ease-in-out capitalize theme-text-color theme-inactive-box f-t">
                {{-- Out of Stock --}}
                {{-- Fractional Home Equity --}}
                Exited
            </button>
            <button data-cat="3"
                class="w-full font-semibold mx-auto py-2 px-4 smmd:px-8 lg:py-4 lg:px-14 rounded-3xl transition-all duration-200 ease-in-out capitalize theme-text-color theme-inactive-box f-t">
                {{-- Soon In stock --}}
                Secondary Market
            </button>
        </div>
    </div>
</div>

@push('styles')
    <style>
        /* Hide scrollbar but keep functionality */
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        /* Improve touch handling on mobile */
        .touch-manipulation {
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }

        /* Smooth scrolling for better experience */
        .overflow-x-auto {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }
    </style>
@endpush
