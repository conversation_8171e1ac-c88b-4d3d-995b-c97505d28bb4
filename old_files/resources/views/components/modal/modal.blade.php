<!-- Modal -->
@if (Request::route()->getName() != 'cart-page')
    <!-- The Modal -->
    <div id="myModal" class="modal2">
        <!-- Modal content -->
        <div class="modal2-content">
            <span id="modal2-btn-close" class="modal2-btn-close transition duration-200 ease-in-out">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-14 h-14">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <path d="M14.5 9.50002L9.5 14.5M9.49998 9.5L14.5 14.5" stroke="currentColor" stroke-width="1.5"
                            stroke-linecap="round"></path>
                        <path
                            d="M7 3.33782C8.47087 2.48697 10.1786 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 10.1786 2.48697 8.47087 3.33782 7"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                    </g>
                </svg>
            </span>
            <div class="modal2-content__loading-screen" id="modal2-content__loading-screen">
                <div role="status"
                    class="absolute w-full h-full left-0 top-0 bg-[#80808025] flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-3 h-[60px] w-[60px] text-black" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                            stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                </div>
            </div>
            {{-- modal2-content__image --}}
            {{-- grid grid-rows-[55px_calc(100%_-_165px)_110px] grid-cols-1 --}}
            <div class="w-full h-full border-r max-w-full grid grid-rows-[3rem_calc(100%_-_3rem)] grid-cols-1">
                <!-- Tab -->
                <div class="w-full p-2 tabs" id="modal2-tablist" role="tablist">
                    <div class="inline-block py-1 px-5 border-b-4 cursor-pointer active" id="tab-modal2-image">Image
                    </div>
                    <div class="inline-block py-1 px-5 border-b-4 cursor-pointer" id="tab-modal2-video">Video</div>
                </div>
                <div class="modal-tab" id="modal2-image-view">
                    <div class="w-full max-w-full h-full max-h-[calc(100%_-_7rem)] modal2-content__imageContainer"
                        id="modal2-image">
                    </div>
                    <div class="w-full relative max-w-full h-full max-h-28 py-2 modal2-content__moreImages"
                        id="modal2-moreImages">
                    </div>
                </div>
                <div class="modal-tab" id="modal2-video-view">
                    <div class="grid grid-rows-1 w-full h-full place-items-center">
                        <div class="w-full h-full grid place-items-center">
                            <div class="w-full h-full video-container">
                                <iframe style="width:100%; height:100%;" src="" id=""
                                    allow="accelerometer; autoplay; encrypted-media; gyroscope;" allowfullscreen
                                    frameborder="0">
                                </iframe>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal2-content__content">
                <div class="modal2-content__body">
                    <div class="modal2-content__body--top">
                        <div id="modal2-content__title" class="text-2xl md:text-5xl font-bold"></div>
                        <div id="modal2-content__availability" class="mb-4 flex"></div>
                        <div id="modal2-content__price" class="font-bold mb-4"></div>
                        <div id="modal2-content__desc" class="gray-2-text text-base mb-4"></div>
                        <div id="modal2-content__branch" class="gap-[15px] items-center"></div>
                        <div id="modal2-content__itemCount" class="grap-color text-lg mb-2 font-bold"></div>
                        <div id="modal2-content__variant" class="mb-4"></div>
                    </div>
                    <div class="modal2-content__body--bottom border-t-2">
                        <div id="modal2-content__count"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Slick slider for modal images
        $('#modal2-image').slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            asNavFor: '#modal2-moreImages',
            arrows: false,
            fade: false,
            infinite: false,
            dots: true,
        });

        $('#modal2-moreImages').slick({
            slidesToShow: 4,
            slidesToScroll: 4,
            asNavFor: '#modal2-image',
            arrows: true,
            fade: false,
            infinite: false,
            dots: false,
            centerMode: false,
            focusOnSelect: true,
            prevArrow: `<div class="slick-button slick-prev cursor-pointer">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <path
                            d="M14.2893 5.70708C13.8988 5.31655 13.2657 5.31655 12.8751 5.70708L7.98768 10.5993C7.20729 11.3805 7.2076 12.6463 7.98837 13.427L12.8787 18.3174C13.2693 18.7079 13.9024 18.7079 14.293 18.3174C14.6835 17.9269 14.6835 17.2937 14.293 16.9032L10.1073 12.7175C9.71678 12.327 9.71678 11.6939 10.1073 11.3033L14.2893 7.12129C14.6799 6.73077 14.6799 6.0976 14.2893 5.70708Z"
                            fill="#000000"></path>
                    </g>
                </svg>
            </div>`,
            nextArrow: `<div class="slick-button slick-next cursor-pointer">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <path d="M9 5L11 7.33333M9 19L15 12L13.5 10.25" stroke="currentColor" stroke-width="1.5"
                            stroke-linecap="round" stroke-linejoin="round"></path>
                    </g>
                </svg>
            </div>`,
        });
    </script>
@endif

<script>
    // Modal 2
    let modal = document.getElementById("myModal");

    // Show modal loading popup
    function showModalLoading() {
        let modalLoad = document.getElementById("modal2-content__loading-screen");
        modalLoad.style.display = "block";
    }

    // Hide modal loading popup
    function hideModalLoading() {
        let modalLoad = document.getElementById("modal2-content__loading-screen");
        modalLoad.style.display = "none";
    }

    function hideModal() {
        modal.classList.remove("show");
        let modalViews = document.querySelectorAll('#modal2-image-view, #modal2-video-view');
        modalViews.forEach(function(div) {
            $(div).removeClass("active-tab");
        });
        let modalNav = document.querySelectorAll('#tab-modal2-image, #tab-modal2-video');
        modalNav.forEach(function(div) {
            $(div).removeClass("active");
        });
        document.getElementsByTagName("BODY")[0].style.overflow = "auto";
    }

    function showModal() {
        $('div[id="tab-modal2-image"]').click();
        modal.classList.add("show");
        // modal.style.display = "block";
        document.getElementsByTagName("BODY")[0].style.overflow = "hidden";
    }

    // Modal close button
    let closeButton = document.getElementById("modal2-btn-close");

    // When the user clicks on <span> (x), close the modal
    if (closeButton) {
        closeButton.onclick = function() {
            // Hide modal
            hideModal();
        }
    }

    // Initializing Modal images slick slider
    function initializeModalImages(id1, id2) {
        if (id1)
            $(`#${id1}`).slick({
                slidesToShow: 1,
                slidesToScroll: 1,
                asNavFor: `#${id2}`,
                arrows: false,
                fade: false,
                infinite: false,
                prevArrow: `<div class="slick-button slick-prev cursor-pointer">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M14.2893 5.70708C13.8988 5.31655 13.2657 5.31655 12.8751 5.70708L7.98768 10.5993C7.20729 11.3805 7.2076 12.6463 7.98837 13.427L12.8787 18.3174C13.2693 18.7079 13.9024 18.7079 14.293 18.3174C14.6835 17.9269 14.6835 17.2937 14.293 16.9032L10.1073 12.7175C9.71678 12.327 9.71678 11.6939 10.1073 11.3033L14.2893 7.12129C14.6799 6.73077 14.6799 6.0976 14.2893 5.70708Z"
                                fill="#000000"></path>
                        </g>
                    </svg>
                </div>`,
                nextArrow: `<div class="slick-button slick-next cursor-pointer">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8">
                        <g stroke-width="0"></g>
                        <g stroke-linecap="round" stroke-linejoin="round"></g>
                        <g>
                            <path
                                d="M9.71069 18.2929C10.1012 18.6834 10.7344 18.6834 11.1249 18.2929L16.0123 13.4006C16.7927 12.6195 16.7924 11.3537 16.0117 10.5729L11.1213 5.68254C10.7308 5.29202 10.0976 5.29202 9.70708 5.68254C9.31655 6.07307 9.31655 6.70623 9.70708 7.09676L13.8927 11.2824C14.2833 11.6729 14.2833 12.3061 13.8927 12.6966L9.71069 16.8787C9.32016 17.2692 9.32016 17.9023 9.71069 18.2929Z"
                                fill="#000000"></path>
                        </g>
                    </svg>
                </div>`,
            });

        if (id2)
            $(`#${id2}`).slick({
                slidesToShow: 4,
                slidesToScroll: 1,
                asNavFor: `#${id1}`,
                arrows: true,
                fade: false,
                infinite: false,
                dots: false,
                centerMode: true,
                focusOnSelect: true,
                prevArrow: `<div class="slick-button slick-prev cursor-pointer">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M14.2893 5.70708C13.8988 5.31655 13.2657 5.31655 12.8751 5.70708L7.98768 10.5993C7.20729 11.3805 7.2076 12.6463 7.98837 13.427L12.8787 18.3174C13.2693 18.7079 13.9024 18.7079 14.293 18.3174C14.6835 17.9269 14.6835 17.2937 14.293 16.9032L10.1073 12.7175C9.71678 12.327 9.71678 11.6939 10.1073 11.3033L14.2893 7.12129C14.6799 6.73077 14.6799 6.0976 14.2893 5.70708Z"
                                fill="#000000"></path>
                        </g>
                    </svg>
                </div>`,
                nextArrow: `<div class="slick-button slick-next cursor-pointer">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8">
                        <g stroke-width="0"></g>
                        <g stroke-linecap="round" stroke-linejoin="round"></g>
                        <g>
                            <path
                                d="M9.71069 18.2929C10.1012 18.6834 10.7344 18.6834 11.1249 18.2929L16.0123 13.4006C16.7927 12.6195 16.7924 11.3537 16.0117 10.5729L11.1213 5.68254C10.7308 5.29202 10.0976 5.29202 9.70708 5.68254C9.31655 6.07307 9.31655 6.70623 9.70708 7.09676L13.8927 11.2824C14.2833 11.6729 14.2833 12.3061 13.8927 12.6966L9.71069 16.8787C9.32016 17.2692 9.32016 17.9023 9.71069 18.2929Z"
                                fill="#000000"></path>
                        </g>
                    </svg>
                </div>`,
            });
    }

    // Build select element for address
    const buildSelectElement = (data, productId, id, callBack) => {
        let select = document.createElement("select");
        select.setAttribute("id", id);
        select.setAttribute("name", 'product-address');
        select.classList.add("nice-select");
        // Set branches
        let branches = '';
        data.forEach(branchData => {
            branches += `
                    <option value="${branchData.id}" class="inline-block py-[10px] px-[15px] border border-solid rounded-[32px] cursor-pointer theme-main-bordercolorHover transition duration-200 ease-in-out branch">
                        ${branchData.name}
                    </option>
                `;
        });
        select.innerHTML = `${branches}`;

        let div = document.createElement("div");
        div.setAttribute("name", 'product-address');
        div.classList.add("inline-flex", "flex-nowrap", "items-center", "relative", "custom-select",
            `custom-select-product-${productId}`);

        let p = document.createElement("p");
        p.classList.add("mr-[5px]", "whitespace-nowrap", "select-none");
        const textNode = document.createTextNode("Address: ");
        p.appendChild(textNode);

        div.appendChild(p);
        div.appendChild(select);

        // Add event listener for onchange event
        select.addEventListener("change", function(event) {
            // Get the selected value
            var selectedValue = event.target.value;

            // Call a function or perform any desired action
            // alert(selectedValue);
            // callBack(0, selectedValue, productId);
        });

        return div;
    }

    const initializeAddress = (id, variantId, data) => {
        const addIcon = () => {
            // Arrow Icon
            const iconSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            const iconPath = document.createElementNS(
                'http://www.w3.org/2000/svg',
                'path'
            );

            iconSvg.setAttribute('fill', 'none');
            iconSvg.setAttribute('viewBox', '0 0 24 24');
            iconSvg.setAttribute('stroke', 'currentColor');
            iconSvg.classList.add('w-[25px]');
            iconSvg.classList.add('h-[25px]');

            iconPath.setAttribute(
                'd',
                'M19.5 8.25l-7.5 7.5-7.5-7.5'
            );
            iconPath.setAttribute('stroke-linecap', 'round');
            iconPath.setAttribute('stroke-linejoin', 'round');
            iconPath.setAttribute('stroke-width', '2');

            iconSvg.appendChild(iconPath);
            return iconSvg;
        }

        var x, i, j, l, ll, selElmnt, a, b, c;
        /* Look for any elements with the class "custom-select": */
        x = document.getElementsByClassName(`custom-select-product-${id}`);
        // Set Default Address for add that have only one
        if (data.length == 1) {
            $(document).ready(function() {
                branchSelect(0, data[0].id, id, variantId);
            });
            x[0].innerHTML =
                `<p class="mr-[5px] whitespace-nowrap grap-color text-lg select-none font-bold">Address:</p><span class="text-base"><a target="_blank" href="https://www.google.com/maps/place/${data[0].lat},${data[0].lng}">${data[0].name}</a></span>`;
            return;
        }
        l = x.length;
        for (i = 0; i < l; i++) {
            selElmnt = x[i].getElementsByTagName("select")[0];
            ll = selElmnt.length;
            /* For each element, create a new DIV that will act as the selected item: */
            a = document.createElement("DIV");
            a.setAttribute("class", "select-selected line-clamp-1");
            let arrowDownIcon = addIcon();
            a.innerHTML = selElmnt.options[selElmnt.selectedIndex].innerHTML;
            a.appendChild(arrowDownIcon);
            x[i].appendChild(a);
            /* For each element, create a new DIV that will contain the option list: */
            b = document.createElement("DIV");
            b.setAttribute("class", "select-items");
            for (j = 0; j < ll; j++) {
                /* For each option in the original select element,
                create a new DIV that will act as an option item: */
                c = document.createElement("DIV");
                c.innerHTML = selElmnt.options[j].innerHTML;
                c.setAttribute("data-value", selElmnt.options[j].value);
                if (j == 0) {
                    c.setAttribute("class", "same-as-selected");
                }
                c.addEventListener("click", function(e) {
                    /* When an item is clicked, update the original select box,
                    and the selected item: */
                    var y, i, k, s, h, sl, yl;
                    s = this.parentNode.parentNode.getElementsByTagName("select")[0];
                    sl = s.length;
                    h = this.parentNode.previousSibling;
                    for (i = 0; i < sl; i++) {
                        if (s.options[i].innerHTML == this.innerHTML) {
                            s.selectedIndex = i;
                            h.innerHTML = this.innerHTML;
                            h.appendChild(arrowDownIcon);
                            y = this.parentNode.getElementsByClassName("same-as-selected");
                            yl = y.length;
                            for (k = 0; k < yl; k++) {
                                y[k].removeAttribute("class");
                            }
                            this.setAttribute("class", "same-as-selected");
                            branchSelect(0, this.getAttribute("data-value"), id, variantId);
                            break;
                        }
                    }
                    // h.click();
                });
                b.appendChild(c);
                if (data.length > 1 && j == 0) {
                    $(document).ready(function() {
                        c.click();
                    });
                }
            }
            x[i].appendChild(b);
            if (data.length == 1) {
                $(document).ready(function() {
                    branchSelect(0, data[0].id, id, 0);
                });
            } else {
                a.addEventListener("click", function(e) {
                    /* When the select box is clicked, close any other select boxes,
                    and open/close the current select box: */
                    e.stopPropagation();
                    closeAllSelect(this);
                    // this.nextSibling.classList.toggle("select-hide");
                    if (this.nextSibling.classList.contains("rolldown")) {
                        this.nextSibling.classList.remove("rolldown");
                        this.nextSibling.classList.add("rollup");
                    } else {
                        this.nextSibling.classList.remove("rollup");
                        this.nextSibling.classList.add("rolldown");
                    }
                    this.classList.toggle("select-arrow-active");
                });
            }
        }

        const closeAllSelect = (elmnt) => {
            /* A function that will close all select boxes in the document,
            except the current select box: */
            var x, y, i, xl, yl, arrNo = [];
            x = document.getElementsByClassName("select-items");
            y = document.getElementsByClassName("select-selected");
            xl = x.length;
            yl = y.length;
            for (i = 0; i < yl; i++) {
                if (elmnt == y[i]) {
                    arrNo.push(i)
                } else {
                    y[i].classList.remove("select-arrow-active");
                }
            }
            for (i = 0; i < xl; i++) {
                if (arrNo.indexOf(i)) {
                    // x[i].classList.add("rollup");
                    // x[i].classList.remove("rolldown");
                    // modified
                    if (x[i].classList.contains("rolldown")) {
                        x[i].classList.remove("rolldown");
                        x[i].classList.add("rollup");
                    }
                }
            }
        }

        /* If the user clicks anywhere outside the select box,
        then close all select boxes: */
        document.addEventListener("click", closeAllSelect);
    }

    // Get the button that opens the modal
    function modalBtn(id, cat = 1) {
        // Destroy slick slider
        destorySlick('#modal2-image', '#modal2-moreImages');
        // show modal
        showModal();
        // Show loading screen popup
        showModalLoading();
        let image = document.getElementById('modal2-image');
        let moreImages = document.getElementById('modal2-moreImages');
        let title = document.getElementById('modal2-content__title');
        let availability = document.getElementById('modal2-content__availability');
        let desc = document.getElementById('modal2-content__desc');
        let itemCount = document.getElementById('modal2-content__itemCount');
        let price = document.getElementById('modal2-content__price');
        let branch = document.getElementById('modal2-content__branch');
        let variant = document.getElementById('modal2-content__variant');
        let count = document.getElementById('modal2-content__count');
        let urlParam = new URLSearchParams({
            id: id,
            view: 1,
            cat: cat
        }).toString();
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            type: 'GET',
            url: `{{ route('product.info') }}?${urlParam}`,
            data: {},
            success: function(data) {
                // console.log("modal data: ", data);
                if (data.error || data.data == null) {
                    // Hide loading screen popup
                    hideModalLoading();
                    return Toast.fire({
                        icon: "error",
                        title: "Error",
                        text: 'Something went wrong'
                    });
                }
                modalProduct = data.data;
                // button
                let addToCartButton;
                let addToWishlistButton;
                if (image) {
                    image.innerHTML = ``;
                    moreImages.innerHTML = '';
                    if (data.data.images.length > 0) {
                        data.data.images.forEach((imageData, index) => {
                            image.innerHTML +=
                                ` <div class="w-full h-full flex" style="display: flex;">
                                    <img class="img-contain" src="${imageData.image_path}" alt='${data.data.name} ${index}'>
                                </div>
                            `;
                            moreImages.innerHTML +=
                                `<div class="w-full h-full !flex border mx-2 cursor-pointer"><img class="img-contain" src="${imageData.image_path}" alt='${data.data.name}_small_${index}'></div>`;
                        });
                    }
                }
                // initialize slick slider
                initializeModalImages('modal2-image', 'modal2-moreImages');
                if (data.data.video_link) {
                    loadingIcon('tab-modal2-video', 1);
                    $('iframe').attr('src', data.data.video_link);
                } else {
                    loadingIcon('tab-modal2-video', 0);
                }
                let url = `{{ route('details', ':id') }}`;
                url = url.replace(':id', data.data.id);
                title.innerHTML =
                    `<a href="${url}" class="inline-block line-clamp-1 !leading-normal font-bold text-2xl md:text-5xl theme-main-hovercolor">${data.data.name}</a>`;
                // let stars = '';
                // stars +=
                //     `<div id="modal-rate-icon-${data.data.id}" class="w-full flex mb-2 gap-[5px] items-center justify-start">`;
                // for (var i = 1; i <= data.data.rate; i++) {
                //     if (authCheck) {
                //         stars +=
                //             `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" product="${data.data.id}" viewBox="0 0 24 24" fill="currentColor" class="rating cursor-pointer w-[16px] h-[16px] primary-text-color">`;
                //     } else {
                //         stars +=
                //             `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" viewBox="0 0 24 24" fill="currentColor" class="w-[16px] h-[16px] primary-text-color">`;
                //     }
                //     stars += `<path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                //     </svg>`;
                // }
                // if (i <= 5) {
                //     for (i; i <= 5; i++) {
                //         if (authCheck) {
                //             stars +=
                //                 `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" product="${data.data.id}" viewBox="0 0 24 24" fill="currentColor" class="rating cursor-pointer w-[16px] h-[16px] light-gray-text theme-main-hovercolor ease-in-out duration-150">`;
                //         } else {
                //             stars +=
                //                 `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" viewBox="0 0 24 24" fill="currentColor" class="w-[16px] h-[16px] light-gray-text">`;
                //         }
                //         stars += `<path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                //         </svg>`;
                //     }
                // }
                // if (data.data.rateCustomerCount) {
                //     stars +=
                //         `<span class="gray-2-text text-[11px] leading-normal">(${data.data.rateCustomerCount} Customer Reviews)</span></div>`;
                // }
                // title.innerHTML += stars;
                initializeRateButtons();
                // add to cart button
                addToCartButton = `
                        <button id="add-to-cart-button${data.data.id}" onClick="addToCart(${data.data.id},${data.data.branch_id},${0},${1},${data.data.soon_instock == 1 ? 2 : 0});" class="inline-flex rounded-full gap-[8px] items-center justify-center w-full text-center px-9 py-3 z-20 cursor-pointer theme-borderradius text-white bg-black text-white text-sm font-bold transition-all duration-200 ease-in-out uppercase hover:opacity-60 btn-addtocart">
                            <span>Add To Cart</span>
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" height="20px" width="20px" class="w-[25px] h-[25px]">
                                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                <g id="SVGRepo_iconCarrier">
                                    images           <path d="M7.5 18C8.32843 18 9 18.6716 9 19.5C9 20.3284 8.32843 21 7.5 21C6.67157 21 6 20.3284 6 19.5C6 18.6716 6.67157 18 7.5 18Z" stroke="currentColor" stroke-width="1.5"></path>
                                    <path d="M16.5 18.0001C17.3284 18.0001 18 18.6716 18 19.5001C18 20.3285 17.3284 21.0001 16.5 21.0001C15.6716 21.0001 15 20.3285 15 19.5001C15 18.6716 15.6716 18.0001 16.5 18.0001Z" stroke="currentColor" stroke-width="1.5"></path>
                                    <path d="M13 13V11M13 11V9M13 11H15M13 11H11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                    <path d="M2 3L2.26121 3.09184C3.5628 3.54945 4.2136 3.77826 4.58584 4.32298C4.95808 4.86771 4.95808 5.59126 4.95808 7.03836V9.76C4.95808 12.7016 5.02132 13.6723 5.88772 14.5862C6.75412 15.5 8.14857 15.5 10.9375 15.5H12M16.2404 15.5C17.8014 15.5 18.5819 15.5 19.1336 15.0504C19.6853 14.6008 19.8429 13.8364 20.158 12.3075L20.6578 9.88275C21.0049 8.14369 21.1784 7.27417 20.7345 6.69708C20.2906 6.12 18.7738 6.12 17.0888 6.12H11.0235M4.95808 6.12H7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                </g>
                            </svg>
                        </button>
                    `;
                // add to wishlist button
                addToWishlistButton = `
                        <button id="wishlistM-${data.data.id}" onclick="addToWishList({ id: ${data.data.id}, branch_id: ${data.data.branch_id} });" class="group-theme inline-flex text-center p-3 cursor-pointer theme-borderradius theme-main-hovercolor bg-gray-100 w-[48px] h-[48px] leading-[48px] btn-addtowish transition duration-200 ease-in-out">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 m-auto">
                                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                <g id="SVGRepo_iconCarrier">
                                    <path
                                        d="M8.96173 18.9109L9.42605 18.3219L8.96173 18.9109ZM12 5.50063L11.4596 6.02073C11.601 6.16763 11.7961 6.25063 12 6.25063C12.2039 6.25063 12.399 6.16763 12.5404 6.02073L12 5.50063ZM15.0383 18.9109L15.5026 19.4999L15.0383 18.9109ZM7.00061 16.4209C6.68078 16.1577 6.20813 16.2036 5.94491 16.5234C5.68169 16.8432 5.72758 17.3159 6.04741 17.5791L7.00061 16.4209ZM2.34199 13.4115C2.54074 13.7749 2.99647 13.9084 3.35988 13.7096C3.7233 13.5108 3.85677 13.0551 3.65801 12.6917L2.34199 13.4115ZM2.75 9.1371C2.75 6.98623 3.96537 5.18252 5.62436 4.42419C7.23607 3.68748 9.40166 3.88258 11.4596 6.02073L12.5404 4.98053C10.0985 2.44352 7.26409 2.02539 5.00076 3.05996C2.78471 4.07292 1.25 6.42503 1.25 9.1371H2.75ZM8.49742 19.4999C9.00965 19.9037 9.55954 20.3343 10.1168 20.6599C10.6739 20.9854 11.3096 21.25 12 21.25V19.75C11.6904 19.75 11.3261 19.6293 10.8736 19.3648C10.4213 19.1005 9.95208 18.7366 9.42605 18.3219L8.49742 19.4999ZM15.5026 19.4999C16.9292 18.3752 18.7528 17.0866 20.1833 15.4758C21.6395 13.8361 22.75 11.8026 22.75 9.1371H21.25C21.25 11.3345 20.3508 13.0282 19.0617 14.4798C17.7469 15.9603 16.0896 17.1271 14.574 18.3219L15.5026 19.4999ZM22.75 9.1371C22.75 6.42503 21.2153 4.07292 18.9992 3.05996C16.7359 2.02539 13.9015 2.44352 11.4596 4.98053L12.5404 6.02073C14.5983 3.88258 16.7639 3.68748 18.3756 4.42419C20.0346 5.18252 21.25 6.98623 21.25 9.1371H22.75ZM14.574 18.3219C14.0479 18.7366 13.5787 19.1005 13.1264 19.3648C12.6739 19.6293 12.3096 19.75 12 19.75V21.25C12.6904 21.25 13.3261 20.9854 13.8832 20.6599C14.4405 20.3343 14.9903 19.9037 15.5026 19.4999L14.574 18.3219ZM9.42605 18.3219C8.63014 17.6945 7.82129 17.0963 7.00061 16.4209L6.04741 17.5791C6.87768 18.2624 7.75472 18.9144 8.49742 19.4999L9.42605 18.3219ZM3.65801 12.6917C3.0968 11.6656 2.75 10.5033 2.75 9.1371H1.25C1.25 10.7746 1.66995 12.1827 2.34199 13.4115L3.65801 12.6917Z"
                                        fill="currentColor"></path>
                                </g>
                            </svg>
                        </button>
                    `;
                // availability
                availability.innerHTML = `
                        <p>Property Availability:
                            <div id="product-availability-${data.data.id}">
                            </div>
                        </p>
                    `;
                // in stock count
                // console.log('quant: ', data.data, data.data.quantity);
                itemCount.innerHTML = `
                        Availability: <span id="item_count" class="mr-1 font-normal text-black text-base">${0}</span>
                        <br>
                        Minimum purchase quantity: <span id="min_item_count" class="mr-1 font-normal text-black text-base">${0}</span>
                        <br>
                        Maximum purchase quantity: <span id="max_item_count" class="mr-1 font-normal text-black text-base">${0}</span>
                    `;
                // branch
                let branches = ``;
                branches = buildSelectElement(data.data.branches, data.data.id,
                    `product-${data.data.id}-address`, branchSelect);
                // data.data.branches.forEach(branchData => {
                //     branches += `
                //             <div id="branch_id_${branchData.branch_id}" onclick="branchSelect(0, ${branchData.branch_id}, ${branchData.product_id})" class="inline-block py-[10px] px-[15px] border border-solid rounded-[32px] cursor-pointer theme-main-bordercolorHover transition duration-200 ease-in-out branch">
                //                 ${branchData.name}
                //             </div>
                //         `;
                // });
                // branch.innerHTML = `
                //     <div class="gap-[15px] mb-[15px] items-center">
                //         <h4 class="text-[18px] mb-[5px] font-bold">Address:</h4>
                //         <div class="inline-block gap-[15px]" id="branches">
                //             ${branches}
                //         </div>
                //     </div>
                // `;
                branch.innerHTML = '';
                branch.appendChild(branches);
                initializeAddress(data.data.id, 0, data.data.branches);
                // initializeAddress(`custom-select-product-product-${data.data.id}-address`);

                let productFound = 0;
                for (var i = basketItems.length; i--;) {
                    if (basketItems[i].stockItem == 1 && basketItems[i].id == data.data.id) {
                        productFound = 1;
                        branchSelect(0, basketItems[i].branchId, basketItems[i].id)

                    }
                }
                if (productFound == 0) {
                    branchSelect(0, data.data.branches[0].id, data.data.id)
                }
                availability.style.display = "";
                branch.style.display = "block";
                itemCount.style.display = "block";
                // description
                if (data.data.desc) {
                    desc.innerHTML = `
                        <h1 class="text-lg font-bold grap-color">Description:</h1>
                        ${data.data.desc}
                    `;
                } else {
                    desc.innerHTML = '';
                }
                // price
                let showPrice = "";
                let productSaleDiscount = '';
                if (data.data.discount_price && data.data.discount_price != 0) {
                    // With discount price
                    showPrice = `
                            <div class="block">
                                <span class="text-[15px] gray-2-text font-bold line-through mr-2">
                                    ${data.data.price2}
                                </span>
                                <span class="text-[20px] leading-normal primary-text-color font-bold">
                                    ${data.data.discount_price2}
                                </span>
                            </div>
                        `;
                    productSaleDiscount = `
                            <div class="text-white bg-[#1e1d23] z-[3] text-[12px] leading-normal absolute top-1 left-0 rounded-[20px] px-2 py-0 font-bold">
                                Sale!
                            </div>
                        `;
                } else {
                    // No discount price
                    showPrice = `
                            <div class="text-[20px] leading-normal primary-text-color font-bold">
                                ${data.data.price2}
                            </div>
                        `;
                    productSaleDiscount = ``;
                }
                price.innerHTML = showPrice;
                // variant
                if (data.data.variants.length != 0) {
                    let variantData = '';
                    variantData += `
                            <div id="all_variants" class="w-full inline-flex gap-[15px] items-center">
                                <div id="product_${data.data.id}" onclick="selectProduct(${data.data.id}, 0)" class="w-[80px] h-[80px] relative flex cursor-pointer border theme-main-bordercolorHover">
                                    <img class="img-contain" src="${data.data.images[0].image_path}" alt="${data.data.name}" />
                                    ${productSaleDiscount}
                                </div>
                        `;
                    data.data.variants.forEach(pVariant => {
                        let sale = ``;
                        let vPrice = ``;
                        if (pVariant.dprice != 0) {
                            sale = `
                                    <div class="text-white bg-[#1e1d23] z-[3] text-sm leading-normal absolute top-1 right-0 rounded-[20px] px-2 py-0 font-bold">
                                        Sale!
                                    </div>
                                `;
                            vPrice = `
                                    <span class="inline-flex gap-[5px]">
                                        <span class="text-base md:text-lg primary-text-color font-bold">${pVariant.dprice2}</span>
                                        <span class="text-sm md:text-base gray-2-text font-bold line-through">${pVariant.price2}</span>
                                    </span>
                                `;
                        } else {
                            vPrice = `
                                    <span class="inline-flex gap-[5px]">
                                        <span class="text-base md:text-lg primary-text-color font-bold">${pVariant.price2}</span>
                                    </span>
                                `;
                        }
                        // add to cart for stock items NOT done
                        variantData += `
                                    <div id="variant_${pVariant.id}" onclick="selectProduct(${pVariant.product_id}, ${pVariant.id})" class="w-[80px] h-[80px] relative flex cursor-pointer border inactive-border-hover">
                                        <img class="img-contain" src="${pVariant.images[0]}" alt="${pVariant.name}" />
                                        ${sale}
                                    </div>
                                `;
                    });
                    variantData += `</div>`;
                    // <div class="w-full flex gap-[25px] items-center justify-between">
                    //     <div class="flex gap-4 items-center">
                    //         <div class="w-[80px] h-[80px] relative flex">
                    //             <img class="img-contain" src="${pVariant.image}" alt="${pVariant.name}" />
                    //             ${sale}
                    //         </div>
                    //         <div class="inline-block">
                    //             <h1 class="text-lg md:text-xl font-bold">${pVariant.name}</h1>
                    //             ${vPrice}
                    //         </div>
                    //     </div>
                    //     <div class="inline-block">
                    //         <button onclick="addToBasketById(${pVariant.product }, ${pVariant.id})" class="flex items-center justify-center uppercase text-[12px] md:text-sm leading-normal font-bold self-baseline text-center p-[15px] cursor-pointer rounded-xl primary-text-color hover:text-white theme-main-hovercolor transition duration-200 ease-in-out">
                    //             <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-[20px] h-[20px] md:w-6 md:h-6">
                    //                 <path d="M2.25 2.25a.75.75 0 000 1.5h1.386c.17 0 .318.114.362.278l2.558 9.592a3.752 3.752 0 00-2.806 3.63c0 .414.336.75.75.75h15.75a.75.75 0 000-1.5H5.378A2.25 2.25 0 017.5 15h11.218a.75.75 0 00.674-.421 60.358 60.358 0 002.96-7.228.75.75 0 00-.525-.965A60.864 60.864 0 005.68 4.509l-.232-.867A1.875 1.875 0 003.636 2.25H2.25zM3.75 20.25a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zM16.5 20.25a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"></path>
                    //             </svg>
                    //         </button>
                    //     </div>
                    // </div>
                    variant.innerHTML = `
                            ${variantData}
                        `;
                    variant.style.display = "block";
                    document.getElementById(`product_${data.data.id}`).classList.add('active-border');
                } else {
                    variant.style.display = "none";
                }
                // add to cart count
                count.innerHTML = `
                        <div class="py-[10px] px-[15px] flex">
                            <div class="flex flex-col gap-4 lsm:flex-row md:flex-col lg:gap-0 lg:flex-row items-center lg:space-x-2 w-full">
                                <div class="flex items-center pro-mqty pro-qty mr-auto w-full max-w-[150px] justify-start border border-[#dee2e6] lg:justify-center transition duration-200 ease-linear">
                                    <input class="pro-mqty-input text-center px-2 font-bold appearance-none inline-block text-gray-700 leading-normal focus:outline-none focus:shadow-outline max-w-[90px]" id="product_details_count" type="text" value="1" />
                                </div>
                                <div class="flex items-center w-full gap-[15px] btn-contain">
                                    ${addToCartButton}
                                    ${addToWishlistButton}
                                </div>
                            </div>
                        </div>
                    `;
                $('#modal2-image-view').addClass("active-tab");
                // Cart count
                quantityCount();
                // Hide loading screen popup
                hideModalLoading();
                // add active to wishlist btn to added products in wishlist
                addWishlistActive();
            },
            error: function(e) {
                // console.log(e);
            }
        });
    }

    let selectedProductModal = '';
    // change content for the selected vairant/product
    function selectProduct(productId, variantId) {
        // TODO: Condition
        // console.log("condition 1", productId && variantId == 0 && selectedProductModal.id == productId);
        // console.log("condition 2", productId && variantId != 0 && selectedProductModal.id == productId);
        // if ((productId && variantId == 0 && selectedProductModal && selectedProductModal.id == productId)) {
        //     return;
        // }
        // if (productId && variantId != 0 && selectedProductModal.id == variantId) {
        //     return;
        // }

        // Destroy slick slider
        destorySlick('#modal2-image', '#modal2-moreImages');
        let image = document.getElementById('modal2-image');
        let moreImages = document.getElementById('modal2-moreImages');
        let branch = document.getElementById('modal2-content__branch');
        let title;
        let availability;
        // price
        let showPrice = "";
        // branch
        let branches = ``;
        let addToCartButton;
        let addToWishlistButton;
        let stockItem;
        let moreImagesData = '';
        let stars = '';

        Array.from(document.getElementById('all_variants').children).forEach((el) => {
            el.classList.remove('active-border');
        });
        let variant_temp = 0;
        if (variantId) {
            document.getElementById(`variant_${variantId}`).classList.add('active-border');
            let variantData = modalProduct.variants.find(function(currentValue, index, arr) {
                if (currentValue.id == variantId)
                    return currentValue;
            });
            variant_temp = variantData;

            // selected modal product
            selectedProductModal = variantData;

            let url = `{{ route('details', ':id') }}`;
            url = url.replace(':id', modalProduct.id);
            title =
                `<a href="${url}" class="inline-block line-clamp-1 !leading-normal font-bold text-2xl md:text-5xl theme-main-hovercolor">${variantData.name}</a>`;
            // stars +=
            //     `<div id="modal-rate-icon-${modalProduct.id}-${variantData.id}" class="w-full flex mb-2 gap-[5px] items-center justify-start">`;
            // for (var i = 1; i <= variantData.rate; i++) {
            //     if (authCheck) {
            //         stars +=
            //             `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" product="${modalProduct.id}" variant="${variantData.id}" viewBox="0 0 24 24" fill="currentColor" class="rating cursor-pointer w-[16px] h-[16px] primary-text-color">`;
            //     } else {
            //         stars +=
            //             `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" viewBox="0 0 24 24" fill="currentColor" class="w-[16px] h-[16px] primary-text-color">`;
            //     }
            //     stars += `<path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
            //     </svg>`;
            // }
            // if (i <= 5) {
            //     for (i; i <= 5; i++) {
            //         if (authCheck) {
            //             stars +=
            //                 `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" product="${modalProduct.id}" variant="${variantData.id}" viewBox="0 0 24 24" fill="currentColor" class="rating cursor-pointer w-[16px] h-[16px] light-gray-text theme-main-hovercolor ease-in-out duration-150">`;
            //         } else {
            //             stars +=
            //                 `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" viewBox="0 0 24 24" fill="currentColor" class="w-[16px] h-[16px] light-gray-text">`;
            //         }
            //         stars += `<path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
            //         </svg>`;
            //     }
            // }
            // stars +=
            //     `<span class="gray-2-text text-[11px] leading-normal">(${variantData.rateCustomerCount} Customer Reviews)</span></div>`;
            // title += stars;
            if (image) {
                image.innerHTML = ``;
                if (moreImages) moreImages.innerHTML == ``;
                if (variantData.images && variantData.images.length > 0) {
                    variantData.images.forEach((imageData, index) => {
                        image.innerHTML += `<div class="w-full h-full flex" style="display: flex;">
                            <img class="img-contain" src="${imageData}" alt='${variantData.name} ${index}'>
                        </div>`;
                        moreImagesData += `<div class="w-full h-full flex" style="display: flex;">
                            <img class="img-contain" src="${imageData}" alt='${variantData.name}_small_${index}' />
                        </div>`;
                    });
                }

                if (moreImages) moreImages.innerHTML = moreImagesData;
            }
            stockItem = variantData.stockItem;
            // add to cart button
            addToCartButton = `
                    <button id="add-to-cart-button${modalProduct.id}" onClick="addToCart(${modalProduct.id}, ${modalProduct.branch_id}, ${variantData.id}, ${1}, ${modalProduct.soon_instock == 1 ? 2 : 0});" class="inline-flex rounded-full gap-[8px] items-center justify-center w-full text-center px-9 py-3 z-20 cursor-pointer theme-borderradius text-white bg-black text-white text-sm font-bold transition-all duration-200 ease-in-out uppercase hover:opacity-60 btn-addtocart">
                        <span>Add To Cart</span>
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" height="20px" width="20px" class="w-[25px] h-[25px]">
                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                            <g id="SVGRepo_iconCarrier">
                                <path d="M7.5 18C8.32843 18 9 18.6716 9 19.5C9 20.3284 8.32843 21 7.5 21C6.67157 21 6 20.3284 6 19.5C6 18.6716 6.67157 18 7.5 18Z" stroke="currentColor" stroke-width="1.5"></path>
                                <path d="M16.5 18.0001C17.3284 18.0001 18 18.6716 18 19.5001C18 20.3285 17.3284 21.0001 16.5 21.0001C15.6716 21.0001 15 20.3285 15 19.5001C15 18.6716 15.6716 18.0001 16.5 18.0001Z" stroke="currentColor" stroke-width="1.5"></path>
                                <path d="M13 13V11M13 11V9M13 11H15M13 11H11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                <path d="M2 3L2.26121 3.09184C3.5628 3.54945 4.2136 3.77826 4.58584 4.32298C4.95808 4.86771 4.95808 5.59126 4.95808 7.03836V9.76C4.95808 12.7016 5.02132 13.6723 5.88772 14.5862C6.75412 15.5 8.14857 15.5 10.9375 15.5H12M16.2404 15.5C17.8014 15.5 18.5819 15.5 19.1336 15.0504C19.6853 14.6008 19.8429 13.8364 20.158 12.3075L20.6578 9.88275C21.0049 8.14369 21.1784 7.27417 20.7345 6.69708C20.2906 6.12 18.7738 6.12 17.0888 6.12H11.0235M4.95808 6.12H7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                            </g>
                        </svg>
                    </button>
                `;
            // add to wishlist button
            addToWishlistButton = `
                    <button id="wishlistM-${modalProduct.id}" onclick="addToWishList({ id: ${modalProduct.id}, branch_id: ${modalProduct.branch_id} });" class="group-theme inline-flex text-center p-3 cursor-pointer theme-borderradius theme-main-hovercolor bg-gray-100 w-[48px] h-[48px] leading-[48px] btn-addtowish transition duration-200 ease-in-out">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 m-auto">
                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                            <g id="SVGRepo_iconCarrier">
                                <path
                                    d="M8.96173 18.9109L9.42605 18.3219L8.96173 18.9109ZM12 5.50063L11.4596 6.02073C11.601 6.16763 11.7961 6.25063 12 6.25063C12.2039 6.25063 12.399 6.16763 12.5404 6.02073L12 5.50063ZM15.0383 18.9109L15.5026 19.4999L15.0383 18.9109ZM7.00061 16.4209C6.68078 16.1577 6.20813 16.2036 5.94491 16.5234C5.68169 16.8432 5.72758 17.3159 6.04741 17.5791L7.00061 16.4209ZM2.34199 13.4115C2.54074 13.7749 2.99647 13.9084 3.35988 13.7096C3.7233 13.5108 3.85677 13.0551 3.65801 12.6917L2.34199 13.4115ZM2.75 9.1371C2.75 6.98623 3.96537 5.18252 5.62436 4.42419C7.23607 3.68748 9.40166 3.88258 11.4596 6.02073L12.5404 4.98053C10.0985 2.44352 7.26409 2.02539 5.00076 3.05996C2.78471 4.07292 1.25 6.42503 1.25 9.1371H2.75ZM8.49742 19.4999C9.00965 19.9037 9.55954 20.3343 10.1168 20.6599C10.6739 20.9854 11.3096 21.25 12 21.25V19.75C11.6904 19.75 11.3261 19.6293 10.8736 19.3648C10.4213 19.1005 9.95208 18.7366 9.42605 18.3219L8.49742 19.4999ZM15.5026 19.4999C16.9292 18.3752 18.7528 17.0866 20.1833 15.4758C21.6395 13.8361 22.75 11.8026 22.75 9.1371H21.25C21.25 11.3345 20.3508 13.0282 19.0617 14.4798C17.7469 15.9603 16.0896 17.1271 14.574 18.3219L15.5026 19.4999ZM22.75 9.1371C22.75 6.42503 21.2153 4.07292 18.9992 3.05996C16.7359 2.02539 13.9015 2.44352 11.4596 4.98053L12.5404 6.02073C14.5983 3.88258 16.7639 3.68748 18.3756 4.42419C20.0346 5.18252 21.25 6.98623 21.25 9.1371H22.75ZM14.574 18.3219C14.0479 18.7366 13.5787 19.1005 13.1264 19.3648C12.6739 19.6293 12.3096 19.75 12 19.75V21.25C12.6904 21.25 13.3261 20.9854 13.8832 20.6599C14.4405 20.3343 14.9903 19.9037 15.5026 19.4999L14.574 18.3219ZM9.42605 18.3219C8.63014 17.6945 7.82129 17.0963 7.00061 16.4209L6.04741 17.5791C6.87768 18.2624 7.75472 18.9144 8.49742 19.4999L9.42605 18.3219ZM3.65801 12.6917C3.0968 11.6656 2.75 10.5033 2.75 9.1371H1.25C1.25 10.7746 1.66995 12.1827 2.34199 13.4115L3.65801 12.6917Z"
                                    fill="currentColor"></path>
                            </g>
                        </svg>
                    </button>
                `;
            // availability
            availability = `
                <p>Property Availability:
                    <div id="product-availability-${modalProduct.id}">
                    </div>
                </p>
            `;

            // branch
            let branches = ``;
            branches = buildSelectElement(variantData.branches, modalProduct.id,
                `product-${modalProduct.id}-address`, branchSelect);
            branch.innerHTML = '';
            branch.appendChild(branches);
            initializeAddress(modalProduct.id, variantData.id, variantData.branches);

            // let productFound = 0;
            // for (var i = basketItems.length; i--;) {
            //     if (basketItems[i].id == modalProduct.id && basketItems[i].product_variant_id == variantData.id) {
            //         productFound = 1;
            //         branchSelect(0, basketItems[i].branchId, basketItems[i].id, variantData.id);
            //     }
            // }
            // if (productFound == 0) {
            //     branchSelect(0, variantData.branches[0].id, modalProduct.id, variantData.id);
            // }

            // variantData.branches.forEach(branchData => {
            //     branches += `
            //         <div id="branch_id_${branchData.branch_id}" onclick="branchSelect(0, ${branchData.branch_id}, ${branchData.product_id}, ${variantData.id})" class="inline-block py-[10px] px-[15px] border border-solid rounded-[32px] cursor-pointer theme-main-bordercolorHover transition duration-200 ease-in-out branch">
            //             ${branchData.name}
            //         </div>
            //     `;
            // });
            if (variantData.discount_price && variantData.discount_price != 0) {
                // With discount price
                showPrice = `
                        <div class="block">
                            <span class="text-[15px] gray-2-text font-bold line-through mr-2">
                                ${variantData.price2}
                            </span>
                            <span class="text-[20px] leading-[30px] primary-text-color font-bold">
                                ${variantData.discount_price2}
                            </span>
                        </div>
                    `;
            } else {
                // No discount price
                showPrice = `
                        <div class="text-[20px] leading-[55px] primary-text-color font-bold">
                            ${variantData.price2}
                        </div>
                    `;
            }
        } else {
            // selected modal product
            selectedProductModal = modalProduct;

            document.getElementById(`product_${productId}`).classList.add('active-border');
            let url = `{{ route('details', ':id') }}`;
            url = url.replace(':id', modalProduct.id);
            title =
                `<a href="${url}" class="inline-block line-clamp-1 !leading-normal font-bold text-2xl md:text-5xl theme-main-hovercolor">${modalProduct.name}</a>`;
            // stars +=
            //     `<div id="modal-rate-icon-${modalProduct.id}" class="w-full flex mb-2 gap-[5px] items-center justify-start">`;
            // for (var i = 1; i <= modalProduct.rate; i++) {
            //     if (authCheck) {
            //         stars +=
            //             `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" product="${modalProduct.id}" variant="0" viewBox="0 0 24 24" fill="currentColor" class="rating cursor-pointer w-[16px] h-[16px] primary-text-color">`;
            //     } else {
            //         stars +=
            //             `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" viewBox="0 0 24 24" fill="currentColor" class="w-[16px] h-[16px] primary-text-color">`;
            //     }
            //     stars += `<path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
            //     </svg>`;
            // }
            // if (i <= 5) {
            //     for (i; i <= 5; i++) {
            //         if (authCheck) {
            //             stars +=
            //                 `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" product="${modalProduct.id}" variant="0" viewBox="0 0 24 24" fill="currentColor" class="rating cursor-pointer w-[16px] h-[16px] light-gray-text theme-main-hovercolor ease-in-out duration-150">`;
            //         } else {
            //             stars +=
            //                 `<svg xmlns="http://www.w3.org/2000/svg" value="${i}" viewBox="0 0 24 24" fill="currentColor" class="w-[16px] h-[16px] light-gray-text">`;
            //         }
            //         stars += `<path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
            //         </svg>`;
            //     }
            // }
            // stars +=
            //     `<span class="gray-2-text text-[11px] leading-normal">(${modalProduct.rateCustomerCount} Customer Reviews)</span></div>`;
            // title += stars;
            if (image) {
                image.innerHTML = ``;
                if (moreImages) moreImages.innerHTML = '';
                if (modalProduct.images.length > 0) {
                    modalProduct.images.forEach((imageData, index) => {
                        image.innerHTML += `<div class="w-full h-full flex" style="display: flex;">
                            <img class="img-contain" src="${imageData}" alt='${modalProduct.name} ${index}'>
                        </div>`;
                        moreImagesData += `<div class="w-full h-full flex" style="display: flex;">
                            <img class="img-contain" src="${imageData}" alt='${modalProduct.name}_small_${index}'>
                        </div>`;
                    });
                }
                if (moreImages) moreImages.innerHTML = moreImagesData;
            }
            stockItem = modalProduct.stockItem;
            // add to cart button
            addToCartButton = `
                    <button id="add-to-cart-button${modalProduct.id}" onClick="addToCart(${modalProduct.id}, ${modalProduct.branch_id}, ${0}, ${1}, ${modalProduct.soon_instock == 1 ? 2 : 0});" class="inline-flex rounded-full gap-[8px] items-center justify-center w-full text-center px-9 py-3 z-20 cursor-pointer theme-borderradius text-white bg-black text-white text-sm font-bold transition-all duration-200 ease-in-out uppercase hover:opacity-60 btn-addtocart">
                        <span>Add To Cart</span>
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" height="20px" width="20px" class="w-[25px] h-[25px]">
                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                            <g id="SVGRepo_iconCarrier">
                                <path d="M7.5 18C8.32843 18 9 18.6716 9 19.5C9 20.3284 8.32843 21 7.5 21C6.67157 21 6 20.3284 6 19.5C6 18.6716 6.67157 18 7.5 18Z" stroke="currentColor" stroke-width="1.5"></path>
                                <path d="M16.5 18.0001C17.3284 18.0001 18 18.6716 18 19.5001C18 20.3285 17.3284 21.0001 16.5 21.0001C15.6716 21.0001 15 20.3285 15 19.5001C15 18.6716 15.6716 18.0001 16.5 18.0001Z" stroke="currentColor" stroke-width="1.5"></path>
                                <path d="M13 13V11M13 11V9M13 11H15M13 11H11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                                <path d="M2 3L2.26121 3.09184C3.5628 3.54945 4.2136 3.77826 4.58584 4.32298C4.95808 4.86771 4.95808 5.59126 4.95808 7.03836V9.76C4.95808 12.7016 5.02132 13.6723 5.88772 14.5862C6.75412 15.5 8.14857 15.5 10.9375 15.5H12M16.2404 15.5C17.8014 15.5 18.5819 15.5 19.1336 15.0504C19.6853 14.6008 19.8429 13.8364 20.158 12.3075L20.6578 9.88275C21.0049 8.14369 21.1784 7.27417 20.7345 6.69708C20.2906 6.12 18.7738 6.12 17.0888 6.12H11.0235M4.95808 6.12H7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                            </g>
                        </svg>
                    </button>
                `;
            // add to wishlist button
            addToWishlistButton = `
                    <button id="wishlistM-${modalProduct.id}" onclick="addToWishList({ id: ${modalProduct.id}, branch_id: ${modalProduct.branch_id} });" class="group-theme inline-flex text-center p-3 cursor-pointer theme-borderradius theme-main-hovercolor bg-gray-100 w-[48px] h-[48px] leading-[48px] btn-addtowish transition duration-200 ease-in-out">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 m-auto">
                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                            <g id="SVGRepo_iconCarrier">
                                <path
                                    d="M8.96173 18.9109L9.42605 18.3219L8.96173 18.9109ZM12 5.50063L11.4596 6.02073C11.601 6.16763 11.7961 6.25063 12 6.25063C12.2039 6.25063 12.399 6.16763 12.5404 6.02073L12 5.50063ZM15.0383 18.9109L15.5026 19.4999L15.0383 18.9109ZM7.00061 16.4209C6.68078 16.1577 6.20813 16.2036 5.94491 16.5234C5.68169 16.8432 5.72758 17.3159 6.04741 17.5791L7.00061 16.4209ZM2.34199 13.4115C2.54074 13.7749 2.99647 13.9084 3.35988 13.7096C3.7233 13.5108 3.85677 13.0551 3.65801 12.6917L2.34199 13.4115ZM2.75 9.1371C2.75 6.98623 3.96537 5.18252 5.62436 4.42419C7.23607 3.68748 9.40166 3.88258 11.4596 6.02073L12.5404 4.98053C10.0985 2.44352 7.26409 2.02539 5.00076 3.05996C2.78471 4.07292 1.25 6.42503 1.25 9.1371H2.75ZM8.49742 19.4999C9.00965 19.9037 9.55954 20.3343 10.1168 20.6599C10.6739 20.9854 11.3096 21.25 12 21.25V19.75C11.6904 19.75 11.3261 19.6293 10.8736 19.3648C10.4213 19.1005 9.95208 18.7366 9.42605 18.3219L8.49742 19.4999ZM15.5026 19.4999C16.9292 18.3752 18.7528 17.0866 20.1833 15.4758C21.6395 13.8361 22.75 11.8026 22.75 9.1371H21.25C21.25 11.3345 20.3508 13.0282 19.0617 14.4798C17.7469 15.9603 16.0896 17.1271 14.574 18.3219L15.5026 19.4999ZM22.75 9.1371C22.75 6.42503 21.2153 4.07292 18.9992 3.05996C16.7359 2.02539 13.9015 2.44352 11.4596 4.98053L12.5404 6.02073C14.5983 3.88258 16.7639 3.68748 18.3756 4.42419C20.0346 5.18252 21.25 6.98623 21.25 9.1371H22.75ZM14.574 18.3219C14.0479 18.7366 13.5787 19.1005 13.1264 19.3648C12.6739 19.6293 12.3096 19.75 12 19.75V21.25C12.6904 21.25 13.3261 20.9854 13.8832 20.6599C14.4405 20.3343 14.9903 19.9037 15.5026 19.4999L14.574 18.3219ZM9.42605 18.3219C8.63014 17.6945 7.82129 17.0963 7.00061 16.4209L6.04741 17.5791C6.87768 18.2624 7.75472 18.9144 8.49742 19.4999L9.42605 18.3219ZM3.65801 12.6917C3.0968 11.6656 2.75 10.5033 2.75 9.1371H1.25C1.25 10.7746 1.66995 12.1827 2.34199 13.4115L3.65801 12.6917Z"
                                    fill="currentColor"></path>
                            </g>
                        </svg>
                    </button>
                `;
            // availability
            availability = `
                <p>Property Availability:
                    <div id="product-availability-${modalProduct.id}">
                    </div>
                </p>
            `;
            // in stock count
            // branch
            let branches = ``;
            branches = buildSelectElement(modalProduct.branches, modalProduct.id,
                `product-${modalProduct.id}-address`, branchSelect);
            branch.innerHTML = '';
            branch.appendChild(branches);
            initializeAddress(modalProduct.id, 0, modalProduct.branches);

            // let productFound = 0;
            // for (var i = basketItems.length; i--;) {
            //     if (basketItems[i].id == modalProduct.id && basketItems[i].product_variant_id == variantData.id) {
            //         productFound = 1;
            //         branchSelect(0, basketItems[i].branchId, basketItems[i].id);
            //     }
            // }
            // if (productFound == 0) {
            //     branchSelect(0, modalProduct.branches[0].id, modalProduct.id);
            // }

            // modalProduct.branches.forEach(branchData => {
            //     branches += `
            //                 <div id="branch_id_${branchData.branch_id}" onclick="branchSelect(0, ${branchData.branch_id}, ${branchData.product_id})" class="inline-block py-[10px] px-[15px] border border-solid rounded-[32px] cursor-pointer theme-main-bordercolorHover transition duration-200 ease-in-out branch">
            //                     ${branchData.name}
            //                 </div>
            //             `;
            // });
            if (modalProduct.discount_price && modalProduct.discount_price != 0) {
                // With discount price
                showPrice = `
                        <div class="block">
                            <span class="text-[15px] gray-2-text font-bold line-through mr-2">
                                ${modalProduct.price2}
                            </span>
                            <span class="text-[20px] leading-normal primary-text-color font-bold">
                                ${modalProduct.discount_price2}
                            </span>
                        </div>
                    `;
            } else {
                // No discount price
                showPrice = `
                        <div class="text-[20px] leading-normal primary-text-color font-bold">
                            ${modalProduct.price2}
                        </div>
                    `;
            }
        }
        document.getElementById('modal2-content__title').innerHTML = title;
        initializeRateButtons();
        let availabilityView = document.getElementById('modal2-content__availability');
        let itemCountView = document.getElementById('modal2-content__itemCount');
        availabilityView.innerHTML = availability;
        itemCountView.innerHTML = `Availability: <span id="item_count" class="mr-1">0</span>`;

        let productFound = 0;
        if (variant_temp) {
            if (basketItems.length) {
                for (var i = basketItems.length; i--;) {
                    if (
                        basketItems[i].id == modalProduct.id &&
                        basketItems[i].variant_id == variant_temp.id
                    ) {
                        productFound = 1;
                        branchSelect(0, basketItems[i].branchId, basketItems[i].id, basketItems[i].variant_id)
                    }
                }
            }
            if (productFound == 0) {
                branchSelect(0, variant_temp.branches[0].id, modalProduct.id, variant_temp.id);
            }
        } else {
            for (var i = basketItems.length; i--;) {
                if (basketItems[i].stockItem == 1 && basketItems[i].id == modalProduct.id) {
                    productFound = 1;
                    branchSelect(0, basketItems[i].branchId, basketItems[i].id)
                }
            }
            if (productFound == 0) {
                branchSelect(0, modalProduct.branches[0].id, modalProduct.id)
            }
        }

        // price
        let price = document.getElementById('modal2-content__price');
        price.innerHTML = showPrice;

        // add to cart count
        let countView = document.getElementById('modal2-content__count');
        countView.innerHTML = `
                <div class="py-[10px] px-[15px] flex">
                    <div class="flex flex-col gap-4 lsm:flex-row md:flex-col lg:gap-0 lg:flex-row items-center lg:space-x-2 w-full">
                        <div class="flex items-center pro-qty mr-auto w-full max-w-[150px] justify-start border border-[#dee2e6] lg:justify-center transition duration-200 ease-linear">
                            <input class="pro-qty-input text-center px-2 font-bold appearance-none inline-block text-gray-700 leading-tight focus:outline-none focus:shadow-outline max-w-[90px]" id="product_details_count" type="text" value="1" />
                        </div>
                        <div class="flex items-center w-full gap-[15px] btn-contain">
                            ${addToCartButton}
                            ${addToWishlistButton}
                        </div>
                    </div>
                </div>
            `;
        // initialize slick slider
        initializeModalImages('modal2-image', 'modal2-moreImages');
        quantityCount();
        // add active to wishlist btn to added products in wishlist
        addWishlistActive();
    }

    function quantityCount() {
        // Cart count
        $('.pro-mqty').prepend(
            '<button class="dec qty-modal-btn qty-btn inline-block bg-[#f7f4ef] w-[30px] h-[30px] leading-[30px] text-black border-r border-r-[#dee2e6] font-bold theme-main-hovercolor transition duration-200 ease-in-out">-</button>'
        );
        $('.pro-mqty').append(
            '<button class="inc qty-modal-btn qty-btn inline-block bg-[#f7f4ef] w-[30px] h-[30px] leading-[30px] text-black border-l border-l-[#dee2e6] font-bold theme-main-hovercolor transition duration-200 ease-in-out">+</button>'
        );
        $('.qty-modal-btn').on('click', function(e) {
            e.preventDefault();
            var $button = $(this);
            var oldValue = $button.parent().find('input').val();
            if ($button.hasClass('inc')) {
                var newVal = parseFloat(oldValue) + 1;
            } else {
                // Don't allow decrementing below zero
                if (oldValue > 1) {
                    var newVal = parseFloat(oldValue) - 1;
                } else {
                    newVal = 1;
                }
            }
            $button.parent().find('input').val(newVal);
        });
    }

    // Routing
    $('div[id="tab-modal2-image"], div[id="tab-modal2-video"]').click(function() {
        let selectedViewId = $(this).attr('id');
        let tablists = $('#modal2-tablist');
        // console.log('selectedViewId: ', selectedViewId);
        for (var i = 0; i < tablists.children().length; i++) {
            if (tablists.find(tablists.children()[i]).attr('id') == selectedViewId) {
                tablists.find(tablists.children()[i]).removeClass('inactive');
                tablists.find(tablists.children()[i]).addClass('active');

                let tabActive = '#' + selectedViewId.replace("tab-", "") + '-view';
                $(tabActive).addClass('active-tab');
                if (document.getElementById(tabActive.replace("#", ""))) {
                    // document.getElementById(tabActive.replace("#", "")).style.height = '';
                    document.getElementById(tabActive.replace("#", "")).style.display = '';
                }
            } else {
                let closeTab = tablists.children()[i];
                tablists.find(closeTab).removeClass('active');
                tablists.find(closeTab).addClass('inactive');

                let tabInActive = tablists.find(closeTab).attr('id').replace("tab-", "") + '-view';
                $('#' + tabInActive).removeClass('active-tab');
                if (document.getElementById(tabInActive)) {
                    // document.getElementById(tabInActive).style.height = '0';
                    document.getElementById(tabInActive).style.display = 'none';
                }

                if (tablists.find(closeTab).attr('id') == 'video') {
                    $('iframe').attr('src', $('iframe').attr('src'));
                }
            }
        }
    });
</script>
