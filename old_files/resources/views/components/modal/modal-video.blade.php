<div id="video-modal" class="modal2 p-0">
    <!-- Modal content -->
    <div class="grid grid-rows-[50px_calc(50px_-_100%)] w-full h-full place-items-center max-w-[1224px] max-h-[350px] md:max-h-[500px]">
        <div class="w-full h-full flex gap-2 items-center px-4 py-2">
            <button type="button" class="ml-auto close text-4xl" data-dismiss="modal" id="closeBtn-modal-video"
                aria-hidden="true">&times;</button>
            {{-- <h4 class="modal-title">YouTube Video</h4> --}}
        </div>
        <div class="w-full h-full grid place-items-center">
            <div class="w-full p-2 md:p-4 flex items-center justify-center h-[245px] md:h-[450px]">
                {{-- //www.youtube.com/embed/YE7VzlLtp-4 --}}
                <iframe id="video-link" style='width:100%; height:100%;' src="" frameborder="0"
                    allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>

<script>
    // Modal 2
    let videoModal = document.getElementById("video-modal");
    let videoLink = document.getElementById("video-link");

    // Close sidebar left on background click
    $('#closeBtn-modal-video').click(function(e) {
        hideVideoModal();
    });

    function hideVideoModal() {
        // modal.style.display = "none";
        videoModal.classList.remove("show");
        document.getElementsByTagName("BODY")[0].style.overflow = "auto";
        $("#video-link").attr('src', '');
    }

    function showVideoModal() {
        videoModal.classList.add("show");
        // modal.style.display = "block";
        document.getElementsByTagName("BODY")[0].style.overflow = "hidden";
    }

    function openVideo(link) {
        $("#video-link").attr('src', link);
        showVideoModal();
    }
</script>
