<script>
    // for pagination
    function buildPagination(page, pages) {
        page = parseInt(page);
        pages = parseInt(pages);
        let numberPages = 5;
        pageNumbers = '';
        let maxLeft = Math.ceil(page - (numberPages / 2));
        let maxRight = Math.ceil(page + (numberPages / 2));
        if (maxLeft < 1)
        {
            maxLeft = 1;
            maxRight = numberPages;
        }
        if (maxRight > pages)
        {
            maxLeft = pages - (numberPages - 1);
            maxRight = pages;
            if (maxLeft < 1)
            {
                maxLeft = 1;
            }
        }
        for (let i = maxLeft; i <= maxRight; i++)
        {
            if (i == page) {
                pageNumbers += `<span value="${i}" class="page active">${i}</span>`;
            }
            else {
                pageNumbers += `<span value="${i}" class="page">${i}</span>`;
            }
        }
        if (maxLeft >= parseInt(numberPages / 2)) {
            pageNumbers = `<span value="1" class="page">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                </svg>
            </span>` + pageNumbers;
        }
        if (maxRight != pages) {
            pageNumbers += `<span value="${pages}" class="page">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
            </span>`;
        }
        return pageNumbers;
    }

    function initializePaginations() {
        $('.page').on('click', function() {
            let newPage = parseInt($(this).attr('value'));
            buildChatUsers(newPage);
        });
    }
</script>

<div class="wrapper">
    <div class="sidebar" data-color="white" data-active-color="primary">
        @include('components.sidebars.menus', [])
        <script>
            setTimeout(() => {
                // To scroll to current sidebar active link
                var activeLinkPosition = document.querySelector(".sidebar-wrapper > .nav > li.active").offsetTop;
                var activeSubLinkPosition = document.querySelector(".sidebar-wrapper > .nav > li.active > .ml-menu > .q-menu-active");
                if (activeSubLinkPosition != null) {
                    document.querySelector(".sidebar-wrapper").scrollTo(0,(activeLinkPosition+activeSubLinkPosition.offsetTop)-10);
                } else {
                    document.querySelector(".sidebar-wrapper").scrollTo(0,activeLinkPosition-10);
                }
            }, 150);
        </script>
    </div>
    <div class="main-panel">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-absolute fixed-top navbar-transparent">
            <div class="container-fluid">
                <div class="navbar-wrapper navbar-header">
                    <div class="navbar-toggle">
                        <button type="button" class="navbar-toggler">
                            <span class="navbar-toggler-bar bar1"></span>
                            <span class="navbar-toggler-bar bar2"></span>
                            <span class="navbar-toggler-bar bar3"></span>
                        </button>
                    </div>
                    <a class="navbar-brand" href="/">Admin</a>
                </div>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navigation"
                    aria-controls="navigation-index" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-bar navbar-kebab"></span>
                    <span class="navbar-toggler-bar navbar-kebab"></span>
                    <span class="navbar-toggler-bar navbar-kebab"></span>
                </button>
                <div class="collapse navbar-collapse justify-content-end" id="navigation">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('orders') }}">
                                <i class="nc-icon nc-bell-55"></i>
                                <p class="notification">
                                    <span id="countNewOrders" class="label-count">0</span>
                                </p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('chat') }}">
                                <i class="nc-icon nc-chat-33"></i>
                                <p class="notification">
                                    <span id="countChatNewMessages" class="label-count">0</span>
                                </p>
                            </a>
                        </li>
                        <li class="nav-item" style="display:none;">
                            <a class="nav-link btn-rotate" href="javascript:;">
                                <i class="material-icons">dark_mode</i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn-rotate" href="#">
                                <i class="material-icons">account_circle</i>
                                <p>
                                    <span class="d-lg-none d-md-block">{{ $lang->get(562) }}</span> {{-- Profile --}}
                                </p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn-rotate" href="{{ route('logout') }}">
                                <i class="material-icons">logout</i>
                                <p>
                                    <span class="d-lg-none d-md-block">{{ $lang->get(563) }}</span> {{-- Sign Out --}}
                                </p>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        <!-- End Navbar -->

        <div class="content">
            <!-- Page Loader -->
            <div class="page-loader-wrapper d-none">
                <div class="loader">
                    <div class="preloader">
                        <div class="spinner-layer pl-red">
                            <div class="circle-clipper left">
                                <div class="circle"></div>
                            </div>
                            <div class="circle-clipper right">
                                <div class="circle"></div>
                            </div>
                        </div>
                    </div>
                    <p>Please wait...</p>
                </div>
            </div>
            <!-- #END# Page Loader -->
            @yield('content')
        </div>

        <!-- Footer -->
        <footer class="footer footer-black  footer-white ">
            <div class="container-fluid">
                <div class="row">
                    <div class="credits ml-auto">
                        {{-- <span class="copyright">
                            &copy; {{ $settings->getCopyright() }}
                            <script>
                                document.write(new Date().getFullYear())
                            </script>. {{ $lang->get(36) }}: </b> {{ $settings->getVersion() }}
                        </span> --}}
                    </div>
                </div>
            </div>
        </footer>
        <!-- #Footer -->
    </div>
</div>

<style>
    .q-menu-header2 {
        border-top: 1px solid #ffffff8f;
        border-bottom: 1px solid #ffffff8f;
        display: flex;
        justify-content: space-between;
    }

    .info2 {
        margin-right: 10px;
        display: inline-flex;
        flex-direction: column;
    }

    .circlebuttons {
        background-color: #148ea263;
        border-radius: 50%;
        padding: 3px 3px;
    }
</style>

{{-- <script>
    // Menu toggle
    $(document).ready(function() {
        //jquery for toggle sub menus
        $('.menu-toggle').click(function() {
            $(this).next('.ml-menu').slideToggle();
            $(this).find('.dropdown').toggleClass('rotate');
        });

        var dropdown = document.getElementsByClassName("menu-toggle");
        var i;
        for (i = 0; i < dropdown.length; i++) {
            var currentLocation = location.href;
            if (dropdown[i].nextElementSibling.getElementsByClassName('q-menu-active')[0] != null) {
                var dropdownLocation = dropdown[i].nextElementSibling.getElementsByClassName('q-menu-active')[0]
                    .getElementsByClassName('menu-text')[0].href;
                $(this).find('.q-menu-active .dropdown').addClass('rotate');
                if (currentLocation == dropdownLocation) {
                    dropdown[i].nextElementSibling.style.display = "block";
                }
            }
        }
    });

    function showNotification(colorName, text, placementFrom, placementAlign, animateEnter, animateExit) {
        if (colorName === null || colorName === '') {
            colorName = 'pastel-danger';
        }
        if (colorName == "bg-teal")
            colorName = "pastel-info";
        if (colorName == "bg-red")
            colorName = "pastel-danger";
        if (text === null || text === '') {
            text = 'alert';
        }
        if (animateEnter === null || animateEnter === '') {
            animateEnter = 'animated fadeInDown';
        }
        if (animateExit === null || animateExit === '') {
            animateExit = 'animated fadeOutUp';
        }
        var allowDismiss = true;
        $.notify({
            message: text
        }, {
            type: colorName,
            allow_dismiss: allowDismiss,
            newest_on_top: true,
            timer: 5000,
            placement: {
                from: placementFrom,
                align: placementAlign
            },
            animate: {
                enter: animateEnter,
                exit: animateExit
            },
        });
    }

    function inputHandler(e, parent, min, max) {
        var value = parseInt(e.target.value);
        if (value.isEmpty)
            value = 0;
        if (isNaN(value))
            value = 0;
        if (value > max)
            value = max;
        if (value < min)
            value = min;
        parent.value = value;
    }

    var lastOrders = 0;

    function getChatNewMessages() {
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ url('getChatMessagesNewCount') }}',
            data: {},
            success: function(data) {
                // console.log(data);
                if (document.getElementById("countChatNewMessages") != null)
                    document.getElementById("countChatNewMessages").innerHTML = data.count;
                document.getElementById("countNewOrders").innerHTML = data.orders;
                if (data.orders != lastOrders) {
                    lastOrders = data.orders;
                    const audio = new Audio("img/sound.mp3");
                    audio.play();
                    $orders = document.getElementById("orders-table");
                    if ($orders != null) { // open orders page
                        paginationGoPage(1);
                        // getChatNewMessages();
                    }
                }
                // if (document.getElementById("messagesWindow") != null)
                //     buildChatUsers();
            },
            error: function(e) {
                console.log(e);
            }
        });
    }

    setInterval(getChatNewMessages, 10000); // one time in 10 sec
    getChatNewMessages();

    function moveToPageWithSelectedItem(id) {
        var itemsTable = $('#data_table').DataTable();
        var indexes = itemsTable
            .rows()
            .indexes()
            .filter(function(value, index) {
                return id === itemsTable.row(value).data()[0];
            });
        var numberOfRows = itemsTable.data().length;
        var rowsOnOnePage = itemsTable.page.len();
        if (rowsOnOnePage < numberOfRows) {
            var selectedNode = itemsTable.row(indexes).node();
            var nodePosition = itemsTable.rows({
                order: 'current'
            }).nodes().indexOf(selectedNode);
            var pageNumber = Math.floor(nodePosition / rowsOnOnePage);
            itemsTable.page(pageNumber).draw(false); //move to page with the element
            return pageNumber;
        }
        return 0;
    }

    function showDeleteMessage(id, url) {
        swal({
            title: "Are you sure?",
            text: "You will not be able to recover this item!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, delete it!",
            cancelButtonText: "No, cancel please!",
            closeOnConfirm: true,
            closeOnCancel: true
        }, function(isConfirm) {
            if (isConfirm) {
                // console.log(id);
                $.ajax({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                    },
                    type: 'POST',
                    url: url,
                    data: {
                        id: id
                    },
                    success: function(data) {
                        // console.log(data);
                        if (data.error != "0") {
                            if (data.error == '2')
                                return showNotification("bg-red", data.text, "bottom", "center", "",
                                    "");
                            return showNotification("bg-red", "Something went wrong", "bottom",
                                "center", "", ""); // Something went wrong
                        }
                        //
                        // remove from ui
                        //
                        paginationGoPage(currentPage);
                    },
                    error: function(e) {
                        console.log(e);
                    }
                });
            } else {

            }
        });
    }

    function onCloseSideBar() {
        document.getElementById("leftsidebar").style.display = "none";
        document.getElementById("content-section").style.margin = '100px 15px 0 15px';
        document.getElementById("sidebar-open-button").hidden = false;
    }

    function onOpenSideBar() {
        document.getElementById("leftsidebar").style.display = "inline-block";
        document.getElementById("content-section").style.margin = '100px 15px 0 300px';
        document.getElementById("sidebar-open-button").hidden = true;
    }

    // For mouse horizontall scroll
    const slider = document.querySelector('.table-responsive');
    let mouseDown = false;
    let startX, scrollLeft;

    let startDragging = function (e) {
        mouseDown = true;
        startX = e.pageX - slider.offsetLeft;
        scrollLeft = slider.scrollLeft;
    };
    let stopDragging = function (event) {
        mouseDown = false;
    };

    if (slider != null) {
        slider.addEventListener('mousemove', (e) => {
            e.preventDefault();
            if(!mouseDown) { return; }
            const x = e.pageX - slider.offsetLeft;
            const scroll = x - startX;
            slider.scrollLeft = scrollLeft - scroll;
        });
        // Add the event listeners
        slider.addEventListener('mousedown', startDragging, false);
        slider.addEventListener('mouseup', stopDragging, false);
        slider.addEventListener('mouseleave', stopDragging, false);
    }
</script> --}}
