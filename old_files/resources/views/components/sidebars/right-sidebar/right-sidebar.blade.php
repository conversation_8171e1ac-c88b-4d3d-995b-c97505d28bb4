<div class="h-screen grid grid-rows-[64px_minmax(calc(100%_-_(64px_+_120px)),_1fr)_120px] fixed right-0 top-0 w-[345px] bg-white transition-all duration-200 ease-in-out z-[9999] sidebar-right"
    id="cart-sidebar">
    <div class="flex justify-between items-center border-b border-r p-4 text-left">
        <h1 class="font-normal text-xl">Cart</h1>
        <svg onclick="hidecart();" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" height="36px" width="36px"
            class="w-12 h-12 cursor-pointer transition-all duration-300 ease-in-out hover:opacity-60">
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
                <g id="icomoon-ignore"> </g>
                <path
                    d="M10.722 9.969l-0.754 0.754 5.278 5.278-5.253 5.253 0.754 0.754 5.253-5.253 5.253 5.253 0.754-0.754-5.253-5.253 5.278-5.278-0.754-0.754-5.278 5.278z"
                    fill="#000000"> </path>
            </g>
        </svg>
    </div>
    <div class="block p-2 overflow-auto">
        <div class="grid grid-cols-1 auto-rows-auto" id="sidebar-carts">
            <div class="block text-4xl text-center place-content-center">
                Your cart is empty!
            </div>
            <div class="grid place-items-center mb-2 border-b">
                <div class="grid grid-cols-[100px_minmax(calc(100%_-_(100px_+_50px)),_1fr)_50px] w-full h-full">
                    <div class="w-full h-full p-2 flex rounded-full overflow-hidden h-16">
                        <img src="https://img.freepik.com/free-vector/bird-colorful-logo-gradient-vector_343694-1365.jpg?w=826&t=st=1711096163~exp=1711096763~hmac=0e26cd35349f829787d38f145bfdf3cdb9fd5ab7b06fb148aca8b46c65b15570"
                            alt="Logo Image" srcset=""
                            class="w-auto h-auto max-w-full max-h-full mx-auto text-center">
                    </div>
                    <div class="block self-center p-2">
                        <h2 class="font-bold text-base line-clamp-2"></h2>
                        <h2 class="font-normat text-base">0</h2>
                    </div>
                    <div class="h-full flex text-black">
                        <svg viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink" fill="currentColor" height="24px" width="24px"
                            class="w-6 h-6 m-auto cursor-pointer">
                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                            <g id="SVGRepo_iconCarrier">
                                <g id="icomoon-ignore"> </g>
                                <path
                                    d="M26.129 5.871h-5.331v-1.066c0-1.178-0.955-2.132-2.133-2.132h-5.331c-1.178 0-2.133 0.955-2.133 2.132v1.066h-5.331v1.066h1.099l1.067 20.259c0 1.178 0.955 2.133 2.133 2.133h11.729c1.178 0 2.133-0.955 2.133-2.133l1.049-20.259h1.051v-1.066zM12.268 4.804c0-0.588 0.479-1.066 1.066-1.066h5.331c0.588 0 1.066 0.478 1.066 1.066v1.066h-7.464v-1.066zM22.966 27.14l-0.002 0.027v0.028c0 0.587-0.478 1.066-1.066 1.066h-11.729c-0.587 0-1.066-0.479-1.066-1.066v-0.028l-0.001-0.028-1.065-20.203h15.975l-1.046 20.204z"
                                    fill="currentColor"> </path>
                                <path d="M15.467 9.069h1.066v17.060h-1.066v-17.060z" fill="currentColor"> </path>
                                <path d="M13.358 26.095l-1.091-17.027-1.064 0.068 1.091 17.027z" fill="currentColor">
                                </path>
                                <path d="M20.805 9.103l-1.064-0.067-1.076 17.060 1.064 0.067z" fill="currentColor">
                                </path>
                            </g>
                        </svg>
                    </div>
                </div>
                <div class="grid grid-cols-1 smmd:grid-cols-2 place-items-baseline px-2 pb-2 w-full">
                    <div class="inline-flex mr-auto rounded-md outline outline-1 outline-[#e5e7eb]">
                        <span
                            class="text-lg p-3 leading-4 border-r flex items-center justify-center cursor-pointer select-none">-</span>
                        <input type="text" value="5"
                            class="text-center text-lg px-2 bg-white max-w-20 focus-within:outline-0">
                        <span
                            class="text-lg p-3 leading-4 border-l flex items-center justify-center cursor-pointer select-none">+</span>
                    </div>
                    <div class="w-full h-full text-base leading-8 text-right font-bold primary-color">0
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="flex flex-col gap-1 border-t py-4 px-4" id="checkout-btn">
        <div class="text-base uppercase flex flex-wrap justify-between items-center font-bold">Subtotal:
            <span id="cart-subtotal" class="flex flex-wrap flex-col md:flex-row font-bold text-xl primary-color"></span>
        </div>
        <div class="w-full">
            @include('components.buttons.button-custom', [
                'onclick' => 'cartPage();',
                'id' => 'sidebar-cart-btn',
                'title' => 'Checkout',
            ])
        </div>
    </div>
</div>
<div class="menuBgOverlay" id="cart-backgroundOverlay"></div>

<style>
    .sidebar-right {
        max-width: 80vw;
        margin-right: -345px;
    }

    .sidebar-right.right-show {
        margin-right: 0px;
    }

    .menuBgOverlay {
        position: fixed;
        width: 100vw;
        height: 100vh;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        background-color: #000000b3;
        transition: all .2s ease-in-out;
    }

    .menuBgOverlay.visible {
        opacity: 1;
        visibility: visible;
    }
</style>

<script>
    // Opens right sidebar cart
    function showcart() {
        let backgrounOverlay = document.getElementById("cart-backgroundOverlay");
        let cartSidebar = document.getElementById("cart-sidebar");
        backgrounOverlay.classList.add("visible");
        cartSidebar.classList.add("right-show");
    }

    // Hides right sidebar cart
    function hidecart() {
        let backgrounOverlay = document.getElementById("cart-backgroundOverlay");
        let cartSidebar = document.getElementById("cart-sidebar");
        backgrounOverlay.classList.remove("visible");
        cartSidebar.classList.remove("right-show");
    }

    // Close sidebar left on background click
    $('#cart-backgroundOverlay').click(function(e) {
        hidecart();
    });

    function cartPage() {
        window.location.href = "{{ route('cart') }}";
    }
</script>
