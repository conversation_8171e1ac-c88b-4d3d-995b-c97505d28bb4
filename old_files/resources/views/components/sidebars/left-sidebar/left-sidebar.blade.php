<div class="sidebar" data-color="white" data-active-color="success">
    <div class="logo">
        <a href="https://www.creative-tim.com" class="simple-text logo-mini">
            <div class="logo-image-small">
                <img src="/img/user-circle.png">
                <i class="nc-icon nc-single-02"></i>
            </div>
            <!-- <p>CT</p> -->
        </a>
        <a href="https://www.creative-tim.com" class="simple-text logo-normal">
            {{ Auth()->user()->name }}
            <!-- <div class="logo-image-big">
          <img src="../assets/img/logo-big.png">
        </div> -->
        </a>
    </div>
    <div class="sidebar-wrapper">
        <ul class="nav">
            <!-- Dashboard -->
            @include('components.sidebars.left-sidebar.menu', [
                'text' => 'Dashboard',
                'route' => 'admin.dashboard',
                'icon' => 'nc-icon nc-layout-11',
            ])

            <!-- Orders -->
            @include('components.sidebars.left-sidebar.menu', [
                'text' => 'Orders',
                'route' => 'admin.orders',
                'icon' => 'nc-icon nc-basket',
            ])

            <!-- Transactions -->
            @if (Auth::user()->role_id === 1 || Auth::user()->role_id === 3 || Auth::user()->role_id === 5)
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Transactions',
                    'route' => 'admin.transaction',
                    'icon' => 'nc-icon nc-money-coins',
                ])
            @endif

            @if (Auth::user()->role_id === 1 || Auth::user()->role_id === 3)
                <!-- Address -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Address',
                    'route' => 'admin.addresses',
                    'icon' => 'nc-icon nc-pin-3',
                ])
            @endif

            @if (Auth::user()->role_id === 1 || Auth::user()->role_id === 3 || Auth::user()->role_id === 4)
                <!-- Products -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Properties',
                    'route' => 'admin.product',
                    'icon' => 'nc-icon nc-box-2',
                ])

                <!-- Requested Products -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Requested Properties',
                    'route' => 'admin.requested-product',
                    // 'icon' => 'nc-icon nc-box-2',
                    'svg' => "
                        <svg style='display: inline-block: width: 2rem; height: 2rem;' viewBox='0 0 512 512' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' fill='currentColor' class=''>
                            <g stroke-width='0'></g>
                            <g stroke-linecap='round' stroke-linejoin='round'></g>
                            <g>
                                <g stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'>
                                    <g fill='currentColor' transform='translate(64.000000, 34.346667)'>
                                        <path
                                            d='M192,7.10542736e-15 L384,110.851252 L384,332.553755 L192,443.405007 L1.42108547e-14,332.553755 L1.42108547e-14,110.851252 L192,7.10542736e-15 Z M127.999,206.918 L128,357.189 L170.666667,381.824 L170.666667,231.552 L127.999,206.918 Z M42.6666667,157.653333 L42.6666667,307.920144 L85.333,332.555 L85.333,182.286 L42.6666667,157.653333 Z M275.991,97.759 L150.413,170.595 L192,194.605531 L317.866667,121.936377 L275.991,97.759 Z M192,49.267223 L66.1333333,121.936377 L107.795,145.989 L233.374,73.154 L192,49.267223 Z'>
                                        </path>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    ",
                ])

                <!-- Business Calculator -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Business Calculator',
                    'route' => 'admin.business-calculators',
                    'icon' => 'nc-icon nc-chart-bar-32',
                ])
            @endif

            @if (Auth::user()->role_id === 1 || Auth::user()->role_id === 3 || Auth::user()->role_id === 6)
                <!-- Chat -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Chat',
                    'route' => 'admin.chat',
                    'icon' => 'nc-icon nc-chat-33',
                ])
            @endif

            @if (Auth::user()->role_id === 1 || Auth::user()->role_id === 3)
                <!-- Users -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Users',
                    'route' => 'admin.user',
                    'icon' => 'nc-icon nc-single-02',
                ])
            @endif

            @if (Auth::user()->role_id === 1 || Auth::user()->role_id === 3)
                <!-- Advert -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Advert',
                    'route' => 'admin.advert',
                    'icon' => 'nc-icon nc-image',
                ])
            @endif

            @if (Auth::user()->role_id === 1 || Auth::user()->role_id === 3)
                <!-- Coupons -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Coupons',
                    'route' => 'admin.coupon',
                    'icon' => 'nc-icon nc-credit-card',
                ])
            @endif

            @if (Auth::user()->role_id === 1)
                <!-- Payment Method -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Payment Method',
                    'route' => 'admin.payment-method',
                    'icon' => 'nc-icon nc-bank',
                ])

                <!-- SMS API -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'SMS API',
                    'route' => 'admin.sms-api',
                    'icon' => 'nc-icon nc-spaceship',
                ])

                <!-- Settings -->
                @include('components.sidebars.left-sidebar.menu', [
                    'text' => 'Settings',
                    'route' => 'admin.settings',
                    'icon' => 'nc-icon nc-settings-gear-65',
                ])
            @endif

            {{-- <li class="active-pro">
                <a href="./upgrade.html">
                    <i class="nc-icon nc-spaceship"></i>
                    <p>Logout</p>
                </a>
            </li> --}}
        </ul>
    </div>
</div>
