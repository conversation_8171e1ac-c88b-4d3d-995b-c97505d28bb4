
<div class="logo" style="background-color: #000;">
    <a href="javascript:void(0)" class="simple-text logo-mini"
        style="width: auto;max-width: 95px;height: 50px;display: flex;">
        <div class="logo-image-small" style="margin: auto;height: 100%;width:auto;max-width: inherit;">
            {{-- <img src="{{ $userinfo->getUserAvatar() }}" alt="User"
                style="width: auto;max-width: 100%;height: 100%;object-fit: contain;" /> --}}
        </div>
    </a>
    <a href="/" class="simple-text logo-normal" style="display: inline-block;color:#fff;font-weight: bold;">
        {{-- {{ Auth::user()->name }} --}} TEST
    </a>
</div>
<div class="sidebar-wrapper">
    <!-- Left Sidebar -->
    <ul class="nav">
        <!-- Home -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(0),
            'href' => 'home',
            'icon' => 'home',
        ])

        <!-- Orders -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(14),
            'href' => 'orders',
            'icon' => 'shopping_cart',
        ])

        <!-- Transactions -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(596),
            'href' => 'transactions',
            'icon' => 'money',
        ])

        <!-- Markets -->
        @if (\Request::is('companies') or \Request::is('marketsreviews'))
            <li class="q-menu-active active">
            @else
            <li class="q-menu-item">
        @endif
        <a href="javascript:void(0);" class="menu-toggle menu-text"> {{-- menu-toggle --}}
            <i class="material-icons">business</i>
            <p class="menu-icon">{{ $lang->get(8) }}
                <i class="fa fa-caret-right dropdown"></i>
            </p> {{-- Markets --}}
        </a>
        <ul class="ml-menu">
            @include('components.sidebars.sub-menu', ['text' => $lang->get(8), 'href' => 'companies']) {{-- Markets --}}
            @include('components.sidebars.sub-menu', ['text' => $lang->get(9), 'href' => 'marketsreviews']) {{-- Markets Reviews --}}
        </ul>
        </li>

        <!-- Roles -->
        @include('components.sidebars.menu-link', [
            'text' => 'User Roles',
            'href' => 'usersRoles',
            'icon' => 'manage_accounts',
        ])

        <!-- Payment Methods -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(29),
            'href' => 'payment-methods',
            'icon' => 'payment',
        ])

        <!-- Vendor Types -->
        @include('components.sidebars.menu-link', [
            'text' => 'Vendor Types',
            'href' => 'vendorTypes',
            'icon' => 'business',
        ])

        <!-- Vendors -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(552),
            'href' => 'vendors',
            'icon' => 'business',
        ])

        <!-- Payment Gateways -->
        @include('components.sidebars.menu-link', [
            'text' => 'Payment Gateways',
            'href' => 'payment-gateways',
            'icon' => 'payments',
        ])

        <!-- Drivers -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(20),
            'href' => 'drivers',
            'icon' => 'directions_car',
        ])

        <!-- Users -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(11),
            'href' => 'users',
            'icon' => 'account_circle',
        ])

        <!-- City -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(637),
            'href' => 'city',
            'icon' => 'foundation',
        ])

        <!-- Categories -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(2),
            'href' => 'categories',
            'icon' => 'category',
        ])

        <!-- Reports -->
        @if (\Request::is('mostpopular') or \Request::is('mostpurchase') or \Request::is('topcompanies'))
            <li class="q-menu-active active">
            @else
            <li class="q-menu-item">
        @endif
        <a href="javascript:void(0);" class="menu-toggle menu-text">
            <i class="material-icons">assessment</i>
            <p class="menu-icon">{{ $lang->get(16) }}
                <i class="fa fa-caret-right dropdown"></i>
            </p>
        </a>
        <ul class="ml-menu">
            @include('components.sidebars.sub-menu', ['text' => $lang->get(17), 'href' => 'mostpopular'])
            @include('components.sidebars.sub-menu', ['text' => $lang->get(18), 'href' => 'mostpurchase'])
            @include('components.sidebars.sub-menu', ['text' => $lang->get(19), 'href' => 'topcompanies'])
        </ul>
        </li>

        <!-- Notifications -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(22),
            'href' => 'notify',
            'icon' => 'notifications',
        ])

        <!-- chat -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(23),
            'href' => 'chat',
            'icon' => 'chat_bubble_outline',
        ])

        <!-- wallet -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(24),
            'href' => 'wallet',
            'icon' => 'credit_card',
        ])

        <!-- Banner -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(505),
            'href' => 'banners',
            'icon' => 'folder_open',
        ])

        <!-- Documents -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(497),
            'href' => 'documents',
            'icon' => 'content_paste',
        ])

        <!-- FAQ -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(26),
            'href' => 'faq',
            'icon' => 'question_answer',
        ])

        <li class="q-menu-header">{{ $lang->get(27) }}</li> {{-- Settings --}}

        <!-- Media Library -->
        @include('components.sidebars.menu-link', [
            'text' => $lang->get(25),
            'href' => 'media',
            'icon' => 'image',
        ])

        <!-- Settings -->
        @if (
            \Request::is('payments') or
                \Request::is('settings') or
                \Request::is('currencies') or
                \Request::is('topproducts') or
                \Request::is('topcompanies2'))
            <li class="q-menu-active active">
            @else
            <li>
        @endif
        <a href="javascript:void(0);" class="menu-toggle menu-text">
            <i class="material-icons">settings</i>
            <p class="menu-icon">{{ $lang->get(27) }}
                <i class="fa fa-caret-right dropdown"></i>
            </p>
        </a>
        <ul class="ml-menu">
            @include('components.sidebars.sub-menu', ['text' => $lang->get(27), 'href' => 'settings'])
            @include('components.sidebars.sub-menu', ['text' => $lang->get(28), 'href' => 'currencies'])
            @include('components.sidebars.sub-menu', ['text' => $lang->get(29), 'href' => 'payment-methods'])
            @include('components.sidebars.sub-menu', ['text' => 'Payment Gateways', 'href' => 'payment-gateways'])
            @include('components.sidebars.sub-menu', ['text' => $lang->get(7), 'href' => 'topproducts'])
            @include('components.sidebars.sub-menu', [
                'text' => $lang->get(10),
                'href' => 'topcompanies2',
            ])
        </ul>
        </li>

        <!-- Customer App Settings -->
        @if (
            \Request::is('caLayout') or
                \Request::is('caLayoutColors') or
                \Request::is('caTheme') or
                \Request::is('caLayoutSizes') or
                \Request::is('caSkins'))
            <li class="q-menu-active active">
            @else
            <li>
        @endif
        <a href="javascript:void(0);" class="menu-toggle menu-text">
            <i class="material-icons">tune</i>
            <p class="menu-icon">{{ $lang->get(30) }}
                <i class="fa fa-caret-right dropdown"></i>
            </p>
        </a>
        <ul class="ml-menu">
            @include('components.sidebars.sub-menu', ['text' => $lang->get(31), 'href' => 'caTheme'])
            @include('components.sidebars.sub-menu', ['text' => $lang->get(32), 'href' => 'caLayout'])
            @include('components.sidebars.sub-menu', ['text' => $lang->get(612), 'href' => 'caSkins']) {{-- Select Skin --}}
            @include('components.sidebars.sub-menu', [
                'text' => $lang->get(33),
                'href' => 'caLayoutColors',
            ])
            @include('components.sidebars.sub-menu', [
                'text' => $lang->get(34),
                'href' => 'caLayoutSizes',
            ])
        </ul>
        </li>

        <!-- App Panel Settings - Admin Panel Settings -->
        @include('components.sidebars.menu-link', [
            'text' => 'App Panel Settings',
            'href' => 'apSettings',
            'icon' => 'app_settings_alt',
        ])

        <!-- Web Site Settings -->
        @if (\Request::is('webSettings') or \Request::is('webSeller'))
            <li class="q-menu-active active">
            @else
            <li>
        @endif
        <a href="javascript:void(0);" class="menu-toggle menu-text">
            <i class="material-icons">web</i>
            <p class="menu-icon">{{ $lang->get(609) }} {{-- Web Site Settings --}}
                <i class="fa fa-caret-right dropdown"></i>
            </p>
        </a>
        <ul class="ml-menu">
            <!-- General -->
            @include('components.sidebars.sub-menu', ['text' => $lang->get(31), 'href' => 'webSettings'])
            <!-- Seller Registration Page -->
            @include('components.sidebars.sub-menu', ['text' => $lang->get(615), 'href' => 'webSeller'])
        </ul>
    </ul>
</div>
