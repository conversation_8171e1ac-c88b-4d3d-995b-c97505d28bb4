@inject('util', 'App\Models\Util')

<ul class="select-none dark-color">
    <!-- Home Menu Item -->
    @include('components.menu.menu-item', [
        'href' => 'home',
        'type' => 'link',
        'name' => 'Home',
        'svg' => "<svg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' height='25px' width='25px' class='w-7 h-7'>
            <g stroke-width='0'></g>
            <g stroke-linecap='round' stroke-linejoin='round'></g>
            <g>
                <path
                    d='M2 12.2039C2 9.91549 2 8.77128 2.5192 7.82274C3.0384 6.87421 3.98695 6.28551 5.88403 5.10813L7.88403 3.86687C9.88939 2.62229 10.8921 2 12 2C13.1079 2 14.1106 2.62229 16.116 3.86687L18.116 5.10812C20.0131 6.28551 20.9616 6.87421 21.4808 7.82274C22 8.77128 22 9.91549 22 12.2039V13.725C22 17.6258 22 19.5763 20.8284 20.7881C19.6569 22 17.7712 22 14 22H10C6.22876 22 4.34315 22 3.17157 20.7881C2 19.5763 2 17.6258 2 13.725V12.2039Z'
                    stroke='currentColor' stroke-width='1.5'></path>
                <path d='M15 18H9' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'></path>
            </g>
        </svg>",
    ])


    <!-- Cart Menu Item -->
    @include('components.menu.menu-item', [
        'href' => 'cart',
        'type' => 'button',
        'onclick' => 'showcart();',
        'name' => 'Cart',
        'id' => 'totalItemsCountDesktopView',
        'svg' => "<svg viewBox='0 0 32 32' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'
            xmlns:sketch='http://www.bohemiancoding.com/sketch/ns' fill='currentColor' height='25px' width='25px' class='w-7 h-7'>
            <g stroke-width='0'></g>
            <g stroke-linecap='round' stroke-linejoin='round'></g>
            <g>
                <g stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' sketch:type='MSPage'>
                    <g sketch:type='MSLayerGroup' transform='translate(-516.000000, -723.000000)'
                        fill='currentColor'>
                        <path
                            d='M538,749 C539.104,749 540,749.896 540,751 C540,752.104 539.104,753 538,753 C536.896,753 536,752.104 536,751 C536,749.896 536.896,749 538,749 L538,749 Z M534,751 C534,753.209 535.791,755 538,755 C540.209,755 542,753.209 542,751 C542,748.791 540.209,747 538,747 C535.791,747 534,748.791 534,751 L534,751 Z M522,729 L546,729 L543.443,739.229 L522,741 L522,729 L522,729 Z M524,745 C522.896,745 522,744.104 522,743 L545,741 C545,741 548,728.875 548,728 C548,727.447 547.553,727 547,727 L522,727 L522,725 L523,725 C523.553,725 524,724.553 524,724 C524,723.448 523.553,723 523,723 L517,723 C516.447,723 516,723.448 516,724 C516,724.553 516.447,725 517,725 L520,725 L520,743 C520,745.209 521.791,747 524,747 L547,747 C547.031,747 547,746.009 547,745 L524,745 L524,745 Z M526,749 C527.104,749 528,749.896 528,751 C528,752.104 527.104,753 526,753 C524.896,753 524,752.104 524,751 C524,749.896 524.896,749 526,749 L526,749 Z M522,751 C522,753.209 523.791,755 526,755 C528.209,755 530,753.209 530,751 C530,748.791 528.209,747 526,747 C523.791,747 522,748.791 522,751 L522,751 Z'
                            sketch:type='MSShapeGroup'> </path>
                    </g>
                </g>
            </g>
        </svg>",
    ])

    @if (Auth::check())
        <!-- Chat Item -->
        @include('components.menu.menu-item', [
            'href' => 'chat',
            'type' => 'link',
            'name' => 'Chat',
            'id' => 'chat-count',
            'svg' => "<svg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' height='25px' width='25px' class='w-7 h-7'>
                <g id='SVGRepo_bgCarrier' stroke-width='0'></g>
                <g id='SVGRepo_tracerCarrier' stroke-linecap='round' stroke-linejoin='round'></g>
                <g id='SVGRepo_iconCarrier'>
                    <path
                        d='M9 12C9 12.5523 8.55228 13 8 13C7.44772 13 7 12.5523 7 12C7 11.4477 7.44772 11 8 11C8.55228 11 9 11.4477 9 12Z'
                        fill='currentColor'></path>
                    <path
                        d='M13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11C12.5523 11 13 11.4477 13 12Z'
                        fill='currentColor'></path>
                    <path
                        d='M17 12C17 12.5523 16.5523 13 16 13C15.4477 13 15 12.5523 15 12C15 11.4477 15.4477 11 16 11C16.5523 11 17 11.4477 17 12Z'
                        fill='currentColor'></path>
                    <path fill-rule='evenodd' clip-rule='evenodd'
                        d='M22.75 12C22.75 6.06294 17.9371 1.25 12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 13.7183 1.65371 15.3445 2.37213 16.7869C2.47933 17.0021 2.50208 17.2219 2.4526 17.4068L1.857 19.6328C1.44927 21.1566 2.84337 22.5507 4.3672 22.143L6.59324 21.5474C6.77814 21.4979 6.99791 21.5207 7.21315 21.6279C8.65553 22.3463 10.2817 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12ZM12 2.75C17.1086 2.75 21.25 6.89137 21.25 12C21.25 17.1086 17.1086 21.25 12 21.25C10.5189 21.25 9.12121 20.9025 7.88191 20.2852C7.38451 20.0375 6.78973 19.9421 6.20553 20.0984L3.97949 20.694C3.57066 20.8034 3.19663 20.4293 3.30602 20.0205L3.90163 17.7945C4.05794 17.2103 3.96254 16.6155 3.7148 16.1181C3.09752 14.8788 2.75 13.4811 2.75 12C2.75 6.89137 6.89137 2.75 12 2.75Z'
                        fill='currentColor'></path>
                </g>
            </svg>",
        ])
    @endif

    <!-- Portfolio Menu Item -->
    @include('components.menu.menu-item', [
        'href' => 'customer.profile',
        'type' => 'link',
        'name' => 'Portfolio',
        'id' => 'wishlistCount',
        'svg' => "<svg fill='currentColor' height='25px' width='25px' version='1.1' id='Layer_1'
            xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'
            viewBox='0 0 512 512' xml:space='preserve' class='w-7 h-7'>
            <g stroke-width='0'></g>
            <g stroke-linecap='round' stroke-linejoin='round'></g>
            <g>
                <g>
                    <g>
                        <path
                            d='M469.779,94.063H352.573l-9.106-36.426c-4.709-18.832-21.554-31.983-40.962-31.983h-93.011 c-19.408,0-36.253,13.152-40.963,31.984l-9.105,36.425H42.221C18.941,94.063,0,113.003,0,136.284v307.841 c0,23.281,18.941,42.221,42.221,42.221h427.557c23.281,0,42.221-18.941,42.221-42.221V136.284 C512,113.003,493.059,94.063,469.779,94.063z M184.086,61.528c2.922-11.682,13.371-19.841,25.409-19.841h93.011 c12.038,0,22.486,8.159,25.409,19.84l8.133,32.536h-18.732l-7.033-28.132c-0.891-3.569-4.098-6.072-7.777-6.072h-93.011 c-3.678,0-6.885,2.503-7.777,6.072l-7.031,28.132h-18.732L184.086,61.528z M300.789,94.063h-89.578l4.543-18.171h80.492 L300.789,94.063z M42.221,110.096h427.557c8.005,0,15.177,3.614,19.985,9.291l-52.05,156.149 c-4.736,14.208-17.98,23.754-32.957,23.754H289.67v-17.637c0-9.136-7.432-16.568-16.568-16.568h-34.205 c-9.136,0-16.568,7.432-16.568,16.568v17.637H107.243c-14.976,0-28.221-9.546-32.957-23.753l-52.05-156.15 C27.044,113.71,34.216,110.096,42.221,110.096z M238.363,316.393v-34.739c0-0.295,0.239-0.534,0.534-0.534h34.205 c0.295,0,0.534,0.239,0.534,0.534v34.739H238.363z M273.637,332.426v17.637c0,0.295-0.239,0.534-0.534,0.534h-34.205 c-0.295,0-0.534-0.239-0.534-0.534v-17.637H273.637z M495.967,444.125c0,14.44-11.748,26.188-26.188,26.188H42.221 c-14.44,0-26.188-11.748-26.188-26.188V151.481l43.042,129.126c6.922,20.765,26.279,34.717,48.168,34.717H222.33v34.739 c0,9.136,7.432,16.568,16.568,16.568h34.205c9.136,0,16.568-7.432,16.568-16.568v-34.739h115.087 c21.889,0,41.245-13.951,48.168-34.717l43.042-129.126V444.125z'>
                        </path>
                    </g>
                </g>
            </g>
        </svg>",
    ])

    <!-- Sell Property Menu Item -->
    @include('components.menu.menu-item', [
        'href' => 'requestProduct',
        'type' => 'link',
        'name' => 'Sell Share/Equity',
        'id' => '',
        'svg' => "<svg fill='currentColor' viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg' height='25px' width='25px' class='w-7 h-7'>
            <g id='SVGRepo_bgCarrier' stroke-width='0'></g>
            <g id='SVGRepo_tracerCarrier' stroke-linecap='round' stroke-linejoin='round'></g>
            <g id='SVGRepo_iconCarrier'>
                <path
                    d='M229.927,70.82178c-.02612-.04883-.04639-.09864-.07373-.147-.01172-.0205-.02612-.03906-.03809-.05957a15.98449,15.98449,0,0,0-5.97168-5.88183l-87.999-49.5a16.09525,16.09525,0,0,0-15.68848,0L32.155,64.73389A15.99355,15.99355,0,0,0,26.168,70.646c-.01539.02685-.03394.05127-.04908.07861-.02441.04346-.042.08887-.06543.13281a15.9818,15.9818,0,0,0-2.05371,7.82129v98.64258A16.02109,16.02109,0,0,0,32.156,191.2666l88.001,49.50049a15.97809,15.97809,0,0,0,7.23486,2.02148c.18189.01416.363.03077.54834.03272h.07227c.25195,0,.50049-.01465.74658-.0376a15.95877,15.95877,0,0,0,7.08447-2.01709l88-49.5a16.01817,16.01817,0,0,0,8.15625-13.94531V78.67871A15.98036,15.98036,0,0,0,229.927,70.82178ZM127.99975,29.17871l79.74366,44.856-30.62061,17.396L96.43969,46.93115Zm.91016,89.64258L48.37866,73.96582,80.03833,56.15723l80.76513,44.54492Zm7.21167,103.43164.78686-89.57373,32.11377-18.24463v38.07617a8,8,0,0,0,16,0v-47.166l30.97754-17.59864v89.5752Z'>
                </path>
            </g>
        </svg>",
    ])

    @if (!Auth::check())
        <!-- Login Menu Item -->
        @include('components.menu.menu-item', [
            'href' => 'login',
            'type' => 'link',
            'name' => 'Login',
            'id' => '',
            'svg' => "<svg fill='currentColor' viewBox='-14.25 0 122.88 122.88' version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg'
                xmlns:xlink='http://www.w3.org/1999/xlink' enable-background='new 0 0 94.38 122.88' xml:space='preserve' height='25px' width='25px' class='w-7 h-7'>
                <g id='SVGRepo_bgCarrier' stroke-width='0'></g>
                <g id='SVGRepo_tracerCarrier' stroke-linecap='round' stroke-linejoin='round'></g>
                <g id='SVGRepo_iconCarrier'>
                    <g>
                        <path
                            d='M8.723,45.706h2.894v-8.729c0-10.139,3.987-19.368,10.412-26.069C28.479,4.177,37.386,0,47.19,0 c9.805,0,18.711,4.177,25.163,10.907c6.424,6.701,10.411,15.931,10.411,26.069v8.729h2.894c2.401,0,4.583,0.98,6.162,2.56 s2.56,3.761,2.56,6.162v59.73c0,2.401-0.98,4.583-2.56,6.162s-3.761,2.56-6.162,2.56H8.723c-2.402,0-4.583-0.98-6.163-2.56 S0,116.56,0,114.158v-59.73c0-2.401,0.981-4.583,2.56-6.162C4.14,46.687,6.321,45.706,8.723,45.706L8.723,45.706z M44,87.301 L39.81,98.28h14.762l-3.884-11.13c2.465-1.27,4.15-3.84,4.15-6.803c0-4.223-3.425-7.647-7.647-7.647 c-4.223,0-7.648,3.425-7.648,7.647C39.542,83.432,41.369,86.091,44,87.301L44,87.301z M17.753,45.706h58.875v-8.729 c0-8.511-3.326-16.236-8.686-21.826C62.61,9.589,55.265,6.137,47.19,6.137S31.77,9.589,26.438,15.15 c-5.359,5.59-8.686,13.315-8.686,21.826V45.706L17.753,45.706z M85.658,51.843H8.723c-0.708,0-1.353,0.292-1.823,0.762 c-0.47,0.47-0.762,1.116-0.762,1.823v59.73c0,0.707,0.292,1.353,0.762,1.822c0.47,0.471,1.116,0.762,1.823,0.762h76.936 c0.708,0,1.354-0.291,1.823-0.762c0.47-0.47,0.762-1.115,0.762-1.822v-59.73c0-0.707-0.292-1.353-0.762-1.823 C87.011,52.135,86.366,51.843,85.658,51.843L85.658,51.843z'>
                        </path>
                    </g>
                </g>
            </svg>",
        ])

        <!-- Signup Menu Item -->
        @include('components.menu.menu-item', [
            'href' => 'register',
            'type' => 'link',
            'name' => 'Sign up',
            'id' => '',
            'svg' => "<svg fill='currentColor' viewBox='0 0 16 16' id='register-16px' xmlns='http://www.w3.org/2000/svg' height='25px' width='25px' class='w-7 h-7'>
                <g id='SVGRepo_bgCarrier' stroke-width='0'></g>
                <g id='SVGRepo_tracerCarrier' stroke-linecap='round' stroke-linejoin='round'></g>
                <g id='SVGRepo_iconCarrier'>
                    <path id='Path_184' data-name='Path 184'
                        d='M57.5,41a.5.5,0,0,0-.5.5V43H47V31h2v.5a.5.5,0,0,0,.5.5h5a.5.5,0,0,0,.5-.5V31h2v.5a.5.5,0,0,0,1,0v-1a.5.5,0,0,0-.5-.5H55v-.5A1.5,1.5,0,0,0,53.5,28h-3A1.5,1.5,0,0,0,49,29.5V30H46.5a.5.5,0,0,0-.5.5v13a.5.5,0,0,0,.5.5h11a.5.5,0,0,0,.5-.5v-2A.5.5,0,0,0,57.5,41ZM50,29.5a.5.5,0,0,1,.5-.5h3a.5.5,0,0,1,.5.5V31H50Zm11.854,4.646-2-2a.5.5,0,0,0-.708,0l-6,6A.5.5,0,0,0,53,38.5v2a.5.5,0,0,0,.5.5h2a.5.5,0,0,0,.354-.146l6-6A.5.5,0,0,0,61.854,34.146ZM54,40V38.707l5.5-5.5L60.793,34.5l-5.5,5.5Zm-2,.5a.5.5,0,0,1-.5.5h-2a.5.5,0,0,1,0-1h2A.5.5,0,0,1,52,40.5Zm0-3a.5.5,0,0,1-.5.5h-2a.5.5,0,0,1,0-1h2A.5.5,0,0,1,52,37.5ZM54.5,35h-5a.5.5,0,0,1,0-1h5a.5.5,0,0,1,0,1Z'
                        transform='translate(-46 -28)'></path>
                </g>
            </svg>",
        ])
    @endif

    <!-- Cart Menu Item -->
    {{-- @include('components.menu.menu-item', [
        'type' => 'button',
        'onclick' => 'showModal();',
        'name' => 'Modal',
        'id' => '',
        'svg' => "<svg viewBox='0 0 32 32' version='1.1' xmlns='http://www.w3.org/2000/svg'
                            xmlns:xlink='http://www.w3.org/1999/xlink' fill='currentColor' height='25px' width='25px'
                            class='w-7 h-7'>
                            <g id='SVGRepo_bgCarrier' stroke-width='0'></g>
                            <g id='SVGRepo_tracerCarrier' stroke-linecap='round' stroke-linejoin='round'></g>
                            <g id='SVGRepo_iconCarrier'>
                                <g id='icomoon-ignore'> </g>
                                <path
                                    d='M30.622 9.602h-22.407l-1.809-7.464h-5.027v1.066h4.188l5.198 21.443c-1.108 0.323-1.923 1.334-1.923 2.547 0 1.472 1.193 2.666 2.666 2.666s2.666-1.194 2.666-2.666c0-0.603-0.208-1.153-0.545-1.599h7.487c-0.337 0.446-0.545 0.997-0.545 1.599 0 1.472 1.193 2.666 2.665 2.666s2.666-1.194 2.666-2.666c0-1.473-1.193-2.665-2.666-2.666v0h-11.403l-0.517-2.133h14.968l4.337-12.795zM13.107 27.196c0 0.882-0.717 1.599-1.599 1.599s-1.599-0.717-1.599-1.599c0-0.882 0.717-1.599 1.599-1.599s1.599 0.718 1.599 1.599zM24.836 27.196c0 0.882-0.718 1.599-1.6 1.599s-1.599-0.717-1.599-1.599c0-0.882 0.717-1.599 1.599-1.599 0.882 0 1.6 0.718 1.6 1.599zM11.058 21.331l-2.585-10.662h20.662l-3.615 10.662h-14.462z'
                                    fill='currentColor'> </path>
                            </g>
                        </svg>",
    ]) --}}

    @if (Auth::check())
        <hr class="my-4">

        <!-- Wallet Menu Item -->
        @include('components.menu.menu-item', [
            'href' => 'customer.profile',
            'type' => 'link',
            'name' => 'Wallet',
            'id' => '',
            'svg' => "<svg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' height='25px' width='25px' class='w-7 h-7'>
                <g id='SVGRepo_bgCarrier' stroke-width='0'></g>
                <g id='SVGRepo_tracerCarrier' stroke-linecap='round' stroke-linejoin='round'></g>
                <g id='SVGRepo_iconCarrier'>
                    <path d='M6 8H10' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'>
                    </path>
                    <path
                        d='M20.8333 9H18.2308C16.4465 9 15 10.3431 15 12C15 13.6569 16.4465 15 18.2308 15H20.8333C20.9167 15 20.9583 15 20.9935 14.9979C21.5328 14.965 21.9623 14.5662 21.9977 14.0654C22 14.0327 22 13.994 22 13.9167V10.0833C22 10.006 22 9.96726 21.9977 9.9346C21.9623 9.43384 21.5328 9.03496 20.9935 9.00214C20.9583 9 20.9167 9 20.8333 9Z'
                        stroke='currentColor' stroke-width='1.5'></path>
                    <path
                        d='M20.965 9C20.8873 7.1277 20.6366 5.97975 19.8284 5.17157C18.6569 4 16.7712 4 13 4L10 4C6.22876 4 4.34315 4 3.17157 5.17157C2 6.34315 2 8.22876 2 12C2 15.7712 2 17.6569 3.17157 18.8284C4.34315 20 6.22876 20 10 20H13C16.7712 20 18.6569 20 19.8284 18.8284C20.6366 18.0203 20.8873 16.8723 20.965 15'
                        stroke='currentColor' stroke-width='1.5'></path>
                    <path d='M17.9912 12H18.0002' stroke='currentColor' stroke-width='2' stroke-linecap='round'
                        stroke-linejoin='round'></path>
                </g>
            </svg>",
        ])

        <li class="block mb-4 relative z-50">
            <div class="relative flex items-center gap-2 py-2 px-3 opacity-85 hover:opacity-100 hover:bg-[#E9FBF5] rounded-md cursor-pointer"
                id="user-menu-button">
                <div class="bg-slate-200 text-sm py-2 px-3 rounded-full uppercase">
                    {{ $util->getFirstCharacters(Auth::user()->name) }}</div>
                <p class="text-lg leading-7 dark-color font-medium">Account</p>
                <svg viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" height="25px" width="25px"
                    class="w-9 h-9 ml-auto">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <g id="icomoon-ignore"> </g>
                        <path
                            d="M19.159 16.767l0.754-0.754-6.035-6.035-0.754 0.754 5.281 5.281-5.256 5.256 0.754 0.754 3.013-3.013z"
                            fill="#000000"> </path>
                    </g>
                </svg>
                <div class="absolute left-0 top-full max-w-full my-2 z-40
                md:top-0 md:left-full md:my-0 md:ml-2 mt-1 w-full md:max-w-52 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
                    role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1"
                    id="user-menu-list">
                    <!-- Active: "bg-gray-100", Not Active: "" -->
                    <a href="{{ route('customer.account') }}"
                        class="flex items-center gap-2 md:gap-4 font-bold px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        role="menuitem" tabindex="-1" id="user-menu-item-0">
                        <svg viewBox="0 0 24 24" id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor" class="w-6 h-6">
                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                            <g id="SVGRepo_iconCarrier">
                                <defs>
                                    <style>
                                        .cls-1 {
                                            fill: none;
                                            stroke: currentColor;
                                            stroke-miterlimit: 10;
                                            stroke-width: 1.91px;
                                        }
                                    </style>
                                </defs>
                                <circle class="cls-1" cx="12" cy="7.25" r="5.73"></circle>
                                <path class="cls-1"
                                    d="M1.5,23.48l.37-2.05A10.3,10.3,0,0,1,12,13h0a10.3,10.3,0,0,1,10.13,8.45l.37,2.05">
                                </path>
                            </g>
                        </svg>
                        <span>
                            Your Profile
                        </span>
                    </a>
                    <a href="{{ route('logout') }}"
                        class="flex items-center gap-2 md:gap-4 font-bold px-4 py-2 text-sm red-color hover:text-white red-bg-hover-color"
                        role="menuitem" tabindex="-1" id="user-menu-item-2">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-6 h-6">
                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                            <g id="SVGRepo_iconCarrier">
                                <path
                                    d="M2 12L1.21913 11.3753L0.719375 12L1.21913 12.6247L2 12ZM11 13C11.5523 13 12 12.5523 12 12C12 11.4477 11.5523 11 11 11V13ZM5.21913 6.3753L1.21913 11.3753L2.78087 12.6247L6.78087 7.6247L5.21913 6.3753ZM1.21913 12.6247L5.21913 17.6247L6.78087 16.3753L2.78087 11.3753L1.21913 12.6247ZM2 13H11V11H2V13Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M10 8.13193V7.38851C10 5.77017 10 4.961 10.474 4.4015C10.9479 3.84201 11.7461 3.70899 13.3424 3.44293L15.0136 3.1644C18.2567 2.62388 19.8782 2.35363 20.9391 3.25232C22 4.15102 22 5.79493 22 9.08276V14.9172C22 18.2051 22 19.849 20.9391 20.7477C19.8782 21.6464 18.2567 21.3761 15.0136 20.8356L13.3424 20.5571C11.7461 20.291 10.9479 20.158 10.474 19.5985C10 19.039 10 18.2298 10 16.6115V16.066"
                                    stroke="currentColor" stroke-width="2"></path>
                            </g>
                        </svg>
                        <span>
                            Sign out
                        </span>
                    </a>
                </div>
            </div>
        </li>
    @endif
</ul>

<script>
    const dropdown = document.getElementById('user-menu-button');
    const menuList = document.getElementById('user-menu-list');
    if (dropdown) {
        dropdown.addEventListener("click", function(e) {
            menuList.classList.toggle("hidden");
            dropdown.classList.toggle("bg-[#E9FBF5]");
        });
    }

    // Close the dropdown if the user clicks outside of it
    // window.onclick = function(event) {
    //     if (menuList) {
    //         console.log(1);
    //         if (!menuList.contains(event.target) && !dropdown.contains(event.target)) {
    //             console.log('hit');
    //             if (!menuList.classList.contains('hidden')) {
    //                 menuList.classList.add('hidden');
    //                 dropdown.classList.remove("bg-[#E9FBF5]");
    //             }
    //         }
    //     }
    // }
</script>
