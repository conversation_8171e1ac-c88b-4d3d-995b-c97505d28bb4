@inject('setting', 'App\Models\Setting')

{{-- hidden --}}
<footer class="w-full block" id="footer">
    <div class="mb-[53px] md:mb-[53px] lg:mb-0">
        <div class="flex flex-wrap flex-col md:gap-4 md:flex-row bg-white shadow-md p-6 border-t">
            <div class="w-full h-full flex-1 py-6 md:p-6">
                <div class="h-[150px] flex">
                    <a href="{{ route('home') }}" class="flex h-full mr-auto">
                        {{-- <img class="img-contain" src="{{ $theme->getLogo() }}" /> --}}
                        <img src="{{ $util->getLogo() }}" class="img-contain" alt="">
                    </a>
                </div>

                <ul class="theme-text-color mt-4">
                    <!-- phone -->
                    @if ($setting->getInfo('phone'))
                        <li class="mb-4 flex gap-2 items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                class="w-8 h-8 primary-text-color">
                                <path fill-rule="evenodd"
                                    d="M1.5 4.5a3 3 0 013-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 01-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 006.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 011.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 01-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="font-semibold text-lg">
                                {{ $setting->getInfo('phone') }}
                            </span>
                        </li>
                    @endif
                    <!-- mobilephone -->
                    {{-- <li class="mb-4">
                        Mobile Phone:
                        <span class="primary-text-color font-semibold text-lg">
                            {Info.mobilephone}
                        </span>
                    </li> --}}
                    {{-- @if ($docs['address'] != '')
                    @endif --}}
                    <!-- address -->
                    @if ($setting->getInfo('address'))
                        <li class="flex gap-2 items-center">
                            <svg fill="currentColor" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"
                                xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" xml:space="preserve"
                                class="w-8 h-8 primary-text-color">
                                <g stroke-width="0"></g>
                                <g stroke-linecap="round" stroke-linejoin="round"></g>
                                <g>
                                    <path
                                        d="M12,2C6.5,2,2,6.5,2,12s4.5,10,10,10s10-4.5,10-10v-1c-0.6,0.9-1.2,1.7-1.7,2.4c-0.4,2.2-1.6,4.1-3.3,5.4 c-0.1-0.3-0.2-0.7-0.3-1c-0.1-0.4-0.2-0.8-0.4-1.2c-0.1-0.4-0.2-0.8-0.4-1.1c-0.1-0.1-0.3-0.2-0.5-0.3c-0.7-0.2-1.6,0.1-2.1-0.6 c-0.2-0.3-0.2-0.6-0.1-1c0.2-0.3,0.3-0.6,0.5-0.9c0.2-0.4,0.5-0.9,0.6-1.4c-0.8-1.2-1.6-2.6-2-3.9h-0.1c-0.1,0-0.1,0-0.2-0.1 c-0.2-0.2-0.3-0.7-0.2-1c0-0.5,0.3-0.8,0.2-1.3c0-0.1-0.1-0.9-0.1-0.9c-0.3,0-0.8,0-0.7-0.5V3.5H12h0.5c0.2-0.6,0.6-1.1,0.9-1.5H12z M18,2c-2.2,0-4,1.8-4,4s4,7,4,7s4-4.8,4-7S20.2,2,18,2z M18,4.5c0.8,0,1.5,0.7,1.5,1.5S18.8,7.5,18,7.5S16.5,6.8,16.5,6 S17.2,4.5,18,4.5z M8,5.1c0.4,0,0.7,0,1,0.1s0.6,0.3,0.8,0.5s0.5,0.5,0.5,0.8c0,0.1,0,0.2-0.1,0.2C10.1,6.8,10,6.8,9.9,6.8 c-0.3,0-0.6,0-0.8-0.1C9,6.6,8.8,6.4,8.6,6.3C8.1,6.1,7.2,7.4,7.1,7.8C7,8.1,7,8.8,7.5,8.9c0.3,0,1-0.6,1.2-0.8C8.9,8,9,7.9,9.2,7.8 c0.8-0.1,1.4,0.6,1.6,1.3C11,9.9,9.6,10.5,9,10.7c-0.2,0.1-0.3-0.1-0.5,0c-0.5,0.2-1.1,0.9-1.1,1.4s-0.1,1-0.2,1.5 c-0.1,0-0.2-0.1-0.2-0.1v-0.2c0-0.3-0.1-0.6-0.4-0.8c-0.1,0-0.1-0.1-0.2-0.1c-0.3-0.1-0.6-0.4-0.9-0.1c-0.2,0.2-0.4,0.5-0.4,0.8 c0,0.1,0,0.2,0.1,0.3c0.2,0.1,0.4,0,0.6,0c0.1,0,0.2,0.2,0.3,0.3c0.2,0.3,0.3,0.8,0.7,0.8h0.7h1.3c0.3,0.1,0.8,0.2,1,0.4 c0.1,0.2,0.1,0.4,0.2,0.6c0.4,0.5,1.1,0.5,1.7,0.7c0.2,0.1,0.3,0.2,0.3,0.4c0,0.3-0.1,0.7-0.2,1s-0.2,0.7-0.4,0.9s-0.4,0.3-0.6,0.4 c-0.4,0.2-0.6,0.6-0.8,0.9c0,0-0.1,0.2-0.2,0.3c-0.8-0.2-1.5-0.5-2.2-1v-0.2c-0.1-0.4-0.2-0.7-0.3-1c-0.2-0.5-0.5-1.1-0.6-1.6 c0-0.5,0.1-1-0.2-1.4c-0.3-0.5-1.1-0.5-1.6-0.8c-0.4-0.4-0.9-0.8-1.3-1.3V12c0-2.7,1.3-5.1,3.3-6.7C7.3,5.2,7.6,5.1,8,5.1z">
                                    </path>
                                    <rect style="fill: none;" width="24" height="24"></rect>
                                    <rect style="fill: none;" width="24" height="24"></rect>
                                </g>
                            </svg>
                            <span class="font-semibold text-lg">
                                {{ $setting->getInfo('address') }}
                            </span>
                        </li>
                    @endif
                </ul>
            </div>

            <!-- Legal info -->
            @if (
                $setting->legalsInfo('about')['about_visible'] == 1 ||
                    $setting->legalsInfo('privacy')['privacy_visible'] == 1 ||
                    $setting->legalsInfo('terms')['terms_visible'] == 1 ||
                    $setting->legalsInfo('delivery')['delivery_visible'] == 1 ||
                    $setting->legalsInfo('refund')['refund_visible'] == 1)
                <div class="w-full h-full flex-1 py-6 md:p-6">
                    <h2 class="mb-6 text-xl font-semibold uppercase">
                        Legal
                    </h2>
                    <ul class="theme-text-color">
                        <!-- About -->
                        @if ($setting->legalsInfo('about')['about_visible'] == 1)
                            <a href="{{ route('aboutUs') }}"
                                class="primary-hover-color transition duration-200 ease-in-out">
                                <li class="mb-4">About us</li>
                            </a>
                        @endif
                        <!-- Privacy -->
                        @if ($setting->legalsInfo('privacy')['privacy_visible'] == 1)
                            <a href="{{ route('privacyPolicy') }}"
                                class="primary-hover-color transition duration-200 ease-in-out">
                                <li class="mb-4">Privacy Policy</li>
                            </a>
                        @endif
                        <!-- Terms -->
                        @if ($setting->legalsInfo('terms')['terms_visible'] == 1)
                            <a href="{{ route('termsCondition') }}"
                                class="primary-hover-color transition duration-200 ease-in-out">
                                <li class="mb-4">Terms &amp; Condition</li>
                            </a>
                        @endif
                        <!-- Delivery -->
                        @if ($setting->legalsInfo('delivery')['delivery_visible'] == 1)
                            <a href="{{ route('deliveryInfo') }}"
                                class="primary-hover-color transition duration-200 ease-in-out">
                                <li class="mb-4">Delivery Info</li>
                            </a>
                        @endif
                        <!-- Refund -->
                        @if ($setting->legalsInfo('refund')['refund_visible'] == 1)
                            <a href="{{ route('refundPolicy') }}"
                                class="primary-hover-color transition duration-200 ease-in-out">
                                <li class="mb-4">Refund Policy</li>
                            </a>
                        @endif
                    </ul>
                </div>
            @endif

            <!-- Legal info -->
            <div class="w-full h-full flex-1 py-6 md:p-6">
                <h2 class="mb-6 text-xl font-semibold uppercase">
                    My Account
                </h2>
                <ul class="theme-text-color">
                    <a href="{{ route('customer.account') }}"
                        class="primary-hover-color transition duration-200 ease-in-out">
                        <li class="mb-4">Profile</li>
                    </a>
                    <a href="{{ route('customer.profile') }}" class="primary-hover-color transition duration-200 ease-in-out">
                        <li class="mb-4">Wishlist</li>
                    </a>
                    <a href="{{ route('cart') }}" class="primary-hover-color transition duration-200 ease-in-out">
                        <li class="mb-4">Cart</li>
                    </a>
                </ul>
            </div>
        </div>

        {{-- @if ($docs['copyright_text'] || $doc->socialMediaLink('facebook') != '' || $doc->socialMediaLink('twitter') != '' || $doc->socialMediaLink('instagram') != '' || $doc->socialMediaLink('telegram') != '')
        @endif --}}
        @if (
            $setting->getInfo('name') ||
                $setting->socialMediasInfo('facebook')['facebook_visible'] == 1 ||
                $setting->socialMediasInfo('youtube')['youtube_visible'] == 1 ||
                $setting->socialMediasInfo('instagram')['instagram_visible'] == 1 ||
                $setting->socialMediasInfo('tiktok')['tiktok_visible'] == 1 ||
                $setting->socialMediasInfo('telegram')['telegram_visible'] == 1)
        @endif
        <div class="w-full primary-bg-color flex items-center">
            <div class="w-full p-4 md:px-12 md:py-4 mx-auto flex items-center justify-between">
                <!-- Copyright -->
                <div class="block text-black">
                    <span class="text-sm font-normal">
                        &#169; 2024 {{ $setting->getInfo('name') }}
                    </span>
                </div>
                <div class="flex gap-4 items-center sm:justify-center text-right text-black">
                    <!-- FacebookLink -->
                    @if ($setting->socialMediasInfo('facebook')['facebook_visible'])
                        <a href="{{ $setting->socialMediasInfo('facebook')['facebook'] }}" target="_blank"
                            class="hover:text-white transition duration-200 ease-in-out font-[900]">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8">
                                <g stroke-width="0"></g>
                                <g stroke-linecap="round" stroke-linejoin="round"></g>
                                <g>
                                    <path
                                        d="M22 12C22 6.47714 17.5229 1.99999 12 1.99999C6.47715 1.99999 2 6.47714 2 12C2 16.9913 5.65686 21.1283 10.4375 21.8785V14.8906H7.89844V12H10.4375V9.79687C10.4375 7.29062 11.9304 5.90624 14.2146 5.90624C15.3087 5.90624 16.4531 6.10155 16.4531 6.10155V8.56249H15.1921C13.9499 8.56249 13.5625 9.33333 13.5625 10.1242V12H16.3359L15.8926 14.8906H13.5625V21.8785C18.3431 21.1283 22 16.9913 22 12Z"
                                        stroke="currentColor" stroke-linejoin="round"></path>
                                </g>
                            </svg>
                        </a>
                    @endif
                    <!-- YouTubeLink -->
                    @if ($setting->socialMediasInfo('youtube')['youtube_visible'])
                        <a href="{{ $setting->socialMediasInfo('youtube')['youtube'] }}" target="_blank"
                            class="hover:text-white transition duration-200 ease-in-out:900">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                class="w-8 h-8">
                                <g stroke-width="0"></g>
                                <g stroke-linecap="round" stroke-linejoin="round"></g>
                                <g>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M20.5949 4.45999C21.5421 4.71353 22.2865 5.45785 22.54 6.40501C22.9982 8.12001 23 11.7004 23 11.7004C23 11.7004 23 15.2807 22.54 16.9957C22.2865 17.9429 21.5421 18.6872 20.5949 18.9407C18.88 19.4007 12 19.4007 12 19.4007C12 19.4007 5.12001 19.4007 3.405 18.9407C2.45785 18.6872 1.71353 17.9429 1.45999 16.9957C1 15.2807 1 11.7004 1 11.7004C1 11.7004 1 8.12001 1.45999 6.40501C1.71353 5.45785 2.45785 4.71353 3.405 4.45999C5.12001 4 12 4 12 4C12 4 18.88 4 20.5949 4.45999ZM15.5134 11.7007L9.79788 15.0003V8.40101L15.5134 11.7007Z"
                                        stroke="currentColor" stroke-linejoin="round"></path>
                                </g>
                            </svg>
                        </a>
                    @endif
                    <!-- InstagramLink -->
                    @if ($setting->socialMediasInfo('instagram')['instagram_visible'])
                        <a href="{{ $setting->socialMediasInfo('instagram')['instagram'] }}" target="_blank"
                            class="hover:text-white transition duration-200 ease-in-out:900">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                class="w-8 h-8">
                                <g stroke-width="0"></g>
                                <g stroke-linecap="round" stroke-linejoin="round"></g>
                                <g>
                                    <path
                                        d="M3.06167 7.24464C3.10844 6.22264 3.26846 5.56351 3.48487 5.00402L3.48778 4.99629C3.70223 4.42695 4.03818 3.91119 4.47224 3.48489L4.47833 3.47891L4.48431 3.47282C4.91096 3.0382 5.42691 2.70258 5.99575 2.4887L6.00556 2.48495C6.56378 2.26786 7.22162 2.10843 8.24447 2.06167M3.06167 7.24464C3.0125 8.33659 2.99997 8.67508 2.99997 11.5063C2.99997 14.3381 3.01181 14.6758 3.06164 15.768M3.06167 7.24464L3.06167 7.52008M3.48867 18.0168C3.70255 18.5856 4.03817 19.1015 4.47279 19.5282L4.47887 19.5342L4.48484 19.5402C4.91116 19.9743 5.42694 20.3103 5.99628 20.5247L6.00478 20.5279C6.56351 20.7446 7.22167 20.9041 8.24447 20.9509M3.48867 18.0168L3.48492 18.0069C3.26783 17.4487 3.1084 16.7909 3.06164 15.768M3.48867 18.0168L3.47585 17.9492M3.06164 15.768L3.07839 15.8562M3.06164 15.768L3.06164 15.4919M3.47585 17.9492L3.07839 15.8562M3.47585 17.9492C3.30704 17.5033 3.13322 16.881 3.07839 15.8562M3.47585 17.9492C3.48177 17.9649 3.48768 17.9803 3.49359 17.9955C3.70766 18.5726 4.04685 19.0952 4.48679 19.5256C4.91708 19.9655 5.43944 20.3046 6.01636 20.5187C6.47934 20.699 7.13172 20.8875 8.24431 20.9385C9.3671 20.9896 9.71399 21 12.5062 21C15.2985 21 15.6457 20.9896 16.7685 20.9385C17.8824 20.8874 18.534 20.6979 18.9954 20.519C19.5726 20.305 20.0953 19.9657 20.5257 19.5256C20.9655 19.0953 21.3046 18.573 21.5187 17.9961C21.699 17.5331 21.8875 16.8808 21.9384 15.7682C21.9895 14.6454 22 14.2978 22 11.5063C22 8.71472 21.9895 8.36684 21.9384 7.24405C21.8871 6.12427 21.6959 5.47168 21.5161 5.00992C21.2811 4.40322 20.9831 3.94437 20.525 3.48627C20.0678 3.02999 19.6102 2.73179 19.003 2.49654C18.5396 2.31537 17.8866 2.12531 16.7685 2.07406C16.6712 2.06964 16.5798 2.06552 16.4921 2.06168M3.07839 15.8562C3.07684 15.8273 3.07539 15.7981 3.07403 15.7685C3.06961 15.6712 3.06548 15.5797 3.06164 15.4919M8.24447 2.06167C9.33668 2.01184 9.67505 2 12.5062 2C15.3374 2 15.6756 2.01252 16.7675 2.06168M8.24447 2.06167L8.52062 2.06167M16.7675 2.06168L16.4921 2.06168M16.7675 2.06168C17.7897 2.10844 18.4489 2.26844 19.0085 2.48487L19.0162 2.48781C19.5855 2.70226 20.1013 3.03821 20.5276 3.47227L20.5335 3.4783L20.5396 3.48422C20.9737 3.91055 21.3096 4.42646 21.5239 4.99596L21.5275 5.00559C21.7446 5.56381 21.9041 6.22165 21.9508 7.2445M8.52062 2.06167L16.4921 2.06168M8.52062 2.06167C9.44548 2.02123 9.95666 2.01253 12.5062 2.01253C15.056 2.01253 15.5671 2.02124 16.4921 2.06168M8.52062 2.06167C8.43284 2.06551 8.34134 2.06964 8.24402 2.07406C7.13004 2.12512 6.47843 2.31464 6.01708 2.49358C5.43767 2.70837 4.91328 3.04936 4.48192 3.49186C4.0281 3.94756 3.73105 4.40422 3.49655 5.0094C3.31536 5.4728 3.12527 6.12614 3.07402 7.24434C3.06961 7.34135 3.06549 7.43257 3.06167 7.52008M21.9508 15.768C21.9041 16.7908 21.7446 17.449 21.5279 18.0077L21.5247 18.0162C21.3102 18.5856 20.9743 19.1013 20.5402 19.5276L20.5341 19.5336L20.5282 19.5397C20.1015 19.9743 19.5856 20.3099 19.0167 20.5238L19.0069 20.5276C18.4487 20.7447 17.7908 20.9041 16.768 20.9509M3.06164 15.4919C3.0212 14.567 3.0125 14.0558 3.0125 11.5063C3.0125 8.95591 3.0212 8.44544 3.06167 7.52008M3.06164 15.4919L3.06167 7.52008M10.8155 15.5881C11.3515 15.8101 11.926 15.9244 12.5062 15.9244C13.678 15.9244 14.8018 15.4589 15.6304 14.6304C16.4589 13.8018 16.9244 12.678 16.9244 11.5063C16.9244 10.3345 16.4589 9.21072 15.6304 8.38215C14.8018 7.55359 13.678 7.0881 12.5062 7.0881C11.926 7.0881 11.3515 7.20238 10.8155 7.42442C10.2794 7.64645 9.79239 7.97189 9.38213 8.38215C8.97187 8.79242 8.64643 9.27947 8.42439 9.81551C8.20236 10.3515 8.08808 10.9261 8.08808 11.5063C8.08808 12.0865 8.20236 12.661 8.42439 13.197C8.64643 13.7331 8.97187 14.2201 9.38213 14.6304C9.79239 15.0406 10.2794 15.3661 10.8155 15.5881ZM9.37229 8.37231C10.2035 7.54113 11.3308 7.07418 12.5062 7.07418C13.6817 7.07418 14.809 7.54113 15.6402 8.37231C16.4714 9.20349 16.9383 10.3308 16.9383 11.5063C16.9383 12.6817 16.4714 13.809 15.6402 14.6402C14.809 15.4714 13.6817 15.9383 12.5062 15.9383C11.3308 15.9383 10.2035 15.4714 9.37229 14.6402C8.54111 13.809 8.07416 12.6817 8.07416 11.5063C8.07416 10.3308 8.54111 9.20349 9.37229 8.37231ZM19.434 6.04229C19.434 6.37873 19.3003 6.70139 19.0625 6.93929C18.8246 7.17719 18.5019 7.31084 18.1655 7.31084C17.829 7.31084 17.5064 7.17719 17.2685 6.93929C17.0306 6.70139 16.8969 6.37873 16.8969 6.04229C16.8969 5.70585 17.0306 5.38319 17.2685 5.1453C17.5064 4.9074 17.829 4.77375 18.1655 4.77375C18.5019 4.77375 18.8246 4.9074 19.0625 5.1453C19.3003 5.38319 19.434 5.70585 19.434 6.04229Z"
                                        stroke="currentColor" stroke-linejoin="round"></path>
                                </g>
                            </svg>
                        </a>
                    @endif
                    <!-- tiktokLink -->
                    @if ($setting->socialMediasInfo('tiktok')['tiktok_visible'])
                        <a href="{{ $setting->socialMediasInfo('tiktok')['tiktok_visible'] }}" target="_blank"
                            class="hover:text-white transition duration-200 ease-in-out:900">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                class="w-8 h-8">
                                <g stroke-width="0"></g>
                                <g stroke-linecap="round" stroke-linejoin="round"></g>
                                <g>
                                    <path
                                        d="M16.8217 5.1344C16.0886 4.29394 15.6479 3.19805 15.6479 2H14.7293M16.8217 5.1344C17.4898 5.90063 18.3944 6.45788 19.4245 6.67608C19.7446 6.74574 20.0786 6.78293 20.4266 6.78293V10.2191C18.645 10.2191 16.9932 9.64801 15.6477 8.68211V15.6707C15.6477 19.1627 12.8082 22 9.32386 22C7.50043 22 5.85334 21.2198 4.69806 19.98C3.64486 18.847 2.99994 17.3331 2.99994 15.6707C2.99994 12.2298 5.75592 9.42509 9.17073 9.35079M16.8217 5.1344C16.8039 5.12276 16.7861 5.11101 16.7684 5.09914M6.9855 17.3517C6.64217 16.8781 6.43802 16.2977 6.43802 15.6661C6.43802 14.0734 7.73249 12.7778 9.32394 12.7778C9.62087 12.7778 9.9085 12.8288 10.1776 12.9124V9.40192C9.89921 9.36473 9.61622 9.34149 9.32394 9.34149C9.27287 9.34149 8.86177 9.36884 8.81073 9.36884M14.7244 2H12.2097L12.2051 15.7775C12.1494 17.3192 10.8781 18.5591 9.32386 18.5591C8.35878 18.5591 7.50971 18.0808 6.98079 17.3564"
                                        stroke="currentColor" stroke-linejoin="round"></path>
                                </g>
                            </svg>
                        </a>
                    @endif
                    <!-- TelegramLink -->
                    @if ($setting->socialMediasInfo('telegram')['telegram_visible'])
                        <a href="{{ $setting->socialMediasInfo('telegram')['telegram_visible'] }}" target="_blank"
                            class="hover:text-white transition duration-200 ease-in-out:900 mr-[15px]">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                class="w-8 h-8">
                                <g stroke-width="0"></g>
                                <g stroke-linecap="round" stroke-linejoin="round"></g>
                                <g>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M21.997 12C21.997 17.5228 17.5198 22 11.997 22C6.47415 22 1.99699 17.5228 1.99699 12C1.99699 6.47715 6.47415 2 11.997 2C17.5198 2 21.997 6.47715 21.997 12ZM12.3553 9.38244C11.3827 9.787 9.43876 10.6243 6.52356 11.8944C6.05018 12.0827 5.8022 12.2669 5.77962 12.4469C5.74147 12.7513 6.12258 12.8711 6.64155 13.0343C6.71214 13.0565 6.78528 13.0795 6.86026 13.1038C7.37085 13.2698 8.05767 13.464 8.41472 13.4717C8.7386 13.4787 9.10009 13.3452 9.49918 13.0711C12.2229 11.2325 13.629 10.3032 13.7172 10.2831C13.7795 10.269 13.8658 10.2512 13.9243 10.3032C13.9828 10.3552 13.977 10.4536 13.9708 10.48C13.9331 10.641 12.4371 12.0318 11.6629 12.7515C11.4216 12.9759 11.2504 13.135 11.2154 13.1714C11.137 13.2528 11.0571 13.3298 10.9803 13.4038C10.506 13.8611 10.1502 14.204 11 14.764C11.4083 15.0331 11.7351 15.2556 12.0611 15.4776C12.4171 15.7201 12.7722 15.9619 13.2317 16.2631C13.3487 16.3398 13.4605 16.4195 13.5694 16.4971C13.9837 16.7925 14.3559 17.0579 14.8158 17.0155C15.083 16.991 15.359 16.7397 15.4992 15.9903C15.8305 14.2193 16.4817 10.382 16.6322 8.80081C16.6454 8.66228 16.6288 8.48498 16.6154 8.40715C16.6021 8.32932 16.5743 8.21842 16.4731 8.13633C16.3533 8.03911 16.1683 8.01861 16.0856 8.02C15.7095 8.0267 15.1324 8.22735 12.3553 9.38244Z"
                                        stroke="currentColor" stroke-linejoin="round"></path>
                                </g>
                            </svg>
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Scroll to top -->
<button
    class="scroll-top fixed z-50 right-4 bottom-28 p-3 lg:right-12 lg:bottom-12 lg:p-4 bg-black text-white rounded-full hover:opacity-60 transition-opacity duration-200 ease-out"
    style="display: none;">
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8">
        <g stroke-width="0"></g>
        <g stroke-linecap="round" stroke-linejoin="round"></g>
        <g>
            <path
                d="M18.2929 15.2893C18.6834 14.8988 18.6834 14.2656 18.2929 13.8751L13.4007 8.98766C12.6195 8.20726 11.3537 8.20757 10.5729 8.98835L5.68257 13.8787C5.29205 14.2692 5.29205 14.9024 5.68257 15.2929C6.0731 15.6835 6.70626 15.6835 7.09679 15.2929L11.2824 11.1073C11.673 10.7168 12.3061 10.7168 12.6966 11.1073L16.8787 15.2893C17.2692 15.6798 17.9024 15.6798 18.2929 15.2893Z"
                fill="currentColor"></path>
        </g>
    </svg>
</button>

<script>
    $(document).ready(function() {
        let mainBody = document.querySelector('#main-body');
        mainBody.onscroll = function(e) {
            let scroll = e.target.scrollTop;

            if (scroll >= 800) {
                $('.scroll-top').fadeIn();
            } else {
                $('.scroll-top').fadeOut();
            }
        };
    });

    /*---- Scroll to top ----*/
    $('.scroll-top').on('click', function() {
        $('#main-body').animate({
            scrollTop: 0
        }, 520);
    });

    // When the user scrolls down 250px from the top of the document, show the button
    // window.onscroll = function() {
    //     scrollFunction()
    // };

    // function scrollFunction() {
    //     if (document.body.scrollTop > 250 || document.documentElement.scrollTop > 250) {
    //         scrollTopBtn.classList.remove("hide-scroll");
    //         scrollTopBtn.classList.add("show-scroll");
    //     } else {
    //         scrollTopBtn.classList.remove("show-scroll");
    //         scrollTopBtn.classList.add("hide-scroll");
    //     }
    // }

    // // When the user clicks on the button, scroll to the top of the document
    // function topFunction() {
    //     // document.body.scrollTop = 0;
    //     // document.documentElement.scrollTop = 0;
    //     $("html, body").animate({
    //         scrollTop: 0
    //     }, 800);
    // }
</script>

{{-- Wishlist --}}
@include('layouts.landing-layout-js.wishlist-management-js')
