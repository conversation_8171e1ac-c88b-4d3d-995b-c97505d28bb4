<button
    @if (isset($onclick)) onclick="{{ $onclick ?? '' }}" @endif
    @if (isset($id)) id="{{ $id }}" @endif
    @if (isset($disabled)) disabled="{{ $disabled }}" @endif
    class="m-auto flex gap-2 md:gap-4 justify-center items-center relative w-full py-4 px-2 md:py-3 md:px-5 text-center text-sm lg:text-base rounded-full @isset($theme) {{ $theme }} @else button-dark @endisset font-bold opacity-90 transition-all duration-200 ease-in-out uppercase hover:opacity-60"
    type="@if (isset($type)) {{ $type }} @else button @endif"
>
    @if (isset($id))
        <svg id="{{ $id . '--loading' }}"
            class="animate-spin -ml-1 h-6 w-6 @isset($mr) {{ $mr }} @else mr-3 @endisset flex hidden"
            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
            </circle>
            <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
        </svg>
    @endif

    @if (isset($svg))
        {!! $svg !!}
    @endif

    @isset($title)
        {{ $title }}
    @endisset
</button>
