<div class="custom-select custom-select_{{ $id }}">
    {{-- onchange="changeSort" --}}
    <select name="sort-by" id="sort-by" class="nice-select" data-p="{{ $id }}" data-pv="0">
        <option value="0">-- Select address --</option>
        @foreach ($data['branch_products'] as $branchProduct)
            <option value="{{ $branchProduct['branch_id'] }}">{{ $branchProduct['branch']['name'] }}</option>
        @endforeach
    </select>
</div>

<script>
    // function changeSort(e) {
    //     var selectBox = document.getElementById("sort-by");
    //     var selectedValue = selectBox.options[selectBox.selectedIndex].value;
    //     console.log("event: ", e, selectedValue, selectBox);
    //     sortProduct = selectedValue;
    //     let b = selectedValue;
    //     let pId = selectBox.getAttribute("data-p");
    //     let pvId = selectBox.getAttribute("data-pv");
    //     console.log('b, p , pv: ', b, spId, pvId);
    //     return;
    //     branchSelect(0, b, id, variantId);
    // }

    // function addIcon() {
    //     // Arrow Icon
    //     const iconSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    //     const iconPath = document.createElementNS(
    //         'http://www.w3.org/2000/svg',
    //         'path'
    //     );

    //     iconSvg.setAttribute('fill', 'none');
    //     iconSvg.setAttribute('viewBox', '0 0 24 24');
    //     iconSvg.setAttribute('stroke', 'currentColor');
    //     iconSvg.classList.add('w-[25px]');
    //     iconSvg.classList.add('h-[25px]');

    //     iconPath.setAttribute(
    //         'd',
    //         'M19.5 8.25l-7.5 7.5-7.5-7.5'
    //     );
    //     iconPath.setAttribute('stroke-linecap', 'round');
    //     iconPath.setAttribute('stroke-linejoin', 'round');
    //     iconPath.setAttribute('stroke-width', '2');

    //     iconSvg.appendChild(iconPath);
    //     return iconSvg;
    // }

    // var x, i, j, l, ll, selElmnt, a, b, c;
    // /* Look for any elements with the class "custom-select": */
    // x = document.getElementsByClassName("custom-select_{{ $id }}");
    // l = x.length;
    // for (i = 0; i < l; i++) {
    //     selElmnt = x[i].getElementsByTagName("select")[0];
    //     ll = selElmnt.length;
    //     /* For each element, create a new DIV that will act as the selected item: */
    //     a = document.createElement("DIV");
    //     a.setAttribute("class", "select-selected line-clamp-1");
    //     let arrowDownIcon = addIcon();
    //     a.innerHTML = selElmnt.options[selElmnt.selectedIndex].innerHTML;
    //     a.appendChild(arrowDownIcon);
    //     x[i].appendChild(a);
    //     /* For each element, create a new DIV that will contain the option list: */
    //     b = document.createElement("DIV");
    //     b.setAttribute("class", "select-items");
    //     for (j = 0; j < ll; j++) {
    //         /* For each option in the original select element,
    //         create a new DIV that will act as an option item: */
    //         c = document.createElement("DIV");
    //         c.innerHTML = selElmnt.options[j].innerHTML;
    //         c.setAttribute("data-value", selElmnt.options[j].value);
    //         if (j == 0) {
    //             c.setAttribute("class", "same-as-selected");
    //         }
    //         c.addEventListener("click", function(e) {
    //             /* When an item is clicked, update the original select box,
    //             and the selected item: */
    //             var y, i, k, s, h, sl, yl;
    //             s = this.parentNode.parentNode.getElementsByTagName("select")[0];
    //             sl = s.length;
    //             h = this.parentNode.previousSibling;
    //             for (i = 0; i < sl; i++) {
    //                 if (s.options[i].innerHTML == this.innerHTML) {
    //                     s.selectedIndex = i;
    //                     h.innerHTML = this.innerHTML;
    //                     h.appendChild(arrowDownIcon);
    //                     y = this.parentNode.getElementsByClassName("same-as-selected");
    //                     yl = y.length;
    //                     for (k = 0; k < yl; k++) {
    //                         y[k].removeAttribute("class");
    //                     }
    //                     this.setAttribute("class", "same-as-selected");
    //                     sortProduct = this.getAttribute("data-value");
    //                     console.log("sortProduct: ", sortProduct);
    //                     let pId = this.getAttribute("data-p");
    //                     let pvId = this.getAttribute("data-pv");
    //                     console.log('b, p , pv: ', b, pId, pvId);
    //                     paginationGoPage(1);
    //                     break;
    //                 }
    //             }
    //             // h.click();
    //         });
    //         b.appendChild(c);
    //     }
    //     x[i].appendChild(b);
    //     a.addEventListener("click", function(e) {
    //         /* When the select box is clicked, close any other select boxes,
    //         and open/close the current select box: */
    //         e.stopPropagation();
    //         closeAllSelect(this);
    //         // this.nextSibling.classList.toggle("select-hide");
    //         if (this.nextSibling.classList.contains("rolldown")) {
    //             this.nextSibling.classList.remove("rolldown");
    //             this.nextSibling.classList.add("rollup");
    //         } else {
    //             this.nextSibling.classList.remove("rollup");
    //             this.nextSibling.classList.add("rolldown");
    //         }
    //         this.classList.toggle("select-arrow-active");
    //     });
    // }

    // function closeAllSelect(elmnt) {
    //     /* A function that will close all select boxes in the document,
    //     except the current select box: */
    //     var x, y, i, xl, yl, arrNo = [];
    //     x = document.getElementsByClassName("select-items");
    //     y = document.getElementsByClassName("select-selected");
    //     xl = x.length;
    //     yl = y.length;
    //     for (i = 0; i < yl; i++) {
    //         if (elmnt == y[i]) {
    //             arrNo.push(i)
    //         } else {
    //             y[i].classList.remove("select-arrow-active");
    //         }
    //     }
    //     for (i = 0; i < xl; i++) {
    //         if (arrNo.indexOf(i)) {
    //             // x[i].classList.add("rollup");
    //             // x[i].classList.remove("rolldown");
    //             // modified
    //             if (x[i].classList.contains("rolldown")) {
    //                 x[i].classList.remove("rolldown");
    //                 x[i].classList.add("rollup");
    //             }
    //         }
    //     }
    // }

    // /* If the user clicks anywhere outside the select box,
    // then close all select boxes: */
    // document.addEventListener("click", closeAllSelect);

    // Address select
    const initializAddress = (id, variantId, divClass) => {
        const addIcon = () => {
            // Arrow Icon
            const iconSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            const iconPath = document.createElementNS(
                'http://www.w3.org/2000/svg',
                'path'
            );

            iconSvg.setAttribute('fill', 'none');
            iconSvg.setAttribute('viewBox', '0 0 24 24');
            iconSvg.setAttribute('stroke', 'currentColor');
            iconSvg.classList.add('w-[25px]');
            iconSvg.classList.add('h-[25px]');

            iconPath.setAttribute(
                'd',
                'M19.5 8.25l-7.5 7.5-7.5-7.5'
            );
            iconPath.setAttribute('stroke-linecap', 'round');
            iconPath.setAttribute('stroke-linejoin', 'round');
            iconPath.setAttribute('stroke-width', '2');

            iconSvg.appendChild(iconPath);
            return iconSvg;
        }

        var x, i, j, l, ll, selElmnt, a, b, c;
        /* Look for any elements with the class "custom-select": */
        x = document.getElementsByClassName(divClass);
        // Set Default Address for add that have only one
        @if (count($data['branch_products']) == 1)
            $(document).ready(function() {
                branchSelect(0, {{ $data['branch_products'][0]['branch_id'] }}, {{ $data['id'] }}, 0);
            });
            x[0].innerHTML =
                `<a target="_blank" href="https://www.google.com/maps/place/{{ $data['branch_products'][0]['branch']['lat'] }},{{ $data['branch_products'][0]['branch']['lng'] }}">{{ $data['branch_products'][0]['branch']['name'] }}</a>`;
            $(document).ready(function() {
                showPosition(@json($data['branch_products'][0]['branch']));
            });
            return;
        @endif
        l = x.length;
        for (i = 0; i < l; i++) {
            selElmnt = x[i].getElementsByTagName("select")[0];
            ll = selElmnt.length;
            /* For each element, create a new DIV that will act as the selected item: */
            a = document.createElement("DIV");
            a.setAttribute("class", "select-selected line-clamp-1");
            let arrowDownIcon = addIcon();
            a.innerHTML = selElmnt.options[selElmnt.selectedIndex].innerHTML;
            a.appendChild(arrowDownIcon);
            x[i].appendChild(a);
            /* For each element, create a new DIV that will contain the option list: */
            b = document.createElement("DIV");
            b.setAttribute("class", "select-items");
            for (j = 0; j < ll; j++) {
                /* For each option in the original select element,
                create a new DIV that will act as an option item: */
                c = document.createElement("DIV");
                c.innerHTML = selElmnt.options[j].innerHTML;
                c.setAttribute("data-value", selElmnt.options[j].value);
                if (j == 0) {
                    c.setAttribute("class", "same-as-selected");
                }
                c.addEventListener("click", function(e) {
                    /* When an item is clicked, update the original select box,
                    and the selected item: */
                    var y, i, k, s, h, sl, yl;
                    s = this.parentNode.parentNode.getElementsByTagName("select")[0];
                    sl = s.length;
                    h = this.parentNode.previousSibling;
                    for (i = 0; i < sl; i++) {
                        if (s.options[i].innerHTML == this.innerHTML) {
                            s.selectedIndex = i;
                            h.innerHTML = this.innerHTML;
                            h.appendChild(arrowDownIcon);
                            y = this.parentNode.getElementsByClassName("same-as-selected");
                            yl = y.length;
                            for (k = 0; k < yl; k++) {
                                y[k].removeAttribute("class");
                            }
                            let err = document.querySelector('.select-selected');
                            if (err && err.style.border) err.style.border = "";
                            this.setAttribute("class", "same-as-selected");
                            // console.log('select: ', 0, this.getAttribute("data-value"), id, variantId);
                            branchSelect(0, this.getAttribute("data-value"), id, variantId, 1);
                            break;
                        }
                    }
                    // h.click();
                });
                b.appendChild(c);
                @if (count($data['branch_products']) > 1)
                    if (j == 0) {
                        $(document).ready(function() {
                            c.click();
                        });
                    }
                @endif
            }
            x[i].appendChild(b);
            // Disable dropdown menu
            @if (count($data['branch_products']) == 1)
                a.classList.add("disabled-select");
                $(document).ready(function() {
                    branchSelect(0, {{ $data['branch_products'][0]['branch_id'] }}, {{ $data['id'] }},
                        0);
                });
            @else
                a.addEventListener("click", function(e) {
                    /* When the select box is clicked, close any other select boxes,
                    and open/close the current select box: */
                    e.stopPropagation();
                    closeAllSelect(this);
                    // this.nextSibling.classList.toggle("select-hide");
                    if (this.nextSibling.classList.contains("rolldown")) {
                        this.nextSibling.classList.remove("rolldown");
                        this.nextSibling.classList.add("rollup");
                    } else {
                        this.nextSibling.classList.remove("rollup");
                        this.nextSibling.classList.add("rolldown");
                    }
                    this.classList.toggle("select-arrow-active");
                });
            @endif
        }

        const closeAllSelect = (elmnt) => {
            /* A function that will close all select boxes in the document,
            except the current select box: */
            var x, y, i, xl, yl, arrNo = [];
            x = document.getElementsByClassName("select-items");
            y = document.getElementsByClassName("select-selected");
            xl = x.length;
            yl = y.length;
            for (i = 0; i < yl; i++) {
                if (elmnt == y[i]) {
                    arrNo.push(i)
                } else {
                    y[i].classList.remove("select-arrow-active");
                }
            }
            for (i = 0; i < xl; i++) {
                if (arrNo.indexOf(i)) {
                    // x[i].classList.add("rollup");
                    // x[i].classList.remove("rolldown");
                    // modified
                    if (x[i].classList.contains("rolldown")) {
                        x[i].classList.remove("rolldown");
                        x[i].classList.add("rollup");
                    }
                }
            }
        }

        /* If the user clicks anywhere outside the select box,
        then close all select boxes: */
        document.addEventListener("click", closeAllSelect);
    }
    initializAddress({{ $data['id'] }}, 0, 'custom-select_{{ $id }}');
</script>

<style>
    .disabled-select {
        background-color: #e6e6e6 !important;
        cursor: no-drop;
    }
</style>
