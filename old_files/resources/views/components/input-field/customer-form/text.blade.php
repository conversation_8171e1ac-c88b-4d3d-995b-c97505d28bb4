@if (isset($label))
    <label class="block text-sm md:text-base text-gray-900 select-none" for="{{ $id }}">
        {{ $label }} {!! isset($important) && $important == 0 ? '' : '<span class="error-text">*</span>' !!}
    </label>
@endif
<div
    class="relative flex items-center bg-[#ffffff] my-2 border border-solid transition duration-200 ease-in-out active-focus-border w-full lg:text-xs rounded-lg">
    <input
        class="bg-[#ffffff] border-0 focus-visible:outline-none py-3 px-4 w-full lg:text-base rounded-lg @isset($currency) pr-20 @endif"
        placeholder="{{ $placeholder }}" id="{{ $id }}" name="{{ $name }}" type="{{ $type }}"
        value="{{ $value ?? '' }}" required aria-label="{{ $ariaLabel }}" oninput="{{ $oninput ?? '' }}"
        @isset($disabled) {{ $disabled ? 'disabled readonly' : '' }} @endisset />
    @isset($currency)
        <div class="absolute
        flex items-center justify-center right-0 top-0 h-full text-base bg-slate-200 text-gray-600 p-4 border
        rounded-r-lg">ETB
</div>
@endif
</div>
