<label class="block text-sm md:text-base text-gray-900 select-none" for="{{ $id }}">
    {{ $label }} <span class="error-text">*</span>
</label>

<div id="{{ $id }}"
    class="fallback dropzone my-2 transition duration-200 ease-in-out active-focus-border rounded-lg border-dashed">
    <div class="dz-message text-slate-600">
        <div class="drag-icon-cph">
            <i class="nc-icon nc-tap-01" style="font-size: 4.8rem;"></i>
            <svg fill="currentColor" height="200px" width="200px" version="1.1" id="Layer_1"
                xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                viewBox="0 0 512.001 512.001" xml:space="preserve" class="m-auto w-12 h-12 mb-2">
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                <g id="SVGRepo_iconCarrier">
                    <g>
                        <g>
                            <path
                                d="M401.809,212.523c-12.295-1.17-24.556,2.892-33.639,11.15c-1.122,1.021-2.186,2.096-3.188,3.217 c-6.805-12.704-19.329-21.819-33.946-23.214c-12.295-1.17-24.556,2.892-33.639,11.15c-1.122,1.021-2.186,2.096-3.188,3.217 c-5.941-11.089-16.24-19.443-28.485-22.315c21.223-21.098,33.958-50.2,33.958-81.275C299.681,51.344,248.337,0,185.227,0 S70.774,51.344,70.774,114.454c0,46.302,28.254,88.244,70.773,105.817v49.155l-31.869,22.764 c-18.882,13.488-26.638,37.341-19.3,59.353l31.431,94.297c13.193,39.573,50.082,66.162,91.796,66.162h130.862 c53.354,0,96.76-43.406,96.76-96.76V257.522C441.227,234.396,423.913,214.632,401.809,212.523z M87.361,114.454 c0-53.963,43.903-97.866,97.866-97.866c53.963,0,97.866,43.903,97.866,97.866c0,37.248-21.382,71.191-54.186,87.594v-21.686 c21.942-14.579,35.387-39.4,35.387-65.908c0-43.597-35.47-79.067-79.067-79.067c-43.597,0-79.067,35.47-79.067,79.067 c0,26.506,13.446,51.328,35.387,65.908v21.686C108.745,185.645,87.361,151.701,87.361,114.454z M189.489,70.978 c-12.296-1.172-24.556,2.89-33.638,11.149c-9.09,8.265-14.304,20.048-14.304,32.327v44.644 c-11.839-11.626-18.799-27.699-18.799-44.644c0-34.451,28.028-62.479,62.479-62.479c34.451,0,62.479,28.028,62.479,62.479 c0,16.947-6.96,33.019-18.799,44.645v-43.123C228.908,92.85,211.594,73.084,189.489,70.978z M344.467,495.413H213.604 c-34.564,0-65.129-22.03-76.059-54.819l-31.431-94.296c-5.022-15.061,0.285-31.381,13.205-40.609l22.228-15.878v72.352 c0,4.58,3.712,8.294,8.294,8.294c4.581,0,8.294-3.713,8.294-8.294V114.454c0-7.617,3.235-14.927,8.874-20.053 c5.716-5.197,13.146-7.652,20.906-6.91c13.686,1.304,24.406,13.816,24.406,28.484v175.413c0,4.58,3.712,8.294,8.294,8.294 c4.581,0,8.294-3.713,8.294-8.294v-53.08c0-7.617,3.235-14.927,8.874-20.053c5.715-5.196,13.137-7.657,20.906-6.91 c13.685,1.305,24.405,13.817,24.405,28.485v7.325v53.08c0,4.58,3.712,8.294,8.294,8.294s8.294-3.713,8.294-8.294v-53.08 c0-7.617,3.235-14.927,8.874-20.053c5.715-5.196,13.137-7.657,20.906-6.91c13.685,1.305,24.405,13.817,24.405,28.485V256v53.08 c0,4.58,3.712,8.294,8.294,8.294s8.294-3.713,8.294-8.294V256c0-7.617,3.234-14.927,8.874-20.053 c5.715-5.196,13.137-7.657,20.906-6.91c13.685,1.305,24.405,13.817,24.405,28.485V415.24h0.003 C424.64,459.448,388.675,495.413,344.467,495.413z">
                            </path>
                        </g>
                    </g>
                </g>
            </svg>
        </div>
        <h3>Drop files here or click to upload.</h3>
    </div>
    <div class="fallback">
        <input name="file" type="file" multiple />
    </div>
</div>

<style>
    .swal-wide {
        position: fixed !important;
        width: 70% !important;
        left: 30% !important;
    }
</style>

<script>
    let editFileNameNotify{{ $id }};

    Dropzone.autoDiscover = false;

    let my{{ $id }} = new Dropzone("div#{{ $id }}", {
        url: "{{ url('image/upload/store-customer') }}",
        maxFilesize: 5.0,
        renameFile: function(file) {
            let dt = new Date();
            // let time = dt.getTime();
            // return time + file.name;
            return file.name;
        },
        init: function() {
            this.hiddenFileInput.removeAttribute('multiple');
        },
        sending: function(file, xhr, formData) {
            formData.append("_token", $('meta[name="_token"]').attr('content'));
        },
        acceptedFiles: ".jpeg,.jpg,.png,.gif",
        addRemoveLinks: true,
        timeout: 50000,
        removedfile: function(file) {
            // console.log("removedfile " + this.files.length);
            if (this.files.length == 0)
                {{ $id }} = 0;
            let fileRef;
            return (fileRef = file.previewElement) != null ?
                fileRef.parentNode.removeChild(file.previewElement) : void 0;
        },
        success: async function(file, response) {
            if (this.files.length > 1) {
                this.removeFile(this.files[0]);
            }
            // console.log("response: ", response);
            {{ $id }}_data = response.data;
            {{ $id }} = response.data.id;
            // console.log("success " + {{ $id }});
        },
        error: function(file, response) {
            return Toast.fire({
                icon: "error",
                title: response
            });
            return false;
        }
    });

    function loadDialog2(data) {
        let text = `<div id="div1" style="height: 600px; position:relative;">
                        <div id="div2" style="max-height:100%; min-height: 100%; overflow:auto; border:2px solid grey;">
                            <div id="thumbimagesEdit" class="row">`;
        data.forEach(function(data, i, arr) {
            text = `${text}
                    <div class="col-md-2" style="position: relative; top: 10px; left: 20px; height: 250px; margin-bottom: 10px">
                        <div id="thumbEdit${data.filename}" onclick="klikajEdit3('thumbEdit${data.filename}',
                                    'iconokEdit${data.filename}', ${data.id}, '${data.filename}')"  class="thumbnail" style="height: 250px">
                                <img id="iconokEdit${data.filename}"  src="img/iconok.png" style='visibility:hidden; width: 40px; position: absolute; z-index: 100; top: 100px; left: 50px' >
                                <img src="images/${data.filename}" class="img-thumbnail" style='height: 150px; max-height: 150px; min-height: 150px; object-fit: contain; z-index: 10; ' >

                               <div style="font-size: 13px; overflow: hidden; font-weight: bold;">${data.title}</div>
                               <p>${data.updated_at}</p>
                           </div>
                       </div>
            `;
        });
        text = `${text}</div></div></div>`;

        swal({
            title: "",
            text: text,
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Ok",
            cancelButtonText: "Cancel",
            customClass: 'swal-wide',
            closeOnConfirm: true,
            closeOnCancel: true,
            html: true
        }, function(isConfirm) {
            if (isConfirm) {
                if (selectId{{ $id }} != "") {
                    {{ $id }} = selectId{{ $id }};
                    mockFile = {
                        name: "images/" + selectName{{ $id }},
                        size: 0,
                        dataURL: "images/" + selectName{{ $id }},
                    };
                    my{{ $id }}.createThumbnailFromUrl(mockFile, my{{ $id }}.options
                        .thumbnailWidth, my{{ $id }}
                        .options.thumbnailHeight, my{{ $id }}.options.thumbnailMethod, true,
                        function(dataUrl) {
                            my{{ $id }}.emit("thumbnail", mockFile, dataUrl);
                        });
                    my{{ $id }}.emit("addedfile", mockFile);
                    my{{ $id }}.emit("complete", mockFile);
                    my{{ $id }}.files.push(mockFile);
                    if (my{{ $id }}.files.length > 1) {
                        my{{ $id }}.removeFile(my{{ $id }}.files[0]);
                    }
                    editFileNameNotify{{ $id }} = selectName{{ $id }};
                }
            } else {

            }
        })
    }

    let lastEdit{{ $id }} = "";
    let lastJEdit{{ $id }} = "";
    let selectId{{ $id }} = "";
    let selectName{{ $id }} = "";

    function klikajEdit3(i, j, id, name) {
        selectName{{ $id }} = name;
        if (lastEdit{{ $id }} !== "")
            document.getElementById(lastEdit{{ $id }}).style.borderColor = "#e0e0e0";
        if (lastJEdit{{ $id }} !== "")
            document.getElementById(lastJEdit{{ $id }}).style.visibility = 'hidden';
        lastJEdit{{ $id }} = j;
        lastEdit{{ $id }} = i;
        document.getElementById(i).style.border = "3";
        document.getElementById(i).style.borderColor = "#00FF00";
        document.getElementById(i).style.borderStyle = "solid";
        document.getElementById(j).style.visibility = 'visible';
        selectId{{ $id }} = id;
    }

    let editFileName{{ $id }};
    let {{ $id }} = 0;
    let {{ $id }}_data = {};

    function clear{{ $id }}() {
        {{ $id }} = 0;
        if (my{{ $id }}.files.length == 1) {
            my{{ $id }}.removeFile(my{{ $id }}.files[0]);
        }
    }

    function addEditImage{{ $id }}(image) {
        let id = image.id;
        let path = image.image_path;
        let fileImage = image.filename;
        let size = image.size;
        if (my{{ $id }}.files.length == 1) {
            my{{ $id }}.removeFile(my{{ $id }}.files[0]);
        }
        if (id == 0 || fileImage == "noimage.png")
            return;
        editFileName{{ $id }} = fileImage;
        {{ $id }} = id;
        mockFile{{ $id }} = {
            name: fileImage,
            size: size,
            dataURL: path
        };
        my{{ $id }}.createThumbnailFromUrl(mockFile{{ $id }}, my{{ $id }}.options.thumbnailWidth,
            my{{ $id }}.options
            .thumbnailHeight, my{{ $id }}.options.thumbnailMethod, true,
            function(dataUrl) {
                my{{ $id }}.emit("thumbnail", mockFile{{ $id }}, dataUrl);
            });
        my{{ $id }}.emit("addedfile", mockFile{{ $id }});
        my{{ $id }}.emit("complete", mockFile{{ $id }});
        my{{ $id }}.files.push(mockFile{{ $id }});
    }
</script>
