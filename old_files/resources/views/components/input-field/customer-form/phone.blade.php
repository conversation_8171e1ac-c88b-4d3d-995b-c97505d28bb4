<label class="block text-sm md:text-base text-gray-900 select-none" for="{{ $id }}">
    {{ $label }} {!! isset($important) && $important == 0 ? '' : '<span class="error-text">*</span>' !!}
</label>
<div
    class="relative flex items-center bg-[#ffffff] my-2 border border-solid transition duration-200 ease-in-out active-focus-border w-full lg:text-xs rounded-lg">
    <div class="mx-[15px] w-[45px] h-[45px] items-center rounded-l-[5px] select-none flex">
        <img src="{{ asset('img/ET_flag.png') }}" class="img-contain" alt="Ethiopian Flag" />
    </div>
    <input class="bg-[#ffffff] border-0 focus-visible:outline-none py-3 px-4 w-full lg:text-base rounded-r-lg"
        placeholder="{{ $placeholder ?? '0*********' }}" id="{{ $id }}" name="{{ $name }}"
        type="tel" required aria-label="{{ $ariaLabel }}" minlength="9" maxlength="12"
        value="{{ $value }}" @isset($disabled) {{ $disabled ? 'disabled readonly' : '' }} @endisset />
</div>
