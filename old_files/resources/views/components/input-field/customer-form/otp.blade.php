<label class="block text-sm md:text-base text-gray-900 select-none" for="{{ $id }}_0">
    {{ $label }} <span class="error-text">*</span>
    @if (isset($id))
        <svg id="{{ $id . '--loading' }}"
            class="animate-spin -ml-1 h-6 w-6 @isset($mr) {{ $mr }} @else mr-3 @endisset flex hidden"
            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
            </circle>
            <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
        </svg>
    @endif
</label>
<div class="flex gap-4 items-center max-w-full text-base">
    <div
        class="relative overflow-hidden flex items-center bg-[#ffffff] my-2 border border-solid transition duration-200 ease-in-out active-focus-border w-full rounded-lg">
        <input
            class="bg-[#ffffff] text-center otp-input border-0 focus-visible:outline-none py-3 px-4 w-full rounded-lg text-xl"
            placeholder="" id="{{ $id }}_0" name="{{ $name }}" type="{{ $type }}" required
            max="9" maxlength="1" aria-label="{{ $ariaLabel }}" />
    </div>
    <div
        class="relative overflow-hidden flex items-center bg-[#ffffff] my-2 border border-solid transition duration-200 ease-in-out active-focus-border w-full rounded-lg">
        <input
            class="bg-[#ffffff] text-center otp-input border-0 focus-visible:outline-none py-3 px-4 w-full rounded-lg text-xl"
            placeholder="" id="{{ $id }}_1" name="{{ $name }}" type="{{ $type }}" required
            max="9" maxlength="1" aria-label="{{ $ariaLabel }}" />

    </div>
    <div
        class="relative overflow-hidden flex items-center bg-[#ffffff] my-2 border border-solid transition duration-200 ease-in-out active-focus-border w-full rounded-lg">
        <input
            class="bg-[#ffffff] text-center otp-input border-0 focus-visible:outline-none py-3 px-4 w-full rounded-lg text-xl"
            placeholder="" id="{{ $id }}_2" name="{{ $name }}" type="{{ $type }}" required
            max="9" maxlength="1" aria-label="{{ $ariaLabel }}" />
    </div>
    <div
        class="relative overflow-hidden flex items-center bg-[#ffffff] my-2 border border-solid transition duration-200 ease-in-out active-focus-border w-full rounded-lg">
        <input
            class="bg-[#ffffff] text-center otp-input border-0 focus-visible:outline-none py-3 px-4 w-full rounded-lg text-xl"
            placeholder="" id="{{ $id }}_3" name="{{ $name }}" type="{{ $type }}" required
            max="9" maxlength="1" aria-label="{{ $ariaLabel }}" />
    </div>
</div>

<script>
    const otp = ["", "", "", ""];
    const otpInputs = document.querySelectorAll(".otp-input");
    otpInputs.forEach(element => {
        element.addEventListener("input", function(e) {
            handleKeyUp(e);
        });
    });

    const handleKeyUp = (e) => {
        let index = parseInt(e.target.id.split("_")[1]);
        otp[index] = e.target.value;
        if (otp[index] && index < otp.length - 1) {
            const nextInput = document.getElementById(`otp_${index + 1}`);
            nextInput.focus();
        } else if (!otp[index] && index > 0) {
            const previousInput = document.getElementById(`otp_${index - 1}`);
            previousInput.focus();
        }
    };
</script>
