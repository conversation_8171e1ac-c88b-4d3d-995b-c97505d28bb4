@if (isset($label))
    <label class="block text-sm md:text-base text-gray-900 select-none" for="{{ $id }}">
        {{ $label }}
        @isset($optional)
            (Optional)
        @else
            {!! isset($important) && $important == 0 ? '' : '<span class="error-text">*</span>' !!}
        @endisset
    </label>
@endif
<div
    class="relative flex items-center bg-[#ffffff] my-2 border border-solid transition duration-200 ease-in-out active-focus-border w-full lg:text-xs rounded-lg">
    <textarea
        class="bg-[#ffffff] border-0 focus-visible:outline-none py-3 px-4 w-full lg:text-base rounded-lg @isset($currency) pr-20 @endif"
        placeholder="{{ $placeholder ?? '' }}" id="{{ $id }}" name="{{ $name }}" type="{{ $type }}"
        value="{{ $value ?? '' }}" aria-label="{{ $ariaLabel }}" oninput="{{ $oninput ?? '' }}"
        @isset($disabled) {{ $disabled ? 'disabled readonly' : '' }} @endisset
        rows="4"></textarea>
</div>
