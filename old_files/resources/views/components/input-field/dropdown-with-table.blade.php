<div id="element_{{ $id }}">
    <div class="form-group">
        <label for="{{ $id }}">{{ $label ?? '' }}</label>
        <table class="table" id="branchTable_{{ $id }}">
            <thead class="branchHead">
                <tr>
                    <th>Address</th>
                    <th>Quantity</th>
                    <th>Minimum Quantity</th>
                    <th style="text-align:center;">
                        <button id="resetBranch_{{ $id }}" onClick="resetBranch_{{ $id }}(event)"
                            type="button" class="btn btn-info btn-round" style="display: none;">&#8635;</button>
                        <button onClick="cloneRow_{{ $id }}(event)" type="button"
                            class="btn btn-success btn-round">+</button>
                    </th>
                </tr>
            </thead>
            <tbody class="branchBody" id="branchBody_{{ $id }}">
                <tr class="branch branch_element_{{ $id }}">
                    <td>
                        @include('components.input-field.dropdown', [
                            'text' => 'Category',
                            'id' => 'cat_search',
                            'datas' => [],
                            'onchange' => 'onCatSearchSelect(this)',
                        ])
                        {{-- <select name="branchName" id="branchName"
                            class="branchNames branchNames_{{ $id }} q-form">
                            @if ($noitem == 'true')
                                <option value="0">--Please choose a branch--</option>
                            @endif
                            <option value="1">1</option>
                            <option value="1">1</option>
                            {{-- @foreach ($util->getBranches() as $key => $data)
                                <option value="{{ $data->id }}">{!! $data->name !!}</option>
                            @endforeach
                        </select> --}}
                    </td>
                    <td>
                        @include('components.input-field.number', [
                            'id' => 'quantity',
                            'name' => 'quantity',
                            'class' => 'quantity',
                        ])
                    </td>
                    <td>
                        @include('components.input-field.number', [
                            'id' => 'minquantity',
                            'name' => 'minquantity',
                            'class' => 'minquantity',
                        ])
                    </td>
                    <td>
                        @include('components.input-field.number', [
                            'id' => 'maxquantity',
                            'name' => 'maxquantity',
                            'class' => 'maxquantity',
                        ])
                    </td>
                    <td>
                        <div class="col-5 ml-auto mr-auto">
                            <div class="form-group">
                                @include('components.buttons.button-danger', [
                                    'name' => '-',
                                ])
                            </div>
                        </div>
                        {{-- <button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button> --}}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<style>
    .table>thead.branchHead:first-child>tr:first-child>th {
        padding: 0px 5px;
        padding-bottom: 3px;
        text-align: left;
    }

    .branchHead {
        border-bottom: 1px solid rgba(0, 0, 0, .125);
    }

    tbody>tr.branch>td {
        border: none;
    }
</style>

<script type="text/javascript">
    var selected = "";
    // Add multiple rows when the plus icon is clicked
    function cloneRow_{{ $id }}(e) {
        let tableId = e.target.parentElement.parentElement.parentElement.parentElement.id;
        let lastChild = document.querySelector(`#${tableId} .branch_element_{{ $id }}:last-child`);
        if (!lastChild) {
            // All branches
            let allBranches = {!! json_encode($branches) !!};
            // branch option lists
            let options = [];
            allBranches.forEach(data => {
                options.push(`<option id="${data.id}" value="${data.id}">${data.name}</option>`);
            });
            let ptr = `
                <tr class="branch branch_element_{{ $id }}">` +
                `<td>
                    <div id="element_branchName">
                        <div class="form-group">
                            <select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_{{ $id }}">
                                <option value="0" selected>--Please choose a branch--</option>
                                ${options}
                            </select>
                        </div>
                    </div>
                </td>` +
                `<td>
                    <div id="element_quantity">
                        <div class="form-group">
                            <input type="number" value="0" name="quantity" id="quantity" class="form-control quantity" value="0">
                        </div>
                    </div>
                </td>` +
                `<td>
                    <div id="element_minQuantity">
                        <div class="form-group">
                            <input type="number" value="0" name="minquantity" id="minquantity" class="form-control minquantity" value="0">
                        </div>
                    </div>
                </td>` +
                `<td>
                    <div id="element_maxQuantity">
                        <div class="form-group">
                            <input type="number" value="0" name="maxquantity" id="maxquantity" class="form-control maxquantity" value="0">
                        </div>
                    </div>
                </td>` +
                `<td>
                    <div class="col-5 ml-auto mr-auto">
                        <div class="form-group">
                            <button class="btn btn-danger btn-round btn-block deleteRow">-</button>
                        </div>
                    </div>
                </td>` +
                `</tr>`;
            $('tbody#branchBody_{{ $id }}').append(ptr);
        } else {
            e.preventDefault();
            let lastRowValue = document.querySelector(
                `#${tableId} #branchBody_{{ $id }} tr:last-child #branchName`).value;
            if (lastRowValue != 0) {
                let row = document.querySelector(".branch_element_{{ $id }}:last-child");
                let tableBody = document.querySelector(
                    "#branchTable_{{ $id }} tbody#branchBody_{{ $id }}");
                let clone = row.cloneNode(true);
                if (row.querySelector('.branchNames_{{ $id }}').selectedIndex != "0") {
                    // To check if enough branches are there
                    if (clone.querySelector('#branchName').length == 1 || tableBody.rows.length == document
                        .querySelector('.branchNames_{{ $id }}').length - 1) {
                        return;
                    };
                    tableBody.appendChild(clone);
                };
                clone.querySelector('#branchName').value = "0";
                clone.querySelector('.quantity').value = "0";
                clone.querySelector('.minquantity').value = "0";
                clone.querySelector('.maxquantity').value = "0";
            }

            // To clone a row and remove branch option name dublicates
            // tableBody.appendChild(clone);
            // clone.querySelector('.branchNames').options.remove(row.querySelector('.branchNames').selectedIndex);
            // for (var i=0; i<clone.previousElementSibling.childNodes[0].childNodes[0].options.length; i++) {
            //     console.log("Clone prevSibling", clone.previousElementSibling);
            //     if (clone.previousElementSibling.childNodes[0].childNodes[0].options[i] != 0 && clone.previousElementSibling.childNodes[0].childNodes[0].options[i].value == clone.childNodes[0].childNodes[0].options[1].value) {
            //         clone.previousElementSibling.childNodes[0].childNodes[0].options.remove(i);
            //     }
            // }

            // else if (bvalue == 0) {
            //     var row = document.querySelector(".branch:last-child");
            //     var clone = row.cloneNode(true);
            //     var tableBody = document.querySelector("#branchTable_{{ $id }} tbody.branchBody");

            //     // To return if the branch row length is equal to branch names length
            //     //  tableBody.rows.length == document.querySelector('.branchNames').length

            //     // To check if enough branches are there
            //     if (clone.querySelector('#branchName').length == 1){
            //         return;
            //     };
            //     clone.childNodes[0].childNodes[0].options.remove(1);
            //     // clone.querySelector('.branchNames').remove(row.querySelector('.branchNames').options[1]);
            //     console.log("check",
            //         row.querySelector('.branchNames').selectedIndex);
            //     console.log("row", row.childNodes[0].childNodes[0]);
            //     console.log("clone", clone.childNodes[0].childNodes[0]);
            //     tableBody.appendChild(clone);
            //     console.log("clone", clone.childNodes[0].childNodes[0]);
            //     for (var i=0; i<clone.previousElementSibling.childNodes[0].childNodes[0].options.length; i++) {
            //         if (clone.previousElementSibling.childNodes[0].childNodes[0].options[i] != 0 && clone.previousElementSibling.childNodes[0].childNodes[0].options[i].value == clone.childNodes[0].childNodes[0].options[1].value) {
            //             console.log("valid",
            //                 clone.previousElementSibling.childNodes[0].childNodes[0].options[i] != 0 &&
            //                 clone.previousElementSibling.childNodes[0].childNodes[0].options[i].value == clone.childNodes[0].childNodes[0].options[1].value);
            //             console.log("valid",
            //                 clone.previousElementSibling.childNodes[0].childNodes[0].options[i],
            //                 clone.previousElementSibling.childNodes[0].childNodes[0].options[i].value,
            //                 clone.childNodes[0].childNodes[0]);
            //             console.log("Clone remove", i);
            //             console.log("Clone remove", clone.previousElementSibling.childNodes[0].childNodes[0].options[i]);
            //             clone.previousElementSibling.childNodes[0].childNodes[0].options.remove(i);
            //         }
            //     }
            //     clone.querySelector('.quantity').value = "";
            //     clone.querySelector('.minquantity').value = "";
            // }
        }
    }

    // Remove added row when minus button clicked
    $('#branchBody_{{ $id }}').on('click', '.deleteRow', function() {
        let branchid = parseInt($(this).parent().parent().parent().parent().find(":selected").val());
        let row = $(this).parent().parent().parent().parent();
        let tableName = $(this).parent().parent().parent().parent().parent().attr('id').split('_')[1];
        // let tableName = $(this).parent().parent().parent().attr('id').split('_')[1];
        if (tableName == 'variant' && variantId != 0) {
            return swal.fire({
                title: "Are you sure?",
                text: "",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, delete it!",
                cancelButtonText: "No, cancel please!",
                closeOnConfirm: true,
                closeOnCancel: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Delete branch from database
                    $.ajax({
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                        },
                        type: 'POST',
                        url: '{{ url('deleteBranchProductVariant') }}',
                        data: {
                            branch_id: branchid,
                            product_id: editId,
                            variant_id: variantId
                        },
                        success: function(data) {
                            // console.log("data: ", data);
                            if (data.status == 0) {
                                return Toast.fire({
                                    icon: "error",
                                    title: "Something went wrong"
                                });
                            }
                            // remove branch
                            row.remove();
                            editBranches_{{ $id }} = data.editBranches;
                            return;
                        },
                        error: function(e) {
                            Toast.fire({
                                icon: "error",
                                title: "Something went wrong"
                            });
                            // console.log(e);
                        }
                    });
                }
            });
        }
        if (tableName == 'product' && editId != 0 && editId != null) {
            // checkDublicateBranch(0, '{{ $id }}');
            // if (dublicateBranch) {
            //     if (row.get(0).previousElementSibling != null) {
            //         row.remove();
            //     }

            //     if (row.get(0).nextElementSibling != null) {
            //         row.remove();
            //     }
            //     return;
            // }

            // Confirmation for delete
            swal.fire({
                icon: "warning",
                title: "Are you sure?",
                text: "Deleting this will result in permanent loss. Are you sure you want to proceed? This action cannot be reversed.",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes",
                cancelButtonText: "No",
            }).then((result) => {
                if (result.isConfirmed) {
                    // Delete branch from database
                    $.ajax({
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                        },
                        type: 'POST',
                        url: '',
                        // url: 'route('admin.delete-branch-product')',
                        data: {
                            branch_id: branchid,
                            product_id: editId
                        },
                        success: function(data) {
                            // console.log("res data: ", data);
                            if (data.status == 0) {
                                return Toast.fire({
                                    icon: "error",
                                    title: "Something went wrong"
                                });
                            }
                            Toast.fire({
                                icon: "success",
                                title: "Deleted successfully"
                            });
                            // remove branch
                            row.remove();
                            editBranches_{{ $id }} = data.editBranches;
                            return;
                            // branch
                            let branchNames = data.data.branch_products;
                            let branches = data.data.branches;
                            $('tbody .branch_element_product_branch').remove();
                            let ptr = '';
                            if (branchNames.length != 0) {
                                branchNames.forEach(data => {
                                    let options = '';
                                    allBranches.forEach(data1 => {
                                        if (data1.id != data.branch.id) {
                                            options +=
                                                `<option id="${data1.id}" value="${data1.id}">${data1.name}</option>`;
                                        } else {
                                            options +=
                                                `<option id="${data.branch.id}" value="${data.branch.id}" selected="selected">${data.name}</option>`;
                                        }
                                    });
                                    ptr = `
                                        <tr class="branch branch_element_{{ $id }}">` +
                                        `<td>
                                            <div id="element_branchName">
                                                <div class="form-group">
                                                    <select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_{{ $id }}">
                                                        <option value="0" selected>--Please choose a branch--</option>
                                                        ${options}
                                                    </select>
                                                </div>
                                            </div>
                                        </td>` +
                                        `<td>
                                            <div id="element_quantity">
                                                <div class="form-group">
                                                    <input type="number" name="quantity" id="quantity" class="form-control quantity" value="${data.quantity}">
                                                </div>
                                            </div>
                                        </td>` +
                                        `<td>
                                            <div id="element_minQuantity">
                                                <div class="form-group">
                                                    <input type="number" name="minquantity" id="minquantity" class="form-control minquantity" value="${data.minquantity}">
                                                </div>
                                            </div>
                                        </td>` +
                                        `<td>
                                            <div id="element_maxQuantity">
                                                <div class="form-group">
                                                    <input type="number" name="maxquantity" id="maxquantity" class="form-control maxquantity" value="${data.maxquantity}">
                                                </div>
                                            </div>
                                        </td>` +
                                        `<td>
                                            <div class="col-5 ml-auto mr-auto">
                                                <div class="form-group">
                                                    <button class="btn btn-danger btn-round btn-block deleteRow">-</button>
                                                </div>
                                            </div>
                                        </td>` +
                                        `</tr>`;
                                    $('tbody#branchBody_{{ $id }}')
                                        .append(ptr);
                                });

                                // For Object Type
                                // for (const key in notAddedBranches) {
                                //     if (notAddedBranches.hasOwnProperty(key)) {
                                //         let trNotAdded = `<option id="${notAddedBranches[key].id}" value="${notAddedBranches[key].id}">${notAddedBranches[key].name}</option>`;
                                //         $('.branchNames_{{ $id }}').append(trNotAdded);
                                //         // console.log(`${key}: ${notAddedBranches[key].name}`);
                                //     }
                                // }

                                // For Array Type
                                // notAddedBranches.forEach(data => {
                                //     var trNotAdded = `<option id="${data.id}" value="${data.id}">${data.name}</option>`;
                                //     $('.branchNames').append(trNotAdded);
                                // });
                            } else {
                                ptr = `
                                    <tr class="branch branch_element_{{ $id }}">` +
                                    `<td>
                                        <div id="element_branchName">
                                            <div class="form-group">
                                                <select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_{{ $id }}">
                                                    <option value="0" selected>--Please choose a branch--</option>
                                                    ${options}
                                                </select>
                                            </div>
                                        </div>
                                    </td>` +
                                    `<td>
                                        <div id="element_quantity">
                                            <div class="form-group">
                                                <input type="number" name="quantity" id="quantity" class="form-control quantity" value="0">
                                            </div>
                                        </div>
                                    </td>` +
                                    `<td>
                                        <div id="element_minQuantity">
                                            <div class="form-group">
                                                <input type="number" name="minquantity" id="minquantity" class="form-control minquantity" value="0">
                                            </div>
                                        </div>
                                    </td>` +
                                    `<td>
                                        <div id="element_maxQuantity">
                                            <div class="form-group">
                                                <input type="number" name="maxquantity" id="maxquantity" class="form-control maxquantity" value="0">
                                            </div>
                                        </div>
                                    </td>` +
                                    `<td>
                                        <div class="col-5 ml-auto mr-auto">
                                            <div class="form-group">
                                                <button class="btn btn-danger btn-round btn-block deleteRow">-</button>
                                            </div>
                                        </div>
                                    </td>` +
                                    `</tr>`;
                                $('tbody#branchBody_{{ $id }}').append(ptr);
                            }
                        },
                        error: function(e) {
                            Toast.fire({
                                icon: "error",
                                title: "Something went wrong"
                            });
                            console.log(e);
                        }
                    });
                }
            });
            return;
        }
        // Remove row from last
        if (row.get(0).previousElementSibling != null) {
            return row.remove();
        }
        // Remove row from first
        if (row.get(0).nextElementSibling != null) {
            return row.remove();
        }

        // var prev = $(this).parent().parent().get(0).previousElementSibling.childNodes[0].childNodes[0];
        // var selectedIndex = prev.selectedIndex;
        // var current = $(this).parent().parent().get(0).childNodes[0].childNodes[0];
        // console.log("$(this).parent().parent().get(0).previousElementSibling.childNodes[0]", $(this).parent().parent().get(0).previousElementSibling.childNodes[0]);
        // console.log("$(this).parent().parent().get(0).previousElementSibling.childNodes[0].childNodes[0]", $(this).parent().parent().get(0).previousElementSibling.childNodes[0].childNodes[0]);
        // return;
        // for (var i=0; i<current.options.length; i++) {
        //     for (var j=0; j<prev.options.length; j++) {
        //         if (current.options[i].value == prev.options[j].value) {
        //             current.options.remove(i);
        //         }
        //     }
        // }
        // for (var j=0; j<current.options.length; j++) {
        //     prev.appendChild(current.options[j]);
        //     // Insert at beginning
        //     // prev.insertBefore(current.options[j],prev.firstChild);
        // }
        // prev.selectedIndex = selectedIndex;
    });

    function resetBranch_{{ $id }}(event) {
        if (editId != 0 || editBranches_{{ $id }}.length != 0) {
            $('tbody .branch_element_{{ $id }}').remove();
            let allBranches = {!! json_encode($branches) !!};
            editBranches_{{ $id }}.forEach(data => {
                let options = '';
                allBranches.forEach(data1 => {
                    if (data1.id != data.branch.id) {
                        options +=
                            `<option id="${data1.id}" value="${data1.id}">${data1.name}</option>`;
                    } else {
                        options +=
                            `<option id="${data.branch.id}" value="${data.branch.id}" selected="selected">${data.branch.name}</option>`;
                    }
                });
                ptr = `
                    <tr class="branch branch_element_{{ $id }}">` +
                    `<td>
                        <div id="element_branchName">
                            <div class="form-group">
                                <select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_{{ $id }}">
                                    <option value="0" selected>--Please choose a branch--</option>
                                    ${options}
                                </select>
                            </div>
                        </div>
                    </td>` +
                    `<td>
                        <div id="element_quantity">
                            <div class="form-group">
                                <input type="number" name="quantity" id="quantity" class="form-control quantity" value="${data.quantity}">
                            </div>
                        </div>
                    </td>` +
                    `<td>
                        <div id="element_minQuantity">
                            <div class="form-group">
                                <input type="number" name="minquantity" id="minquantity" class="form-control minquantity" value="${data.minquantity}">
                            </div>
                        </div>
                    </td>` +
                    `<td>
                        <div id="element_maxQuantity">
                            <div class="form-group">
                                <input type="number" name="maxquantity" id="maxquantity" class="form-control maxquantity" value="${data.maxquantity}">
                            </div>
                        </div>
                    </td>` +
                    `<td>
                        <div class="col-5 ml-auto mr-auto">
                            <div class="form-group">
                                <button class="btn btn-danger btn-round btn-block deleteRow">-</button>
                            </div>
                        </div>
                    </td>` +
                    `</tr>`;
                $(`tbody#branchBody_{{ $id }}`).append(ptr);
            });
        }
    }
</script>
