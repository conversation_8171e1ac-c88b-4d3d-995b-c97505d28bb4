@inject('setting', 'App\Models\Setting')

<div id="element_{{ $id }}">
    <div class="form-group">
        <label for="{{ $id }}">{{ $label }}</label>
        <div style="position: relative;">
            <input type="text" class="form-control" name="{{ $id }}" id="{{ $id }}"
                placeholder="{{ $text }}" maxlength="{{ $maxlength }}">
            <div id="{{ $id }}--loading" class="spin spin-loader spin-position" hidden></div>
        </div>
        <p id="latlng" class="mt-2"></p>
        <div class="col-md-12" style="display: none">
            <div id="map"></div>
            <div id="infowindow-content">
                <img src="" width="16" height="16" id="place-icon" />
                <span id="place-name" class="title"></span><br />
                <span id="place-address"></span>
            </div>
        </div>
    </div>
</div>

<script>
    let lat = 0;
    let lng = 0;

    function initMap_{{ $id }}() {
        const map = new google.maps.Map(document.getElementById("map"), {
            center: {
                lat: {{ $setting->getDefaultLat() }},
                lng: {{ $setting->getDefaultLng() }}
            },
            zoom: 13,
        });
        const card = document.getElementById("pac-card");
        const input = document.getElementById("{{ $id }}");
        map.controls[google.maps.ControlPosition.TOP_RIGHT].push(card);
        const autocomplete = new google.maps.places.Autocomplete(input);
        // Bind the map's bounds (viewport) property to the autocomplete object,
        // so that the autocomplete requests use the current map bounds for the
        // bounds option in the request.
        autocomplete.bindTo("bounds", map);
        // Set the data fields to return when the user selects a place.
        autocomplete.setFields(["address_components", "geometry", "icon", "name"]);
        const infowindow = new google.maps.InfoWindow();
        const infowindowContent = document.getElementById("infowindow-content");
        infowindow.setContent(infowindowContent);
        const marker = new google.maps.Marker({
            map,
            anchorPoint: new google.maps.Point(0, -29),
        });
        autocomplete.addListener("place_changed", () => {
            infowindow.close();
            marker.setVisible(false);
            const place = autocomplete.getPlace();

            if (!place.geometry) {
                // User entered the name of a Place that was not suggested and
                // pressed the Enter key, or the Place Details request failed.
                window.alert("No details available for input: '" + place.name + "'");
                return;
            }

            // If the place has a geometry, then present it on a map.
            if (place.geometry.viewport) {
                map.fitBounds(place.geometry.viewport);
            } else {
                map.setCenter(place.geometry.location);
                map.setZoom(17); // Why 17? Because it looks good.
            }
            marker.setPosition(place.geometry.location);
            lat = place.geometry.location.lat();
            lng = place.geometry.location.lng();

            marker.setVisible(true);
            let address = "";

            if (place.address_components) {
                address = [
                    (place.address_components[0] &&
                        place.address_components[0].short_name) ||
                    "",
                    (place.address_components[1] &&
                        place.address_components[1].short_name) ||
                    "",
                    (place.address_components[2] &&
                        place.address_components[2].short_name) ||
                    "",
                ].join(" ");
            }
            infowindowContent.children["place-icon"].src = place.icon;
            infowindowContent.children["place-name"].textContent = place.name;
            infowindowContent.children["place-address"].textContent = address;
            infowindow.open(map, marker);
        });
    }
</script>

<script
    src="https://maps.googleapis.com/maps/api/js?key={{ $setting->getGoogleMapKey() }}&callback=initMap_{{ $id }}&libraries=places&v=weekly">
</script>
{{-- TODO: Removed --}}
{{-- <script src="https://polyfill.io/v3/polyfill.min.js?features=default"></script> --}}
