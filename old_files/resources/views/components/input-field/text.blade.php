<div id="element_{{ $id }}">
    <div class="form-group">
        @if (isset($label))
            <label for="{{ $id }}">{{ $label }}</label>
        @endif
        <div class="relative d-flex">
            @if (isset($type) && $type == 'tel')
                <span class="country-code">
                    +251
                </span>
            @endif
            <input type="{{ $type ?? 'text' }}"
                class="form-control"
                name="{{ $id }}" id="{{ $id }}" placeholder="{{ $placeholder ?? '' }}"
                maxlength="{{ $maxlength ?? '' }}" minlength="{{ $minlength ?? '' }}" step="{{ $step ?? '' }}"
                min="{{ $min ?? '' }}" max="{{ $max ?? '' }}"
                @if(!isset($type)) autocomplete="on" @endif
                @if(isset($required)) required @endif
                @if(isset($disabled)) disabled @endif
            />
            @if (isset($type) && $type == 'password')
                <span class="eye-icon">
                    <svg id="show-password_{{ $id }}" style="display: none;"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" width="25px" height="25px">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" />
                    </svg>
                    <svg id="hide-password_{{ $id }}" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="25px" height="25px">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                </span>
            @endif
        </div>
        <small id="{{ $id }}Help" class="form-text text-muted">{{ $desc ?? '' }}</small>
    </div>
</div>

@if (isset($type) && $type == 'password')
    <script>
        $('#show-password_{{ $id }}').on('click', function() {
            var passInput = $(this).parent().parent().find("input");
            if (passInput.attr('type') === 'password') {
                passInput.attr('type', 'text');
            } else {
                passInput.attr('type', 'password');
            }
            $(this).hide();
            $("#hide-password_{{ $id }}").show();
        });

        $('#hide-password_{{ $id }}').on('click', function() {
            var passInput = $(this).parent().parent().find("input");
            if (passInput.attr('type') === 'password') {
                passInput.attr('type', 'text');
            } else {
                passInput.attr('type', 'password');
            }
            $(this).hide();
            $("#show-password_{{ $id }}").show();
        });
    </script>
@endif
