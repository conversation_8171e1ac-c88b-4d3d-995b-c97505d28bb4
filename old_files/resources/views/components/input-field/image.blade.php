<div>
    @php
        $lbl = 'Image';
        if (isset($lable)) {
            $lbl = $lable;
        }
        $drpId = '';
        if (isset($id)) {
            $drpId = $id;
        }
        $libView = 'true';
        if (isset($viewLib)) {
            $libView = $viewLib;
        }
    @endphp
    <div class="form-group">
        <label>{{ $lbl }}</label>
    </div>
    <div id="dropzone3{{ $drpId }}" class="fallback dropzone">
        <div class="dz-message">
            <div class="drag-icon-cph">
                <i class="nc-icon nc-tap-01" style="font-size: 4.8rem;"></i>
            </div>
            <h3>Drop files here or click to upload.</h3>
        </div>
        <div class="fallback">
            <input name="file" type="file" multiple />
        </div>
    </div>

    {{-- Select from Library --}}
    @if ($libView == 'true')
        <button type="button" onclick="fromLibrary3{{ $drpId }}()" class="w-100 btn btn-info btn-round">Select
            from Library</button>
    @endif
</div>

<style>
    .swal-wide {
        position: fixed !important;
        width: 70% !important;
        left: 30% !important;
    }
</style>

<script>
    let editFileNameNotify3{{ $drpId }};

    Dropzone.autoDiscover = false;

    let myDropzone3{{ $drpId }} = new Dropzone("div#dropzone3{{ $drpId }}", {
        url: "{{ url('image/upload/store') }}",
        maxFilesize: 0.2,
        renameFile: function(file) {
            let dt = new Date();
            let time = dt.getTime();
            return time + file.name;
        },
        init: function() {
            this.hiddenFileInput.removeAttribute('multiple');
        },
        sending: function(file, xhr, formData) {
            formData.append("_token", $('meta[name="_token"]').attr('content'));
        },
        acceptedFiles: ".jpeg,.jpg,.png,.gif",
        addRemoveLinks: true,
        timeout: 50000,
        removedfile: function(file) {
            // console.log("removedfile " + this.files.length);
            if (this.files.length == 0)
                imageid3{{ $drpId }} = 0;
            let fileRef;
            return (fileRef = file.previewElement) != null ?
                fileRef.parentNode.removeChild(file.previewElement) : void 0;
        },
        success: async function(file, response) {
            // console.log('success upload res: ', response);
            if (this.files.length > 1) {
                this.removeFile(this.files[0]);
            }
            imageid3{{ $drpId }} = response.data.id;
        },
        error: function(file, response) {
            // console.log('error upload res: ', response);
            Toast.fire({
                icon: "error",
                title: "Error",
                text: response
            });
            return false;
        }
    });


    function fromLibrary3{{ $drpId }}() {
        lastEdit3{{ $drpId }} = "";
        lastJEdit3{{ $drpId }} = "";
        selectId3{{ $drpId }} = "";
        selectName3{{ $drpId }} = "";

        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ url('getImagesList') }}',
            data: {},
            success: function(data) {
                // console.log("getImagesList");
                // console.log(data);
                if (data.error != "0")
                    return Toast.fire({
                        icon: "error",
                        title: "Error",
                        text: 'Something went wrong'
                    });
                loadDialog2{{ $drpId }}(data.data);
            },
            error: function(e) {
                Toast.fire({
                    icon: "error",
                    title: "Error",
                    text: 'Something went wrong'
                });
                // console.log(e);
            }
        });
    }

    function loadDialog2{{ $drpId }}(data) {
        let text = `<div id="div1" style="height: 600px; position:relative;">
                        <div id="div2" style="max-height:100%; min-height: 100%; overflow:auto; border:2px solid grey;">
                            <div id="thumbimagesEdit" class="row">`;
        data.forEach(function(data, i, arr) {
            text = `${text}
                    <div class="col-md-2" style="position: relative; top: 10px; left: 20px; height: 250px; margin-bottom: 10px">
                        <div id="thumbEdit${data.filename}" onclick="klikajEdit3{{ $drpId }}('thumbEdit${data.filename}',
                                    'iconokEdit${data.filename}', ${data.id}, '${data.filename}')"  class="thumbnail" style="height: 250px">
                                <img id="iconokEdit${data.filename}"  src="img/iconok.png" style='visibility:hidden; width: 40px; position: absolute; z-index: 100; top: 100px; left: 50px' >
                                <img src="images/${data.filename}" class="img-thumbnail" style='height: 150px; max-height: 150px; min-height: 150px; object-fit: contain; z-index: 10; ' >

                               <div style="font-size: 13px; overflow: hidden; font-weight: bold;">${data.title}</div>
                               <p>${data.updated_at}</p>
                           </div>
                       </div>
            `;
        });
        text = `${text}</div></div></div>`;

        swal({
            title: "",
            text: text,
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Ok",
            cancelButtonText: "Cancel",
            customClass: 'swal-wide',
            closeOnConfirm: true,
            closeOnCancel: true,
            html: true
        }, function(isConfirm) {
            if (isConfirm) {
                if (selectId3{{ $drpId }} != "") {
                    imageid3{{ $drpId }} = selectId3{{ $drpId }};
                    let mockFile{{ $drpId }} = {
                        name: "images/" + selectName3{{ $drpId }},
                        size: 0,
                        dataURL: "images/" + selectName3{{ $drpId }},
                    };
                    myDropzone3{{ $drpId }}.createThumbnailFromUrl(mockFile{{ $drpId }},
                        myDropzone3{{ $drpId }}.options.thumbnailWidth,
                        myDropzone3{{ $drpId }}
                        .options.thumbnailHeight, myDropzone3{{ $drpId }}.options.thumbnailMethod,
                        true,
                        function(dataUrl) {
                            myDropzone3{{ $drpId }}.emit("thumbnail", mockFile{{ $drpId }},
                                dataUrl);
                        });
                    myDropzone3{{ $drpId }}.emit("addedfile", mockFile{{ $drpId }});
                    myDropzone3{{ $drpId }}.emit("complete", mockFile{{ $drpId }});
                    myDropzone3{{ $drpId }}.files.push(mockFile{{ $drpId }});
                    if (myDropzone3{{ $drpId }}.files.length > 1) {
                        myDropzone3{{ $drpId }}.removeFile(myDropzone3{{ $drpId }}.files[0]);
                    }
                    editFileNameNotify3{{ $drpId }} = selectName3{{ $drpId }};
                }
            } else {

            }
        })
    }

    let lastEdit3{{ $drpId }} = "";
    let lastJEdit3{{ $drpId }} = "";
    let selectId3{{ $drpId }} = "";
    let selectName3{{ $drpId }} = "";

    function klikajEdit3{{ $drpId }}(i, j, id, name) {
        selectName3{{ $drpId }} = name;
        if (lastEdit3{{ $drpId }} !== "")
            document.getElementById(lastEdit3{{ $drpId }}).style.borderColor = "#e0e0e0";
        if (lastJEdit3{{ $drpId }} !== "")
            document.getElementById(lastJEdit3{{ $drpId }}).style.visibility = 'hidden';
        lastJEdit3{{ $drpId }} = j;
        lastEdit3{{ $drpId }} = i;
        document.getElementById(i).style.border = "3";
        document.getElementById(i).style.borderColor = "#00FF00";
        document.getElementById(i).style.borderStyle = "solid";
        document.getElementById(j).style.visibility = 'visible';
        selectId3{{ $drpId }} = id;
    }

    let editFileName3{{ $drpId }};
    let imageid3{{ $drpId }} = 0;

    function clearDropZone3{{ $drpId }}() {
        imageid3{{ $drpId }} = 0;
        if (myDropzone3{{ $drpId }}.files.length == 1) {
            myDropzone3{{ $drpId }}.removeFile(myDropzone3{{ $drpId }}.files[0]);
        }
    }

    function addEditImage3{{ $drpId }}(image) {
        let id = image.id;
        let filename = image.filename;
        let fileImage = image.image_path;
        let size = image.size;
        if (myDropzone3{{ $drpId }}.files.length == 1) {
            myDropzone3{{ $drpId }}.removeFile(myDropzone3{{ $drpId }}.files[0]);
        }
        if (id == 0 || fileImage == "noimage.png")
            return;
        editFileName3{{ $drpId }} = fileImage;
        imageid3{{ $drpId }} = id;
        let mockFile{{ $drpId }} = {
            name: filename,
            size: size,
            dataURL: fileImage
        };
        myDropzone3{{ $drpId }}.createThumbnailFromUrl(mockFile{{ $drpId }},
            myDropzone3{{ $drpId }}.options.thumbnailWidth, myDropzone3{{ $drpId }}.options
            .thumbnailHeight, myDropzone3{{ $drpId }}.options.thumbnailMethod, true,
            function(dataUrl) {
                myDropzone3{{ $drpId }}.emit("thumbnail", mockFile{{ $drpId }}, dataUrl);
            });
        myDropzone3{{ $drpId }}.emit("addedfile", mockFile{{ $drpId }});
        myDropzone3{{ $drpId }}.emit("complete", mockFile{{ $drpId }});
        myDropzone3{{ $drpId }}.files.push(mockFile{{ $drpId }});
    }
</script>
