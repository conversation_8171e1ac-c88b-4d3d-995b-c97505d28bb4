<div id="element_{{ $id }}">
    <div class="form-group">
        <label for="{{ $id }}">{{ $label }}</label>
        <select name="{{ $id }}" id="{{ $id }}" class="q-form-s show-tick q-radius"
            onchange="{{ $onchange }};">
            @if ($noitem == 'true')
                <option value="0">No</option>
            @endif
            <option value="1">1</option>
            <option value="2">2</option>
            {{-- @if ($id == 'brand')
                @foreach ($util->getBrands() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->name !!}</option>
                @endforeach
            @elseif ($id == 'parent')
                @foreach ($util->getCategories() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->name !!}</option>
                @endforeach
            @elseif ($id == 'brand_parent')
                @foreach ($util->getBranches() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->name !!}</option>
                @endforeach
            @elseif ($id == 'role')
                @foreach ($util->getRoles() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->role !!}</option>
                @endforeach
            @elseif ($id == 'payment_method_type')
                @foreach ($util->getPaymentMethods() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->payment_method !!}</option>
                @endforeach
            @elseif ($id == 'vendor_type')
                @foreach ($util->getVendorTypes() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->type !!}</option>
                @endforeach
            @elseif ($id == 'vendor')
                @foreach ($util->getVendors() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->name !!}</option>
                @endforeach
            @elseif ($id == 'position')
                <option value="1">Advertisement Image for the first vertical display</option>
                <option value="2">Advertisement Image for the second horizontal display</option>
                <option value="3">Advertisement Image for the third vertical display</option>
                <option value="4">Advertisement Image for the fourth horizontal display</option>
            @elseif ($id == 'branch_manager')
                @foreach ($util->getBranchManagers() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->name !!}</option>
                @endforeach
            @elseif ($id == 'ingredient_group')
                @foreach ($util->getIngredientGroups() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->name !!}</option>
                @endforeach
            @elseif ($id == 'extras_group')
                @foreach ($util->getExtrasGroups() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->name !!}</option>
                @endforeach
            @elseif ($id == 'product')
                @foreach ($util->getProducts() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->name !!}</option>
                @endforeach
            @elseif ($id == 'ingredient')
                @foreach ($util->getIngredient() as $key => $data)
                    <option value="{{ $data->id }}">{!! $data->name !!}</option>
                @endforeach
            @endif --}}
        </select>
    </div>
</div>
