<div id="{{ $id }}" onclick="onCheckClick{{ $id }}()" style="font-weight: bold; cursor: pointer; user-select: none;"></div>

<script>
    let {{ $id }} = {{ $initvalue }};
    if ({{ $id }})
        document.getElementById('{{ $id }}').innerHTML =
        "<img src='{{ asset('img/check_on.png') }}' width='25px'>&nbsp{{ $text }}";
    else
        document.getElementById('{{ $id }}').innerHTML =
        "<img src='{{ asset('img/check_off.png') }}' width='25px'>&nbsp{{ $text }}";

    function onCheckClick{{ $id }}() {
        let value = "on";
        if ({{ $id }}) value = "off";
        else value = "on";
        {{ $id }} = !{{ $id }};
        document.getElementById('{{ $id }}').innerHTML =
            `<img src='{{ asset('img/check_${value}.png') }}' width='25px'>&nbsp{{ $text }}`;
    }

    function onSetCheck_{{ $id }}(value) {
        if (value == '1')
            {{ $id }} = false;
        else
            {{ $id }} = true;
        onCheckClick{{ $id }}();
    }
</script>
