<div>
    <div class="form-group">
        <label>Image</label>
    </div>
    <div id="dropzone2" class="fallback dropzone">
        <div class="dz-message">
            <div class="drag-icon-cph">
                <i class="nc-icon nc-tap-01" style="font-size: 4.8rem;"></i>
            </div>
            <h3>Drop files here or click to upload</h3>
        </div>
        <div class="fallback">
            <input name="file" type="file" multiple />
        </div>
    </div>
    <button type="button" onclick="fromLibrary()" class="w-100 btn btn-info btn-round">Select from Library</button>
</div>

<script>
    Dropzone.autoDiscover = false;

    var myDropzone = new Dropzone("div#dropzone2", {
        url: "{{ url('image/upload/store') }}",
        // maxFilesize: 0.2,
        maxFilesize: 2.2,
        renameFile: function(file) {
            var dt = new Date();
            var time = dt.getTime();
            return time + file.name;
        },
        sending: function(file, xhr, formData) {
            formData.append("_token", $('meta[name="_token"]').attr('content'));
        },
        acceptedFiles: ".jpeg,.jpg,.png,.gif",
        addRemoveLinks: true,
        timeout: 50000,
        removedfile: function(file) {
            // console.log(file);
            for (var i = imageArray.length; i--;) {
                if (file.upload != null) {
                    if (imageArray[i].filename === file.upload.filename)
                        imageArray.splice(i, 1);
                } else {
                    if (imageArray[i].filename === file.name)
                        imageArray.splice(i, 1);
                }
            }
            // console.log(imageArray);
            var fileRef;
            return (fileRef = file.previewElement) != null ?
                fileRef.parentNode.removeChild(file.previewElement) : void 0;
        },
        success: async function(file, response) {
            // console.log("res: ", file, response);
            imageArray.push({
                // filename: file.upload.filename,
                id: response.data.id,
                // new: true
            })
        },
        error: function(file, response) {
            response.msg.image.forEach((msg) => {
                return Toast.fire({
                    icon: "error",
                    title: "Error",
                    text: msg
                });
            });
        }
    });

    let imageArray = [];

    function fromLibrary() {
        lastEdit = "";
        lastJEdit = "";
        selectId = "";
        selectName = "";

        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ url('getImagesList') }}',
            data: {},
            success: function(data) {
                // console.log("getImagesList");
                // console.log(data);
                if (data.error != "0") {
                    return Toast.fire({
                        icon: "error",
                        title: "Error",
                        text: 'Something went wrong'
                    });
                }
                loadDialog(data.data);
            },
            error: function(e) {
                // console.log(e);
                return Toast.fire({
                    icon: "error",
                    title: "Error",
                    text: 'Something went wrong'
                });
            }
        });
    }

    function loadDialog(data) {
        var text = `<div id="div1" style="height: 600px; position:relative;">
                        <div id="div2" style="max-height:100%; min-height: 100%; overflow:auto; border:2px solid grey;">
                            <div id="thumbimagesEdit" class="row">`;
        data.forEach(function(data, i, arr) {
            text = `${text}
                    <div class="col-md-2" style="position: relative; top: 10px; left: 20px; height: 250px; margin-bottom: 10px">
                        <div id="thumbEdit${data.filename}" onclick="klikajEdit('thumbEdit${data.filename}',
                                'iconokEdit${data.filename}', ${data.id}, '${data.filename}')"  class="thumbnail" style="height: 250px">
                            <img id="iconokEdit${data.filename}"  src="img/iconok.png" style='visibility:hidden; width: 40px; position: absolute; z-index: 100; top: 100px; left: 50px' >
                            <img src="images/${data.filename}" class="img-thumbnail" style='height: 150px; max-height: 150px; min-height: 150px; object-fit: contain; z-index: 10; ' >

                            <div style="font-size: 13px; overflow: hidden; font-weight: bold;">${data.title}</div>
                            <p>${data.updated_at}</p>
                        </div>
                    </div>
            `;
        });
        text = `${text}</div></div></div>`;

        swal({
            title: "",
            text: text,
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Ok",
            cancelButtonText: "Cancel",
            customClass: 'swal-wide',
            closeOnConfirm: true,
            closeOnCancel: true,
            html: true
        }, function(isConfirm) {
            if (isConfirm) {
                if (selectId != "") {
                    // = selectId;
                    var mockFile = {
                        name: "images/" + selectName,
                        size: 0,
                        dataURL: "images/" + selectName,
                    };
                    myDropzone.createThumbnailFromUrl(mockFile, myDropzone.options.thumbnailWidth, myDropzone
                        .options.thumbnailHeight, myDropzone.options.thumbnailMethod, true,
                        function(dataUrl) {
                            myDropzone.emit("thumbnail", mockFile, dataUrl);
                        });
                    myDropzone.emit("addedfile", mockFile);
                    myDropzone.emit("complete", mockFile);
                    myDropzone.files.push(mockFile);
                    imageArray.push({
                        filename: mockFile.name,
                        id: selectId,
                        new: false
                    })
                    // console.log(imageArray);
                }
            } else {

            }
        })
    }

    var lastEdit = "";
    var lastJEdit = "";
    var selectId = "";
    var selectName = "";

    function klikajEdit(i, j, id, name) {
        selectName = name;
        if (lastEdit !== "")
            document.getElementById(lastEdit).style.borderColor = "#e0e0e0";
        if (lastJEdit !== "")
            document.getElementById(lastJEdit).style.visibility = 'hidden';
        lastJEdit = j;
        lastEdit = i;
        document.getElementById(i).style.border = "3";
        document.getElementById(i).style.borderColor = "#00FF00";
        document.getElementById(i).style.borderStyle = "solid";
        document.getElementById(j).style.visibility = 'visible';
        selectId = id;
    }

    function clearDropZone() {
        myDropzone.removeAllFiles(true);
        imageArray = [];
    }

    function addEditImages(images_files) {
        images_files.forEach(function(item, i, arr) {
            let path = `{{ asset(':path') }}`;
            path = path.replace(':path', item.image_path);
            addEditImage(item.id, path, item.size);
        });
    }

    function addEditImage(id, fileImage, size) {
        if (id == 0 || fileImage == "noimage.png")
            return;
        var mockFile = {
            name: fileImage,
            size: size,
            dataURL: fileImage
        };
        myDropzone.createThumbnailFromUrl(mockFile, myDropzone.options.thumbnailWidth, myDropzone.options
            .thumbnailHeight, myDropzone.options.thumbnailMethod, true,
            function(dataUrl) {
                myDropzone.emit("thumbnail", mockFile, dataUrl);
            });
        myDropzone.emit("addedfile", mockFile);
        myDropzone.emit("complete", mockFile);
        myDropzone.files.push(mockFile);
        // console.log(myDropzone);
        imageArray.push({
            filename: mockFile.name,
            id: id,
            new: false
        })
        // console.log(imageArray);
    }
</script>
