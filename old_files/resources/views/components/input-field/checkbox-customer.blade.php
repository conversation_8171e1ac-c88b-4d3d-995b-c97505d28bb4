<div id="{{ $id }}" onclick="onCheckClick{{ $id }}()"
    class="flex items-center select-none cursor-pointer"></div>

<script>
    let {{ $id }} = {{ $initvalue }};
    if ({{ $id }}) {
        // &nbsp{{ $text }}
        document.getElementById('{{ $id }}').innerHTML =
            "<img src='{{ asset('img/check_on.png') }}' width='25px'>";
    } else {
        document.getElementById('{{ $id }}').innerHTML =
            "<img src='{{ asset('img/check_off.png') }}' width='25px'>";
    }
    @if (isset($link))
        document.getElementById('{{ $id }}').innerHTML += `<a href="{{ $link }}" target="_blank" class="primary-hover-color transition duration-200 ease-in-out">
        &nbsp{{ $text }}
    </a>`;
    @else
        document.getElementById('{{ $id }}').innerHTML += '&nbsp{{ $text }}';
    @endisset

    function onCheckClick{{ $id }}() {
        var value = "on";
        if ({{ $id }}) value = "off";
        else value = "on";
        {{ $id }} = !{{ $id }};
        // &nbsp{{ $text }}
        document.getElementById('{{ $id }}').innerHTML =
            `<img src='{{ asset('img/check_${value}.png') }}' width='25px'>`;
        @if (isset($link))
            document.getElementById('{{ $id }}').innerHTML += `<a href="{{ $link }}" target="_blank" class="primary-hover-color transition duration-200 ease-in-out">
        &nbsp{{ $text }}
    </a>`;
        @else
            document.getElementById('{{ $id }}').innerHTML += '&nbsp{{ $text }}';
        @endisset
        if ("{{ $callback }}" != "null")
            {{ $callback }}("{{ $id }}", {{ $id }});
    }

    function onSetCheck_{{ $id }}(value) {
        if (value == '1')
            {{ $id }} = false;
        else
            {{ $id }} = true;
        onCheckClick{{ $id }}();
    }
</script>
