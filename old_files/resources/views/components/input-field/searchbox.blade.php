<div
    class="mb-4 grid grid-cols-[minmax(calc(100%_-_3rem),_1fr)_3rem] bg-white items-center border rounded-md w-full overflow-hidden transition duration-200 outer-focus-border-primary-focus">
    @if (!isset($searchInput))
        <input class="px-4 py-2 md:py-3 text-base dark-color bg-white w-full focus-visible:outline-none" type="search"
            id="mobile-view-search" placeholder="Search..." name="search-field">
    @else
        <input class="px-4 py-2 md:py-3 text-base dark-color bg-white w-full focus-visible:outline-none" type="search"
            id="mobile-view-search" placeholder="Search..." name="search-field" value="{{ $searchInput }}">
    @endif
    <div class="h-full dark-bg-color flex cursor-pointer text-white transition duration-200 ease-in-out hover:opacity-60"
        onclick="onSearch(document.getElementById('mobile-view-search').value);">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" height="22px" width="22px"
            class="w-6 h-6 m-auto">
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
                <path d="M18.5 18.5L22 22" stroke="currentColor" stroke-width="1.5" stroke-linecap="round">
                </path>
                <path
                    d="M6.75 3.27093C8.14732 2.46262 9.76964 2 11.5 2C16.7467 2 21 6.25329 21 11.5C21 16.7467 16.7467 21 11.5 21C6.25329 21 2 16.7467 2 11.5C2 9.76964 2.46262 8.14732 3.27093 6.75"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
            </g>
        </svg>
    </div>
</div>
