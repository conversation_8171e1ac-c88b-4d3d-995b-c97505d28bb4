<div id="element_{{ $id }}">
    <div class="form-group">
        <label for="{{ $id }}">{{ $label }}</label>
        <div @if (isset($datas)) class="d-flex gap-4" @endif>
            <input type="number" name="{{ $id }}" placeholder="{{ $placeholder ?? '' }}" id="{{ $id }}"
                class="form-control" value="0" step="0.01"
                @if (isset($max)) max="{{ $max }}" @endif
                @if (isset($min)) min="{{ $min }}" @endif>
            @if (isset($datas))
                <select name="{{ $id }}_select" id="{{ $id }}_select" class="form-control show-tick"
                    style="max-width: 12rem;"
                    @isset($onchange) onchange="{{ $onchange }};" @endisset>
                    @foreach ($datas as $data)
                        <option value="{{ $data['id'] }}" style="font-size: 16px  !important;"
                            @if ($data['id'] == 1) selected @endif>
                            {{ array_key_exists('name', $data) ? $data['name'] : $data['role'] }}</option>
                    @endforeach
                </select>
            @endif
        </div>
        <small id="{{ $id }}Help" class="form-text text-muted">{{ $desc ?? '' }}</small>
    </div>
</div>
