<div class="footer-navbar lg:hidden">
    <ul class="menu-list bg-white relative z-20">
        <!-- Home -->
        @include('components.navbar.bottom-nav.nav-link', [
            'href' => 'home',
            'name' => 'Securities',
            'svg' => "<svg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' height='22px' width='22px' class='w-[22px] h-[22px]'>
                <g stroke-width='0'></g>
                <g stroke-linecap='round' stroke-linejoin='round'></g>
                <g>
                    <path
                        d='M2 12.2039C2 9.91549 2 8.77128 2.5192 7.82274C3.0384 6.87421 3.98695 6.28551 5.88403 5.10813L7.88403 3.86687C9.88939 2.62229 10.8921 2 12 2C13.1079 2 14.1106 2.62229 16.116 3.86687L18.116 5.10812C20.0131 6.28551 20.9616 6.87421 21.4808 7.82274C22 8.77128 22 9.91549 22 12.2039V13.725C22 17.6258 22 19.5763 20.8284 20.7881C19.6569 22 17.7712 22 14 22H10C6.22876 22 4.34315 22 3.17157 20.7881C2 19.5763 2 17.6258 2 13.725V12.2039Z'
                        stroke='currentColor' stroke-width='1.5'></path>
                    <path d='M15 18H9' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'></path>
                </g>
            </svg>",
        ])

        <!-- Portfolio -->
        @include('components.navbar.bottom-nav.nav-link', [
            'href' => 'customer.profile',
            'name' => 'Portfolio',
            'svg' => "<svg fill='currentColor' height='25px' width='25px' version='1.1' id='Layer_1'
                        xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' class='w-7 h-7'>
                        <g stroke-width='0'></g>
                        <g stroke-linecap='round' stroke-linejoin='round'></g>
                        <g>
                            <g>
                                <g>
                                    <path
                                        d='M469.779,94.063H352.573l-9.106-36.426c-4.709-18.832-21.554-31.983-40.962-31.983h-93.011 c-19.408,0-36.253,13.152-40.963,31.984l-9.105,36.425H42.221C18.941,94.063,0,113.003,0,136.284v307.841 c0,23.281,18.941,42.221,42.221,42.221h427.557c23.281,0,42.221-18.941,42.221-42.221V136.284 C512,113.003,493.059,94.063,469.779,94.063z M184.086,61.528c2.922-11.682,13.371-19.841,25.409-19.841h93.011 c12.038,0,22.486,8.159,25.409,19.84l8.133,32.536h-18.732l-7.033-28.132c-0.891-3.569-4.098-6.072-7.777-6.072h-93.011 c-3.678,0-6.885,2.503-7.777,6.072l-7.031,28.132h-18.732L184.086,61.528z M300.789,94.063h-89.578l4.543-18.171h80.492 L300.789,94.063z M42.221,110.096h427.557c8.005,0,15.177,3.614,19.985,9.291l-52.05,156.149 c-4.736,14.208-17.98,23.754-32.957,23.754H289.67v-17.637c0-9.136-7.432-16.568-16.568-16.568h-34.205 c-9.136,0-16.568,7.432-16.568,16.568v17.637H107.243c-14.976,0-28.221-9.546-32.957-23.753l-52.05-156.15 C27.044,113.71,34.216,110.096,42.221,110.096z M238.363,316.393v-34.739c0-0.295,0.239-0.534,0.534-0.534h34.205 c0.295,0,0.534,0.239,0.534,0.534v34.739H238.363z M273.637,332.426v17.637c0,0.295-0.239,0.534-0.534,0.534h-34.205 c-0.295,0-0.534-0.239-0.534-0.534v-17.637H273.637z M495.967,444.125c0,14.44-11.748,26.188-26.188,26.188H42.221 c-14.44,0-26.188-11.748-26.188-26.188V151.481l43.042,129.126c6.922,20.765,26.279,34.717,48.168,34.717H222.33v34.739 c0,9.136,7.432,16.568,16.568,16.568h34.205c9.136,0,16.568-7.432,16.568-16.568v-34.739h115.087 c21.889,0,41.245-13.951,48.168-34.717l43.042-129.126V444.125z'>
                                    </path>
                                </g>
                            </g>
                        </g>
                    </svg>
            ",
            // <div class="bubble bubble--theme-color" id="wishlistCountMobile">0</div>
        ])

        <!-- Chat -->
        @if (Auth::check())
            @include('components.navbar.bottom-nav.nav-link', [
                'href' => 'home',
                'name' => 'Message',
                'svg' => "<svg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' height='22px' width='22px' class='w-[22px] h-[22px]'>
                        <g id='SVGRepo_bgCarrier' stroke-width='0'></g>
                        <g id='SVGRepo_tracerCarrier' stroke-linecap='round' stroke-linejoin='round'></g>
                        <g id='SVGRepo_iconCarrier'>
                            <path
                                d='M9 12C9 12.5523 8.55228 13 8 13C7.44772 13 7 12.5523 7 12C7 11.4477 7.44772 11 8 11C8.55228 11 9 11.4477 9 12Z'
                                fill='currentColor'></path>
                            <path
                                d='M13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11C12.5523 11 13 11.4477 13 12Z'
                                fill='currentColor'></path>
                            <path
                                d='M17 12C17 12.5523 16.5523 13 16 13C15.4477 13 15 12.5523 15 12C15 11.4477 15.4477 11 16 11C16.5523 11 17 11.4477 17 12Z'
                                fill='currentColor'></path>
                            <path fill-rule='evenodd' clip-rule='evenodd'
                                d='M22.75 12C22.75 6.06294 17.9371 1.25 12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 13.7183 1.65371 15.3445 2.37213 16.7869C2.47933 17.0021 2.50208 17.2219 2.4526 17.4068L1.857 19.6328C1.44927 21.1566 2.84337 22.5507 4.3672 22.143L6.59324 21.5474C6.77814 21.4979 6.99791 21.5207 7.21315 21.6279C8.65553 22.3463 10.2817 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12ZM12 2.75C17.1086 2.75 21.25 6.89137 21.25 12C21.25 17.1086 17.1086 21.25 12 21.25C10.5189 21.25 9.12121 20.9025 7.88191 20.2852C7.38451 20.0375 6.78973 19.9421 6.20553 20.0984L3.97949 20.694C3.57066 20.8034 3.19663 20.4293 3.30602 20.0205L3.90163 17.7945C4.05794 17.2103 3.96254 16.6155 3.7148 16.1181C3.09752 14.8788 2.75 13.4811 2.75 12C2.75 6.89137 6.89137 2.75 12 2.75Z'
                                fill='currentColor'></path>
                        </g>
                    </svg>
                    <div id='chat-countMobileView' class='bubble bubble--red hidden'>0</div>",
            ])
        @endif

        <!-- Cart -->
        @include('components.navbar.bottom-nav.nav-link', [
            'href' => 'cart-page',
            'name' => 'Cart',
            'svg' => "
                <svg viewBox='0 0 32 32' version='1.1' xmlns='http://www.w3.org/2000/svg'
                    fill='currentColor'
                    height='25px' width='25px' class='w-[22px] h-[22px]'>
                    <g stroke-width='0'></g>
                    <g stroke-linecap='round' stroke-linejoin='round'></g>
                    <g>
                        <g stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'
                            sketch:type='MSPage'>
                            <g sketch:type='MSLayerGroup' transform='translate(-516.000000, -723.000000)'
                                fill='currentColor'>
                                <path
                                    d='M538,749 C539.104,749 540,749.896 540,751 C540,752.104 539.104,753 538,753 C536.896,753 536,752.104 536,751 C536,749.896 536.896,749 538,749 L538,749 Z M534,751 C534,753.209 535.791,755 538,755 C540.209,755 542,753.209 542,751 C542,748.791 540.209,747 538,747 C535.791,747 534,748.791 534,751 L534,751 Z M522,729 L546,729 L543.443,739.229 L522,741 L522,729 L522,729 Z M524,745 C522.896,745 522,744.104 522,743 L545,741 C545,741 548,728.875 548,728 C548,727.447 547.553,727 547,727 L522,727 L522,725 L523,725 C523.553,725 524,724.553 524,724 C524,723.448 523.553,723 523,723 L517,723 C516.447,723 516,723.448 516,724 C516,724.553 516.447,725 517,725 L520,725 L520,743 C520,745.209 521.791,747 524,747 L547,747 C547.031,747 547,746.009 547,745 L524,745 L524,745 Z M526,749 C527.104,749 528,749.896 528,751 C528,752.104 527.104,753 526,753 C524.896,753 524,752.104 524,751 C524,749.896 524.896,749 526,749 L526,749 Z M522,751 C522,753.209 523.791,755 526,755 C528.209,755 530,753.209 530,751 C530,748.791 528.209,747 526,747 C523.791,747 522,748.791 522,751 L522,751 Z'
                                    sketch:type='MSShapeGroup'> </path>
                            </g>
                        </g>
                    </g>
                </svg>
                <div class='bubble bubble--theme-color primary-bg-color hidden' id='totalItemsCountMobileView'>0</div>
            ",
        ])

        <!-- Profile -->
        @include('components.navbar.bottom-nav.nav-link', [
            'href' => 'customer.account',
            'name' => 'Profile',
            'svg' => "<svg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' height='22px' width='22px' class='w-[22px] h-[22px]'>
                <g id='SVGRepo_bgCarrier' stroke-width='0'></g>
                <g id='SVGRepo_tracerCarrier' stroke-linecap='round' stroke-linejoin='round'></g>
                <g id='SVGRepo_iconCarrier'>
                    <circle cx='12' cy='9' r='3' stroke='currentColor' stroke-width='1.5'></circle>
                    <path d='M17.9691 20C17.81 17.1085 16.9247 15 11.9999 15C7.07521 15 6.18991 17.1085 6.03076 20' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'></path>
                    <path d='M7 3.33782C8.47087 2.48697 10.1786 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 10.1786 2.48697 8.47087 3.33782 7' stroke='currentColor' stroke-width='1.5' stroke-linecap='round'></path>
                </g>
            </svg>",
        ])
    </ul>
</div>
