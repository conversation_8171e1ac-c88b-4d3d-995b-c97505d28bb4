{{-- grid-rows-2 grid-cols-[100px_minmax(calc(100%_-_100px),_1fr)] --}}
{{-- sm:grid-cols-[200px_minmax(calc(100%_-_200px),_1fr)] --}}
<div
    class="border max-w-full grid grid-rows-2 grid-cols-1 md:grid-rows-1 md:grid-cols-[102px_minmax(calc(100%_-_102px),_1fr)] shadow-md rounded-xl overflow-hidden hover:shadow-lg transition-all duration-200 ease-in-out">
    <a href="{{ route('details', $data['id']) }}" rel="{{ $data['name'] }}"
        class="w-full flex relative bg-center bg-no-repeat cursor-pointer bg-contain lg:bg-[85%]" style="background-image: url('{{ asset($data['images'][0]['image_path']) }}');">

        <!-- On Sale -->
        @if ($data['discount_price'] && $data['discount_price'] != '0')
            <div
                class="absolute top-2 right-2 py-1 px-3 md:px-5 md:py-1 text-center font-bold text-xs leading-[normal] uppercase rounded-full text-white red-bg-color">
                On Sale</div>
        @endif
    </a>
    <div class="w-full h-full py-2 px-4 text-left bg-white">
        <!-- Name -->
        <a href="{{ route('details', $data['id']) }}" rel="{{ $data['name'] }}"
            class="font-bold text-base md:text-xl line-clamp-2 cursor-pointer">
            {{ $data['name'] }}
        </a>

        <!-- Price -->
        @if ($data['discount_price'] && $data['discount_price'] != '0')
            <div class="md:mt-2 flex flex-col gap-1">
                <h3 class="font-normal text-base md:text-xl text-slate-500 line-through">{{ $data['price2'] }}</h3>
                <h3 class="font-bold text-lg md:text-2xl primary-color">{{ $data['discount_price2'] }}</h3>
            </div>
        @else
            <div class="md:mt-2 flex">
                <h3 class="font-bold text-lg md:text-2xl primary-color">{{ $data['price2'] }}</h3>
            </div>
        @endif
    </div>
</div>
