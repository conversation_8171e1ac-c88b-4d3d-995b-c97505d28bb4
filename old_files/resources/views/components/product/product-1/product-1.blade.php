<div
    class="grid grid-rows-[200px_minmax(calc(100%_-_200px),_1fr)] md:grid-rows-[400px_minmax(calc(100%_-_400px),_1fr)] grid-cols-1 border shadow-md rounded-xl overflow-hidden hover:-translate-y-2 hover:shadow-xl hover:scale-[1.005] transition-all duration-200 ease-out">
    <div class="h-full w-full relative bg-white border-b overflow-hidden">
        <div class="w-ful h-full product-images">
            {{-- @if ($data['video_link'])
                <div
                    class="grid grid-rows-1 w-full h-full place-items-center max-w-[1224px] max-h-[350px] md:max-h-[500px]">
                    <div class="w-full h-full grid place-items-center">
                        <div class="w-full p-2 md:p-4 flex items-center justify-center h-[245px] md:h-[450px]">
                            <!-- //www.youtube.com/embed/YE7VzlLtp-4 -->
                            <iframe style='width:100%; height:100%;' src="https://www.youtube.com/embed/YE7VzlLtp-4" frameborder="0"
                                allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            @endif --}}

            @foreach ($data['images'] as $image)
                <a href="{{ route('details', $data['id']) }}" rel="{{ $data['name'] }}"
                    class="w-full h-full flex bg-center bg-contain bg-no-repeat"
                    style="background-image: url('{{ asset($image['image_path']) }}');">
                </a>
            @endforeach

            <!-- TODO: Remove them -->
            {{-- <div class="w-full h-full flex bg-center bg-contain bg-no-repeat"
                style="background-image: url('{{ $data['images'][0] }}');">
            </div>
            <div class="w-full h-full flex bg-center bg-contain bg-no-repeat"
                style="background-image: url('{{ $data['images'][0] }}');">
            </div> --}}
            <!-- ----------------- -->

        </div>

        <!-- On Sale -->
        @if ($data['discount_price'] && $data['discount_price'] != '0')
            @include('components.product.product-1.onSale')
        @endif
    </div>
    <div
        class="grid grid-cols-[minmax(calc(100%_-_35px),_1fr)_35px] md:grid-cols-[minmax(calc(100%_-_70px),_1fr)_70px] bg-white">
        <div class="h-full w-full py-2 px-4 md:py-4 md:px-6">
            <!-- Title -->
            <a href="{{ route('details', $data['id']) }}" rel="{{ $data['name'] }}"
                class="font-bold text-base md:text-xl my-2 line-clamp-3">{{ $data['name'] }}</a>

            <!-- In stock -->
            {{-- <p class="line-clamp-3 text-sm my-2">
                Lorem ipsum dolor, sit amet consectetur adipisicing elit. Rem corrupti, quos
                facilis
                a ea nisi, sequi aspernatur consectetur dicta, libero possimus optio eligendi
                harum
                totam placeat vero nam! Soluta, rem?
            </p> --}}

            <div class="py-1 px-2 md:py-3 md:px-6 rounded-xl bg-slate-100">
                <div class="w-full text-xs md:text-sm inline-flex gap-2 italic">
                    <span class="ml-auto">{{ $data['in_stock2'] }}% Available,</span>
                    <span>{{ $data['sold2'] }}% sold</span>
                </div>
                <div class="flex w-full overflow-hidden mb-2 h-4 rounded-lg bg-white">
                    <div id="product__{{ $data['id'] }}-in_stock-bar"
                        class="h-full {{ $data['sold2'] ? 'rounded-l-lg' : 'rounded-lg' }} {{ $data['in_stock2'] > 45 ? 'primary-bg-color' : ($data['in_stock2'] > 20 ? 'bg-warning' : 'bg-error') }}"
                        style="width: {{ $data['in_stock2'] }}%;">
                    </div>
                    <div id="product__{{ $data['id'] }}-sold-bar" class="h-full secondary-bg-color rounded-r-lg"
                        style="width: {{ $data['sold2'] }}%;">
                    </div>
                </div>
                <div class="">
                    <!-- Address -->
                    {{-- <div class="my-4 flex flex-nowrap items-center">
                        @include('components.input-field.select-custom.select-custom', [
                            'id' => 'product_' . $data['id'],
                            'data' => $data['branch_products'],
                        ])
                    </div> --}}

                    <!-- In Stock -->
                    <div class="my-1">
                        <p class="text-sm">
                            <span class="inline-block align-middle {{ $data['in_stock2'] > 45 ? 'primary-bg-color' : ($data['in_stock2'] > 20 ? 'bg-warning' : 'bg-error') }} w-4 h-4 rounded-full mr-2"></span>
                            <span id="product__{{ $data['id'] }}-in_stock">{{ $data['in_stock'] }}</span>
                            Available
                        </p>
                    </div>
                    <!-- Sold -->
                    <div class="my-1">
                        <p class="text-sm">
                            <span class="inline-block align-middle secondary-bg-color w-4 h-4 rounded-full mr-2"></span>
                            <span id="product__{{ $data['id'] }}-sold">{{ $data['sold'] }}</span>
                            Sold
                        </p>
                    </div>
                    <!-- Users -->
                    <div class="my-1">
                        <p class="text-sm">
                            <span class="inline-block align-middle bg-blue-500 w-4 h-4 rounded-full mr-2"></span>
                            <span id="product__{{ $data['id'] }}-users">
                                {{ $data['users_bought'] }} User(s) Bought
                            </span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Price -->
            @if ($data['discount_price'] && $data['discount_price'] != '0')
                <div class="mt-4 flex flex-col flex-wrap gap-1 md:gap-2 items-start justify-center">
                    <h3 class="font-normal text-lg md:text-2xl text-slate-500 line-through">{{ $data['price2'] }}</h3>
                    <h3 class="font-bold text-xl md:text-3xl primary-color">{{ $data['discount_price2'] }}</h3>
                </div>
            @else
                <div class="mt-4 flex flex-col flex-wrap items-start justify-center">
                    <h3 class="font-bold text-xl md:text-3xl primary-color">{{ $data['price2'] }}</h3>
                </div>
            @endif
        </div>
        <div class="w-full h-full px-1 py-2 md:px-2 md:py-4 flex flex-col items-center gap-2 overflow-hidden">
            <!-- Video link -->
            @if ($data['video_link'])
                <span
                    class="p-1 rounded-full cursor-pointer dark-color transition-all duration-200 ease-in-out uppercase hover:opacity-60">
                    <svg onclick="openVideo('{{ $data['video_link'] }}')" viewBox="0 0 32 32" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000"
                        height="20px" width="20px" class="w-6 h-6">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <g id="icomoon-ignore"> </g>
                            <path
                                d="M28.186 9.069l-6.855 4.122v-4.655c0-0.883-0.716-1.599-1.599-1.599h-17.060c-0.884 0-1.599 0.716-1.599 1.599v14.928c0 0.883 0.715 1.599 1.599 1.599h17.060c0.883 0 1.599-0.716 1.599-1.599v-4.744l7.006 4.211h2.591v-13.861h-2.742zM20.265 23.464c0 0.294-0.24 0.533-0.533 0.533h-17.060c-0.295 0-0.533-0.239-0.533-0.533v-14.928c0-0.294 0.238-0.533 0.533-0.533h17.060c0.294 0 0.533 0.239 0.533 0.533v14.928zM29.861 21.864h-1.229l-7.301-4.389v-3.039l7.15-4.3h1.38v11.729z"
                                fill="#000000"> </path>
                        </g>
                    </svg>
                </span>
            @endif
            <span
                onclick="addToWishList({ id: @json($data['id']), branch_id: @json($data['branch_products'][0]['branch_id']) });"
                id="wishlist-{{ $data['id'] }}"
                class="p-1 rounded-full cursor-pointer dark-color transition-all duration-200 ease-in-out uppercase hover:opacity-60">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" height="25px" width="25px"
                    class="w-6 h-6">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round">
                    </g>
                    <g id="SVGRepo_iconCarrier">
                        <path
                            d="M17.2 22C19.851 22 22 19.851 22 17.2C22 14.549 19.851 12.4 17.2 12.4C14.549 12.4 12.4 14.549 12.4 17.2C12.4 19.851 14.549 22 17.2 22Z"
                            stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                            stroke-linejoin="round"></path>
                        <path d="M18.99 17.26H15.41" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"
                            stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M17.2 15.51V19.1" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10"
                            stroke-linecap="round" stroke-linejoin="round"></path>
                        <path
                            d="M22 8.69C22 10.66 21.49 12.4 20.69 13.91C19.81 12.98 18.57 12.4 17.2 12.4C14.55 12.4 12.4 14.55 12.4 17.2C12.4 18.43 12.87 19.55 13.63 20.4C13.26 20.57 12.92 20.71 12.62 20.81C12.28 20.93 11.72 20.93 11.38 20.81C8.48 19.82 2 15.69 2 8.69C2 5.6 4.49 3.09998 7.56 3.09998C9.37 3.09998 10.99 3.98002 12 5.33002C13.01 3.98002 14.63 3.09998 16.44 3.09998C19.51 3.09998 22 5.6 22 8.69Z"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        </path>
                    </g>
                </svg>
            </span>
            <span onclick="modalBtn({{ $data['id'] }})"
                class="p-1 rounded-full cursor-pointer dark-color transition-all duration-200 ease-in-out uppercase hover:opacity-60">
                <svg fill="currentColor" viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg" height="25px"
                    width="25px" class="w-6 h-6">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round">
                    </g>
                    <g id="SVGRepo_iconCarrier">
                        <path
                            d="M854.397 1594.874c-408.288 0-740.477-332.19-740.477-740.477 0-408.288 332.189-740.477 740.477-740.477 408.287 0 740.477 332.189 740.477 740.477 0 408.287-332.19 740.477-740.477 740.477ZM1920 1839.345l-423.325-423.211c131.577-150.488 212.118-346.543 212.118-561.737C1708.793 383.225 1325.568 0 854.397 0 383.225 0 0 383.225 0 854.397c0 471.17 383.225 854.396 854.397 854.396 215.08 0 411.363-80.54 561.737-212.118L1839.345 1920l80.655-80.655ZM911.356 455.678h-113.92v341.759H455.679v113.92h341.759v341.758h113.92V911.356h341.758v-113.92h-341.76V455.679Z"
                            fill-rule="evenodd"></path>
                    </g>
                </svg>
            </span>
            {{-- <span onclick="addToCart({{ $data['id'] }})"
                class="p-1 rounded-full cursor-pointer dark-color transition-all duration-200 ease-in-out uppercase hover:opacity-60">
                <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 294.873 294.873" xml:space="preserve"
                    fill="currentColor" transform="matrix(-1, 0, 0, 1, 0, 0)" height="25px" width="25px"
                    class="w-6 h-6">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round">
                    </g>
                    <g id="SVGRepo_iconCarrier">
                        <g>
                            <path style="fill:currentColor;"
                                d="M287.373,37.98h-46.046c-8.789,0-17.546,6.626-19.936,15.085l-12.438,44.023 c-1.423-0.396-2.92-0.625-4.478-0.625H99.761c-5.056-22.543-25.217-39.442-49.263-39.442C22.653,57.021,0,79.675,0,107.518 c0,25.479,18.974,46.601,43.532,50.006c-0.011,0.329-0.009,0.661,0.024,0.998l2.61,26.457c0.925,9.373,9.027,16.715,18.446,16.715 h115.462c8.827,0,17.546-6.675,19.85-15.195l14.439-53.397c0.001-0.001,0.001-0.003,0.001-0.003l21.46-75.955 c0.583-2.061,3.359-4.163,5.502-4.163h46.046c4.142,0,7.5-3.357,7.5-7.5S291.515,37.98,287.373,37.98z M15,107.518 c0-19.573,15.924-35.497,35.498-35.497s35.497,15.924,35.497,35.497c0,19.573-15.924,35.497-35.497,35.497S15,127.092,15,107.518z M185.445,182.583c-0.551,2.036-3.262,4.111-5.371,4.111H64.612c-1.646,0-3.356-1.549-3.518-3.188l-2.578-26.135 c22.774-3.65,40.497-22.58,42.31-45.908h103.648c0.072,0,0.137,0.003,0.193,0.007c-0.011,0.056-0.025,0.119-0.044,0.188 L185.445,182.583z">
                            </path>
                            <path style="fill:currentColor;"
                                d="M86.504,210.236c-12.863,0-23.328,10.465-23.328,23.328c0,12.863,10.465,23.328,23.328,23.328 c12.863,0,23.329-10.465,23.329-23.328C109.833,220.701,99.367,210.236,86.504,210.236z M86.504,241.892 c-4.592,0-8.328-3.736-8.328-8.328c0-4.592,3.736-8.328,8.328-8.328c4.592,0,8.329,3.736,8.329,8.328 C94.833,238.156,91.096,241.892,86.504,241.892z">
                            </path>
                            <path style="fill:currentColor;"
                                d="M160.472,210.236c-12.863,0-23.328,10.465-23.328,23.328c0,12.863,10.465,23.328,23.328,23.328 c12.863,0,23.328-10.465,23.328-23.328C183.8,220.701,173.335,210.236,160.472,210.236z M160.472,241.892 c-4.592,0-8.328-3.736-8.328-8.328c0-4.592,3.736-8.328,8.328-8.328c4.592,0,8.328,3.736,8.328,8.328 C168.8,238.156,165.064,241.892,160.472,241.892z">
                            </path>
                            <path style="fill:currentColor;"
                                d="M57.996,126.094v-11.075h11.078c4.142,0,7.5-3.357,7.5-7.5s-3.358-7.5-7.5-7.5H57.996V88.94 c0-4.143-3.358-7.5-7.5-7.5s-7.5,3.357-7.5,7.5v11.078H31.921c-4.142,0-7.5,3.357-7.5,7.5s3.358,7.5,7.5,7.5h11.075v11.075 c0,4.143,3.358,7.5,7.5,7.5S57.996,130.236,57.996,126.094z">
                            </path>
                        </g>
                    </g>
                </svg>
            </span> --}}
        </div>
    </div>
</div>
