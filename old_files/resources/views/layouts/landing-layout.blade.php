@inject('util', 'App\Models\Util')
@inject('setting', 'App\Models\Setting')

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{{ csrf_token() }}" />
    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no'
        name='viewport' />

    <title>
        @isset($title)
            {{ $title }}
        @else
            {{-- Welcome To | {{ config('app.name') }} --}}
            Welcome to Kircha | Democratizing real estate
        @endisset
    </title>

    <!-- Favicon Logo -->
    @isset($logo)
        <link rel="icon" href="{{ $logo }}" type="image">
    @else
        <link rel="icon" href="{{ asset('img/logo.png') }}" type="image">
    @endisset

    {{-- <!-- <link rel="icon" href={{ $theme->getLogo() }} type="image/png"> --> --}}
    <!-- <link rel="icon" type="image/png" href="../assets/img/favicon.png"> -->

    <!-- CSS Files -->
    @include('styles.landing-themes')

    <!-- Jquery Core Js -->
    <script src="{{ asset('assets/js/core/jquery.min.js') }}"></script>
    <!-- Jquery Plugin -->
    <script src="{{ asset('assets/js/plugins/plugin.js') }}"></script>

    <!-- Fonts and icons -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">

    <!-- Slick Slider -->
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

    <!-- JS -->

    <!-- DropZone -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.4.0/min/dropzone.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.4.0/dropzone.js"></script>

    <!-- Swal -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <link href="{{ asset('nouislider/nouislider.css') }}" rel="stylesheet">
    <script src="{{ asset('nouislider/nouislider.js') }}"></script>

    <link href="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.snow.css" rel="stylesheet">

    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @laravelPWA
</head>

<body class="antialiased">
    <!-- CSS Files -->
    @include('styles.landing-styles')

    {{-- Custom styles --}}
    @yield('styles')

    {{-- Loading bar --}}
    <div class="loading-container hidden" id="loading-bar">
        <div class="loading-bar left"></div>
        <div class="loading-bar right"></div>
    </div>

    <!-- Common scripts -->
    @include('layouts.landing-layout-js.landing-js-utility')

    <div class="min-h-screen bg-slate-100 overflow-hidden grid grid-cols-[300px_minmax(calc(100%_-_(300px)),_1fr)] transition-all duration-300 ease-out"
        id="main" style="margin-left: -300px;">

        <!-- Left Sidebar -->
        <div class="h-screen bg-white grid grid-rows-[64px_minmax(calc(100%_-_124px),_1fr)_64px] pb-[58px] lg:pb-0">
            <div class="flex items-center border-b border-r py-2 px-2 text-left">
                <a href="{{ route('home') }}" class="h-full inline-flex mr-auto">
                    <img src="{{ $util->getLogo() }}" alt="Logo Image" srcset=""
                        class="w-auto h-auto max-w-full max-h-full ml-0 text-center" width="50px" height="50px">
                </a>

                <span>
                    {{-- <svg fill="#000000" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                        class="w-10 h-10 rounded-full cursor-pointer toggle-sidebar">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M3,8H21a1,1,0,0,0,0-2H3A1,1,0,0,0,3,8Zm18,8H3a1,1,0,0,0,0,2H21a1,1,0,0,0,0-2Zm0-5H3a1,1,0,0,0,0,2H21a1,1,0,0,0,0-2Z">
                            </path>
                        </g>
                    </svg> --}}
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                        class="w-10 h-10 rounded-full cursor-pointer toggle-sidebar">
                        <g stroke-width="0"></g>
                        <g stroke-linecap="round" stroke-linejoin="round"></g>
                        <g>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M10.9393 12L6.9696 15.9697L8.03026 17.0304L12 13.0607L15.9697 17.0304L17.0304 15.9697L13.0607 12L17.0303 8.03039L15.9696 6.96973L12 10.9393L8.03038 6.96973L6.96972 8.03039L10.9393 12Z"
                                fill="currentColor"></path>
                        </g>
                    </svg>
                    {{-- <svg viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000"
                        class="w-10 h-10 rounded-full cursor-pointer toggle-sidebar">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <title>Close</title>
                            <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="Close">
                                    <rect id="Rectangle" fill-rule="nonzero" x="0" y="0" width="24" height="24">
                                    </rect>
                                    <line x1="16.9999" y1="7" x2="7.00001" y2="16.9999" id="Path"
                                        stroke="#000000" stroke-width="2" stroke-linecap="round"> </line>
                                    <line x1="7.00006" y1="7" x2="17" y2="16.9999" id="Path"
                                        stroke="#000000" stroke-width="2" stroke-linecap="round"> </line>
                                </g>
                            </g>
                        </g>
                    </svg> --}}
                </span>
            </div>
            <div class="block border-r py-2 px-2 overflow-y-auto md:overflow-y-clip">
                <!-- Search -->
                @include('components.input-field.searchbox')

                <!-- Menus Items -->
                @include('components.menu.menu-list')
            </div>
            <div class="border-t border-r border-b py-2 px-2 flex items-center justify-end">
                &copy; 2024 {{ $setting->getInfo('name') }}
            </div>
        </div>

        <!-- Main Body -->
        {{-- grid-rows-[64px_minmax(calc(100%_-_64px),_1fr)] --}}
        <div class="h-screen grid grid-cols-1">

            <!-- TODO: Add sidebar menu icon -->

            {{-- <div class="border-b border-b-orange-300 bg-orange-50">
                <div class="max-w-7xl px-8 py-3 flex gap-3 items-center">
                    <div class="secondary-bg-color text-white rounded-lg text-center p-2">
                        <svg viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink" fill="currentColor" height="25px" width="25px"
                            class="w-6 h-6">
                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                            <g id="SVGRepo_iconCarrier">
                                <g id="icomoon-ignore"> </g>
                                <path
                                    d="M16 2.672l-5.331 5.331v-2.133h-4.265v6.398l-3.755 3.755 0.754 0.754 12.597-12.597 12.597 12.597 0.754-0.754-13.351-13.351zM7.47 6.937h2.132v2.132l-2.133 2.133v-4.265z"
                                    fill="currentColor"> </path>
                                <path
                                    d="M6.404 16.533v12.795h7.464v-7.464h4.265v7.464h7.464v-12.795l-9.596-9.596-9.596 9.596zM24.53 28.262h-5.331v-7.464h-6.398v7.464h-5.331v-11.287l8.53-8.53 8.53 8.53v11.287z"
                                    fill="currentColor"> </path>
                            </g>
                        </svg>
                    </div>
                    <div class="block">
                        <h3 class="font-bold text-base">Complete Onboarding</h3>
                        <p class="font-normal text-sm">Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                            Perferendis facere libero molestias
                            saepe.
                        </p>
                    </div>
                    <button
                        class="px-4 py-2 ml-auto border rounded-lg bg-transparent hover:secondary-bg-color hover:text-white transition-all font-medium duration-200 ease-in-out">
                        Complete Onboarding
                    </button>
                </div>
            </div> --}}

            {{-- grid-cols-[minmax(calc(100%_-_350px),_1fr)_350px] --}}
            <div class="h-screen overflow-auto overflow-x-hidden w-full relative" id="main-body">
                <div id="sidebar-cover"
                    class="w-full h-full fixed bg-[#00000066] z-40 opacity-0 invisible duration-200 ease-out"></div>

                {{-- <span
                    class="fixed z-40 py-1 px-3 border bg-white top-[12px] left-[12px] transition-all duration-200 hidden"
                    id="toggle-sidebar">
                    <svg fill="#000000" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                        class="w-10 h-10 rounded-full cursor-pointer toggle-sidebar">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M3,8H21a1,1,0,0,0,0-2H3A1,1,0,0,0,3,8Zm18,8H3a1,1,0,0,0,0,2H21a1,1,0,0,0,0-2Zm0-5H3a1,1,0,0,0,0,2H21a1,1,0,0,0,0-2Z">
                            </path>
                        </g>
                    </svg>
                </span> --}}

                <!-- Search field -->
                <div id="search-container"
                    class="flex shadow-md px-4 py-2 w-full h-16 duration-200 transition-[top] ease-in-out sticky top-0 z-30 overflow-hidden
                    transaparent-glass
                    ">
                    {{-- bg-white --}}
                    <span id="toggle-sidebar" class="flex items-center transition-all duration-200">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                            class="w-10 h-10 rounded-full cursor-pointer toggle-sidebar">
                            <g stroke-width="0"></g>
                            <g stroke-linecap="round" stroke-linejoin="round"></g>
                            <g>
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M4 5C3.44772 5 3 5.44772 3 6C3 6.55228 3.44772 7 4 7H20C20.5523 7 21 6.55228 21 6C21 5.44772 20.5523 5 20 5H4ZM3 12C3 11.4477 3.44772 11 4 11H20C20.5523 11 21 11.4477 21 12C21 12.5523 20.5523 13 20 13H4C3.44772 13 3 12.5523 3 12ZM3 18C3 17.4477 3.44772 17 4 17H20C20.5523 17 21 17.4477 21 18C21 18.5523 20.5523 19 20 19H4C3.44772 19 3 18.5523 3 18Z"
                                    fill="#000000"></path>
                            </g>
                        </svg>
                    </span>

                    {{-- mr-4 ml-4 --}}
                    <a href="{{ route('home') }}" class="h-full min-w-20 inline-flex mx-auto md:ml-8">
                        <img src="{{ $util->getLogo() }}" alt="Logo Image" srcset=""
                            class="w-auto h-auto max-w-full max-h-full ml-0 text-center" width="50px" height="50px">
                    </a>

                    <div class="ml-auto hidden md:flex max-w-max items-center">
                        <button
                            class="m-auto whitespace-nowrap ml-2 flex justify-center items-center gap-2 relative w-full py-2 px-8 text-center text-sm lg:text-base rounded-full bg-black text-white font-bold opacity-90 transition-all duration-200 ease-in-out uppercase hover:opacity-60 sell-product">
                            <svg fill="currentColor" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
                                height="25px" width="25px" class="w-6 h-6">
                                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                <g id="SVGRepo_iconCarrier">
                                    <path
                                        d="M229.927,70.82178c-.02612-.04883-.04639-.09864-.07373-.147-.01172-.0205-.02612-.03906-.03809-.05957a15.98449,15.98449,0,0,0-5.97168-5.88183l-87.999-49.5a16.09525,16.09525,0,0,0-15.68848,0L32.155,64.73389A15.99355,15.99355,0,0,0,26.168,70.646c-.01539.02685-.03394.05127-.04908.07861-.02441.04346-.042.08887-.06543.13281a15.9818,15.9818,0,0,0-2.05371,7.82129v98.64258A16.02109,16.02109,0,0,0,32.156,191.2666l88.001,49.50049a15.97809,15.97809,0,0,0,7.23486,2.02148c.18189.01416.363.03077.54834.03272h.07227c.25195,0,.50049-.01465.74658-.0376a15.95877,15.95877,0,0,0,7.08447-2.01709l88-49.5a16.01817,16.01817,0,0,0,8.15625-13.94531V78.67871A15.98036,15.98036,0,0,0,229.927,70.82178ZM127.99975,29.17871l79.74366,44.856-30.62061,17.396L96.43969,46.93115Zm.91016,89.64258L48.37866,73.96582,80.03833,56.15723l80.76513,44.54492Zm7.21167,103.43164.78686-89.57373,32.11377-18.24463v38.07617a8,8,0,0,0,16,0v-47.166l30.97754-17.59864v89.5752Z">
                                    </path>
                                </g>
                            </svg>
                            <span class="hidden text-xs md:inline-block lg:text-sm">
                                Sell Share/Equity
                            </span>
                        </button>
                    </div>

                    <!-- Cart Icon -->
                    <div class="mx-4 hidden" id="cart-count-view">
                        <button onclick="showcart();"
                            class="flex relative w-full items-center gap-3 p-2 hover:opacity-60 rounded-md">
                            <svg viewBox='0 0 32 32' version='1.1' xmlns='http://www.w3.org/2000/svg'
                                xmlns:xlink='http://www.w3.org/1999/xlink'
                                xmlns:sketch='http://www.bohemiancoding.com/sketch/ns' fill='currentColor'
                                height='25px' width='25px' class='w-8 h-8'>
                                <g stroke-width='0'></g>
                                <g stroke-linecap='round' stroke-linejoin='round'></g>
                                <g>
                                    <g stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'
                                        sketch:type='MSPage'>
                                        <g sketch:type='MSLayerGroup' transform='translate(-516.000000, -723.000000)'
                                            fill='currentColor'>
                                            <path
                                                d='M538,749 C539.104,749 540,749.896 540,751 C540,752.104 539.104,753 538,753 C536.896,753 536,752.104 536,751 C536,749.896 536.896,749 538,749 L538,749 Z M534,751 C534,753.209 535.791,755 538,755 C540.209,755 542,753.209 542,751 C542,748.791 540.209,747 538,747 C535.791,747 534,748.791 534,751 L534,751 Z M522,729 L546,729 L543.443,739.229 L522,741 L522,729 L522,729 Z M524,745 C522.896,745 522,744.104 522,743 L545,741 C545,741 548,728.875 548,728 C548,727.447 547.553,727 547,727 L522,727 L522,725 L523,725 C523.553,725 524,724.553 524,724 C524,723.448 523.553,723 523,723 L517,723 C516.447,723 516,723.448 516,724 C516,724.553 516.447,725 517,725 L520,725 L520,743 C520,745.209 521.791,747 524,747 L547,747 C547.031,747 547,746.009 547,745 L524,745 L524,745 Z M526,749 C527.104,749 528,749.896 528,751 C528,752.104 527.104,753 526,753 C524.896,753 524,752.104 524,751 C524,749.896 524.896,749 526,749 L526,749 Z M522,751 C522,753.209 523.791,755 526,755 C528.209,755 530,753.209 530,751 C530,748.791 528.209,747 526,747 C523.791,747 522,748.791 522,751 L522,751 Z'
                                                sketch:type='MSShapeGroup'> </path>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                            <span id="cart-count-desktop"
                                class="text-sm py-1 px-2 leading-[normal] primary-bg-color text-black rounded-full absolute top-0 -right-2">1</span>
                        </button>
                    </div>

                    <div
                        class="hidden md:flex w-full max-w-96 md:max-w-xl items-center rounded-md overflow-hidden border active-focus-border mx-4">
                        @if (isset($search))
                            <input class="py-2 px-5 h-full w-full rounded-md text-sm focus-visible:outline-none"
                                type="search" id="search" placeholder="Search..." name="search-field"
                                value="{{ $search }}">
                        @else
                            <input class="py-2 px-5 h-full w-full rounded-md text-sm focus-visible:outline-none"
                                type="search" id="search" placeholder="Search..." name="search-field">
                        @endif

                        <div class="py-2 px-4 h-full dark-bg-color flex cursor-pointer text-white transition duration-200 ease-in-out hover:opacity-60"
                            onclick="onSearch(document.getElementById('search').value);">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                class="w-7 h-7 mx-auto cursor-pointer">
                                <path fill-rule="evenodd"
                                    d="M10.5 3.75a6.75 6.75 0 100 13.5 6.75 6.75 0 000-13.5zM2.25 10.5a8.25 8.25 0 1114.59 5.28l4.69 4.69a.75.75 0 11-1.06 1.06l-4.69-4.69A8.25 8.25 0 012.25 10.5z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                    </div>

                    @if (Auth::check())
                        {{-- mx-4  --}}
                        <div class="flex max-w-52 items-center mr-0">
                            <a href="{{ route('customer.account') }}" title="Account">
                                <div class="bg-slate-200 text-base py-3 px-5 rounded-full uppercase">
                                    {{ $util->getFirstCharacters(Auth::user()->name) }}
                                </div>
                            </a>
                        </div>
                    @endif
                </div>

                @yield('content')

                <!-- Footer -->
                @include('components.footer.customer-footer')
            </div>


        </div>

        <!-- Right Sidebar -->
        @include('components.sidebars.right-sidebar.right-sidebar')
    </div>

    <!-- Bottom Navigation -->
    @include('components.navbar.bottom-nav.bottom-nav')

    <!-- Modal -->
    @include('components.modal.modal')

    <!-- Modal Video -->
    @include('components.modal.modal-video')

    <!-- Core JS Files -->
    <script src="{{ asset('assets/js/plugins/perfect-scrollbar.jquery.min.js') }}"></script>

    <!-- JS -->
    @include('layouts.landing-layout-js.landing-js')
</body>

</html>
