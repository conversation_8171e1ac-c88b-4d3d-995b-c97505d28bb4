@inject('setting', 'App\Models\Setting')

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{{ csrf_token() }}" />
    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no'
        name='viewport' />

    <title>Welcome To | {{ config('app.name') }}</title>

    <!-- Favicon Logo -->
    @if ($setting->getInfo('logo'))
        <link rel="icon" href="{{ $setting->getInfo('logo') }}" type="image">
    @else
        <link rel="icon" href="{{ asset('img/logo.png') }}" type="image">
    @endif

    <!-- CSS Files -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('assets/css/paper-dashboard.css?v=2.0.1') }}" rel="stylesheet" />
    @include('styles.dashboard-styles')

    <!-- Jquery Core Js -->
    <script src="{{ asset('assets/js/core/jquery.min.js') }}"></script>

    {{--
    <!-- Select Plugin Js -->
    <script src="plugins/bootstrap-select/js/bootstrap-select.js"></script> --}}

    <!-- Fonts and icons -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">

    <!-- DropZone -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.4.0/min/dropzone.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.4.0/dropzone.js"></script>

    {{-- CKEditor CDN --}} <!-- Works -->
    {{-- Main --}}
    {{-- <script src="https://cdn.ckeditor.com/ckeditor5/23.0.0/classic/ckeditor.js"></script> --}}
    {{-- Others --}}
    {{-- <script src="https://cdn.ckeditor.com/4.21.0/standard/ckeditor.js"></script> --}}
    {{-- <script src="https://cdn.ckeditor.com/4.25.1-lts/standard/ckeditor.js"></script> --}}

    {{-- <link rel="stylesheet" href="https://cdn.ckeditor.com/ckeditor5/44.2.0/ckeditor5.css">
    <script src="https://cdn.ckeditor.com/ckeditor5/44.2.0/ckeditor5.umd.js"></script>
    <script>
        const {
            ClassicEditor,
            Essentials,
            Bold,
            Italic,
            Font,
            Paragraph
        } = CKEDITOR;
    </script> --}}

    {{-- Quill Editor --}}
    <script src="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.snow.css" rel="stylesheet">

    <!-- Swal -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    @vite([])
    @laravelPWA
</head>

<body class="antialiased">
    <style>
        .quill-editor {
            height: 42rem;
        }

        .text-info-desc {
            background-color: #d9d9d9;
            padding: 1rem;
            width: 100%;
            border-radius: .5rem;
            font-size: 1rem;
        }

        .text-info-label {
            min-width: 9.25rem;
        }
    </style>
    @include('layouts.dashboard-layout-js.js')

    {{-- Loading bar --}}
    <div class="loading-container" id="loading-bar">
        <div class="loading-bar left"></div>
        <div class="loading-bar right"></div>
    </div>

    <div class="wrapper">
        @include('components.sidebars.left-sidebar.left-sidebar', [])
        <div class="main-panel">
            <!-- Navbar -->
            @include('components.navbar.header-nav.header-navbar')
            <!-- End Navbar -->

            <div class="content">

                @yield('content')

                {{-- @include('components.tabs-with-table.tabs-with-table') --}}
            </div>

            <!-- Footer -->
            @include('components.footer.footer')
            <!-- End Footer -->
        </div>
    </div>

    <!-- Core JS Files -->
    <script src="{{ asset('assets/js/core/popper.min.js') }}"></script>
    <script src="{{ asset('assets/js/core/bootstrap.min.js') }}"></script>

    <!-- Select Plugin Js -->
    <script src="{{ asset('assets/js/plugins/perfect-scrollbar.jquery.min.js') }}"></script>
    {{-- <script src="{{ asset('assets/js/plugins/perfect-scrollbar.js') }}"></script> --}}

    <!-- JS -->
    {{-- <script type="text/javascript" src="{{ asset('assets/js/paper-dashboard.js') }}"></script> --}}
    <script type="text/javascript" src="{{ asset('assets/js_2/paper-dashboard.js') }}"></script>

    <!--  Google Maps Plugin -->
    {{-- <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_KEY_HERE"></script> --}}

    <!-- Chart JS -->
    <script src="{{ asset('assets/js/plugins/chartjs.min.js') }}"></script>

    <!--  Notifications Plugin -->
    <script src="{{ asset('assets/js/plugins/bootstrap-notify.js') }}"></script>

    <script>
        // Menu toggle
        $(document).ready(function() {
            //jquery for toggle sub menus
            $('.menu-toggle').click(function() {
                $(this).next('.ml-menu').slideToggle();
                $(this).find('.dropdown').toggleClass('rotate');
            });

            var dropdown = document.getElementsByClassName("menu-toggle");
            var i;
            for (i = 0; i < dropdown.length; i++) {
                var currentLocation = location.href;
                if (dropdown[i].nextElementSibling.getElementsByClassName('q-menu-active')[0] != null) {
                    var dropdownLocation = dropdown[i].nextElementSibling.getElementsByClassName('q-menu-active')[0]
                        .getElementsByClassName('menu-text')[0].href;
                    $(this).find('.q-menu-active .dropdown').addClass('rotate');
                    if (currentLocation == dropdownLocation) {
                        dropdown[i].nextElementSibling.style.display = "block";
                    }
                }
            }
            loadingIcon('loading-bar', 0);
        });

        function showNotification(color, msg, from, align) {
            // color -> primary, info, success, warning, danger
            color = color ?? 'primary';

            $.notify({
                icon: "nc-icon nc-bell-55",
                message: msg

            }, {
                type: color,
                timer: 3000,
                placement: {
                    from: from,
                    align: align
                }
            });
        }

        function inputHandler(e, parent, min, max) {
            var value = parseInt(e.target.value);
            if (value.isEmpty)
                value = 0;
            if (isNaN(value))
                value = 0;
            if (value > max)
                value = max;
            if (value < min)
                value = min;
            parent.value = value;
        }

        var lastOrders = 0;

        function getChatNewMessages() {
            return;
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                },
                type: 'POST',
                url: '{{ url('getChatMessagesNewCount') }}',
                data: {},
                success: function(data) {
                    // console.log(data);
                    if (document.getElementById("countChatNewMessages") != null)
                        document.getElementById("countChatNewMessages").innerHTML = data.count;
                    document.getElementById("countNewOrders").innerHTML = data.orders;
                    if (data.orders != lastOrders) {
                        lastOrders = data.orders;
                        const audio = new Audio("img/sound.mp3");
                        audio.play();
                        $orders = document.getElementById("orders-table");
                        if ($orders != null) { // open orders page
                            paginationGoPage(1);
                            // getChatNewMessages();
                        }
                    }
                    // if (document.getElementById("messagesWindow") != null)
                    //     buildChatUsers();
                },
                error: function(e) {
                    console.log(e);
                }
            });
        }

        setInterval(getChatNewMessages, 10000); // one time in 10 sec
        getChatNewMessages();

        function moveToPageWithSelectedItem(id) {
            var itemsTable = $('#data_table').DataTable();
            var indexes = itemsTable
                .rows()
                .indexes()
                .filter(function(value, index) {
                    return id === itemsTable.row(value).data()[0];
                });
            var numberOfRows = itemsTable.data().length;
            var rowsOnOnePage = itemsTable.page.len();
            if (rowsOnOnePage < numberOfRows) {
                var selectedNode = itemsTable.row(indexes).node();
                var nodePosition = itemsTable.rows({
                    order: 'current'
                }).nodes().indexOf(selectedNode);
                var pageNumber = Math.floor(nodePosition / rowsOnOnePage);
                itemsTable.page(pageNumber).draw(false); //move to page with the element
                return pageNumber;
            }
            return 0;
        }

        function showDeleteMessage(id, url, callBack, current_page) {
            swal.fire({
                icon: "warning",
                title: "Are you sure?",
                text: "You will not be able to recover this item!",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: 'Yes',
                cancelButtonText: 'No'
            }).then((result) => {
                if (result.isConfirmed) {
                    url = url.replace(':id', id);
                    $.ajax({
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                        },
                        type: 'DELETE',
                        url: url,
                        data: {
                            id: id
                        },
                        success: function(data) {
                            // console.log(data);
                            if (data.error != 0) {
                                if (data.error == 2) {
                                    return Toast.fire({
                                        icon: 'error',
                                        title: data.text
                                    });
                                }
                                return Toast.fire({
                                    icon: 'error',
                                    title: 'Something went wrong'
                                });
                            }
                            //
                            // remove from ui
                            //
                            if (callBack && data.data) callBack(data.data);
                            else if (callBack) callBack(current_page);
                            else paginationGoPage(currentPage);

                            return Toast.fire({
                                icon: "success",
                                title: "Deleted successfully"
                            });
                        },
                        error: function(e) {
                            console.log(e);
                        }
                    });
                } else {

                }
            });
        }

        function onCloseSideBar() {
            document.getElementById("leftsidebar").style.display = "none";
            document.getElementById("content-section").style.margin = '100px 15px 0 15px';
            document.getElementById("sidebar-open-button").hidden = false;
        }

        function onOpenSideBar() {
            document.getElementById("leftsidebar").style.display = "inline-block";
            document.getElementById("content-section").style.margin = '100px 15px 0 300px';
            document.getElementById("sidebar-open-button").hidden = true;
        }

        // For mouse horizontall scroll
        const tableSlider = document.querySelector('.table-responsive');
        let mouseDown = false;
        let startX, scrollLeft;

        let startDragging = function(e) {
            mouseDown = true;
            startX = e.pageX - tableSlider.offsetLeft;
            scrollLeft = tableSlider.scrollLeft;
        };
        let stopDragging = function(event) {
            mouseDown = false;
        };

        if (tableSlider != null) {
            tableSlider.addEventListener('mousemove', (e) => {
                e.preventDefault();
                if (!mouseDown) {
                    return;
                }
                const x = e.pageX - tableSlider.offsetLeft;
                const scroll = x - startX;
                tableSlider.scrollLeft = scrollLeft - scroll;
            });
            // Add the event listeners
            tableSlider.addEventListener('mousedown', startDragging, false);
            tableSlider.addEventListener('mouseup', stopDragging, false);
            tableSlider.addEventListener('mouseleave', stopDragging, false);
        }
    </script>

    @yield('scripts')

</body>

</html>
