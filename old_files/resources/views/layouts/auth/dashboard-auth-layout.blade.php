@inject('setting', 'App\Models\Setting')

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{{ csrf_token() }}" />
    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no'
        name='viewport' />

    <title>Welcome To | {{ config('app.name') }}</title>

    <!-- Logo Favicon -->
    @if ($setting->getInfo('logo'))
        <link rel="icon" href="{{ $setting->getInfo('logo') }}" type="image">
    @else
        <link rel="icon" href="{{ asset('img/logo.png') }}" type="image">
    @endif

    <!-- CSS Files -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet" />
    <link href="{{ asset('assets/css/paper-dashboard.css?v=2.0.1') }}" rel="stylesheet" />

    <!-- Jquery Core Js -->
    <script src="{{ asset('assets/js/core/jquery.min.js') }}"></script>

    <!-- Fonts and icons -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">

    <!-- Swal -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    @vite([])
</head>

<body class="antialiased">
    @include('styles.dashboard-styles')
    <style>
        .wrapper {
            background: hsla(153, 59%, 53%, 1);
            background: linear-gradient(135deg, hsla(153, 59%, 53%, 1) 0%, hsla(0, 0%, 100%, 1) 100%);
            background: -moz-linear-gradient(135deg, hsla(153, 59%, 53%, 1) 0%, hsla(0, 0%, 100%, 1) 100%);
            background: -webkit-linear-gradient(135deg, hsla(153, 59%, 53%, 1) 0%, hsla(0, 0%, 100%, 1) 100%);
            filter: progid: DXImageTransform.Microsoft.gradient(startColorstr="#41CE8E", endColorstr="#FFFFFF", GradientType=1);
        }
    </style>
    @include('layouts.dashboard-layout-js.js')


    {{-- Loading bar --}}
    <div class="loading-container hidden" id="loading-bar">
        <div class="loading-bar left"></div>
        <div class="loading-bar right"></div>
    </div>

    <div class="wrapper d-flex align-items-center p-4">
        @yield('content')
    </div>

    <!-- Core JS Files -->
    <script src="{{ asset('assets/js/core/popper.min.js') }}"></script>
    <script src="{{ asset('assets/js/core/bootstrap.min.js') }}"></script>

    <!--  Notifications Plugin    -->
    <script src="{{ asset('assets/js/plugins/bootstrap-notify.js') }}"></script>

    <script>
        // Menu toggle
        $(document).ready(function() {});

        // // Toast
        // const Toast = Swal.mixin({
        //     toast: true,
        //     position: "top-end",
        //     showConfirmButton: false,
        //     timer: 4500,
        //     timerProgressBar: true,
        //     didOpen: (toast) => {
        //         toast.onmouseenter = Swal.stopTimer;
        //         toast.onmouseleave = Swal.resumeTimer;
        //     }
        // });
    </script>
    @yield('scripts')
</body>

</html>
