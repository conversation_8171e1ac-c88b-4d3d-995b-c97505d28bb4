@inject('setting', 'App\Models\Setting')


<!-- Common scripts -->
@include('utils.js.js')
<script>
    // Toast
    // colors = success, error, warning, info, question
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 5000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });

    // Loading icon
    function loadingIcon(id, state) {
        let el = document.getElementById(id);
        if (state == 1 && el) {
            el.classList.remove("hidden");
        } else if (state == 0 && el) {
            el.classList.add("hidden");
        }
    }

    function disableBtn(id, state) {
        let el = document.getElementById(id);
        if (state == 1 && el) {
            el.disabled = true;
            el.classList.add('opacity-60');
        } else if (state == 0 && el) {
            el.disabled = false;
            el.classList.remove('opacity-60');
        }
    }

    // Loading icon
    function setinnerHtml(elm, count = 0) {
        let el = document.querySelector(elm);
        if (el) {
            el.innerHTML = count;
        }
    }

    // Checkout
    function clearBasket() {
        basketItems = [];
        localStorage.setItem("items", JSON.stringify(basketItems));
        localStorage.removeItem("orderUpdate");
    }

    @if (Auth::check())
        // logout
        function logoutFromAccount() {
            clearBasket();
            window.location = '{{ route('logout') }}';
        }

        setInterval(getChatNewMessages, 10000); // one time in 10 sec
        getChatNewMessages();
    @endif

    // counts new messages
    function getChatNewMessages() {
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('newMessage') }}',
            data: {},
            success: function(data) {
                // console.log("Checking message: ", data, data.count == 0, data.error != '0');
                if (data.error)
                    return;

                if (data.count == 0) {
                    // Hide
                    loadingIcon('chat-count', 0);
                    loadingIcon('chat-countMobileView', 0);
                    return;
                }
                // Show
                loadingIcon('chat-count', 1);
                loadingIcon('chat-countMobileView', 1);
                setinnerHtml('#chat-count', data.count);
                setinnerHtml('#chat-countMobileView', data.count);
                // document.getElementById("chat-count").innerHTML = data.count;
                // console.log('chat count: ', document.getElementById("chat-count"));
            },
            error: function(e) {
                // console.log(e);
            }
        });
    }

    // Authentication check
    let authCheck = false;
    @if (Auth::check())
        authCheck = true;
    @endif

    // Initializes rating star buttons
    function initializeRateButtons() {
        $('.rating').off('click').on('click', function() {
            let value = parseInt($(this).attr('value'));
            let product = parseInt($(this).attr('product'));
            let variant = parseInt($(this).attr('variant'));
            // rateValue = value;
            rateProduct(product, variant, value);
        });

        let cbox = document.querySelectorAll(".rating");
        for (let i = 0; i < cbox.length; i++) {
            cbox[i].addEventListener("mouseenter", mouseEnter);
            cbox[i].addEventListener("mouseleave", mouseLeave);
        }
    }

    let rateValue = 0;
    // for rating reviews starts
    function mouseEnter(event) {
        rateValue = 0;
        let hoveredValue = parseInt(event.srcElement.getAttribute('value'));
        let childElements = event.srcElement.parentElement.children;
        for (let i = 0; i < 5; i++) {
            if (childElements[i].classList.contains("primary-text-color")) {
                rateValue++;
            }
        }
        for (i = 0; i <= hoveredValue - 1; i++) {
            if (!childElements[i].classList.contains("primary-text-color")) {
                childElements[i].classList.add("primary-text-color");
            }
        }
    }
    // for rating reviews starts
    function mouseLeave(event) {
        let childElements = event.srcElement.parentElement.children;
        if (childElements.length > 0) {
            for (i = 0; i <= rateValue - 1 && i <= 4; i++) {
                if (!childElements[i].classList.contains("primary-text-color")) {
                    childElements[i].classList.add("primary-text-color");
                }
            }
            for (i = rateValue; i <= 4; i++) {
                childElements[i].classList.remove("primary-text-color");
            }
        }
    }

    let allCoupons = new Array();
    let _coupon = "";

    {{--
        @foreach ($setting->getCoupons() as $key => $idata)
            allCoupons.push([
                "{{ $idata->name }}", // 0
                "{{ $idata->discount }}", // 1
                "{{ $idata->inpercents }}", // 2
                "{{ $idata->amount }}", // 3
                "{{ $idata->allCompanies }}", // 4
                "{{ $idata->allCategory }}", // 5
                "{{ $idata->allProducts }}", // 6
                JSON.parse("[{{ $idata->companiesList }}]"), // 7
                JSON.parse("[{{ $idata->categoryList }}]"), // 8
                JSON.parse("[{{ $idata->productsList }}]") // 9
            ]);
        @endforeach
         --}}

    let pickupPresent = "false";
    let marketDiscount = 0;
    // calculates subtotal price and total price
    function getPrices() {
        let _debug = [];
        let subTotalPrice = 0;
        let subTotalPrice2 = 0;
        let taxT = 0;
        let feeT = 0;
        let percentT = 0;
        let perkmT = 0;
        let flatrateT = 0;
        let market_tax = 0;
        let discountAmount = 0;
        let buinessDiscount = 0;
        basketItems.map((product, i) => {
            _debug.push({
                dataOrder: product
            });

            let currentPrice = 0;
            if (product.discountPrice)
                currentPrice = product.discountPrice;
            else
                currentPrice = product.price;

            currentPrice = parseFloat(currentPrice) * parseInt(product.count);

            // TODO: discount logic
            if (product.businessCalc) {
                product.businessCalc.forEach((prBisCalc) => {
                    let discounted = 0;
                    if (currentPrice >= prBisCalc.from_amount && currentPrice <= prBisCalc.to_amount) {
                        if (prBisCalc.discount_amount) {
                            if (prBisCalc.percent == 1) {
                                discounted = currentPrice * parseFloat(parseFloat(prBisCalc
                                        .discount_amount) /
                                    100);
                            } else {
                                discounted = parseFloat(prBisCalc.discount_amount);
                            }
                            buinessDiscount += discounted;
                        }
                    }
                });
            }

            subTotalPrice += currentPrice;
        });

        let couponPrice = subTotalPrice;
        _debug.push({
            subTotalPrice: subTotalPrice
        });

        if (buinessDiscount > 0) {
            subTotalPrice2 = subTotalPrice;
            subTotalPrice2 = subTotalPrice2 - buinessDiscount;
        }

        _coupon = "";
        if (allCoupons != null) {
            let t = document.getElementById("couponCode");
            if (t != null) {
                if (t.value.length > 0) {
                    // Disable coupon status text when not found
                    // document.getElementById("coupon_text").style.display = "none";
                    // document.getElementById("mincoupon_amount").hidden = true;
                }
                for (let coupon of allCoupons) {
                    if (coupon[0].toUpperCase() == document.getElementById("couponCode").value.toUpperCase()) {
                        _coupon = coupon;
                        _debug.push({
                            _coupon: _coupon
                        });
                        // document.getElementById("coupon_text").style.display = "block";
                        // document.getElementById("coupon_text").innerHTML = "Found"; // Found
                        // document.getElementById("coupon_text").style.color = "blue"; // Found
                        // console.log("subTotalPrice => ", subTotalPrice);
                        if (_coupon[3] >= subTotalPrice) {
                            // console.log("hid ", document.getElementById("mincoupon_amount").hidden);
                            // document.getElementById("mincoupon_amount").hidden = false;
                            // document.getElementById("mincoupon_amount").innerHTML =
                            "Minimum order amount must be greater than ETB" + _coupon[3] +
                                " for the coupon to work"; // Minimum order amount
                            // document.getElementById("mincoupon_amount").style.color = "red";
                            _debug.push({
                                subTotalPrice: subTotalPrice
                            });
                        } else {
                            // document.getElementById("mincoupon_amount").hidden = true;
                            subTotalPrice = getSubTotalWithCoupon(_debug);
                            _debug.push({
                                subTotalPrice: subTotalPrice
                            });
                        }
                        break;
                    }
                }
            }
        }

        // company pricing
        let marketPricing = @json($setting->companyPricing());
        // console.log("marketPricing: ", marketPricing, !(Object.keys(marketPricing).length === 0 && marketPricing
        //     .constructor === Object));

        // check if marketPricing is not empty then apply them
        if (!(Object.keys(marketPricing).length === 0 && marketPricing.constructor === Object)) {
            taxT = marketPricing.tax;
            feeT = marketPricing.fee;
            percentT = marketPricing.percent;
            perkmT = marketPricing.perkm;
            flatrateT = marketPricing.flatrate;
            market_tax = marketPricing.tax;
        }
        let tax = taxT * subTotalPrice / 100;
        let fee = feeT;
        if (percentT == 1)
            fee = subTotalPrice * feeT / 100;
        if (perkmT == 1)
            fee = feeT;
        if (flatrateT == 1)
            fee = feeT;
        if (pickupPresent == "true")
            fee = 0;
        if (subTotalPrice == 0)
            fee = 0;
        _debug.push({
            fee: fee
        });
        _debug.push({
            tax: tax
        });
        _debug.push({
            percentT: percentT
        });
        _debug.push({
            perkmT: perkmT
        });
        _debug.push({
            flatrateT: flatrateT
        });

        let _total = subTotalPrice + tax;
        let total2 = 0;
        let tax2 = 0;
        if (subTotalPrice2) {
            tax2 = taxT * subTotalPrice2 / 100;
            total2 = subTotalPrice2 + tax2;
        }
        // console.log("============================================================");
        // console.log("walletAmount: ", walletAmount);
        // console.log("referralPointAmount: ", referralPointAmount);
        // console.log("couponDiscount: ", couponDiscount);
        // console.log("============================================================");
        // if (
        //     (parseFloat(marketPricing.minDiscountAmount) > 0 &&
        //         parseFloat(_total) > parseFloat(marketPricing.minDiscountAmount)) ||
        //     walletAmount ||
        //     referralPointAmount ||
        //     couponDiscount
        // ) {
        //     total2 = _total;
        // }

        let totalDiscountAmount = 0;
        if (parseFloat(marketPricing.minDiscountAmount) > 0 && parseFloat(_total) > parseFloat(marketPricing
                .minDiscountAmount)) {
            marketDiscount = marketPricing.minDiscountAmount;
            discountAmount = marketPricing.discountAmount2;
            totalDiscountAmount = parseFloat(_total) * parseFloat(discountAmount);
            _total = parseFloat(_total) - parseFloat(_total) * parseFloat(discountAmount);
        }
        // let _total = subTotalPrice + tax + fee;
        // if (perkmT == '1' || flatrateT == '1')
        //     _total = subTotalPrice + tax;
        _debug.push({
            _total: _total
        });
        if (walletAmount) {
            _total = _total - walletAmount;
        }
        if (referralPointAmount) {
            _total = _total - referralPointAmount;
        }
        if (couponDiscount) {
            _total = _total - couponDiscount;
        }
        // console.log(_debug);
        return {
            tax: getPriceString(tax),
            _tax: tax,
            tax2: getPriceString(tax2),
            _tax2: tax2,
            fee: getPriceString(fee),
            _fee: fee,
            subTotal: getPriceString(subTotalPrice),
            _subTotal: subTotalPrice,
            subTotal2: getPriceString(subTotalPrice2),
            _subTotal2: subTotalPrice2,
            coupon: _coupon,
            couponPrice: getPriceString(couponPrice),
            total2: getPriceString(total2),
            _total2: total2,
            total: getPriceString(_total),
            _total: _total,
            percent: percentT,
            perkm: perkmT,
            flatrate: flatrateT,
            market_tax: market_tax,
            market_fee: feeT,
            market_discount: marketPricing.discountAmount,
            market_discount2: marketPricing.discountAmount2,
            total_discountAmount: getPriceString(totalDiscountAmount),
            buinessDiscount: getPriceString(buinessDiscount),
            _buinessDiscount: buinessDiscount,
        };
    }

    // sets price with currency string
    function getPriceString(price) {
        let currency = ' ETB';

        price = parseFloat(price).toFixed(2);
        price = Number(price).toLocaleString('en-US');
        let result = price + currency;
        return result;
    }

    // adds items from db to bakset then updates the order if items are different
    function loadCartFromServer() {
        let orderUpdate = localStorage.getItem("orderUpdate") ?? null;
        if (parseInt(orderUpdate) === 1) {
            saveOrder("loadBasketFromServer");
            // order status update off
            localStorage.setItem("orderUpdate", 0);
        } else {
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                type: 'POST',
                url: '{{ route('getCart') }}',
                data: {},
                success: function(data) {
                    // console.log("load basket from server response: ", data);
                    // console.log("load basket from local: ", basketItems);
                    // initial state of new items set to 0
                    let newItemState = 0;
                    // adding if local cart is not the same as online cart data
                    if (basketItems.length > 0) {
                        if ((data.order != null || data.orderdetails != null) && data
                            .orderdetails.length >
                            0) {
                            // console.log('length: ', data.orderdetails.length, basketItems.length);
                            for (let i = data.orderdetails.length; i--;) {
                                // console.log('length2: ', i);
                                // for stock items
                                let tempData = basketItems.find((orderData) => {
                                    // console.log('order details data: ', data.orderdetails[i],
                                    //     data.orderdetails[i], orderData,
                                    //     data.orderdetails[i].product_id == orderData.id,
                                    //     data.orderdetails[i].product_variant_id == orderData
                                    //     .variantId,
                                    //     data.orderdetails[i].branch_id == orderData.branchId,
                                    //     parseInt(data.orderdetails[i].count) == orderData.count
                                    // );
                                    // console.log('variant check: ', data.orderdetails[i]
                                    //     .product_variant_id, orderData.variantId,
                                    //     data.orderdetails[i].product_variant_id == orderData
                                    //     .variantId,
                                    // );
                                    return data.orderdetails[i].product_id == orderData.id &&
                                        data.orderdetails[i].product_variant_id == orderData
                                        .variantId &&
                                        data.orderdetails[i].branch_id == orderData.branchId &&
                                        parseInt(data.orderdetails[i].count) == orderData.count
                                });
                                if (!tempData) {
                                    newItemState = 1;
                                    break;
                                }
                            }
                        }
                    }
                    if (basketItems.length == 0 && ((data.order != null || data.orderdetails != null) &&
                            data.orderdetails.length > 0)) {
                        newItemState = 1;
                    }
                    // clearCart();
                    localStorage.setItem('order_info', JSON.stringify({}));
                    basketItems = [];
                    localStorage.setItem('items', JSON.stringify(basketItems));
                    if ((data.order != null || data.orderdetails != null) && data.orderdetails.length >
                        0) {
                        for (let y = data.orderdetails.length; y--;) {
                            let item;
                            // new item
                            item = {
                                id: data.orderdetails[y].product_id,
                                variantId: data.orderdetails[y].product_variant_id,
                                branchId: data.orderdetails[y].branch_id,
                                branchName: data.orderdetails[y].branchname,
                                businessCalc: data.orderdetails[y].business_calculator,
                                title: data.orderdetails[y].product.name,
                                price: data.orderdetails[y].product.price,
                                discountPrice: data.orderdetails[y].product.discount_price,
                                price2: data.orderdetails[y].product.price2,
                                discountPrice2: data.orderdetails[y].product.discount_price2,
                                images: data.orderdetails[y].product.images,
                                count: data.orderdetails[y].count,
                                quantity: data.orderdetails[y].quantity,
                                orderdetailsId: data.orderdetails[y].id
                            };
                            countToBasket = 1;
                            basketItems.push(item);
                            localStorage.setItem('items', JSON.stringify(basketItems));
                        }
                        saveOrder();
                    } else {
                        redrawCart();
                    }
                    if (parseInt(newItemState) === 1) {
                        Toast.fire({
                            icon: "info",
                            title: "Cart updated"
                        });
                    }
                },
                error: function(e) {
                    console.log(e);
                    return Toast.fire({
                        icon: "error",
                        title: "Error",
                        text: 'Something went wrong'
                    });
                }
            });
        }
    }

    // updates basket's order details id
    function updateBasketOrderDetailsId() {
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('getCart') }}',
            data: {},
            success: function(data) {
                // console.log("update order id response: ", data);
                if (data.order == null || data.orderdetails == null)
                    return;

                let orderdetails = data.orderdetails;

                // Update order id
                for (let i = basketItems.length; i--;) {
                    for (let j = orderdetails.length; j--;) {
                        // for stock items
                        // if (
                        //     parseInt(basketItems[i].id) === parseInt(orderdetails[j].product_id) &&
                        //     parseInt(basketItems[i].branchId) === parseInt(orderdetails[j].branch_id) &&
                        //     (
                        //         parseInt(basketItems[i].variantId) === parseInt(orderdetails[j]
                        //             .product_variant_id) ||
                        //         isNaN(parseInt(basketItems[i].variantId)) === isNaN(parseInt(orderdetails[j]
                        //             .product_variant_id))
                        //     )
                        // ) {
                        if (
                            basketItems[i].id == orderdetails[j].product_id &&
                            basketItems[i].branchId == orderdetails[j].branch_id &&
                            basketItems[i].variantId == orderdetails[j].product_variant_id
                        ) {
                            basketItems[i].orderdetailsId = parseInt(orderdetails[j].id);
                            break;
                        }
                    }
                }
                // redraw cart
                redrawCart();
                localStorage.setItem('items', JSON.stringify(basketItems));
            },
            error: function(e) {
                console.log(e);
            }
        });
    }

    // initializes basket and count total basket items
    function initBasket() {
        let totalCount = 0;
        basketItems.map((product, i) => {
            totalCount += parseInt(product.count);
        });
        let prices = getPrices();
        // console.log('count: ', totalCount);

        if (totalCount === 0) {
            loadingIcon('totalItemsCountDesktopView', 0);
            setinnerHtml('#totalItemsCountDesktopView', 0);

            loadingIcon("cart-count-view", 0);
            setinnerHtml('#cart-count-desktop', 0);

            loadingIcon('totalItemsCountMobileView', 0);
            setinnerHtml('#totalItemsCountMobileView', 0);
        } else {
            loadingIcon('totalItemsCountDesktopView', 1);
            setinnerHtml('#totalItemsCountDesktopView', totalCount);

            loadingIcon("cart-count-view", 1);
            setinnerHtml('#cart-count-desktop', totalCount);

            loadingIcon('totalItemsCountMobileView', 1);
            setinnerHtml('#totalItemsCountMobileView', totalCount);
        }

        // Default
        loadingIcon("sidebar-cart-btn--loading", 0);
        disableBtn("sidebar-cart-btn", 0);
    }

    // Clear cart data
    function clearCart() {
        localStorage.setItem('order_info', JSON.stringify({}));
        basketItems = [];
        localStorage.setItem('items', JSON.stringify(basketItems));
        redrawCart();
    }

    // Initializes right sidebar cart
    function initializeSidebarCarts() {
        let items = '';
        let el_carts = document.getElementById('sidebar-carts');
        let el_subtotal = document.getElementById('cart-subtotal');
        if (el_carts) {
            if (basketItems.length > 0) {
                items = basketItems.map((product, i) => {
                    let buinessDiscount = 0;
                    let currentPrice = 0;
                    if (product.discountPrice)
                        currentPrice = product.discountPrice;
                    else
                        currentPrice = product.price;

                    currentPrice = parseFloat(currentPrice) * parseInt(product.count);
                    let subtotal = currentPrice;

                    if (product.businessCalc) {
                        product.businessCalc.forEach((prBisCalc) => {
                            let discounted = 0;
                            if (currentPrice >= prBisCalc.from_amount && currentPrice <= prBisCalc
                                .to_amount) {
                                if (prBisCalc.discount_amount) {
                                    if (prBisCalc.percent == 1) {
                                        discounted = currentPrice * parseFloat(parseFloat(prBisCalc
                                                .discount_amount) /
                                            100);
                                    } else {
                                        discounted = parseFloat(prBisCalc.discount_amount);
                                    }
                                    buinessDiscount = discounted;
                                }
                            }
                        });
                    }

                    subtotal = getPriceString(subtotal);
                    let subtotalView =
                        `<div class="w-full h-full text-base leading-8 text-right font-bold">${subtotal}</div>`;
                    if (buinessDiscount != 0) {
                        buinessDiscount = getPriceString(buinessDiscount);
                        subtotalView = `<div class="w-full h-full flex flex-col">
                                <span class="w-full h-full text-sm text-right text-slate-500 line-through">${subtotal}</span>
                                <span class="w-full h-full text-base text-right font-bold">${buinessDiscount}</span>
                            </div>
                        `;
                    }

                    let price = getPriceString(currentPrice);
                    let url = `{{ route('details', ':id') }}`;
                    url = url.replace(':id', product.id);

                    return `
                        <div class="grid place-items-center mb-2 border-b">
                            <div class="grid grid-cols-[100px_minmax(calc(100%_-_(100px_+_50px)),_1fr)_50px] w-full h-full">
                                <a href="${url}" class="w-full h-full p-2 flex overflow-hidden h-16">
                                    <img src="{{ asset('${product.images[0].image_path}') }}" alt="${product.title}" srcset=""
                                        class="w-auto h-auto max-w-full max-h-full mx-auto text-center">
                                </a>
                                <div class="block self-center p-2">
                                    <a a href="${url}" class="text-base line-clamp-2">${product.title}</a>
                                    <h2 class="font-normat text-base">${product.count} x ${price}</h2>
                                </div>
                                <div class="h-full flex text-black">
                                    <svg viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" onclick="removeFromCart(${product.id}, ${product.variantId}, ${product.branchId}, ${product.orderdetailsId});"
                                        xmlns:xlink="http://www.w3.org/1999/xlink" fill="currentColor" height="24px" width="24px"
                                        class="w-6 h-6 m-auto cursor-pointer transition-all duration-200 ease-in-out hover:opacity-60">
                                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                        <g id="SVGRepo_iconCarrier">
                                            <g id="icomoon-ignore"> </g>
                                            <path
                                                d="M26.129 5.871h-5.331v-1.066c0-1.178-0.955-2.132-2.133-2.132h-5.331c-1.178 0-2.133 0.955-2.133 2.132v1.066h-5.331v1.066h1.099l1.067 20.259c0 1.178 0.955 2.133 2.133 2.133h11.729c1.178 0 2.133-0.955 2.133-2.133l1.049-20.259h1.051v-1.066zM12.268 4.804c0-0.588 0.479-1.066 1.066-1.066h5.331c0.588 0 1.066 0.478 1.066 1.066v1.066h-7.464v-1.066zM22.966 27.14l-0.002 0.027v0.028c0 0.587-0.478 1.066-1.066 1.066h-11.729c-0.587 0-1.066-0.479-1.066-1.066v-0.028l-0.001-0.028-1.065-20.203h15.975l-1.046 20.204z"
                                                fill="currentColor"> </path>
                                            <path d="M15.467 9.069h1.066v17.060h-1.066v-17.060z" fill="currentColor"> </path>
                                            <path d="M13.358 26.095l-1.091-17.027-1.064 0.068 1.091 17.027z" fill="currentColor">
                                            </path>
                                            <path d="M20.805 9.103l-1.064-0.067-1.076 17.060 1.064 0.067z" fill="currentColor">
                                            </path>
                                        </g>
                                    </svg>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 smmd:grid-cols-2 place-items-baseline px-2 pb-2 w-full">
                                <div class="inline-flex mr-auto rounded-md outline outline-1 outline-[#e5e7eb]">
                                    <span
                                        class="dec qty-btn qty-sidebar-btn text-lg p-3 leading-4 border-r flex items-center justify-center cursor-pointer select-none">-</span>
                                    <input type="text" value="${product.count}" id="qt${product.id}_${product.variantId}_${product.branchId}_${product.orderdetailsId}" type="number"
                                        class="pro-qty-input bg-slate-100 text-center text-lg px-2 max-w-20 focus-within:outline-0">
                                    <span
                                        class="inc qty-btn qty-sidebar-btn text-lg p-3 leading-4 border-l flex items-center justify-center cursor-pointer select-none">+</span>
                                </div>
                                ${subtotalView}
                            </div>
                        </div>
                `;
                }).join("");

                el_carts.classList.remove("h-full");
                el_carts.innerHTML = items;
                document.getElementById('checkout-btn').style.display = "flex";

                // Initialize button
                initQuantity('.qty-sidebar-btn', saveOrder);
            } else {
                el_carts.classList.add("h-full");
                el_carts.innerHTML = `
                    <div class="block text-3xl text-center place-content-center">
                        Your cart is empty!
                    </div>
                `;
                document.getElementById('checkout-btn').style.display = "none";
            }
        }
        let prices = getPrices();
        if (el_subtotal) {
            let subtotalsView =
                `<div class="w-full h-full text-base leading-8 text-right font-bold">${prices.subTotal}</div>`;
            if (prices._subTotal2) {
                subtotalsView = `<div class="flex flex-col">
                            <span class="w-full h-full text-sm text-right text-slate-500 line-through">${prices.subTotal}</span>
                            <span class="w-full h-full text-right font-bold">${prices.subTotal2}</span>
                        </div>
                    `;
            }
            el_subtotal.innerHTML = subtotalsView;
            // if (prices._subTotal2)
            //     el_subtotal.innerHTML = prices.subTotal2;
        }
    }

    function quntupdate(e, callback) {
        if (!isNaN(e.target.value) && !isNaN(parseFloat(e.target.value))) {
            proQtyCallback(e.target, e.target.value, callback);
        }
    }

    function initQuantity(className, callback) {
        // Remove existing event listeners first
        $(className).off('click');

        // Cart count
        $(className).on('click', function(e) {
            e.preventDefault();
            let $button = $(this);
            let $input = $button.parent().find('input');
            let oldValue = parseInt($input.val()) || 1;
            let newVal = oldValue;
            if ($button.hasClass('inc')) {
                newVal = oldValue + 1;
            } else {
                // Don't allow decrementing below zero
                newVal = oldValue > 1 ? oldValue - 1 : 1;
            }
            $input.val(newVal);
            proQtyCallback($input.get(0), newVal, callback);
        });

        // Input field on change
        $('.pro-qty-input').off('input propertychange').on('input propertychange', function(e) {
            quntupdate(e, callback);
        });
    }

    function proQtyCallback(el, val, callback) {
        let id = el.id.substr(2, el.id.length - 2);
        let productId = id.split("_", 4)[0];
        let variantId = id.split("_", 4)[1] == 'null' ? null : id.split("_", 4)[1];
        let branchId = id.split("_", 4)[2];
        let orderdetailsId = id.split("_", 4)[3] == 'null' ? null : id.split("_", 4)[3];

        for (let i = basketItems.length; i--;) {
            // console.log('checking id: ', basketItems[i].id, productId, basketItems[i].id == productId);
            // console.log('checking branchId: ', basketItems[i].branchId, branchId, basketItems[i].branchId == branchId);
            // console.log('checking variantid: ', basketItems[i].variantId, variantId, basketItems[i].variantId ==
            //     variantId);
            // console.log('checking orderdetailsId: ', basketItems[i].orderdetailsId, orderdetailsId, basketItems[i]
            //     .orderdetailsId ==
            //     orderdetailsId);

            // console.log('val: ', basketItems[i].id == productId &&
            //     basketItems[i].variantId == variantId &&
            //     basketItems[i].branchId == branchId &&
            //     basketItems[i].orderdetailsId == orderdetailsId);

            if (
                basketItems[i].id == productId &&
                basketItems[i].variantId == variantId &&
                basketItems[i].branchId == branchId &&
                basketItems[i].orderdetailsId == orderdetailsId
            ) {
                if (parseInt(val) <= basketItems[i].quantity) {
                    basketItems[i].count = val;
                } else {
                    Toast.fire({
                        icon: "error",
                        title: `Cart item quantity limit reached.`
                    });
                }
                break;
            }
        }
        localStorage.setItem('items', JSON.stringify(basketItems));

        // Now we can properly chain the promise
        callback().then(() => {
            redrawCart();
        }).catch(error => {
            console.error('Error saving order:', error);
        });
    }

    @if (Auth::check() && Request::route()->getName() == 'cart')
        // hide cart checkout
        function hideCartData() {
            // Hide delivery method
            loadingIcon("destination-method", 0);

            // Hide user destination
            loadingIcon("destination", 0);

            let div2 = document.getElementsByClassName('div-2');
            if (div2.length > 0) {
                // Hide cart amount
                div2[0].classList.add('hidden');
            }

            cartCheckoutview = document.getElementById("cart-div-1");
            if (cartCheckoutview) {
                cartCheckoutview.classList.add("empty-cart");
            }

            if (document.getElementById("paymentButton")) {
                document.getElementById("paymentButton").innerHTML = ``;
            }

            loadingIcon("update-cart", 0);
        }

        // show cart checkout
        function showCartData() {
            // Show delivery method
            // loadingIcon("destination-method", 1);
            loadingIcon("destination-method", 0);

            // Show user destination
            loadingIcon("destination", 0);

            let div2 = document.getElementsByClassName('div-2');
            if (div2.length > 0) {
                // Hide cart amount
                div2[0].classList.remove('hidden');
            }

            cartCheckoutview = document.getElementById("cart-div-1");
            if (cartCheckoutview) {
                cartCheckoutview.classList.remove("empty-cart");
            }

            if (document.getElementById("paymentButton")) {
                document.getElementById("paymentButton").innerHTML = `
                    <button onclick="onPayment();" id="onPayment" class="cursor-pointer text-black theme-main-bgcolor theme-main-bgcolorHover hover:text-white p-4 theme-borderradius transition duration-200 ease-in-out flex justify-center items-center w-full text-lg">
                        <svg id="payments-method--loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-black flex hidden"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                        </svg>
                        <span id="payment_text" class="uppercase font-bold">Order</span>
                    </button>
                `;
            }

            loadingIcon("update-cart", 1);
        }

        // New Update
        // Initialize cart items
        function initTableCart() {
            if (document.getElementById("cart_tbody") == null)
                return;

            // Show loading cart
            loadingIcon("cart-total--loading", 1);
            loadingIcon("cart-info--loading", 1);
            loadingIcon("cart-infoResponsive--loading", 1);

            // cart pricings
            let prices = getPrices();

            // If cart items are empty
            if (basketItems.length == 0) {
                // hide checkout view
                hideCartData();
                // desktop view cart
                document.getElementById("cart_tbody").innerHTML = `
                    <div class="w-full text-center block p-10 px-4">
                        <div class="block">
                            <svg height="125px" width="125px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-[125px] h-[125px] mx-auto">
                                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                <g id="SVGRepo_iconCarrier">
                                    <path d="M7.5 18C8.32843 18 9 18.6716 9 19.5C9 20.3284 8.32843 21 7.5 21C6.67157 21 6 20.3284 6 19.5C6 18.6716 6.67157 18 7.5 18Z" stroke="#000000" stroke-width="1.5"></path>
                                    <path d="M16.5 18.0001C17.3284 18.0001 18 18.6716 18 19.5001C18 20.3285 17.3284 21.0001 16.5 21.0001C15.6716 21.0001 15 20.3285 15 19.5001C15 18.6716 15.6716 18.0001 16.5 18.0001Z" stroke="#000000" stroke-width="1.5"></path>
                                    <path d="M11.5 12.5L14.5 9.5M14.5 12.5L11.5 9.5" stroke="#000000" stroke-width="1.5" stroke-linecap="round"></path>
                                    <path d="M2 3L2.26121 3.09184C3.5628 3.54945 4.2136 3.77826 4.58584 4.32298C4.95808 4.86771 4.95808 5.59126 4.95808 7.03836V9.76C4.95808 12.7016 5.02132 13.6723 5.88772 14.5862C6.75412 15.5 8.14857 15.5 10.9375 15.5H12M16.2404 15.5C17.8014 15.5 18.5819 15.5 19.1336 15.0504C19.6853 14.6008 19.8429 13.8364 20.158 12.3075L20.6578 9.88275C21.0049 8.14369 21.1784 7.27417 20.7345 6.69708C20.2906 6.12 18.7738 6.12 17.0888 6.12H11.0235M4.95808 6.12H7" stroke="#000000" stroke-width="1.5" stroke-linecap="round"></path>
                                </g>
                            </svg>
                        </div>
                        <div class="uppercase mt-2 align-self-center">
                            Your cart is empty
                        </div>
                    </div>
                `;
                // mobile view cart
                // document.getElementById("cart_responsiveBody").innerHTML = `
                //     <div class="grid grid-cols-2 bg-white hover:bg-[#e9ecef] p-[15px] border border-[#ededed] theme-main-bordercolorHover transition duration-200 ease-in-out rounded-md">
                //         <div class="grid">
                //             <div class="p-[15px] text-base font-bold">Product</div>
                //             <div class="p-[15px] text-base font-bold">Branch</div>
                //             <th class="p-4 text-sm font-bold">Removed Ingredient</th>
                //             <th class="p-4 text-sm font-bold">Extras</th>
                //             <div class="p-[15px] text-base font-bold">Price</div>
                //             <div class="p-[15px] text-base font-bold">Quantity</div>
                //             <div class="p-[15px] text-base font-bold">Total</div>
                //         </div>
                //         <div class="flex">
                //             <div class="my-auto text-center text-[24px]">Cart Is Empty</div>
                //         </div>
                //     </div>
                // `;
                document.getElementById("basket2_subtotal").innerHTML = prices.subTotal;
                document.getElementById("basket2_tax").innerHTML = prices.tax;
                document.getElementById("market_tax").innerHTML = prices.market_tax + "%";

                // per km
                if (prices.perkm == '1') {
                    document.getElementById("basket2_fee").innerHTML = prices.fee +
                        " per {{ $setting->getKmOrMiles() }}";
                } else {
                    document.getElementById("basket2_fee").innerHTML = prices.fee;
                }

                if (prices._total2 != 0) {
                    document.getElementById("basket2_total").innerHTML = `
                        <span class="line-through">${prices.total2}</span>
                        <br>
                        ${prices.total}
                    `;
                } else {
                    document.getElementById("basket2_total").innerHTML = prices.total;
                }

                document.getElementById("basket2_investmentDiscountAmount").innerHTML = '';
                // Hide
                loadingIcon("investment_discount", 0);

                if (marketDiscount != 0) {
                    // Discount amount
                    document.getElementById("market_discountAmount").innerHTML = prices.market_discount;
                    document.getElementById("basket2_discountAmount").innerHTML = prices.total_discountAmount;
                    // Show
                    loadingIcon("market_discount", 1);
                } else {
                    // Hide Discount amount
                    loadingIcon("market_discount", 0);
                    if (document.getElementById("market_discountAmount")) document.getElementById(
                        "market_discountAmount").innerHTML = '';
                    document.getElementById("basket2_discountAmount").innerHTML = '';
                }

                // Turn off loading cart
                loadingIcon("cart-total--loading", 0);
                loadingIcon("cart-info--loading", 0);
                loadingIcon("cart-infoResponsive--loading", 0);
                loadingIcon("cart-total--view", 0);
                loadingIcon("coupon--view", 0);
                return;
            }
            loadingIcon("cart-total--view", 1);
            loadingIcon("coupon--view", 1);
            // show checkout view
            showCartData();
            // load payments
            onPaymentMethodClick();
            let minAmount = 0;
            document.getElementById("cart_tbody").innerHTML = basketItems.map((product, i) => {
                let buinessDiscount = 0;
                let currentPrice = 0;
                if (product.discountPrice)
                    currentPrice = product.discountPrice;
                else
                    currentPrice = product.price;

                currentPrice = parseFloat(currentPrice) * parseInt(product.count)
                let subtotal = currentPrice;
                if (product.businessCalc) {
                    product.businessCalc.forEach((prBisCalc) => {
                        let discounted = 0;
                        if (currentPrice >= prBisCalc.from_amount && currentPrice <= prBisCalc
                            .to_amount) {
                            if (prBisCalc.discount_amount) {
                                if (prBisCalc.percent == 1) {
                                    discounted = currentPrice * parseFloat(parseFloat(prBisCalc
                                            .discount_amount) /
                                        100);
                                } else {
                                    discounted = parseFloat(prBisCalc.discount_amount);
                                }
                                buinessDiscount = discounted;
                            }
                        }
                    });
                }

                subtotal = getPriceString(subtotal);
                let subtotalView =
                    `<div class="w-full h-full text-base leading-8 text-right font-bold">${subtotal}</div>`;
                if (buinessDiscount != 0) {
                    buinessDiscount = getPriceString(buinessDiscount);
                    subtotalView = `<div class="w-full h-full flex flex-col">
                            <span class="w-full h-full text-sm text-right text-slate-500 line-through">${subtotal}</span>
                            <span class="w-full h-full text-base text-right font-bold">${buinessDiscount}</span>
                        </div>
                    `;
                }
                // <div class="w-full h-full text-base leading-8 text-right font-bold">
                //                 ${subtotal}
                //             </div>
                let price = getPriceString(currentPrice);
                let url = `{{ route('details', ':id') }}`;
                url = url.replace(':id', product.id);

                return `
                    <div class="grid grid-cols-2 md:grid-cols-6 gap-2 md:gap-0 place-items-start w-full min-h-[8rem] border-b p-2 md:p-0">
                        <!-- Image -->
                        <div class="w-full h-24 md:h-full p-2 flex overflow-hidden">
                            <a href="${url}" class="w-full h-full flex overflow-hidden">
                                <img src="{{ asset('${product.images[0].image_path}') }}"
                                    alt="${product.title}"
                                    class="w-auto h-auto max-w-full max-h-full mx-auto object-contain">
                            </a>
                        </div>

                        <!-- Details container -->
                        <div class="grid gap-3 w-full md:col-span-5 md:grid-cols-5 md:h-full md:place-items-center">
                            <!-- Title -->
                            <div class="w-full">
                                <a href="${url}" class="text-sm md:text-base line-clamp-2">${product.title}</a>
                            </div>

                            <!-- Price -->
                            <div class="w-full md:justify-self-center">
                                <h2 class="text-sm md:text-base font-normal">${price}</h2>
                            </div>

                            <!-- Quantity -->
                            <div class="w-full md:justify-self-center">
                                <div class="inline-flex rounded-md outline outline-1 outline-[#e5e7eb]">
                                    <span class="dec qty-btn qty-table-btn text-base md:text-lg p-2 md:p-3 leading-4 border-r flex items-center justify-center cursor-pointer select-none">-</span>
                                    <input type="text"
                                        value="${product.count}"
                                        id="qt${product.id}_${product.variantId}_${product.branchId}_${product.orderdetailsId}"
                                        type="number"
                                        class="pro-qty-input bg-slate-100 text-center text-base md:text-lg px-2 w-16 md:w-20 focus-within:outline-0">
                                    <span class="inc qty-btn qty-table-btn text-base md:text-lg p-2 md:p-3 leading-4 border-l flex items-center justify-center cursor-pointer select-none">+</span>
                                </div>
                            </div>

                            <!-- Subtotal -->
                            <div class="w-full md:justify-self-center">
                                ${subtotalView}
                            </div>

                            <!-- Delete button -->
                            <div class="w-full md:justify-self-center flex justify-end md:justify-center">
                                <svg viewBox="0 0 32 32"
                                    version="1.1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    onclick="removeFromCart(${product.id}, ${product.variantId}, ${product.branchId}, ${product.orderdetailsId});"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    fill="currentColor"
                                    class="w-5 h-5 md:w-6 md:h-6 cursor-pointer transition-all duration-200 ease-in-out hover:opacity-60">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                    <g id="SVGRepo_iconCarrier">
                                        <g id="icomoon-ignore"> </g>
                                        <path
                                            d="M26.129 5.871h-5.331v-1.066c0-1.178-0.955-2.132-2.133-2.132h-5.331c-1.178 0-2.133 0.955-2.133 2.132v1.066h-5.331v1.066h1.099l1.067 20.259c0 1.178 0.955 2.133 2.133 2.133h11.729c1.178 0 2.133-0.955 2.133-2.133l1.049-20.259h1.051v-1.066zM12.268 4.804c0-0.588 0.479-1.066 1.066-1.066h5.331c0.588 0 1.066 0.478 1.066 1.066v1.066h-7.464v-1.066zM22.966 27.14l-0.002 0.027v0.028c0 0.587-0.478 1.066-1.066 1.066h-11.729c-0.587 0-1.066-0.479-1.066-1.066v-0.028l-0.001-0.028-1.065-20.203h15.975l-1.046 20.204z"
                                            fill="currentColor"> </path>
                                        <path d="M15.467 9.069h1.066v17.060h-1.066v-17.060z" fill="currentColor"> </path>
                                        <path d="M13.358 26.095l-1.091-17.027-1.064 0.068 1.091 17.027z"
                                            fill="currentColor">
                                        </path>
                                        <path d="M20.805 9.103l-1.064-0.067-1.076 17.060 1.064 0.067z" fill="currentColor">
                                        </path>
                                    </g>
                                </svg>
                            </div>
                        </div>
                    </div>
                `;
            }).join("");

            initQuantity('.qty-table-btn', saveOrder);

            if (prices.coupon != "" && parseInt(prices.couponPrice.slice(3)) > prices.coupon[3])
                document.getElementById("basket2_coupon").innerHTML = `<del>${prices.couponPrice}</del>`;
            else
                document.getElementById("basket2_coupon").innerHTML = "";

            if (prices._subTotal2) {
                document.getElementById("basket2_subtotal").innerHTML = `
                    <span class="line-through gray-2-text inline-block" style="margin-right: 5px;">${prices.subTotal}</span>${prices.subTotal2}
                `;
            } else {
                document.getElementById("basket2_subtotal").innerHTML = `${prices.subTotal}`;
            }
            document.getElementById("basket2_tax").innerHTML = prices._tax2 ? prices.tax2 : prices.tax;
            document.getElementById("market_tax").innerHTML = prices.market_tax + "%";

            // per km
            if (prices.perkm == '1')
                document.getElementById("basket2_fee").innerHTML = prices.fee +
                " per {{ $setting->getKmOrMiles() }}";
            else
                document.getElementById("basket2_fee").innerHTML = prices.fee;

            if (Number(minAmount) >= prices._total && basketItems && basketItems.length > 0) {
                document.getElementById("minamount").hidden = false;
                document.getElementById("minamount-sum").innerHTML = getPriceString(minAmount);
                // document.getElementById("btn-checkout").disabled = true;
            } else {
                document.getElementById("minamount").hidden = true;
                // document.getElementById("btn-checkout").disabled = false;
            }

            if (walletAmount && walletAmount != 0) {
                // Show wallet amount
                loadingIcon('basket_wallet', 1);
                setinnerHtml('#basket2_walletAmount', getPriceString(walletAmount));
            } else {
                // Hide wallet amount
                loadingIcon('basket_wallet', 0);
                setinnerHtml('#basket2_walletAmount', 0);
            }

            if (referralPoints && referralPoints != 0) {
                // Show wallet amount
                loadingIcon('basket_rpoints', 1);
                setinnerHtml('#rpoints', referralPoints);
                setinnerHtml('#basket2_rpointsAmount', getPriceString(referralPointAmount));
            } else {
                // Hide wallet amount
                loadingIcon('basket_rpoints', 0);
                setinnerHtml('#rpoints', 0);
                setinnerHtml('#basket2_rpointsAmount', 0);
            }

            // console.log("couponDiscount", couponDiscount);
            if (couponDiscount != 0) {
                // Show wallet amount
                loadingIcon('basket_coupon', 1);
                setinnerHtml('#basket2_couponDiscount', getPriceString(couponDiscount));
            } else {
                // Hide wallet amount
                loadingIcon('basket_coupon', 0);
                setinnerHtml('#basket2_couponDiscount', 0);
            }

            // console.log("couponMinPurch", couponMinPurch);
            if (couponMinPurch != 0) {
                // Show wallet amount
                loadingIcon('basket_coupon2', 1);
                setinnerHtml('#basket2_couponMinPurchase', couponMinPurch);
            } else {
                // Hide wallet amount
                loadingIcon('basket_coupon2', 0);
                setinnerHtml('#basket2_couponMinPurchase', 0);
            }

            if (prices._total2 != 0) {
                document.getElementById("basket2_total").innerHTML = `
                    <span class="line-through gray-2-text inline-block" style="margin-right: 5px;">${prices.total}</span>${prices.total2}
                `;
            } else {
                document.getElementById("basket2_total").innerHTML = prices.total;
            }

            if (marketDiscount != 0) {
                // Discount amount
                document.getElementById("market_discountAmount").innerHTML = prices.market_discount;
                document.getElementById("basket2_discountAmount").innerHTML = prices.total_discountAmount;
                document.getElementById("market_discount").classList.remove("hidden");
                // Show Discount amount
                loadingIcon("market_discount", 1);
            } else {
                // Discount amount
                // Hide Discount amount
                loadingIcon("market_discount", 0);
                if (document.getElementById("market_discountAmount")) document.getElementById("market_discountAmount")
                    .innerHTML = '';
                document.getElementById("basket2_discountAmount").innerHTML = '';
            }

            if (!prices._total) {
                // document.getElementById("btn-checkout").classList.add("hidden");
            } else {
                // document.getElementById("btn-checkout").classList.remove("hidden");
            }

            // console.log('prices.total2: ', prices);
            if (prices._buinessDiscount) {
                document.getElementById("basket2_investmentDiscountAmount").innerHTML = `
                        ${prices.buinessDiscount}
                    `;
                // Show
                loadingIcon("investment_discount", 1);
            } else {
                document.getElementById("basket2_investmentDiscountAmount").innerHTML = '';
                // Show
                loadingIcon("investment_discount", 0);
            }

            // Turn off loading cart
            loadingIcon("cart-info--loading", 0);
            loadingIcon("cart-infoResponsive--loading", 0);
            loadingIcon("cart-total--loading", 0);

            // return;
            // selectDeliveryMethod();
        }
    @endif

    // redraw cart
    function redrawCart() {
        initBasket();
        initializeSidebarCarts();

        // Cart page
        @if (Auth::check() && Request::route()->getName() == 'cart')
            // Show loading cart
            // To show that orders are updating
            loadingIcon("cart-total--loading", 1);
            loadingIcon("cart-info--loading", 1);
            loadingIcon("cart-infoResponsive--loading", 1);

            initTableCart();
        @endif
    }

    // removes item from cart
    function removeFromCart(id, variant_id, branch_id, orderdetailsId) {
        // Show loading cart table
        loadingIcon("cart-total--loading", 1);
        loadingIcon("cart-info--loading", 1);
        loadingIcon("cart-infoResponsive--loading", 1);
        // Show loading sidebar cart
        loadingIcon("sidebar-cart-btn--loading", 1);
        disableBtn("sidebar-cart-btn", 1);

        for (let i = basketItems.length; i--;) {
            if (basketItems[i].id == id &&
                basketItems[i].branchId == branch_id &&
                basketItems[i].variantId == variant_id &&
                basketItems[i].orderdetailsId == orderdetailsId
            ) {
                basketItems.splice(i, 1);
            }
        }
        localStorage.setItem('items', JSON.stringify(basketItems));
        let data = {
            productid: id,
            variantid: variant_id,
            branchid: branch_id
        };
        @if (Auth::user())
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                type: 'POST',
                url: '{{ route('removeFromCart') }}',
                data: data,
                success: function(data) {
                    // console.log(data);
                    redrawCart();
                },
                error: function(e) {
                    console.log(e);
                }
            });
        @else
            redrawCart();
        @endif
    }

    let walletAmount = 0;
    let referralPoints = 0;
    let referralPointAmount = 0;
    let couponName = "";
    let couponDiscount = 0;
    let couponMinPurch = 0;
    // saves order to db
    function saveOrder(callBack) {
        return new Promise((resolve, reject) => {
            @if (Auth::check())
                let data = {
                    send: "0",
                    tax: 0,
                    fee: 0,
                    method: "",
                    hint: "",
                    address: "",
                    phone: "",
                    total: "0",
                    lat: 0,
                    lng: 0,
                    couponName: couponName,
                    curbsidePickup: "true",
                    data: basketItems,
                    walletAmount: walletAmount,
                    referralPoints: referralPoints,
                };
                loadingIcon('update-cart--loading', 1);
                $.ajax({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    type: 'POST',
                    url: '{{ route('addToCart') }}',
                    data: data,
                    success: function(data) {
                        // console.log("add to basket reponse: ", data);
                        loadingIcon('update-cart--loading', 0);
                        // wallet
                        loadingIcon('apply-wallet--loading', 0);
                        disableBtn('apply-wallet', 0);
                        disableBtn('wallet_amount', 0);
                        // referral
                        loadingIcon('apply-referral_point--loading', 0);
                        disableBtn('apply-referral_point', 0);
                        disableBtn('referral_point', 0);
                        if (data.error == true) {
                            return Toast.fire({
                                icon: "error",
                                title: data.msg
                            });
                        }
                        if (data.error == '2') {
                            return Toast.fire({
                                icon: "error",
                                title: data.text
                            });
                        }
                        basketItems = data.data;
                        localStorage.setItem('items', JSON.stringify(basketItems));
                        localStorage.setItem('order_id', data.orderid);
                        updateBasketOrderDetailsId();
                        if (callBack == "loadBasketFromServer") loadCartFromServer();

                        @if (Request::route()->getName() == 'cart' && Auth::check())
                            // Wallet
                            if (data.walletRemainingAmount != undefined) {
                                setinnerHtml('#wallet_balance', data.walletRemainingAmount);
                                let setVal = document.getElementById("wallet_amount");
                                setVal.value = data.walletAmount;
                            }

                            // Referral
                            if (data.referralPoint != undefined) {
                                referralPointAmount = data.referralPointAmount;
                                setinnerHtml('#referral_points', data.referralPointsRemaining ? data
                                    .referralPointsRemaining : 0);
                                let setVal = document.getElementById("referral_point");
                                setVal.value = data.referralPoint;
                            }

                            // coupon
                            if (data.coupon != undefined) {
                                // setinnerHtml('#coupon_text', 'Coupon Found');
                                // setinnerHtml('#mincoupon_amount', 'Minimum order amount must be greater than ' +
                                //     data
                                //     .couponMinAmount2 + ' for the coupon to work');
                                loadingIcon('coupon_text', 1);
                                // loadingIcon('mincoupon_amount', 1);
                                couponDiscount = data.couponDiscount;
                                if (data.couponMinAmount) {
                                    couponMinPurch = data.couponMinAmount2;
                                } else {
                                    couponMinPurch = 0;
                                }
                            } else {
                                // setinnerHtml('#coupon_text', '');
                                // setinnerHtml('#mincoupon_amount', '');
                                loadingIcon('coupon_text', 0);
                                // loadingIcon('mincoupon_amount', 0);
                                couponDiscount = 0;
                                couponMinPurch = 0;
                            }
                        @endif
                        resolve(data); // Resolve the promise when successful
                    },
                    error: function(e) {
                        loadingIcon('update-cart--loading', 0);
                        console.log(e);
                        reject(e); // Reject the promise on error
                    }
                });
            @else
                resolve(); // Resolve immediately if not authenticated
            @endif
        });
    }

    let selectedBranch = 0;

    function addToCart(id, branch_id, variant_id, count, cat) {
        // Initial validation
        let err = document.querySelector('.select-selected');
        if (id == 0 || branch_id == 0) {
            if (err) err.style.border = "1px solid var(--error-color)";
            return Toast.fire({
                icon: "error",
                title: "Please choose address."
            });
        }
        if (err && err.style.border) err.style.border = "";

        let branchId = document.getElementById(`product-${id}-address`);
        // Parse inputs once
        branch_id = parseInt(branch_id);
        variant_id = variant_id ? parseInt(variant_id) : null;
        temp_variant_id = variant_id;

        // Get count from appropriate source
        countToBasket = document.getElementById('{{ Request::route()->getName() }}' === 'details' ?
            "count" :
            "product_details_count"
        )?.value || 1;

        // First, check if item exists
        let itemExists = false;
        let existingItem = null;

        // update cart item count or changes item branch
        for (let i = basketItems.length; i--;) {
            if (basketItems[i].id == id &&
                basketItems[i].variantId == variant_id &&
                basketItems[i].branchId == branch_id
            ) {
                itemExists = true;
                existingItem = basketItems[i];

                // Validate quantity limits
                const newCount = parseInt(existingItem.count) + parseInt(countToBasket);

                if (newCount < existingItem.minquantity) {
                    return Toast.fire({
                        icon: "error",
                        title: `Minimum purchase count is ${existingItem.minquantity}.`
                    });
                }
                if (newCount > existingItem.maxquantity) {
                    return Toast.fire({
                        icon: "error",
                        title: "Maximum purchase limit reached!"
                    });
                }
                if (newCount > existingItem.quantity) {
                    return Toast.fire({
                        icon: "error",
                        title: "Cart item count limit reached."
                    });
                }

                // Update count if all validations pass
                existingItem.count = newCount;
                localStorage.setItem('items', JSON.stringify(basketItems));

                return saveOrder().then(() => {
                    if (!{{ Auth::check() ? 'true' : 'false' }}) {
                        redrawCart();
                    }
                    Toast.fire({
                        icon: "success",
                        title: `Cart item incremented by ${countToBasket}.`
                    });
                });
            }
        }

        // Prepare URL params once
        const params = {
            id,
            branch_id,
            variant_id: variant_id ?? 0,
            ...(cat && {
                cat
            })
        };

        let urlParam = new URLSearchParams(params).toString();
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            type: 'GET',
            url: `{{ route('product.info') }}?${urlParam}`,
            success: function(data) {
                // console.log("product data: ", data);
                if (!data?.data) {
                    return Toast.fire({
                        icon: "error",
                        title: data?.text || "Something went wrong"
                    });
                }

                const branchProduct = data.data.branch_products[0];

                if (countToBasket < branchProduct.minquantity && branchProduct
                    .minquantity != 0) {
                    countToBasket = 1;
                    return Toast.fire({
                        icon: "error",
                        title: `Minimum purchase property count is ${branchProduct.minquantity}.`
                    });
                }

                if (countToBasket > branchProduct.maxquantity && branchProduct
                    .maxquantity != 0) {
                    countToBasket = 1;
                    return Toast.fire({
                        icon: "error",
                        title: "Maximum purchase limit exceeded!"
                    });
                }

                if (countToBasket > branchProduct.quantity) {
                    return Toast.fire({
                        icon: "error",
                        title: `Almost There!\nWe're sorry, but we don't have enough availability for your current selection.\nYou can still purchase this property by reducing the quantity.\nPlease adjust the count and add again!`
                    });
                }

                if (countToBasket > branchProduct.quantity) {
                    countToBasket = branchProduct.quantity;
                }

                // save to local basket
                let item = {
                    id: data.data.id,
                    variantId: variant_id,
                    branchId: branchProduct.branch_id,
                    branchName: data.data.branch_name,
                    businessCalc: data.data.business_calculator,
                    title: data.data.name,
                    images: data.data.images,
                    price: data.data.price,
                    discountPrice: data.data.discount_price,
                    price2: data.data.price2,
                    discountPrice2: data.data.discount_price2,
                    count: countToBasket,
                    quantity: branchProduct.quantity,
                    orderdetailsId: null
                };

                // MAKE DEEP COPY OF OBJECT (THERE ARE OTHER WAYS TO DO THIS ALSO)
                // THIS IS BECAUSE IT WAS UPDATING EXISTING OBJECT VALUES AND MAKING A DEEP COPY FIXED THE ISSUE
                const newItem = JSON.parse(JSON.stringify(item));
                basketItems.push(newItem);
                localStorage.setItem('items', JSON.stringify(basketItems));

                saveOrder();
                @if (!Auth::check())
                    redrawCart();
                    let orderUpdate = parseInt(localStorage.getItem("orderUpdate")) !== 1;
                    // order status update
                    if (orderUpdate) localStorage.setItem("orderUpdate", 1);
                @endif
                countToBasket = 1;
                return Toast.fire({
                    icon: "success",
                    title: "Added to cart"
                });
            },
            error: function(e) {
                console.log(e);
            }
        });
    }

    // for pagination
    function buildPagination(page, pages) {
        page = parseInt(page);
        pages = parseInt(pages);
        let numberPages = 5;
        pageNumbers = '';
        let maxLeft = Math.ceil(page - (numberPages / 2));
        let maxRight = Math.ceil(page + (numberPages / 2));
        if (maxLeft < 1) {
            maxLeft = 1;
            maxRight = numberPages;
        }
        if (maxRight > pages) {
            maxLeft = pages - (numberPages - 1);
            maxRight = pages;
            if (maxLeft < 1) {
                maxLeft = 1;
            }
        }
        for (let i = maxLeft; i <= maxRight; i++) {
            if (i == page) {
                pageNumbers += `
                    <li value="${i}" class="page page-item active">
                        <span class="page-link">${i} <span class="sr-only">(current)</span></span>
                    </li>
                `;
            } else {
                pageNumbers += `<li value="${i}" class="page page-item"><span class="page-link">${i}</span></li>`;
            }
        }
        if (maxLeft >= parseInt(numberPages / 2)) {
            pageNumbers = `
            <li value="1" class="page page-item">
                <span class="page-link" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                    <span class="sr-only">Previous</span>
                </span>
            </li>` + pageNumbers;
        }
        if (maxRight != pages) {
            pageNumbers += `
            <li value="${pages}" class="page page-item">
                <span class="page-link" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                    <span class="sr-only">Next</span>
                </span>
            </li>`;
        }
        return pageNumbers;
    }

    function initializePaginations(callback) {
        $('.page').on('click', function() {
            let newPage = parseInt($(this).attr('value'));
            // buildChatUsers(newPage);
            callback(newPage);
        });
    }

    // Are you sure? Alert
    function showMessageAlert(id, name, callBackFun) {
        Swal.fire({
            title: "Are you sure?",
            text: `Removing address '${name}'.`,
            icon: "warning",
            confirmButtonColor: "#DD6B55",
            showCancelButton: true,
            confirmButtonText: "Yes, delete it!",
            showCloseButton: true,
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                callBackFun(id);
            } else {
                // Swal.fire("Cancelled", "", "info");
            }
        });
    }

    let basketItems = JSON.parse(localStorage.getItem("items")) || [];

    // Wishlist
    let wishItems = JSON.parse(localStorage.getItem("wishItems")) || [];

    $(document).ready(function() {
        @if (Request::route()->getName() != 'customer.login' && Request::route()->getName() != 'customer.register')
            // Hide footer
            loadingIcon("footer", 1);
        @endif

        // if the route is different from complete or completeStripe page
        // it loads items from db to bakset then updates the order if items are different
        // && Request::route()->getName() != 'cart'
        @if (Request::route()->getName() != 'complete' && Auth::check())
            loadCartFromServer();
        @else
            // Add to show cart on page initial
            redrawCart();
        @endif


        let coverBg = document.getElementById('sidebar-cover');
        $(coverBg).on('click', function(e) {
            toggleLeftSideBar();
        });
    });

    function toggleLeftSideBar() {
        let coverBg = document.getElementById('sidebar-cover');
        let mainBody = document.getElementById('main');
        let toggleBtn = document.getElementById('toggle-sidebar');
        if (!mainBody.style.marginLeft) {
            // setTimeout(function() {
            //     // Show
            //     // loadingIcon('toggle-sidebar', 1);
            // }, 299)
            mainBody.style.marginLeft = '-300px';
            mainBody.style.marginRight = '';
            // Show
            // if (toggleBtn) toggleBtn.style.visibility = 'visible';
            document.getElementsByTagName("BODY")[0].style.overflow = "";
            document.getElementById("main-body").style.overflow = "";
            coverBg.classList.remove("active-cover");
        } else {
            // Hide
            // if (toggleBtn) toggleBtn.style.visibility = 'hidden';
            // loadingIcon('toggle-sidebar', 0);
            mainBody.style.marginLeft = '';
            mainBody.style.marginRight = '-300px';
            document.getElementsByTagName("BODY")[0].style.overflow = "hidden";
            document.getElementById("main-body").style.overflow = "hidden";
            coverBg.classList.add("active-cover");
        }
    }

    @isset($referalCode)
        @if ($referalCode)
            let referal = localStorage.getItem('referalCode');
            if (!referal) {
                localStorage.setItem('referalCode', '{{ $referalCode }}');
            }
        @endif
    @endisset

    // Search
    let search = "";
    let oldSearch = "";
    @if (isset($search))
        search = "{{ $search }}";
        oldSearch = "{{ $search }}";
    @endif

    function errorNotify(data) {
        if (data.error) {
            let msg = data.msg;
            if (typeof msg == 'string') {
                Toast.fire({
                    icon: "error",
                    title: data.title ? data.title : 'Error',
                    text: msg
                });
            }
            if (typeof msg == 'object') {
                let keys = Object.keys(msg);
                for (let i = 0; i < keys.length; i++) {
                    Toast.fire({
                        icon: "error",
                        title: 'Error',
                        text: msg[keys[i]]
                    });
                }
            }
        } else {
            Toast.fire({
                icon: "error",
                title: 'Error',
                text: 'Something went wrong'
            });
        }
    }

    // Destroy slick slider
    function destorySlick(id1, id2) {
        if (id1)
            $(`${id1}`).slick('unslick');
        if (id2)
            $(`${id2}`).slick('unslick');
    }

    // Product images slider
    function activateImageSlider(elems) {
        let productImagesDiv = document.querySelectorAll(elems);
        productImagesDiv.forEach((div) => {
            $(div).slick({
                slidesToShow: 1,
                slidesToScroll: 1,
                arrows: true,
                fade: false,
                infinite: false,
                dots: true,
                prevArrow: `<div class="slick-button slick-prev cursor-pointer">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M14.2893 5.70708C13.8988 5.31655 13.2657 5.31655 12.8751 5.70708L7.98768 10.5993C7.20729 11.3805 7.2076 12.6463 7.98837 13.427L12.8787 18.3174C13.2693 18.7079 13.9024 18.7079 14.293 18.3174C14.6835 17.9269 14.6835 17.2937 14.293 16.9032L10.1073 12.7175C9.71678 12.327 9.71678 11.6939 10.1073 11.3033L14.2893 7.12129C14.6799 6.73077 14.6799 6.0976 14.2893 5.70708Z"
                                fill="#000000"></path>
                        </g>
                    </svg>
                </div>`,
                nextArrow: `<div class="slick-button slick-next cursor-pointer">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8">
                        <g stroke-width="0"></g>
                        <g stroke-linecap="round" stroke-linejoin="round"></g>
                        <g>
                            <path
                                d="M9.71069 18.2929C10.1012 18.6834 10.7344 18.6834 11.1249 18.2929L16.0123 13.4006C16.7927 12.6195 16.7924 11.3537 16.0117 10.5729L11.1213 5.68254C10.7308 5.29202 10.0976 5.29202 9.70708 5.68254C9.31655 6.07307 9.31655 6.70623 9.70708 7.09676L13.8927 11.2824C14.2833 11.6729 14.2833 12.3061 13.8927 12.6966L9.71069 16.8787C9.32016 17.2692 9.32016 17.9023 9.71069 18.2929Z"
                                fill="#000000"></path>
                        </g>
                    </svg>
                </div>`,
            });
        });
    }

    // Format numbers
    function formatNumber(num) {
        if (num >= 1e9) {
            return (num / 1e9).toFixed(1) + 'B'; // For billions
        } else if (num >= 1e6) {
            return (num / 1e6).toFixed(1) + 'M'; // For millions
        } else {
            return num.toString(); // For numbers less than a million
        }
    }

    // Creates an array with given length with zeros filled
    const createArray = (length = 0) => {
        if (length == 0) {
            return [0];
        }

        // Create a dynamic array filled with zeros
        let dynamicArray = new Array(length).fill(0);
        return dynamicArray;
    };

    // Function to check if an index is present
    const isIndexPresent = (arr, index) => {
        return index >= 0 && index < arr.length;
    }
</script>
