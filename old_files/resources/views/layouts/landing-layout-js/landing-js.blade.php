<!-- All pages -->
<script>
    // For mouse horizontall scroll
    const tableSlider = document.querySelector('.table-responsive');
    let mouseDown = false;
    let startX, scrollLeft;

    let startDragging = function(e) {
        mouseDown = true;
        startX = e.pageX - tableSlider.offsetLeft;
        scrollLeft = tableSlider.scrollLeft;
    };
    let stopDragging = function(event) {
        mouseDown = false;
    };

    if (tableSlider != null) {
        tableSlider.addEventListener('mousemove', (e) => {
            e.preventDefault();
            if (!mouseDown) {
                return;
            }
            const x = e.pageX - tableSlider.offsetLeft;
            const scroll = x - startX;
            tableSlider.scrollLeft = scrollLeft - scroll;
        });
        // Add the event listeners
        tableSlider.addEventListener('mousedown', startDragging, false);
        tableSlider.addEventListener('mouseup', stopDragging, false);
        tableSlider.addEventListener('mouseleave', stopDragging, false);
    }

    // changes branch for the item
    function branchSelect(cartPage, branch_id, product_id, variant_id, mapView) {
        selectedBranch = branch_id;

        if (cartPage && selectedBranch) {
            for (var i = basketItems.length; i--;) {
                // console.log("parseInt(basketItems[i].id): ", parseInt(basketItems[i].id));
                // console.log("parseInt(basketItems[i].variantId): ", parseInt(basketItems[i].variantId));
                // console.log("parseInt(basketItems[i].branchId): ", parseInt(basketItems[i].branchId));
                if (
                    parseInt(basketItems[i].id) == parseInt(product_id) &&
                    parseInt(basketItems[i].variantId) == parseInt(variant_id) &&
                    parseInt(basketItems[i].branchId) == parseInt(branch_id)
                ) {
                    let branchName = basketItems[i].branch_name;
                    let title = basketItems[i].title;
                    return Toast.fire({
                        icon: "error",
                        title: title + " is already in cart by branch " + branchName,
                    });
                }
            }
        }

        let sendData;
        let variantId = 0;
        let url = '{{ route('products.branch.check') }}';
        if (variant_id) {
            variantId = variant_id;
            sendData = {
                branch_id: branch_id,
                product_id: product_id,
                variant_id: variant_id
            }
        } else {
            sendData = {
                branch_id: branch_id,
                product_id: product_id
            }
        }
        loadingIcon(`add-to-cart-button${sendData.product_id}--loading`, 1);
        disableBtn(`add-to-cart-button${sendData.product_id}`, 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            type: 'POST',
            url: url,
            data: sendData,
            success: function(data) {
                // console.log("products branch check res: ", data);
                loadingIcon(`add-to-cart-button${sendData.product_id}--loading`, 0);
                disableBtn(`add-to-cart-button${sendData.product_id}`, 0);
                if (data.error != '0') {
                    return Toast.fire({
                        icon: "error",
                        title: "Error",
                        text: 'Something went wrong'
                    });
                }
                // productBranchId = branch_id;
                // var branch_lists = document.getElementById('branches').children;
                // for (var i = 0; i < branch_lists.length; i++) {
                //     if (`branch_id_${branch_id}` == branch_lists[i].getAttribute("id")) {
                //         // branch_lists[i].classList.add("theme-main-bgcolor");
                //         branch_lists[i].className +=
                //             " theme-main-bgcolor theme-main-bordercolor color-white";
                //     } else {
                //         // branch_lists[i].classList.remove("theme-main-bgcolor");
                //         branch_lists[i].className = branch_lists[i].className.replace(
                //             /(?:^|\s)theme-main-bgcolor theme-main-bordercolor color-white(?!\S)/g, '');
                //     }
                // }
                // changes add basket button respective to it's item
                // for variant too
                selectedBranch = branch_id;
                // if not on cart page proceed
                if (!cartPage) {
                    // Defalut
                    let addtobasketButton = document.getElementById(`add-to-cart-button${product_id}`);
                    let addtowishlistButton = document.getElementById(
                        `wishlist-${product_id}-${variantId}`);
                    let addtowishlistButtonM = document.getElementById(
                        `wishlistM-${product_id}-${variantId}`);

                    // On details page
                    let addCartBtn = document.getElementById('add-to-cart-button');
                    let addWishlistBtn = document.getElementById('add-to-wishlist-button');

                    if (variant_id) {
                        if (addCartBtn) {
                            addCartBtn.setAttribute("onclick",
                                `addToCart(${product_id}, ${branch_id}, ${variant_id}, ${1},${data.data.product.soon_instock == 1 ? 3 : 0});`);
                            if (addWishlistBtn) addWishlistBtn.setAttribute("onclick",
                                `addToWishList(${product_id}, ${variant_id}, ${branch_id});`);
                        }

                        if (addtobasketButton) {
                            addtobasketButton.setAttribute("onclick",
                                `addToCart(${product_id}, ${branch_id}, ${variant_id}, ${1},${data.data.product.soon_instock == 1 ? 3 : 0});`);
                        }
                        if (addtowishlistButton) addtowishlistButton.setAttribute("onclick",
                            `addToWishList(${product_id}, ${variant_id}, ${branch_id});`);

                        if (addtowishlistButtonM) addtowishlistButtonM.setAttribute("onclick",
                            `addToWishList(${product_id}, ${variant_id}, ${branch_id});`);
                    } else {
                        if (addCartBtn) {
                            addCartBtn.setAttribute("onclick",
                                `addToCart(${product_id}, ${branch_id}, ${0}, ${1},${data.data.product.soon_instock == 1 ? 3 : 0});`);
                            if (addWishlistBtn) addWishlistBtn.setAttribute("onclick",
                                `addToWishList(${product_id}, ${0}, ${branch_id});`);
                        }

                        if (addtobasketButton) {
                            addtobasketButton.setAttribute("onclick",
                                `addToCart(${product_id}, ${branch_id}, ${0}, ${1},${data.data.product.soon_instock == 1 ? 3 : 0});`);
                                // data.data.product.soon_instock == 1 ||
                            if ((data.data.product.soon_instock == 0) && data.data.branch_products.quantity < data.data.branch_products.minquantity) {
                                addtobasketButton.disabled = true;
                            }
                        }
                        if (addtowishlistButton) addtowishlistButton.setAttribute("onclick",
                            `addToWishList(${product_id}, ${0}, ${branch_id});`);
                        if (addtowishlistButtonM) addtowishlistButtonM.setAttribute("onclick",
                            `addToWishList(${product_id}, ${0}, ${branch_id});`);
                    }

                    // console.log("check availability: ", data.data.product, data.data.product.soon_instock);
                    let branches = document.getElementById('modal2-content__branch');
                    if (branches) branches.style.display = 'block';
                    let countBtn = document.querySelector('.pro-qty');
                    if (countBtn) countBtn.style.display = '';
                    if (data.data.product.soon_instock == 0 && data.data.branch_products.quantity >= data
                        .data.branch_products.minquantity) {
                        productAvailability(1, product_id);
                        let def = document.getElementById("item_count");
                        if (def) def.innerHTML = data.data.branch_products.quantity;

                        let dCount = document.getElementById("item-count");
                        if (dCount) dCount.innerHTML = data.data.branch_products.quantity;

                        setinnerHtml('#min_item_count', data.data.branch_products.minquantity);
                        setinnerHtml('#max_item_count', data.data.branch_products.maxquantity);
                    } else if (data.data.product.soon_instock == 1) {
                        productAvailability(3, product_id);

                        let def = document.getElementById("item_count");
                        if (def) def.innerHTML = data.data.branch_products.quantity;

                        let dCount = document.getElementById("item-count");
                        if (dCount) dCount.innerHTML = data.data.branch_products.quantity;

                        setinnerHtml('#min_item_count', data.data.branch_products.minquantity);
                        setinnerHtml('#max_item_count', data.data.branch_products.maxquantity);
                    } else {
                        let branches = document.getElementById('modal2-content__branch');
                        if (branches) branches.style.display = 'none';
                        let countBtn = document.querySelector('.pro-qty');
                        if (countBtn) countBtn.style.display = 'none';
                        productAvailability(2, product_id);
                    }
                }
                if (mapView == 1) {
                    showPosition(data.data);
                }
            },
            error: function(e) {
                loadingIcon(`add-to-cart-button${sendData.product_id}--loading`, 0);
                disableBtn(`add-to-cart-button${sendData.product_id}`, 0);
                console.log(e);
            }
        });
    }

    function productAvailability(status, id) {
        // status == 1 is in stock | Available
        // status == 2 is out of stock
        // status == 3 is soon to be in stock
        let stat = document.getElementById(`product-availability-${id}`);
        if (stat) {
            // Available
            if (status == 1) {
                stat.innerHTML = `<span class='success ml-1 flex items-start font-bold'>Available
                    <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'
                        stroke-width='1.5' stroke='currentColor' class='w-[20px] h-[20px] ml-1'>
                        <path stroke-linecap='round' stroke-linejoin='round'
                                d='M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z' />
                        </svg>
                    </span>
                `;
            }
            // Soon to be in stock
            else if (status == 3) {
                stat.innerHTML = `<span class='coming ml-1 flex items-center font-bold'>Secondary Market
                        <svg fill="currentColor" viewBox="0 0 24 24" id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg"
                            class="w-[20px] h-[20px] ml-1">
                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                            <g id="SVGRepo_iconCarrier">
                                <path
                                    d="M24,12A12,12,0,0,1,0,12a1,1,0,0,1,2,0A10,10,0,1,0,12,2a1,1,0,0,1,0-2A12.013,12.013,0,0,1,24,12ZM10.277,11H8a1,1,0,0,0,0,2h2.277A1.994,1.994,0,1,0,13,10.277V7a1,1,0,0,0-2,0v3.277A2,2,0,0,0,10.277,11ZM1.827,8.784a1,1,0,1,0-1-1A1,1,0,0,0,1.827,8.784ZM4.221,5.207a1,1,0,1,0-1-1A1,1,0,0,0,4.221,5.207ZM7.779,2.841a1,1,0,1,0-1-1A1,1,0,0,0,7.779,2.841Z">
                                </path>
                            </g>
                        </svg>
                    </span>`;
            }
            // Out of stock (status == 2)
            else {
                stat.innerHTML = `<span class='error ml-1 flex items-start font-bold'>Not Available
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-[20px] h-[20px] ml-1">
                        <g stroke-width="0"></g>
                        <g stroke-linecap="round" stroke-linejoin="round"></g>
                        <g>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M18.364 5.63604C21.8787 9.15076 21.8787 14.8492 18.364 18.364C14.8492 21.8787 9.15076 21.8787 5.63604 18.364C2.12132 14.8492 2.12132 9.15076 5.63604 5.63604C9.15076 2.12132 14.8492 2.12132 18.364 5.63604ZM16.1925 17.6067L6.39327 7.80749C4.33767 10.5493 4.55666 14.4562 7.05025 16.9497C9.54384 19.4433 13.4507 19.6623 16.1925 17.6067ZM16.9497 7.05025C19.4433 9.54384 19.6623 13.4507 17.6067 16.1925L7.80749 6.39327C10.5493 4.33767 14.4562 4.55666 16.9497 7.05025Z"
                                fill="currentColor"></path>
                        </g>
                    </svg>
                    </span>`;
            }
        }

        let dStat = document.getElementById("product-availability");
        if (dStat) {
            // Available
            if (status == 1) {
                dStat.innerHTML = `<span class="success inline-flex items-center leading-[normal] gap-2 text-base font-bold capitalize">
                    Availability
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                        class="w-6 h-6 inline-block">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"></path>
                        </g>
                    </svg>
                </span>
                `;
            } else if (status == 3) {
                dStat.innerHTML = `<span class="coming inline-flex items-center leading-[normal] gap-2 text-base font-bold capitalize">
                    Secondary Market
                    <svg fill="currentColor" viewBox="0 0 24 24" id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg"
                        class="w-[20px] h-[20px] ml-1">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M24,12A12,12,0,0,1,0,12a1,1,0,0,1,2,0A10,10,0,1,0,12,2a1,1,0,0,1,0-2A12.013,12.013,0,0,1,24,12ZM10.277,11H8a1,1,0,0,0,0,2h2.277A1.994,1.994,0,1,0,13,10.277V7a1,1,0,0,0-2,0v3.277A2,2,0,0,0,10.277,11ZM1.827,8.784a1,1,0,1,0-1-1A1,1,0,0,0,1.827,8.784ZM4.221,5.207a1,1,0,1,0-1-1A1,1,0,0,0,4.221,5.207ZM7.779,2.841a1,1,0,1,0-1-1A1,1,0,0,0,7.779,2.841Z">
                            </path>
                        </g>
                    </svg>
                </span>
                `;
            }
            // Out of stock (!status)
            else {
                dStat.innerHTML = `<span class="error inline-flex items-center leading-[normal] gap-2 text-base font-bold capitalize">
                    Not Available
                    <svg fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                        class="w-6 h-6 inline-block">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M4.293,18.293,10.586,12,4.293,5.707A1,1,0,0,1,5.707,4.293L12,10.586l6.293-6.293a1,1,0,1,1,1.414,1.414L13.414,12l6.293,6.293a1,1,0,1,1-1.414,1.414L12,13.414,5.707,19.707a1,1,0,0,1-1.414-1.414Z">
                            </path>
                        </g>
                    </svg>
                `;
            }
        }
    }

    function onSearch(searchVal) {
        isSearch = true;
        search = searchVal ? searchVal : '';
        if ("{{ Request::route()->getName() }}" == "search") {
            if (document.getElementById("search") != null) {
                document.getElementById("search").value = search;
            }
            if (document.getElementById("search-input") != null) {
                document.getElementById("search-input").innerHTML = search;
            }
            if (document.getElementById("search-input2") != null) {
                document.getElementById("search-input2").innerHTML = search;
            }
            if (document.getElementById("mobile-view-search") != null) {
                document.getElementById("mobile-view-search").value = search;
            }
            if (document.getElementById("search-input-field") != null) {
                document.getElementById("search-input-field").value = search;
            }
            if (document.getElementById("mobile-search-input-field") != null) {
                document.getElementById("mobile-search-input-field").value = search;
            }
            paginationGoPage(1);
        } else {
            window.location = '{{ route('search') }}?search=' + search;
        }
    }

    // Search
    let input = document.getElementById("search");
    // let mobileInput = document.getElementById("mobile-view-search");
    input.addEventListener("keyup", function(event) {
        if (event.keyCode === 13) {
            event.preventDefault();
            onSearch(event.target.value);
        }
    });

    let mobileInput = document.getElementById("mobile-view-search");
    // input.addEventListener("keyup", function(event) {
    //     if (event.keyCode === 13) {
    //         event.preventDefault();
    //         onSearch(event.target.value);
    //     }
    // });

    mobileInput.addEventListener("keyup", function(event) {
        if (event.keyCode === 13) {
            event.preventDefault();
            onSearch(event.target.value);
        }
    });

    let isSearch = false;

    // Menu toggle
    // $(document).ready(function() {
    //     //jquery for toggle sub menus
    //     $('.menu-toggle').click(function() {
    //         $(this).next('.ml-menu').slideToggle();
    //         $(this).find('.dropdown').toggleClass('rotate');
    //     });

    //     var dropdown = document.getElementsByClassName("menu-toggle");
    //     var i;
    //     for (i = 0; i < dropdown.length; i++) {
    //         var currentLocation = location.href;
    //         if (dropdown[i].nextElementSibling.getElementsByClassName('q-menu-active')[0] != null) {
    //             var dropdownLocation = dropdown[i].nextElementSibling.getElementsByClassName('q-menu-active')[0]
    //                 .getElementsByClassName('menu-text')[0].href;
    //             $(this).find('.q-menu-active .dropdown').addClass('rotate');
    //             if (currentLocation == dropdownLocation) {
    //                 dropdown[i].nextElementSibling.style.display = "block";
    //             }
    //         }
    //     }
    // });

    window.onclick = function(event) {
        // When the user clicks anywhere outside of the modal, close it
        if (event.target == modal) {
            // Hide modal
            hideModal();
        }

        // When the user clicks anywhere outside of the modal, close it
        if (event.target == videoModal) {
            // Hide modal
            hideVideoModal();
        }

        // Close the dropdown if the user clicks outside of it
        if (menuList) {
            if (!menuList.contains(event.target) && !dropdown.contains(event.target)) {
                if (!menuList.classList.contains('hidden')) {
                    menuList.classList.add('hidden');
                    dropdown.classList.remove("bg-[#E9FBF5]");
                }
            }
        }
    }

    // sidebar toggle buttons
    let barMenuIcons = document.querySelectorAll('.toggle-sidebar');
    barMenuIcons.forEach((div) => {
        div.addEventListener('click', toggleLeftSideBar);
    });

    // sell product
    let sellProductBtn = document.querySelectorAll(".sell-product, #sell-product-2");
    // console.log('sellProductBtn: ', sellProductBtn);
    sellProductBtn.forEach((el) => {
        // console.log('el: ', el);
        el.addEventListener('click', function() {
            if (window.location.pathname != '{{ route('requestProduct') }}') {
                window.location.href = '{{ route('requestProduct') }}';
            } else {
                $('div[id="#account-info"]').trigger('click');
                $('#pr_desc').focus();
            }
        });
    });
</script>
