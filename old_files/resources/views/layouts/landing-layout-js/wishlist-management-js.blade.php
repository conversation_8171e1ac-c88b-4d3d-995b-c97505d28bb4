<script>
    // Update the wishlist counters
    function updateWishlistCount() {
        let wishlistItemsCount = wishItems.length;
        // document.getElementById("wishlistCount").hidden = true;
        if (wishlistItemsCount === 0) {
            loadingIcon("wishlist-count-d", 0);
            loadingIcon("wishlist-count-m", 0);
        } else {
            setinnerHtml("#wishlist-count-d", wishlistItemsCount);
            setinnerHtml("#wishlist-count-m", wishlistItemsCount);
            loadingIcon("wishlist-count-d", 1);
            loadingIcon("wishlist-count-m", 1);
        }
    }

    // adds active to product wishlist icon
    function addWishlistActive(item = null) {
        let itemDView;
        let itemMView;
        // console.log('length: ', item, wishItems.length);
        if (item) {
            itemDView = document.getElementById(`wishlist-${item.id}`);
            itemMView = document.getElementById(`wishlistM-${item.id}`);
            // console.log('itemDView: ', itemDView);
            // console.log('itemMView: ', itemMView);
            if (itemDView) {
                if (itemDView.classList.contains("text-red-500", "scale-125")) {
                    itemDView.classList.remove("text-red-500", "scale-125");
                    itemDView.classList.add("dark-color");
                } else {
                    itemDView.classList.remove("dark-color");
                    itemDView.classList.add("text-red-500", "scale-125");
                }
            }
            if (itemMView) {
                if (itemMView.classList.contains("theme-main-color")) {
                    itemMView.classList.remove("theme-main-color");
                    itemMView.classList.add("group-theme-main-hovercolor");
                } else {
                    itemMView.classList.remove("group-theme-main-hovercolor");
                    itemMView.classList.add("theme-main-color");
                }
            }
        } else if (wishItems.length > 0) {
            wishItems.map((product, i) => {
                itemDView = document.getElementById(`wishlist-${product.id}`);
                itemMView = document.getElementById(`wishlistM-${product.id}`);
                // console.log('element: ', itemDView);
                if (itemDView) {
                    itemDView.classList.remove("dark-color");
                    itemDView.classList.add("text-red-500", "scale-125");
                }
                if (itemMView) {
                    if (itemMView.classList.contains("theme-main-color")) {
                        itemMView.classList.remove("theme-main-color");
                        itemMView.classList.add("group-theme-main-hovercolor");
                    } else {
                        itemMView.classList.remove("group-theme-main-hovercolor");
                        itemMView.classList.add("theme-main-color");
                    }
                }
            });
        }
    }

    // removes item from wishlists
    function removeFromWish(item) {
        // for stock items
        for (var i = wishItems.length; i--;) {
            if (parseInt(wishItems[i].id) == item.id &&
                parseInt(wishItems[i].branch_id) == item.branch_id
            ) {
                wishItems.splice(i, 1);
            }
        }
        localStorage.setItem('wishItems', JSON.stringify(wishItems));
        // Update wishlist counters
        updateWishlistCount();
        @if (Request::route()->getName() == 'customer.profile')
            // Initialize wishlist table
            initTableWish();
        @endif
    }

    // adds item to wishlist
    function addToWishList(item) {
        // let item.id, item.branch_id;
        for (let i = wishItems.length; i--;) {
            if (parseInt(wishItems[i].id) == item.id &&
                parseInt(wishItems[i].branch_id) == item.branch_id
            ) {
                // add active to product wishlist icon
                addWishlistActive(wishItems[i]);
                wishItems.splice(i, 1);
                localStorage.setItem('wishItems', JSON.stringify(wishItems));
                // update wishlist counters
                updateWishlistCount();
                return Toast.fire({
                    icon: "success",
                    title: "Item removed from wishlist."
                });
            }
        }
        let urlParam = new URLSearchParams(item).toString();
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            type: 'GET',
            url: `{{ route('product.info') }}?${urlParam}`,
            data: {},
            success: function(data) {
                // console.log("product data: ", data);
                if (data.error || data.data == null) {
                    if (data.text != "" && data.text != null) {
                        return Toast.fire({
                            icon: "error",
                            title: data.text
                        });
                    }
                    return Toast.fire({
                        icon: "error",
                        title: "Error",
                        text: 'Something went wrong'
                    });
                }

                // save to local basket
                let item = {
                    id: data.data.id,
                    branch_id: data.data.branch_products[0].branch_id,
                    branch: data.data.branch_name,
                    title: data.data.name,
                    image: data.data.images[0].image_path,
                    price: data.data.price,
                    discount_price: data.data.discount_price,
                    price2: data.data.price2,
                    discount_price2: data.data.discount_price2,
                    quantity: data.data.branch_products[0].quantity,
                };
                // MAKE DEEP COPY OF OBJECT (THERE ARE OTHER WAYS TO DO THIS ALSO)
                // THIS IS BECAUSE IT WAS UPDATING EXISTING OBJECT VALUES AND MAKING A DEEP COPY FIXED THE ISSUE
                const newItem = JSON.parse(JSON.stringify(item));
                wishItems.push(item);
                localStorage.setItem('wishItems', JSON.stringify(wishItems));
                // add active to wishlist btn to added products in wishlist
                addWishlistActive(item);
                // update wishlist count
                updateWishlistCount();
                return Toast.fire({
                    icon: "success",
                    title: "Property added to wishlist."
                });
            },
            error: function(e) {
                console.log(e);
            }
        });
    }

    // Updates wishlist counters
    updateWishlistCount();

    @if (Request::route()->getName() == 'customer.profile')
        // Initializes wishlist items
        initTableWish();
    @endif

    // add active to wishlist btn to added products in wishlist
    addWishlistActive();
</script>
