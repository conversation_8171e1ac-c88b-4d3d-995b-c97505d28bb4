<script>
    // Toast
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 4500,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });

    // Loading icon
    function loadingIcon(id, state) {
        let el = document.getElementById(id);
        if (state == 1 && el) {
            el.classList.remove("hidden");
        } else if (state == 0 && el) {
            el.classList.add("hidden");
        }
    }

    function disableBtn(id, state) {
        let el = document.getElementById(id);
        if (state == 1 && el) {
            el.disabled = true;
            el.classList.add('opacity-60');
        } else if (state == 0 && el) {
            el.disabled = false;
            el.classList.remove('opacity-60');
        }
    }

    // Loading icon
    function setinnerHtml(elm, count = 0) {
        let el = document.querySelector(elm);
        if (el) {
            el.innerHTML = count;
        }
    }

    // for pagination
    function buildPagination(page, pages) {
        if (pages < 2) {
            return '';
        }
        page = parseInt(page);
        pages = parseInt(pages);
        let numberPages = 5;
        pageNumbers = '';
        let maxLeft = Math.ceil(page - (numberPages / 2));
        let maxRight = Math.ceil(page + (numberPages / 2));
        if (maxLeft < 1) {
            maxLeft = 1;
            maxRight = numberPages;
        }
        if (maxRight > pages) {
            maxLeft = pages - (numberPages - 1);
            maxRight = pages;
            if (maxLeft < 1) {
                maxLeft = 1;
            }
        }
        for (let i = maxLeft; i <= maxRight; i++) {
            if (i == page) {
                pageNumbers += `
                        <li value="${i}" class="page page-item active">
                            <span class="page-link">${i} <span class="sr-only">(current)</span></span>
                        </li>
                    `;
            } else {
                pageNumbers += `<li value="${i}" class="page page-item"><span class="page-link">${i}</span></li>`;
            }
        }
        if (maxLeft >= parseInt(numberPages / 2)) {
            pageNumbers = `
                <li value="1" class="page page-item">
                    <span class="page-link" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                        <span class="sr-only">Previous</span>
                    </span>
                </li>` + pageNumbers;
        }
        if (maxRight != pages) {
            pageNumbers += `
                <li value="${pages}" class="page page-item">
                    <span class="page-link" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                        <span class="sr-only">Next</span>
                    </span>
                </li>`;
        }
        return pageNumbers;
    }

    function initializePaginations(callback, id) {
        let pageSelector = (id ? '#' + id + ' ' : '') + '.page';
        $(pageSelector).on('click', function() {
            let newPage = parseInt($(this).attr('value'));
            // buildChatUsers(newPage);
            callback(newPage);
        });
    }

    // Removes empty or null or undefined values
    const removeEmptyData = (data) => {
        for (let val of Object.entries(data)) {
            let key = val[0];
            let value = val[1];
            if ((!data[key] && data[key] !== 0) || data[key] === '') {
                delete data[key];
            }
            if (data[key] == true) {
                data[key] == 1;
            }
            if (data[key] == false) {
                data[key] == 0;
            }
        }
        return data;
    }

    // Set loading
    const setLoading = (id, status) => {
        const el = document.getElementById(id + '--loading');
        if (el) {
            if (status) {
                el.hidden = false;
            } else {
                el.hidden = true;
            }
        }
    }

    // Show message status
    const messageStatus = (icon = 'error', title = 'Error', data = 'Something went wrong', condition=true) => {
        if (data) {
            if (condition) {
                if (data.msg || data.text) {
                    let msg = data.msg || data.text;
                    if (typeof msg == 'string') {
                        Toast.fire({
                            icon: icon,
                            title: title,
                            text: msg
                        });
                    }
                    if (typeof msg == 'object') {
                        let keys = Object.keys(msg);
                        for (let i = 0; i < keys.length; i++) {
                            Toast.fire({
                                icon: icon,
                                title: title,
                                text: msg[keys[i]]
                            });
                        }
                    }
                } else {
                    Toast.fire({
                        icon: icon,
                        title: title,
                        text: data
                    });
                }
            }
        }
        return condition;
    }
</script>
