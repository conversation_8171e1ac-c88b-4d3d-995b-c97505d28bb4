<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>{{ $company_name }}</title>
</head>

<body
    style="line-height: normal;
            position: relative;
            overflow-x: hidden;
            margin: 0px;
            padding: 0px;
            box-sizing: border-box;
            font-family: sans-serif;
            max-width: 1024px;
            margin-left: auto;
            margin-right: auto;
            ">
    <section
        style="width: 100%;
            max-width: 64rem;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
            padding-top: 2rem;
            padding-bottom: 2rem;
            margin-left: auto;
            margin-right: auto;
            background-color: #ffffff;">
        <div>
            <a href="{{ config('app.url') }}" style="display: block;">
                <img style="width: auto;
                    height: 4rem;" src="{{ $logo }}" alt="logo">
            </a>
        </div>

        <main style="margin-top: 2rem;">
            <h2 style="color: #374151;">Verify your email address</h2>

            <p style="margin-top: .5rem; line-height: 2; color: #4b5563;">
                This is your otp verification code:
            </p>

            <div>
                @foreach ($otp as $ot)
                    <span
                        style="display: inline-block; height: 2.5rem; width: 2.5rem; font-size: 1.5rem; line-height: 2rem; font-weight: normal; border: 1px solid hsl(153, 59%, 53%); color: hsl(153, 59%, 53%); border-radius: 0.375rem;
                            padding: .5rem; text-align: center; height: 100%; margin: .25rem;">
                        {{ $ot }}
                    </span>
                @endforeach
            </div>

            <p style="margin-top: 1rem; line-height: 2; color: rgb(75,85,99,1);">
                This code will only be valid for the next <b>2 minutes</b>. If the code does not work, you can use this
                login verification link:
            </p>

            <a style="display: inline-block; text-decoration: none; padding-left: 2rem; padding-right: 2rem; padding-top: 0.75rem; padding-bottom: 0.75rem; margin-top: 1.5rem; font-size: 0.875rem;
                line-height: 1.25rem; font-weight: 500; letter-spacing: 0.05em; color: #ffffff; text-transform: capitalize;
                transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
                transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
                transition-duration: 300ms;
                background-color: hsl(220, 45%, 13%);
                border-radius: 0.5rem;
                cursor: pointer;
                border: 0;"
                href="{{ route('verifyOtp') }}" target="_blank">
                Verify email
            </a>

            <p style="margin-top: 2rem; color: hsl(215, 14%, 34%, 1);">
                Thanks, <br>
                {{ $company_name }}
            </p>
        </main>


        <footer style="margin-top: 2rem;">
            <p style="margin-top: 0.75rem; color: #6b7280;">© {{ 2024 }} {{ $company_name }}. All Rights
                Reserved.</p>
        </footer>
    </section>
</body>

</html>
