<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Welcome to Kircha</title>
    <style>
        :root {
            --primary-color: hsl(153, 59%, 53%);
            --primary-hover-color: hsl(160, 69%, 95%);
            --dark-color: hsl(220, 45%, 13%);
            --text-color: #121C30;
            --light-gray: #ced4da;
            --white: #ffffff;
        }

        * {
            margin: 0px;
            padding: 0px;
            box-sizing: border-box;
            font-family: sans-serif;
        }

        body {
            margin: 0;
            line-height: normal;
            position: relative;
            overflow-x: hidden;
        }

        section {
            width: 100%;
            max-width: 64rem;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
            padding-top: 2rem;
            padding-bottom: 2rem;
            margin-left: auto;
            margin-right: auto;
            background-color: #ffffff;
        }

        .logo {
            width: auto;
            height: 2rem;
        }

        .text-color {
            color: #374151;
        }

        .otp-container {
            display: flex;
            align-items: center;
            margin-top: 1rem;
            gap: 1rem;
        }

        .otp {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 2.5rem;
            width: 2.5rem;
            font-size: 1.5rem;
            line-height: 2rem;
            font-weight: normal;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 0.375rem;
        }

        .btn-primary {
            display: inline-block;
            text-decoration: none;
            padding-left: 2rem;
            padding-right: 2rem;
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
            margin-top: 1.5rem;
            font-size: 0.875rem;
            line-height: 1.25rem;
            font-weight: 500;
            letter-spacing: 0.05em;
            color: #ffffff;
            text-transform: capitalize;
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
            transition-duration: 300ms;
            background-color: var(--dark-color);
            border-radius: 0.5rem;
            cursor: pointer;
            border: 0;
        }

        .btn-primary:hover {
            color: var(--dark-color);
            background-color: hsl(219, 45%, 90%);
            border-color: hsl(220, 45%, 90%);
        }

        .btn-primary:focus {
            outline: 0px solid transparent;
            outline-offset: 0px;
        }

        .mt-8 {
            margin-top: 2rem;
        }

        .text-gray-700 {
            color: hsl(217, 19%, 27%);
        }

        .mt-2 {
            margin-top: 0.5rem;
        }

        .leading-loose {
            line-height: 2;
        }

        .text-gray-600 {
            --tw-text-opacity: 1;
            color: hsl(215, 14%, 34%)1);
        }

        .mt-4 {
            margin-top: 1rem;
        }

        /* Small Screens */
        @media (min-width: 320px) {
            .logo {
                height: 3rem;
            }
        }
    </style>
</head>

<body>
    <section>
        <header>
            <a href="{{ config('app.url') }}">
                <img class="logo" src="{{ $logo }}" alt="">
            </a>
        </header>

        <main class="mt-8">
            <h2 class="text-gray-700">Password Reset</h2>

            <p class="mt-2 leading-loose text-gray-600">
                This is your otp verification code:
            </p>

            <div class="otp-container">
                @foreach ($otp as $ot)
                    <p class="otp">
                        {{ $ot }}</p>
                @endforeach
            </div>

            <p class="mt-4 leading-loose text-gray-600">
                This code will only be valid for the next <b>2 minutes</b>. If the code does not work, you can use this
                login verification link:
            </p>

            {{-- TODO:Change url --}}
            <a class="btn-primary" href="{{ route('resetPassword') }}" target="_blank">
                Change Password
            </a>

            <p class="mt-8 text-gray-600">
                Thanks, <br>
                {{ $company_name }}
            </p>
        </main>


        <footer class="mt-8">
            <p class="mt-3 text-gray-500">© {{ 2024 }} {{ $company_name }}. All Rights
                Reserved.</p>
        </footer>
    </section>
</body>

</html>
