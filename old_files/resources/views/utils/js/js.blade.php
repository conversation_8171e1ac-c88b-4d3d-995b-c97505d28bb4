<script>
    // Removes empty or null or undefined values
    const removeEmptyData = (data) => {
        for (let val of Object.entries(data)) {
            let key = val[0];
            let value = val[1];
            if ((!data[key] && data[key] !== 0) || data[key] === '') {
                delete data[key];
            }
        }
        return data;
    }

    // Show message status
    const messageStatus = (status, msgs, color, response = false) => {
        if (!response) {
            if (status)
                Toast.fire({
                    icon: color,
                    title: msgs
                });
            return status;
        } else {
            if (msgs) {
                if (typeof msgs === "object") {
                    for (let key in msgs) {
                        Toast.fire({
                            icon: color,
                            title: (msgs[key])
                        });
                    }
                } else {
                    Toast.fire({
                        icon: color,
                        title: (msgs)
                    });
                }
            } else {
                Toast.fire({
                    icon: color,
                    title: ("Something went wrong")
                });
            }
        }
    }
</script>
