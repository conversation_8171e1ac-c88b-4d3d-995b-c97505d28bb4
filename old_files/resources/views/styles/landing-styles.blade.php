<style>
    :root {
        --primary-color: rgb(65, 206, 142);
        --primary-hover-color: #E9FBF5;
        --dark-color: #121C30;
        --text-color: #121C30;
        --success-color: #8dc63f;
        --error-color: rgb(221, 107, 85);
        --warn-color: #ffcc00;
        --gray-color: #6c757d;
        --gray-2-color: #999999;
        --light-gray: #ced4da;
        --white: #ffffff;
        --blue: #00b4d8;
        /* light gray -- #f3f3f3 */
    }

    * {
        margin: 0px;
        padding: 0px;
        box-sizing: border-box;
        font-family: sans-serif;
    }

    body {
        margin: 0;
        line-height: normal;
        position: relative;
        overflow-x: hidden;
    }

    /* #main-body {
        min-height: 100vh;
    } */

    .success {
        color: var(--success-color);
    }

    .error {
        color: var(--error-color);
    }

    .coming {
        color: var(--blue);
    }

    .bg-error {
        background-color: var(--error-color);
    }

    .error-border {
        border-color: var(--error-color);
    }

    .bg-warning {
        background-color: var(--warn-color);
    }

    .text-warning {
        color: var(--warn-color);
    }

    .active-border {
        border-color: var(--dark-color);
    }

    .grap-color {
        color: var(--gray-color);
    }

    .active-primary-border {
        border-color: var(--primary-color);
    }

    .active-bg {
        background-color: var(--primary-color);
    }

    .active-bg-hover:hover {
        background-color: var(--primary-color);
    }

    .inactive-border-hover:hover {
        border-color: var(--dark-color);
    }

    .inactive-border-primary-hover:hover {
        border-color: var(--primary-color);
    }

    .active-border-hover:hover {
        border-color: var(--dark-color);
    }

    .theme-main-bordercolorFocus:focus {
        border-color: var(--primary-color);
    }

    .theme-main-bordercolorFocus:focus-within {
        border-color: var(--primary-color);
    }

    .theme-main-hovercolor:hover {
        color: var(--primary-color);
    }

    body.swal2-toast-shown .swal2-container.swal2-top-end,
    body.swal2-toast-shown .swal2-container.swal2-top-right {
        z-index: 9999;
    }

    .active-focus-border:focus-within {
        border-color: var(--primary-color);
    }

    body.light-theme {
        --primary-color: #41CE8E;
        --primary-hover-color: #9DD1B9;
        --dark-color: #121C30;
        --text-color: #121C30;
    }

    div:where(.swal2-container) h2:where(.swal2-title) {
        font-weight: 400 !important;
    }

    .primary-text-hover:hover {
        color: var(--primary-color);
    }

    .primary-text-color {
        color: var(--primary-color);
    }

    .dark-text-color {
        color: var(--dark-color);
    }

    .error-text {
        color: var(--error-color);
    }

    .gray-2-text {
        color: var(--gray-2-color);
    }

    .light-gray-text {
        color: var(--light-gray);
    }

    img,
    .img-contain {
        width: auto;
        max-width: 100%;
        height: auto;
        max-height: 100%;
        margin: auto;
        text-align: center;
    }

    input:disabled {
        cursor: not-allowed;
        background-color: #e9ecef;
        user-select: none;
    }

    button:disabled {
        /* cursor: wait; */
        cursor: no-drop;
        user-select: none;
        opacity: .6;
    }

    .swal2-cancel.swal2-styled {
        background-color: var(--dark-color);
    }

    /* button,
    [type='button'],
    [type='reset'],
    [type='submit'] {
        background-color: var(--dark-color);
    } */

    .swal2-close {
        background-color: transparent;
    }

    /* div:where(.swal2-actions):not(.swal2-loading) .swal2-styled:hover */
    .swal2-confirm.swal2-styled {
        background-color: var(--primary-color);
    }

    .swal2-actions {
        width: 100%;
        padding: 0rem 1.75rem;
        justify-content: flex-end;
    }

    /* left sidebar cover */
    div.active-cover {
        opacity: 1;
        visibility: visible;
    }

    /* Skeleton */
    .skeleton {
        animation: skeleton-loading 1s linear infinite alternate;
    }

    .skeleton-text {
        width: 100%;
        height: 0.7rem;
        border-radius: 0.25rem;
    }

    .skeleton-text__body {
        width: 75%;
    }

    .skeleton-footer {
        width: 30%;
    }

    /* Google map */
    #map {
        width: 100%;
        height: 100%;
    }

    #map~#infowindow-content {
        display: inline;
    }

    .animation-pulse {
        animation-name: opacity-pulse;
        animation-duration: .5s;
        animation-iteration-count: infinite;
        animation-direction: alternate-reverse;
        animation-timing-function: ease-in;
        animation-fill-mode: forwards;
    }

    .gap-6 {
        gap: 1.5rem
    }

    .gap-4 {
        gap: 1rem
    }

    .button-dark {
        background-color: var(--dark-color);
        color: var(--white);
    }

    .button-info {
        background-color: var(--blue);
        color: var(--white);
    }

    .button-primary {
        background-color: var(--primary-color);
        color: var(--white);
    }

    .button-secondary {
        background-color: var(--gray-2-color);
        color: var(--white);
    }

    /* Custom radio buttons */
    input[type="radio"] {
        position: absolute;
        opacity: 0;
        /* Add if not using autoprefixer */
        -webkit-appearance: none;
        appearance: none;
    }

    input[type=radio]+label {
        display: block;
        gap: 15px;
    }

    input[type=radio]+label:before {
        content: "";
        background: #fff;
        border-radius: 8px;
        border: 1px solid #9b938c;
        display: inline-block;
        width: 24px;
        height: 24px;
        position: relative;
        top: 0px;
        margin-right: 5px;
        vertical-align: top;
        cursor: pointer;
        text-align: center;
    }

    input[type=radio]:checked+label:before {
        background-color: var(--dark-color);
        -webkit-box-shadow: inset 0 0 0 2px #fff;
        box-shadow: inset 0 0 0 2px #fff;
        border-color: var(--dark-color);
    }

    input[type="radio"]:focus {
        outline: 1px solid var(--dark-color);
        outline-offset: 1px;
    }

    .featured {
        color: #e6f8ea;
        background-color: #00ab00;
        position: absolute;
        display: block;
        font-weight: 700;
        font-size: 11px;
        text-transform: capitalize;
        line-height: 1;
        text-align: center;
        border-radius: 30px;
        margin: 0;
        padding: 5px 15px;
        right: 0px;
        top: auto;
    }

    .page-item {
        cursor: pointer;
        position: relative;
        border: 1px solid var(--gray-2-color);
        background-color: var(--white);
        color: var(--dark-color);
        padding: 12px 16px;
        border-radius: 12px;
        font-size: 14px;
        line-height: normal;
        transition: background-color .2s ease-out;
    }

    .page-item:hover {
        opacity: .6;
        border-color: var(--dark-color);
        background-color: transparent;
    }

    .page-item.active {
        background-color: var(--dark-color);
        color: var(--white);
        opacity: 1 !important;
    }

    /* Scroll to top button */
    .show-scroll {
        opacity: .85;
        display: block;
        visibility: visible;
    }

    /* Cart Page */
    .div-1 {
        border-right: 1px solid #e5e7eb;
        grid-column: span 2 / span 2;
        width: 100%;

        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .div-2 {
        display: block;
        grid-row: 1;
        position: relative;
    }

    .div-2.hidden {
        display: none;
    }

    .div-container {
        display: grid;
        grid-template-columns: repeat(1, minmax(0, 1fr));
        grid-template-rows: auto auto;
        align-items: baseline;
        width: 100%;
    }

    .empty-cart {
        min-height: 100vh;
        border-right: 0;
        grid-column: span 3 / span 3 !important;
        padding-right: 0 !important;
    }

    .cart-box {
        padding: 15px 0px;
        overflow: hidden;
        display: grid;
        grid-template-columns: calc(100% - 115px) 115px;
    }

    .cart-box-1 {
        display: grid;
        grid-template-columns: 100px calc(100% - 100px);
        width: 100%;
        margin-right: auto;
    }

    .cart-item-img {
        width: 100%;
    }

    .cart-item-content {
        padding: 0 15px;
    }

    .cart-item-content .item-name {
        font-weight: bold;
        font-size: 16px;
    }

    .cart-item-content .item-list .lists {}

    .cart-box-2 {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 100%;
        margin-left: auto;
    }

    .payment-img {
        height: 100px;
        min-width: 150px;
        max-width: 200px;
        margin-right: 8px;
        display: flex;
    }

    /* Slick slider */
    .slick-slider .slick-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background-color: #fff;
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        border-radius: 100%;
        padding: 8px;
        z-index: 20;
    }

    .slick-slider .slick-prev {
        left: 15px;
    }

    .slick-slider .slick-next {
        right: 15px;
    }

    .small-images.slick-initialized .slick-slide {
        cursor: pointer;
    }

    /* .small-images.slick-initialized .slick-current img {
        border: 1px solid var(--primary-color);
    } */

    /* Modal */
    .modal2 {
        display: flex;
        /* align-items: center; */
        justify-content: center;
        /* Hidden by default */
        visibility: hidden;
        /* Stay in place */
        position: fixed;
        /* Sit on top */
        z-index: 998;
        /* padding-top: 85px; /* Location of the box */
        /* padding-bottom: 85px; */
        left: 0;
        top: 0;
        /* Full width */
        width: 100%;
        /* Full height */
        height: 100vh;
        /* min-height: 75vh; */
        padding: 1.5rem;
        /* Enable scroll if needed */
        overflow: hidden;
        /* overflow: auto; */
        background-color: rgb(0, 0, 0);
        /* Fallback color */
        background-color: rgba(0, 0, 0, 0.4);
        transition: all .15s ease-in;
        opacity: 0;
        /* Black w/ opacity */
    }

    .modal2.show {
        visibility: visible;
        opacity: 1;
    }

    .modal2-content {
        position: relative;
        background-color: #fff;
        margin: auto;
        padding: 0;
        border: 1px solid #888;
        width: 100%;
        height: 100%;
        max-width: 90%;
        max-height: 95%;
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
        display: grid;
        grid-template-columns: repeat(1, minmax(0, 1fr));
        grid-template-rows: repeat(2, minmax(0, 2fr));
    }

    .modal2-content__loading-screen {
        display: none;
        position: absolute;
        z-index: 5;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2);
    }

    /* Image */
    .modal2-content__image {
        width: 100%;
        height: 100%;
        overflow: hidden;
        padding-top: 65px;
        border-bottom: 1px solid #ced4da;
        display: grid;
        grid-template-columns: repeat(1, minmax(0, 1fr));
        grid-template-rows: calc(100% - 65px) 65px;
    }

    .modal2-content__image .modal2-content__imageContainer {
        width: 100%;
        height: 100%;
        padding: 5px;
        display: flex;
        justify-content: center;
    }

    .modal2-content__imageContainer .slick-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background-color: #fff;
        border-radius: 100%;
        padding: 8px;
        z-index: 20;
    }

    .modal2-content__imageContainer .slick-prev {
        left: 0;
    }

    .modal2-content__imageContainer .slick-next {
        right: 0;
    }

    .modal2-content__image .modal2-content__moreImages {
        width: 100%;
        max-width: 100%;
        height: 100%;
        /* display: flex;
        align-items: center;
        padding: 5px 55px; */
    }

    #modal2-content__branch .branch {
        padding: 10px 15px;
        margin: 2px;
    }

    .modal2-content__content {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: auto;
    }

    .modal2-content__body--bottom #modal2-content__count {
        height: 100%;
        width: 100%;
    }

    #modal2-content__count>div {
        height: 100%;
        padding: 5px;
    }

    #modal2-content__count>div>div {
        flex-direction: row;
    }

    #modal2-content__count>div>div .pro-qty {
        height: 100%;
        display: grid;
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    #modal2-content__count>div>div .pro-qty .qty-btn {
        height: 100%;
        width: 100%;
    }

    #modal2-content__count>div>div .pro-qty .pro-qty-input {
        width: 100%;
        height: 100%;
    }

    #modal2-content__count>div>div .btn-contain {
        height: 100%;
    }

    #modal2-content__count #product_details_count {
        max-width: 60px;
    }

    .modal2-content__body--bottom #modal2-content__count .btn-addtocart {
        /* width: 80px; */
        width: 100%;
        height: 100%;
        padding: 8px 16px;
    }

    .modal2-content__body--bottom #modal2-content__count .btn-addtocart span {
        display: none;
    }

    .modal2-content__body--bottom #modal2-content__count .btn-addtowish {
        width: 60px;
        height: 100%;
        padding: 0;
    }

    .modal2-content__body {
        /* padding: 35px 25px; */
        height: 100%;
        overflow: hidden;
        position: relative;

        display: grid;
        grid-template-columns: repeat(1, minmax(0, 1fr));
        grid-template-rows: calc(100% - 80px) 80px;
    }

    /* The Close Button */
    .modal2-content__content .close {
        position: absolute;
        z-index: 10;
        cursor: pointer;
        right: 30px;
        top: 15px;
        color: #000;
        font-size: 28px;
        font-weight: bold;
        line-height: normal;
        transition: .1s color linear;
    }

    .modal2-content__content .close:hover,
    .modal2-content__content .close:focus {
        color: #00000069;
        text-decoration: none;
        cursor: pointer;
    }

    .modal2-content__body--top {
        padding: 0px 15px;
        padding-top: 15px;
        padding-left: 15px;
        padding-right: 15px;
        height: 100%;
        overflow: auto;
    }

    .modal2-content__body--bottom {
        width: 100%;
        /* padding: 0px; */
        padding: 10px 0px;
        height: 100%;
        background-color: #fff;
    }

    .modal2-btn-close {
        margin: 0px 15px;
        border-radius: 100%;
        font-size: 32px;
        cursor: pointer;
        font-weight: bold;
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 25;
        color: var(--dark-color);
        background-color: var(--white);
    }

    .modal2-btn-close:hover {
        opacity: 60%;
    }

    /* Product Images slick slider */
    .slick-slider div.slick-list {
        height: 100%;
        padding: 0 !important;
    }

    .slick-list .slick-track {
        height: 100%;
        margin: 0;
    }

    .slick-slide.slick-current {
        border-color: var(--primary-color);
    }

    .product-images .slick-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        transition: all .3s ease-in-out;
        z-index: 10;
    }

    .slick-slider .slick-button.slick-disabled {
        opacity: .4 !important;
        /* cursor: not-allowed; */
    }

    .small-images.slick-slider .slick-button.slick-disabled {
        cursor: pointer;
    }

    .product-images .slick-button:hover {
        opacity: .6;
    }

    .product-images .slick-prev {
        left: 10px;
    }

    .product-images .slick-next {
        right: 10px;
    }

    .slick-slider .slick-dots {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        list-style: none;
        display: flex;
        gap: 10px;
        overflow: auto;
        max-width: 100%;
        padding: 5px 10px;
    }

    .slick-slider .slick-dots li {
        color: var(--gray-color);
        line-height: normal;
    }

    .slick-slider .slick-dots li button {
        font-size: 0;
        padding: 5px;
        width: 15px;
        height: 15px;
        border-radius: 100%;
        border: 1px solid var(--gray-color);
        background-color: transparent;
    }

    .slick-slider .slick-dots li.slick-active button {
        background-color: var(--gray-color);
    }

    /* Custom select */
    .custom-select {
        position: relative;
        width: 100%;
        max-width: 300px;
    }

    .custom-select select {
        display: none;
    }

    .custom-select .select-selected {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        width: 100%;
        max-width: 300px;
        padding: 10px 15px;
        background-color: #FFFFFF;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 14px;
    }

    /* Style the arrow inside the select element: */
    .select-selected:after {
        display: none;
        position: absolute;
        content: "";
        top: 25px;
        right: 10px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-color: #000 transparent transparent transparent;
    }

    /* Point the arrow upwards when the select box is open (active): */
    .select-selected.select-arrow-active:after {
        border-color: transparent transparent #000 transparent;
        top: 16px;
    }

    .select-selected svg {
        width: 15px;
        height: 15px;
        transition: .2s all ease-out;
        position: relative;
    }

    .select-selected.select-arrow-active svg {
        transform: rotate(180deg);
    }

    /* style the items (options), including the selected item: */
    .select-items div,
    .select-selected {
        color: var(--dark-color);
        padding: 5px 15px;
        border: 1px solid transparent;
        cursor: pointer;
        font-size: 14px;
    }

    .select-items div:not(:last-child) {
        border-color: transparent transparent rgba(0, 0, 0, 0.1) transparent;
    }

    /* Style items (options): */
    .select-items {
        position: absolute;
        top: 22px;
        left: 0px;
        min-width: 196px;
        z-index: 99;
        background-color: #FFFFFF;
        border: 1px solid #ced4da;
        margin-top: 2px;
        min-width: 100%;
        /* visibility: hidden; */
        display: none;
        opacity: 0;
        max-height: 180px;
        overflow: auto;
        user-select: none;
    }

    .rolldown {
        top: 100%;
        /* visibility: visible; */
        display: block;
        opacity: 1;
        animation: rolldown .2s linear alternate;
    }

    .rollup {
        top: 22px;
        /* visibility: hidden; */
        display: none;
        opacity: 0;
        animation: rollup .2s linear alternate;
    }

    .select-items .select-hide {
        visibility: hidden;
        opacity: 0;
    }

    .select-items div:hover,
    .select-items .same-as-selected {
        /* background-color: rgba(0, 0, 0, 0.1); */
        color: #fff;
        opacity: 1;
        background-color: var(--dark-color);
    }

    /* Account Page */
    /*-- Tab Content & Pane Fix --*/
    .tab-content {
        width: 100%;
    }

    .tab-content .tab-pane {
        display: block;
        height: 0;
        max-width: 100%;
        visibility: hidden;
        overflow: hidden;
        opacity: 0;
        transition: opacity .3s ease-out;
    }

    .tab-content .tab-pane.active {
        height: auto;
        visibility: visible;
        opacity: 1;
        overflow: visible;
    }

    /* table */
    .table-body tr td {
        max-width: 20rem;
        min-width: 7rem;
    }

    /* Navigation footer */
    .footer-navbar {
        /* overflow: hidden; */
        background-color: #fff;
        position: fixed;
        bottom: 0;
        width: 100%;
        box-shadow: 0px -1px 3px 0px #b7b8bf;
        z-index: 996;
    }

    .menu-list {
        display: flex;
    }

    .menu-list li {
        list-style: none;
    }

    .menu-list-icon {
        position: relative;
        width: calc(100% - 100px);
        text-align: center;
        padding: 7px 0px;
    }

    .menu-list-icon:not(:last-child) {
        border-right: 1px solid #e5e5e5;
    }

    .menu-li {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    .menu-li--link {
        position: relative;
    }

    .menu-li--link svg {
        width: 20px;
        height: 20px;
        color: var(--clr-svg);
    }

    .bubble {
        border-radius: 50%;
        min-width: 20px;
        min-height: 20px;
        text-align: center;
        position: absolute;
        right: -10px;
        top: -5px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        font-weight: 500;
        line-height: 19px;
    }

    .bubble.hidden {
        display: none;
    }

    .big-bubble {
        border-radius: 50%;
        min-width: 20px;
        min-height: 20px;
        text-align: center;
        position: absolute;
        top: 10px;
        right: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        font-weight: 500;
        line-height: 19px;
    }

    .bubble--red {
        background-color: var(--error-color);
        color: #fff;
    }

    .nav-desc {
        font-weight: 500;
        font-size: 12px;
        line-height: 19px;
        text-transform: capitalize;
    }

    .no-data {
        width: 100%;
        grid-column: span 4 / span 4;
        max-height: 400px;
    }

    .no-data img {
        border-radius: 32px;
    }

    /* Details page */
    .tabs .active {
        border-color: var(--primary-color);
    }

    .tabs .inactive {
        background-color: var(--white);
        color: var(--dark-color);
    }

    section.details-tab {
        visibility: hidden;
        opacity: 0;
    }

    section.details-tab.active-tab {
        visibility: visible;
        opacity: 1;
    }

    .modal2 .modal-tab {
        visibility: hidden;
        opacity: 0;
    }

    .modal2 .modal-tab.active-tab {
        visibility: visible;
        opacity: 1;
    }

    .custom-select_sort {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: .5rem;
    }

    /* Price range slider */
    .slider-box {
        /* width: 90%; */
        /* margin: 25px 0; */
        margin: 1rem 0;
    }

    /* .slider-box label,
    .slider-box input {
        border: none;
        display: block;
        margin-right: -4px;
        vertical-align: top;
        font-size: 18px;
        line-height: 27px;
        padding-bottom: 3px;
        width: 100%;
    }

    .slider-box label {
        padding-bottom: 8px;
        margin-bottom: 8px;
        border-bottom: 1px dashed #ced4da;
    } */

    .slider-box input {
        width: 100%;
        /* max-width: 290px; */
        padding: .5rem 0px;
        background-color: transparent;
    }

    .slider-box input:focus-visible {
        outline-width: 2px;
        outline-style: solid;
        border-radius: 3px;
        outline-color: var(--primary-color);
    }

    .price-slider {
        cursor: pointer;
        margin: 15px 0;
        width: 100%;
        height: 1.25rem;
        background-color: #c7c7c7;
        position: relative;
    }

    .price-slider .ui-slider-range {
        position: absolute;
        height: 100%;
        top: 0px;
        background-color: var(--primary-color);
    }

    .price-slider .ui-slider-handle {
        position: absolute;
        width: 2rem;
        height: 2rem;
        border-radius: 12px;
        display: block;
        top: -.5rem;
        box-shadow: rgba(0, 0, 0, 0.15) 2px 4px 4px;
        cursor: pointer;
        margin-left: -5px;
        background-color: var(--primary-color);
    }

    .price-slider .ui-slider-handle:hover {
        outline: 1px solid #aaaaaa;
    }

    .price-slider .ui-slider-handle:focus-visible {
        outline-width: 2px;
        outline-style: solid;
        outline-color: #aaaaaa;
    }

    .badge {
        padding: .5rem;
        background-color: var(--primary-color);
        color: white;
        border-radius: 1rem;
        display: inline-block;
        margin: .5rem;
        font-size: .75rem;
        white-space: nowrap;
    }

    .transaparent-glass {
        background: rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .scroll-tap {
        max-height: 12rem;
        overflow-y: auto;
        line-height: 1.5;
        font-size: 1rem;
    }

    .discount-price {
        color: var(--primary-color);
    }

    .incentive-box {
        border: 1px solid;
        border-color: rgb(75, 92, 92);
        padding: .5rem 2rem;
        border-radius: .5rem;
        background-color: rgb(75, 92, 92);
        color: white;
        /* 48px */
        font-size: 3rem;
        line-height: 1;
    }

    .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
        width: 100%;
        height: .5rem;
        overflow: hidden;
    }

    .loading-bar {
        position: absolute;
        width: 50%;
        height: 100%;
        background-color: #468966;
        animation: loading .4s infinite alternate;
    }

    .left {
        left: 50%;
    }

    .right {
        right: 50%;
    }

    .noUi-touch-area {
        background-color: var(--primary-color);
        border-radius: 4rem;
    }

    .noUi-horizontal .noUi-handle {
        border-color: var(--white);
        border-radius: 4rem;
        cursor: pointer;
        width: 2rem;
        height: 2rem;
        border: 0;
        background: none;
        box-shadow: none;
        outline: 3px solid white;
    }

    .noUi-handle:before,
    .noUi-handle:after {
        background: none;
    }

    .noUi-connect {
        background-color: var(--primary-color);
    }

    .noUi-target {
        background-color: #c7c7c7;
        border: 0;
    }

    .video-container {
        position: relative;
        padding-bottom: 56.25%;
        /* display: flex;
        justify-content: center; */
    }

    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        /* aspect-ratio: 16 / 9;
        width: 100% !important; */
    }

    iframe {
        aspect-ratio: 16 / 9;
        width: 100%;
    }

    @supports not (aspect-ratio: 1) {
        .video-container iframe {
            aspect-ratio: 16 / 9;
            width: 100% !important;
        }
    }

    .noUi-value-horizontal {
        top: 1rem;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .switch .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .switch .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .switch input:checked+.slider {
        background-color: var(--primary-color);
    }

    .switch input:focus+.slider {
        box-shadow: 0 0 1px var(--primary-color);
    }

    .switch input:checked+.slider:before {
        -webkit-transform: translateX(26px);
        -ms-transform: translateX(26px);
        transform: translateX(26px);
    }

    /* Rounded sliders */
    .switch .slider.round {
        border-radius: 34px;
    }

    .switch .slider.round:before {
        border-radius: 50%;
    }

    /* Animations */
    @keyframes loading {
        0% {
            width: 5%;
        }

        100% {
            width: 36%;
        }
    }

    @keyframes skeleton-loading {
        0% {
            background-color: hsl(200, 20%, 80%);
        }

        100% {
            background-color: hsl(200, 20%, 95%);
        }
    }

    /* opacity pulse */
    @keyframes opacity-pulse {
        from {
            opacity: 0.45;
        }

        to {
            opacity: 1;
        }
    }
</style>

<!-- Tablet Screen -->
<style>
    @media (min-width: 768px) {
        .product-images .slick-prev {
            left: 15px;
        }

        .product-images .slick-next {
            right: 15px;
        }

        .page-item {
            font-size: 16px;
        }

        .no-data {
            max-height: 600px;
        }
    }

    /* Table responsive */
    @media (max-width: 768px) {
        table thead {
            display: none;
        }

        table tbody td {
            width: 100%;
            display: grid;
            gap: 1rem;
            grid-template-columns: 8rem auto;
            padding: 0;
            border: 1px solid var(--white);
        }

        table tbody td:first-child {
            /* padding-top: 1rem; */
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
        }

        table tbody td:last-child {
            /* padding-bottom: 1rem; */
            border-bottom-left-radius: 1rem;
            border-bottom-right-radius: 1rem;
        }

        table tbody td[data-type]::before {
            content: attr(data-type) ": ";
            font-weight: 700;
            text-transform: capitalize;
            text-align: left;
            align-self: center;
            background-color: #ced4da;
            padding: 1rem;

            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 8rem;
        }

        table tbody td[data-type=""]::before {
            content: "";
        }

        .table-body tr td {
            max-width: 100%;

            padding: 1rem;
            position: relative;
            padding-left: 9rem;
        }
    }
</style>

<!-- Laptop Screen -->
<style>
    @media (min-width: 1024px) {
        .div-container {
            grid-template-columns: repeat(3, minmax(0, 1fr));
            grid-template-rows: repeat(1, minmax(0, 1fr));
        }

        .div-1 {
            padding-right: 25px;
            grid-row: 1;
            grid-column: span 2 / span 2;
        }

        .div-2 {
            position: sticky;
            top: 70px;
            align-self: flex-start;
            padding-left: 25px;
            grid-column: span 1 / span 1;
            width: 100%;
            grid-row: 1;
            grid-column: 3;
        }

        .modal2 {
            padding: 3rem;
        }

        /* Modal 2 */
        .modal2-content {
            /* grid-template-columns: repeat(2, minmax(0, 1fr)); */
            grid-template-columns: calc(100% - 40%) 40%;
            grid-template-rows: repeat(1, minmax(0, 1fr));
            width: 100%;
            margin-left: auto;
            margin-right: auto;
        }

        .modal2-content__image {
            padding-top: 0px;
            border-bottom: 0px;
            border-right: 1px solid #ced4da;
        }

        .modal2-content__body--top {
            padding-top: 60px;
        }

        /* .modal2-content__moreImages.slick-initialized .slick-slide {
            width: auto;
            height: 100%;
        } */

        .modal2-btn-close {
            right: 10px;
            top: 10px;
        }

        .modal2-content__body--bottom #modal2-content__count .btn-addtocart span {
            display: block;
        }

        .no-data {
            max-height: 700px;
        }
    }

    #terms a {
        text-decoration: underline;
        color: #007bff;
    }

    #terms a:hover {
        color: #0056b3;
    }
</style>
