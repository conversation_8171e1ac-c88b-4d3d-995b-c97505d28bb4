<style>
    :root {
        --primary-color: #41CE8E;
        --primary-hover-color: #9DD1B9;
        --dark-color: #121C30;
        --text-color: #121C30;
    }

    body.light-theme {
        --primary-color: #41CE8E;
        --primary-hover-color: #9DD1B9;
        --dark-color: #121C30;
        --text-color: #121C30;
    }

    div:where(.swal2-container) h2:where(.swal2-title) {
        font-weight: 400 !important;
    }

    .img-contain {
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;
        margin: auto;
    }

    .label-span {
        font-size: 0.8571em;
        margin-bottom: 5px;
        color: #9A9A9A;
    }

    .primary-text-color {
        color: var(--primary-color);
    }

    .relative {
        position: relative;
    }

    .gap-6 {
        gap: 1.5rem
    }

    .gap-4 {
        gap: 1rem
    }

    .featured {
        color: #e6f8ea;
        background-color: #00ab00;
        position: absolute;
        display: block;
        font-weight: 700;
        font-size: 11px;
        text-transform: capitalize;
        line-height: 1;
        text-align: center;
        border-radius: 30px;
        margin: 0;
        padding: 5px 15px;
        right: 0px;
        top: auto;
    }

    .page-item {
        cursor: pointer;
    }

    .spin-loader {
        width: 1.75rem;
        height: 1.75rem;
        border: .35rem solid #dee2e6;
        border-radius: 32px;
        border-top: .35rem solid #343a40;
        border-bottom: .35rem solid #343a40;
    }

    .spin {
        -webkit-animation: spin 1s linear infinite;
        animation: spin 1s linear infinite;
    }

    .spin-position {
        position: absolute;
        right: 1rem;
        top: 10%;
        transform: translateY(-10%);
    }

    .eye-icon {
        width: 75px;
        height: 35px;
        display: inline-block;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
    }

    .eye-icon svg {
        width: 100%;
        height: 100%;
        cursor: pointer;
        user-select: none;
    }

    .country-code {
        position: absolute;
        left: 1px;
        height: 95%;
        padding: 0 15px;
        display: flex;
        align-items: center;
        background-color: #f2f2f2;
        align-self: center;
        border-radius: 4px 0px 0px 4px;
    }

    input[type='tel'].form-control {
        padding-left: 72px;
    }

    .card-user div.avatar {
        max-width: 100%;
        height: auto;
        margin: 0;
        width: 2rem;
    }

    .team-members li:not(:last-child) {
        margin-bottom: .25rem;
    }

    .team-members li>div {
        padding: .75rem 1rem;
        cursor: pointer;
    }

    .team-members li>div:hover {
        background-color: #e9ecef;
    }

    /* Chat */
    .msg-box-container {
        padding: 1rem;
        border-radius: .5rem;
        font-size: 1rem;
        font-weight: normal;
        max-width: 50%;
        margin-top: .5rem;
        margin-bottom: .5rem;
    }

    .container-left {
        color: #ffffff;
        background-color: #0096c7;
        margin-right: auto;
    }

    .container-center {
        padding: .5rem .75rem;
        border-radius: .5rem;
        margin: .5rem auto;
        max-width: 150px;
        font-size: 1rem;
        color: #ffffff;
        background-color: #009c98;
        font-weight: normal;
        text-align: center;
    }

    .container-right {
        color: #252422;
        background-color: #ade8f4;
        margin-left: auto;
    }

    #messagesWindow {
        padding: 1rem;
    }

    .info {
        border-left: 2px solid #f00;
        padding-left: 10px;
    }

    .scrollable-wrap {
        background-color: #ffffff;
        padding: 1rem 3rem;
        width: 24rem;
        max-height: 18rem;
        overflow: auto;
        border-radius: 1rem;
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        border: 1px solid #e5e7eb;
        margin: 0;
    }

    .form-group .price-range {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
        width: 100%;
        height: .5rem;
        overflow: hidden;
    }

    .loading-bar {
        position: absolute;
        width: 50%;
        height: 100%;
        background-color: #468966;
        animation: loading .4s infinite alternate;
    }

    .left {
        left: 50%;
    }

    .right {
        right: 50%;
    }

    .hidden {
        display: none;
    }

    textarea.form-control,
    .form-group textarea.form-control {
        max-height: none;
        resize: auto;
        width: auto;
    }

    @-webkit-keyframes spin {
        from {
            -webkit-transform: rotate(0deg);
        }

        to {
            -webkit-transform: rotate(360deg);
        }
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    @keyframes loading {
        0% {
            width: 5%;
        }

        100% {
            width: 36%;
        }
    }
</style>
