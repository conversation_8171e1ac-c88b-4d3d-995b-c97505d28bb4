<style>
    :root {
        --primary-color: #41CE8E;
        /* --secondary-color: #f97316; */
        --secondary-color: #FF0000;
        --red-color: #ef4444;
        --primary-hover-color: #9DD1B9;
        --dark-color: #121C30;
        --text-color: #121C30;
        --white-color: #FFFFFF;
    }

    body.light-theme {
        --primary-color: #41CE8E;
        --primary-hover-color: #9DD1B9;
        --dark-color: #121C30;
        --text-color: #121C30;
    }

    /*
        Main color - #41CE8E
        Hover color - #9DD1B9
        Black - #121C30
        White - #FFFFFF
        Orange active - #FBD38D
        Orange BG - #FFFAF0
    */
    .primary-color {
        color: var(--primary-color);
    }

    .primary-bg-color {
        background-color: var(--primary-color);
    }

    .primary-border-color {
        border-color: var(--primary-color);
    }

    .primary-hover-color:hover {
        color: var(--primary-color);
    }

    .primary-hover-border-color:hover {
        border-color: var(--primary-color);
    }

    .secondary-color {
        color: var(--secondary-color);
    }

    .secondary-bg-color {
        background-color: var(--secondary-color);
    }

    .secondary-bg-hover:hover {
        background-color: var(--secondary-color);

    }

    .dark-color {
        color: var(--dark-color);
    }

    .dark-bg-color {
        background-color: var(--dark-color);
    }

    .red-color {
        color: var(--red-color);
    }

    .red-border-color {
        border-color: var(--red-color);
    }

    .red-bg-color {
        background-color: var(--red-color);
    }

    .red-bg-hover-color:hover {
        background-color: var(--red-color);
    }

    .theme-bg-color {
        background-color: var(--primary-color);
    }

    .theme-text-color {
        color: var(--dark-color);
    }

    .theme-inactive-box:hover {
        color: var(--white-color);
        background-color: var(--dark-color);
    }

    .theme-active-box {
        color: var(--white-color);
        background-color: var(--primary-color);
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    }

    .outer-focus-border-primary-focus:has(input:focus-within) {
        border-color: var(--primary-color);
    }

    /* Table Default */
    .table-default {
        width: 100%;
        border-collapse: collapse;
    }

    .table-default td,
    .table-default th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 8px;
    }

    .table-default tr:nth-child(even) {
        background-color: #dddddd;
    }
</style>
