@inject('util', 'App\Models\Util')
@extends('layouts.dashboard-layout')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card card-user">
                <!-- Tabs -->
                <div class="card-header card-header-danger">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <ul class="nav nav-tabs" data-tabs="tabs">
                                <li role="presentation" class="nav-item" id="tabHome">
                                    <a href="#home" class="nav-link active" data-toggle="tab">List</a>
                                </li>
                                <li role="presentation" class="nav-item">
                                    <a href="#create" class="nav-link" data-toggle="tab">Create</a>
                                </li>
                                <li role="presentation" class="nav-item" id="tabEdit" style="display: none;">
                                    <a href="#edit" class="nav-link" data-toggle="tab">Edit</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- End Tabs -->

                <!-- Tab List -->
                <div id="myTabContent" class="card-body">
                    <div class="tab-content text-left">

                        <!-- Tab View - Listings -->
                        <div role="tabpanel" class="tab-pane active" id="home">
                            <div class="card-header">
                                <h5 class="card-title">Advert Listings</h5>
                                <div class="row">
                                    <div class="col-12 col-md-4">
                                        <div class="form-group">
                                            <span class="label-span">Filter</span>
                                            <div class="row gap-4 col-12">
                                                <!-- Published -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'visible_search',
                                                    'text' => 'Published item',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                                <!-- Unpublished -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'unvisible_search',
                                                    'text' => 'Unpublished item',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-4 ml-auto">
                                        @include('components.input-field.text', [
                                            'text' => 'Search',
                                            'label' => 'Search',
                                            'type' => 'search',
                                            'id' => 'element_search',
                                        ])
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Table -->
                                @include('components.table.light-table')
                                <!-- End Table -->
                            </div>
                        </div>
                        <!-- End Tab View - Listings -->

                        <!-- Tab View - Create -->
                        <div role="tabpanel" class="tab-pane" id="create">
                            <div class="card-header">
                                <h5 class="card-title">Create Advert</h5>
                            </div>
                            <div class="card-body">
                                <div id="createForm">
                                    <div id="form">
                                        <div class="row">
                                            <div class="col-md-6 col-12">
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Name',
                                                            'placeholder' => 'Insert name',
                                                            'desc' => 'Insert Advert Name',
                                                            'id' => 'name',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include(
                                                            'components.input-field.select-custom.select',
                                                            [
                                                                'label' => 'Type',
                                                                'onchange' => 'onChangeType();',
                                                                'id' => 'banner-type',
                                                                'datas' => $util->getBannerTypes(),
                                                            ]
                                                        )
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12" id="product-for-banner-view">
                                                        @include(
                                                            'components.input-field.select-custom.select',
                                                            [
                                                                'label' => 'Product',
                                                                'onchange' => 'onChangeType();',
                                                                'id' => 'product-for-banner',
                                                                'datas' => $products,
                                                            ]
                                                        )
                                                        @if ($pages > 1)
                                                            <div class="d-flex align-items-center">
                                                                <nav aria-label="Page navigation">
                                                                    <ul class="pagination" id="product-pagination-list">
                                                                        @for ($i = 1; $i <= $pages; $i++)
                                                                            @if ($i == $page)
                                                                                <li class="page-item active"><span
                                                                                        class="page-link"
                                                                                        onclick="getProductsPagination({{ $i }});">{{ $i }}<span
                                                                                            class="sr-only">(current)</span></span>
                                                                                </li>
                                                                            @else
                                                                                <li class="page-item"><span
                                                                                        class="page-link"
                                                                                        onclick="getProductsPagination({{ $i }});">{{ $i }}</span>
                                                                                </li>
                                                                            @endif
                                                                        @endfor
                                                                    </ul>
                                                                </nav>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-12">
                                                        @include(
                                                            'components.input-field.select-custom.select',
                                                            [
                                                                'label' => 'Position',
                                                                'onchange' => 'onChangeType();',
                                                                'id' => 'ad-position',
                                                                'datas' => $util->getBannerPositions(),
                                                            ]
                                                        )
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'URL',
                                                            'placeholder' => 'Insert external link',
                                                            'id' => 'external-link',
                                                            'request' => 'false',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <!-- Published -->
                                                        @include('components.input-field.checkbox-2', [
                                                            'id' => 'visible',
                                                            'text' => 'Published',
                                                            'initvalue' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 col-12">
                                                @include('components.input-field.image', [])
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.buttons.button', [
                                                    'label' => 'Save',
                                                    'id' => 'on-save',
                                                    'onclick' => 'onSave();',
                                                    'block' => '1',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <!-- End Tab View - Create -->

                        <!-- Tab View - Edit -->
                        <div role="tabpanel" class="tab-pane" id="edit">
                            <div class="card-header">
                                <h5 class="card-title">Edit Advert</h5>
                            </div>
                            <div class="card-body">
                                <div id="editForm">
                                </div>
                            </div>
                        </div>
                        <!-- End Tab View - Edit -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script -->
    @include('pages.admin.advert.script')
@endsection
