<script>
    // Address Data Listsings
    let pages = 1;
    let currentPage = 1;
    let sortPublished = '1';
    let sortUnPublished = '1';
    let searchText = "";
    let sort = "updated_at";
    let sortBy = "desc";
    let data;
    let products;

    // selected product
    let selectedProduct;

    paginationGoPage(1);
    initPaginationLine(pages, currentPage);
    initTableHeader();

    function paginationGoPage(page) {
        data = {
            page: page,
            per_page: 20,
            sort: sortBy,
            order_by: sort,
            query: searchText,
            visible: sortPublished,
            invisible: sortUnPublished
        };

        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: '{{ route('admin.coupons-get') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                currentPage = data.data.current_page;
                pages = Math.ceil(data.data.total / data.data.per_page);
                products = data.data.data;
                initDataTable(products);
                initPaginationLine(pages, currentPage);
                initTableHeader();
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                dataLoading = false;
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function initDataTable(data) {
        html = "";
        if (data.length == 0) {
            html = `
            <tr>
                <td colspan="8">
                    <div class="d-flex justify-content-center align-items-center">
                        <p class="m-0">There are no records</p>
                    </div>
                </td>
            </tr>
            `;
            document.getElementById("table_body").innerHTML = html;
        } else {
            data.forEach(function(item, i, arr) {
                html += buildOneItem(item);
            });
            document.getElementById("table_body").innerHTML = html;
        }
    }

    function buildOneItem(item) {
        if (item.visible == 1)
            var visible = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;
        else
            var visible = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;

        if (item.percent == 1)
            var percent = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;
        else
            var percent = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;

        return `
            <tr>
                <td>${item.id}</td>
                <td>${item.name}</td>
                <td>${item.start_date}</td>
                <td>${item.end_date}</td>
                <td>
                    <div style="display: flex;">${percent}</div>
                </td>
                <td>
                    <div style="display: flex;">${visible}</div>
                </td>
                <td class="text-center">
                    <div class="">
                        <p class="text-info m-0"><strong>${item.timeago}</strong></p>
                    </div>
                    <p class="h5">${item.updated_at2}</p>
                </td>
                <td class="text-center" style="white-space:nowrap;">
                    <button type="button" class="btn btn-info btn-round" onclick="editItem(${item.id})">
                        Edit
                    </button>
                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '{{ route('admin.coupon-delete', ':id') }}')">
                        <div>Delete</div>
                    </button>
                </td>
            </tr>
        `;
    }

    function initPaginationLine(pages, page) {
        allPages = pages;
        currentPage = page;
        let pageNumbers = '';
        pageNumbers = buildPagination(currentPage, allPages);
        let paginationList = document.getElementById('pagination-list');
        paginationList.innerHTML = pageNumbers;
        initializePaginations(paginationGoPage);
    }

    function tableHeaderSort(newsort) {
        if (newsort == sort) {
            if (sortBy == "asc")
                sortBy = "desc";
            else
                sortBy = "asc";
        } else {
            sort = newsort
            sortBy = "asc";
        }
        paginationGoPage(currentPage);
    }

    function utilGetImg(value) {
        var img = "{{ asset('img/arrow_noactive.png') }}";
        if (sort == value && sortBy == "asc") img = "{{ asset('img/asc_arrow.png') }}";
        if (sort == value && sortBy == "desc") img = "{{ asset('img/desc_arrow.png') }}";
        return img;
    }

    function initTableHeader() {
        let header = `
            <th>ID <img onclick="tableHeaderSort('id');" src="${utilGetImg('id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Code <img onclick="tableHeaderSort('name');" src="${utilGetImg('name')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Start Date <img onclick="tableHeaderSort('start_date');" src="${utilGetImg('start_date')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>End Date <img onclick="tableHeaderSort('end_date');" src="${utilGetImg('end_date')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">In Percent <img onclick="tableHeaderSort('percent');" src="${utilGetImg('percent')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Visible <img onclick="tableHeaderSort('visible');" src="${utilGetImg('visible')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Updated At <img onclick="tableHeaderSort('updated_at');" src="${utilGetImg('updated_at')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Action</th>
        `;
        document.getElementById("table_header").innerHTML = header;
        document.getElementById("table_footer").innerHTML = header;
    }

    $(document).on('input', '#element_search', function() {
        searchText = document.getElementById("element_search").value;
        currentPage = 1;
        paginationGoPage(1);
    });

    function onVisibleSearchSelect() {
        if (visible_search) sortPublished = "1";
        else sortPublished = "0";
        if (unvisible_search) sortUnPublished = "1";
        else sortUnPublished = "0";
        currentPage = 1;
        paginationGoPage(1);
    }
</script>

<script type="text/javascript">
    // Tabs
    $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
        var target = $(e.target).attr("href");
        if (target != "#edit") {
            document.getElementById("tabEdit").style.display = "none";
        }
        if (target == "#create") {
            // Clear form
            clearForm();
            document.getElementById('createForm').appendChild(document.getElementById("form"));
        }
        if (target == "#home") {
            // clearForm();
        }
    });

    let editId = 0;

    function editItem(id) {
        let url = `{{ route('admin.coupon-get', ':id') }}`;
        url = url.replace(':id', id);
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: url,
            data: {},
            success: function(data) {
                loadingIcon('loading-bar', 0);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                document.getElementById("tabEdit").style.display = "block";
                $('.nav-tabs a[href="#edit"]').tab('show');
                let target = document.getElementById("form");
                document.getElementById('editForm').appendChild(target);

                editId = data.data.id;
                document.getElementById("code").value = data.data.name;
                document.getElementById("discount").value = data.data.discount;
                document.getElementById("minimum_purchase_amount").value = data.data.min_amount;
                document.getElementById("desc").value = data.data.desc;
                onSetCheck_percent(data.data.percent);
                onSetCheck_visible(data.data.visible);
                document.getElementById("datetime1").value = data.data.start_date;
                document.getElementById("datetime2").value = data.data.end_date;
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    $("#name,#address").on('keyup', function(event) {
        let el = event.target;
        if (el.id == 'name' || el.id == 'desc') {
            el.classList.remove('is-valid', 'is-invalid');
            if (el.value.length > 0) {
                el.classList.add('is-valid');
            } else {
                el.classList.add('is-invalid');
            }
        }

        if (el.id == 'price') {
            el.classList.remove('is-valid', 'is-invalid');
            if (el.value && el.value != 0) {
                el.classList.add('is-valid');
            } else {
                el.classList.add('is-invalid');
            }
        }

        if (el.id == 'discount_price') {
            el.classList.remove('is-valid');
            if (el.value && el.value != 0) {
                el.classList.add('is-valid');
            }
        }
    });

    function onSave() {
        let name = document.getElementById("code").value;
        let discount = document.getElementById("discount").value;
        let amount = document.getElementById("minimum_purchase_amount").value;
        let desc = document.getElementById("desc").value;
        let start_date = document.getElementById("datetime1").value;
        let end_date = document.getElementById("datetime2").value;

        // Check invalid fields
        if (
            messageStatus('error', 'Error', {
                msg: "The Name field is required"
            }, !name) ||
            messageStatus('error', 'Error', {
                msg: "The Discount field is required"
            }, !discount) ||
            messageStatus('error', 'Error', {
                msg: "The Minimum Purchase Amount field is required"
            }, !amount) ||
            messageStatus('error', 'Error', {
                msg: "The Description field is required"
            }, !desc) ||
            messageStatus('error', 'Error', {
                msg: "The Start Date field is required"
            }, !start_date) ||
            messageStatus('error', 'Error', {
                msg: "The End Date field is required"
            }, !end_date)
        ) {
            return;
        }

        let data = {
            id: editId,
            name: name,
            discount: discount,
            min_amount: amount,
            desc: desc,
            start_date: start_date,
            end_date: end_date,
            percent: (percent) ? 1 : 0,
            visible: (visible) ? 1 : 0,
        }
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.coupon-add') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                if (data.error) {
                    return messageStatus('error', 'Error', data);
                } else if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                if (editId != 0) {
                    paginationGoPage(currentPage);
                } else {
                    var text = buildOneItem(data.data);
                    var text2 = document.getElementById("table_body").innerHTML;
                    document.getElementById("table_body").innerHTML = text + text2;
                }
                $('.nav-tabs a[href="#home"]').tab('show');
                clearForm();
                messageStatus('success', 'Success', {
                    msg: "Data saved successfully"
                });
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log('err res: ', e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    // Clear forms
    function clearForm() {
        editId = 0;
        document.getElementById("code").innerHTML = "";
        document.getElementById("discount").innerHTML = "";
        document.getElementById("minimum_purchase_amount").innerHTML = "";
        document.getElementById("desc").innerHTML = "";
        document.getElementById("datetime1").value = "";
        document.getElementById("datetime2").value = "";
        onSetCheck_percent(false); // percent
        onSetCheck_visible(true); // published
    }
</script>
