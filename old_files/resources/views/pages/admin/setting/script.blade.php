<script type="text/javascript">
    let data = @json($data);

    $("#name,#address").on('keyup', function(event) {
        let el = event.target;
        if (el.id == 'name' || el.id == 'desc') {
            el.classList.remove('is-valid', 'is-invalid');
            if (el.value.length > 0) {
                el.classList.add('is-valid');
            } else {
                el.classList.add('is-invalid');
            }
        }

        if (el.id == 'price') {
            el.classList.remove('is-valid', 'is-invalid');
            if (el.value && el.value != 0) {
                el.classList.add('is-valid');
            } else {
                el.classList.add('is-invalid');
            }
        }

        if (el.id == 'discount_price') {
            el.classList.remove('is-valid');
            if (el.value && el.value != 0) {
                el.classList.add('is-valid');
            }
        }
    });

    function onSave() {
        let name = document.getElementById("name").value;
        let address = document.getElementById("address").value;
        let phone = document.getElementById("phone").value;
        let mobilePhone = document.getElementById("mobile-phone").value;
        let fee = document.getElementById("delivery-fee").value;
        let minimumAmount = document.getElementById("minimum-amount").value;
        let minimumDiscountAmount = document.getElementById("minimum-discount-amount").value;
        let discountAmount = document.getElementById("discount-amount").value;
        let tax = document.getElementById("tax").value;
        let pointAmount = document.getElementById("point_amount").value;

        // Check invalid fields
        if (
            messageStatus('error', 'Error', {
                msg: "The Name field is required"
            }, !name) ||
            messageStatus('error', 'Error', {
                msg: "The Phone field is required"
            }, !phone) ||
            messageStatus('error', 'Error', {
                msg: "The Address field is required"
            }, (!address && !lat && !lng))
        ) {
            return;
        }

        let data = {
            name: name,
            phone: phone,
            image: imageid3,
            mobile_phone: mobilePhone,
            address: address,
            lat: lat,
            lng: lng,
            min_purchase_amount: minimumAmount,
            min_discount_amount: minimumDiscountAmount,
            discount_amount: discountAmount,
            tax: tax,
            point_amount: pointAmount,
            fee: fee,
            percent: percent ? 1 : 0,
            per_km: perKm ? 1 : 0,
            flat_rate: flatRate ? 1 : 0,
            openTimeMonday: document.getElementById("openTimeMondayEdit").value,
            closeTimeMonday: document.getElementById("closeTimeMondayEdit").value,
            openTimeTuesday: document.getElementById("openTimeTuesdayEdit").value,
            closeTimeTuesday: document.getElementById("closeTimeTuesdayEdit").value,
            openTimeWednesday: document.getElementById("openTimeWednesdayEdit").value,
            closeTimeWednesday: document.getElementById("closeTimeWednesdayEdit").value,
            openTimeThursday: document.getElementById("openTimeThursdayEdit").value,
            closeTimeThursday: document.getElementById("closeTimeThursdayEdit").value,
            openTimeFriday: document.getElementById("openTimeFridayEdit").value,
            closeTimeFriday: document.getElementById("closeTimeFridayEdit").value,
            openTimeSaturday: document.getElementById("openTimeSaturdayEdit").value,
            closeTimeSaturday: document.getElementById("closeTimeSaturdayEdit").value,
            openTimeSunday: document.getElementById("openTimeSundayEdit").value,
            closeTimeSunday: document.getElementById("closeTimeSundayEdit").value,
            about: aboutUsData ? aboutUsData.root.innerHTML : document.getElementById("about-us")
                .value, // aboutUsData.getData()
            about_visible: about_visible ? 1 : 0,
            terms: termsConditionData ? termsConditionData.root.innerHTML : document.getElementById(
                "terms-condition").value,
            terms_visible: terms_visible ? 1 : 0,
            privacy: privacyPolicyData ? privacyPolicyData.root.innerHTML : document.getElementById(
                "privacy-policy").value,
            privacy_visible: privacy_visible ? 1 : 0,
            delivery: deliveryInfoData ? deliveryInfoData.root.innerHTML : document.getElementById("delivery-info")
                .value,
            delivery_visible: delivery_visible ? 1 : 0,
            refund: refundPolicyData ? refundPolicyData.root.innerHTML : document.getElementById("refund-policy")
                .value,
            refund_visible: refund_visible ? 1 : 0,
            fb_link: document.getElementById("facebook_link").value,
            fb_visible: facebook_visible ? 1 : 0,
            yt_link: document.getElementById("youtube_link").value,
            yt_visible: youtube_visible ? 1 : 0,
            insta_link: document.getElementById("instagram_link").value,
            insta_visible: instagram_visible ? 1 : 0,
            tiktok_link: document.getElementById("tiktok_link").value,
            tiktok_visible: tiktok_visible ? 1 : 0,
            telegram_link: document.getElementById("telegram_link").value,
            telegram_visible: telegram_visible ? 1 : 0,
        }

        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.settings-add') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                if (data.error) {
                    return messageStatus('error', 'Error', data);
                } else if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                setForm(data.data);
                return messageStatus('success', 'Success', {
                    msg: "Data updated successfully"
                });
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log('err res: ', e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    // Clear forms
    function setForm(data) {
        loadingIcon('loading-bar', 1);
        document.getElementById("name").value = data.company.name;
        document.getElementById("address").value = data.company.address;
        lat = data.company.lat;
        lng = data.company.lng;
        document.getElementById("phone").value = data.company.phone;
        document.getElementById("mobile-phone").value = data.company.mobile_phone;
        document.getElementById("delivery-fee").value = data.company.fee;
        document.getElementById("fee-amount").innerHTML = data.company.fee + "ETB";
        let is_percent = {{ $data['company']->percent }};
        let is_perKm = {{ $data['company']->per_km }};
        let is_flatRate = {{ $data['company']->flat_rate }};
        if (is_percent) onSetCheck_percent(true);
        else if (is_perKm) onSetCheck_perKm(true);
        else if (is_flatRate) onSetCheck_flatRate(true);

        document.getElementById("minimum-amount").value = data.company.min_purchase_amount;
        document.getElementById("minimum-discount-amount").value = data.company.min_discount_amount;
        document.getElementById("discount-amount").value = data.company.discount_amount;
        document.getElementById("tax").value = data.company.tax;
        document.getElementById("point_amount").value = data.point_amount;

        // Monday
        document.getElementById("openTimeMondayEdit").value = data.company.openTimeMonday;
        document.getElementById("closeTimeMondayEdit").value = data.company.closeTimeMonday;
        // Tuesday
        document.getElementById("openTimeTuesdayEdit").value = data.company.openTimeTuesday;
        document.getElementById("closeTimeTuesdayEdit").value = data.company.closeTimeTuesday;
        // Wednesday
        document.getElementById("openTimeWednesdayEdit").value = data.company.openTimeWednesday;
        document.getElementById("closeTimeWednesdayEdit").value = data.company.closeTimeWednesday;
        // Thursday
        document.getElementById("openTimeThursdayEdit").value = data.company.openTimeThursday;
        document.getElementById("closeTimeThursdayEdit").value = data.company.closeTimeThursday;
        // Friday
        document.getElementById("openTimeFridayEdit").value = data.company.openTimeFriday;
        document.getElementById("closeTimeFridayEdit").value = data.company.closeTimeFriday;
        // Saturday
        document.getElementById("openTimeSaturdayEdit").value = data.company.openTimeSaturday;
        document.getElementById("closeTimeSaturdayEdit").value = data.company.closeTimeSaturday;
        // Sunday
        document.getElementById("openTimeSundayEdit").value = data.company.openTimeSunday;
        document.getElementById("closeTimeSundayEdit").value = data.company.closeTimeSunday;

        if (!!data.about) {
            if (!!aboutUsData) {
                // aboutUsData.setData(data.about);
                aboutUsData.clipboard.dangerouslyPasteHTML(0, data.about);
            } else {
                document.getElementById("about-us").value = data.about;
            }
            onSetCheck_about_visible(data.about_visible);
        }
        if (!!data.terms) {
            if (!!termsConditionData) {
                // termsConditionData.setData(data.terms);
                termsConditionData.clipboard.dangerouslyPasteHTML(0, data.terms);
            } else {
                document.getElementById("terms-condition").value = data.terms;
            }
            onSetCheck_terms_visible(data.terms_visible);
        }
        if (!!data.privacy) {
            if (!!privacyPolicyData) {
                // privacyPolicyData.setData(data.privacy);
                privacyPolicyData.clipboard.dangerouslyPasteHTML(0, data.privacy);
            } else {
                document.getElementById("privacy-policy").value = data.privacy;
            }
            onSetCheck_privacy_visible(data.privacy_visible);
        }
        if (!!data.delivery) {
            if (!!deliveryInfoData) {
                // deliveryInfoData.setData(data.delivery);
                deliveryInfoData.clipboard.dangerouslyPasteHTML(0, data.delivery);
            } else {
                document.getElementById("delivery-info").value = data.delivery;
            }
            onSetCheck_delivery_visible(data.delivery_visible);
        }
        if (!!data.refund) {
            if (!!refundPolicyData) {
                // refundPolicyData.setData(data.refund);
                refundPolicyData.clipboard.dangerouslyPasteHTML(0, data.refund);
            } else {
                document.getElementById("delivery-info").value = data.refund;
            }
            onSetCheck_refund_visible(data.refund_visible);
        }

        clearDropZone3();
        if (data.company.image) {
            addEditImage3(data.company.image);
        }

        // Social media links
        // facebook
        if (!!data.facebook) {
            document.getElementById("facebook_link").value = data.facebook;
            onSetCheck_facebook_visible(data.facebook_visible);
        }
        // youtube
        if (!!data.youtube) {
            document.getElementById("youtube_link").value = data.youtube;
            onSetCheck_youtube_visible(data.youtube_visible);
        }
        // instagram
        if (!!data.instagram) {
            document.getElementById("instagram_link").value = data.instagram;
            onSetCheck_instagram_visible(data.instagram_visible);
        }
        // tiktok
        if (!!data.tiktok) {
            document.getElementById("tiktok_link").value = data.tiktok;
            onSetCheck_tiktok_visible(data.tiktok_visible);
        }
        // telegram
        if (!!data.telegram) {
            document.getElementById("telegram_link").value = data.telegram;
            onSetCheck_telegram_visible(data.telegram_visible);
        }
        loadingIcon('loading-bar', 0);
    }

    const deliveryTypes = document.querySelectorAll('#flatRate, #perKm, #percent');
    deliveryTypes.forEach(function(type) {
        type.addEventListener('click', function(e) {
            // flatRate, perKm, percent
            let id = e.target.id;
            if (id == 'flatRate' && flatRate) {
                onSetCheck_percent(false);
                onSetCheck_perKm(false);
                onSetCheck_flatRate(true);
            }
            if (id == 'perKm' && perKm) {
                onSetCheck_percent(false);
                onSetCheck_flatRate(false);
                onSetCheck_perKm(true);
            }
            if (id == 'percent' && percent) {
                onSetCheck_perKm(false);
                onSetCheck_flatRate(false);
                onSetCheck_percent(true);
            }
        });
    });

    document.getElementById("delivery-fee").addEventListener('input', function(e) {
        document.getElementById("fee-amount").innerHTML = e.target.value + "ETB";
    });

    setForm(data);
</script>
