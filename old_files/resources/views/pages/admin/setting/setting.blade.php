@extends('layouts.dashboard-layout')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card card-user">
                <!-- Tab List -->
                <div id="myTabContent" class="card-body">
                    <div class="tab-content text-left">

                        <!-- Tab View - Listings -->
                        <div role="tabpanel" class="tab-pane active" id="home">
                            <div class="card-header">
                                <h5 class="card-title">Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 col-12">
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.text', [
                                                    'label' => 'Company Name',
                                                    'placeholder' => 'eg: LG TV',
                                                    'desc' => 'Insert Company Name',
                                                    'id' => 'name',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.address', [
                                                    'label' => 'Company Address',
                                                    'text' => 'Insert address',
                                                    'id' => 'address',
                                                    'request' => 'false',
                                                    'maxlength' => '100',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.text', [
                                                    'label' => 'Company Phone',
                                                    'placeholder' => '',
                                                    'desc' => 'Insert phone',
                                                    'id' => 'phone',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.text', [
                                                    'label' => 'Company Mobile Phone',
                                                    'placeholder' => '',
                                                    'desc' => 'Insert mobile phone',
                                                    'id' => 'mobile-phone',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.price', [
                                                    'label' => 'Delivery fee',
                                                    'id' => 'delivery-fee',
                                                    'desc' => 'Insert delivery fee',
                                                    'request' => 'true',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                <!-- featured -->
                                                @include('components.input-field.checkbox-2', [
                                                    'id' => 'flatRate',
                                                    'text' => 'Flat Rate',
                                                    'initvalue' => 'false',
                                                ])
                                                <!-- visible -->
                                                @include('components.input-field.checkbox-2', [
                                                    'id' => 'perKm',
                                                    'text' => 'Per Km or mile',
                                                    'initvalue' => 'false',
                                                ])
                                                <!-- visible -->
                                                @include('components.input-field.checkbox-2', [
                                                    'id' => 'percent',
                                                    'text' => 'Percent',
                                                    'initvalue' => 'false',
                                                ])
                                            </div>
                                        </div>
                                        <div class="col-md-12 info" style="border-left: 3px solid #66c4de;">
                                            <h4>Delivery fee may be in percentages from order or a given amount.</h4>
                                            {{-- TODO: Update current delivery fee --}}
                                            <p>Current: <span id="fee-amount">0 ETB</span></p>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.price', [
                                                    'label' => 'Minimum purchase amount',
                                                    'id' => 'minimum-amount',
                                                    'desc' => 'For ex: 100. If 0 - no Minimum purchase amount',
                                                    'request' => 'true',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12 col-md-6">
                                                @include('components.input-field.price', [
                                                    'label' => 'Minimum discount amount',
                                                    'id' => 'minimum-discount-amount',
                                                    'desc' => 'For ex: 100. If 0 - no Minimum discount amount',
                                                    'request' => 'true',
                                                ])
                                            </div>
                                            <div class="col-12 col-md-6">
                                                @include('components.input-field.percent', [
                                                    'label' => 'Discount amount',
                                                    'id' => 'discount-amount',
                                                    'desc' => '',
                                                    'request' => '',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.percent', [
                                                    'label' => 'TAX',
                                                    'id' => 'tax',
                                                    'desc' => 'Insert TAX for this vendor in percentages',
                                                    'request' => '',
                                                ])
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.input-field.price', [
                                                    'label' => '1 Point to ETB',
                                                    'id' => 'point_amount',
                                                    'desc' => 'Insert one point amount in ETB',
                                                    'request' => '',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                        @include('components.input-field.image', [])

                                        @include('components.input-field.time-table', [
                                            'datas' => [
                                                'Monday',
                                                'Tuesday',
                                                'Wednesday',
                                                'Thursday',
                                                'Friday',
                                                'Saturday',
                                                'Sunday',
                                            ],
                                        ])
                                    </div>

                                    <div class="col-12">
                                        {{-- About us --}}
                                        <div class="row">
                                            <div class="col-12">
                                                <div id="element_about-us">
                                                    <div class="form-group">
                                                        <label for="about-us">About us</label>
                                                        <div name="about-us" id="about-us"
                                                            class="form-control quill-editor w-full" style="width: 100%;">
                                                        </div>
                                                        <small id="about-usHelp" class="form-text text-muted">Insert about
                                                            us</small>
                                                    </div>
                                                </div>
                                                <div class="mb-4">
                                                    <!-- about us visible -->
                                                    @include('components.input-field.checkbox-2', [
                                                        'id' => 'about_visible',
                                                        'text' => 'About us visibility',
                                                        'initvalue' => 'false',
                                                    ])
                                                </div>
                                            </div>
                                            <script>
                                                const aboutUsData = new Quill('#about-us', {
                                                    modules: {
                                                        toolbar: [
                                                            [{
                                                                header: [1, 2, 3, false]
                                                            }],
                                                            ['bold', 'italic', 'underline'],
                                                            ['image', 'code-block'],
                                                        ],
                                                    },
                                                    placeholder: 'Write About us page descriptions...',
                                                    theme: 'snow'
                                                });
                                            </script>
                                            {{-- <script>
                                                let aboutUsData;
                                                ClassicEditor.create(document.querySelector('#about-us'))
                                                    .then(editor => {
                                                        // console.log('about Editor was initialized', editor);
                                                        aboutUsData = editor;
                                                    })
                                                    .catch(error => {
                                                        console.error(error);
                                                    });
                                            </script> --}}
                                        </div>


                                        {{-- Terms and Condition --}}
                                        <div class="row">
                                            <div class="col-12">
                                                <div id="element_terms-condition">
                                                    <div class="form-group">
                                                        <label for="terms-condition">Terms and Condition</label>
                                                        <div name="terms-condition" id="terms-condition"
                                                            class="form-control quill-editor w-full" style="width: 100%;">
                                                        </div>
                                                        <small id="terms-conditionHelp" class="form-text text-muted">Insert
                                                            terms and condition</small>
                                                    </div>
                                                </div>
                                                <div class="mb-4">
                                                    <!-- Terms and Condition visible -->
                                                    @include('components.input-field.checkbox-2', [
                                                        'id' => 'terms_visible',
                                                        'text' => 'Terms and Condition visibility',
                                                        'initvalue' => 'false',
                                                    ])
                                                </div>
                                            </div>
                                            <script>
                                                const termsConditionData = new Quill('#terms-condition', {
                                                    modules: {
                                                        toolbar: [
                                                            [{
                                                                header: [1, 2, 3, false]
                                                            }],
                                                            ['bold', 'italic', 'underline'],
                                                            ['image', 'code-block'],
                                                        ],
                                                    },
                                                    placeholder: 'Write About us page descriptions...',
                                                    theme: 'snow'
                                                });
                                            </script>
                                            {{-- <script>
                                                let termsConditionData;
                                                ClassicEditor.create(document.querySelector('#terms-condition'))
                                                    .then(editor => {
                                                        // console.log('term Editor was initialized', editor);
                                                        termsConditionData = editor;
                                                    })
                                                    .catch(error => {
                                                        console.error(error);
                                                    });
                                            </script> --}}
                                        </div>

                                        {{-- Privacy Policy --}}
                                        <div class="row">
                                            <div class="col-12">
                                                <div id="element_privacy-policy">
                                                    <div class="form-group">
                                                        <label for="privacy-policy">Privacy Policy</label>
                                                        <div name="privacy-policy" id="privacy-policy"
                                                            class="form-control quill-editor w-full" style="width: 100%;">
                                                        </div>
                                                        <small id="privacy-policyHelp" class="form-text text-muted">Insert
                                                            privacy policy</small>
                                                    </div>
                                                </div>
                                                <div class="mb-4">
                                                    <!-- Privacy Policy visible -->
                                                    @include('components.input-field.checkbox-2', [
                                                        'id' => 'privacy_visible',
                                                        'text' => 'Privacy Policy visibility',
                                                        'initvalue' => 'false',
                                                    ])
                                                </div>
                                            </div>
                                            <script>
                                                const privacyPolicyData = new Quill('#privacy-policy', {
                                                    modules: {
                                                        toolbar: [
                                                            [{
                                                                header: [1, 2, 3, false]
                                                            }],
                                                            ['bold', 'italic', 'underline'],
                                                            ['image', 'code-block'],
                                                        ],
                                                    },
                                                    placeholder: 'Write About us page descriptions...',
                                                    theme: 'snow'
                                                });
                                            </script>
                                            {{-- <script>
                                                let privacyPolicyData;
                                                ClassicEditor.create(document.querySelector('#privacy-policy'))
                                                    .then(editor => {
                                                        // console.log('privacy Editor was initialized', editor);
                                                        privacyPolicyData = editor;
                                                    })
                                                    .catch(error => {
                                                        console.error(error);
                                                    });
                                            </script> --}}
                                        </div>

                                        {{-- Delivery info --}}
                                        <div class="row">
                                            <div class="col-12">
                                                <div id="element_delivery-info">
                                                    <div class="form-group">
                                                        <label for="delivery-info">Delivery info</label>
                                                        <div name="delivery-info" id="delivery-info"
                                                            class="form-control quill-editor w-full" style="width: 100%;">
                                                        </div>
                                                        <small id="delivery-infoHelp" class="form-text text-muted">Insert
                                                            delivery info</small>
                                                    </div>
                                                </div>
                                                <div class="mb-4">
                                                    <!-- Delivery info visible -->
                                                    @include('components.input-field.checkbox-2', [
                                                        'id' => 'delivery_visible',
                                                        'text' => 'Delivery info visibility',
                                                        'initvalue' => 'false',
                                                    ])
                                                </div>
                                            </div>
                                            <script>
                                                const deliveryInfoData = new Quill('#delivery-info', {
                                                    modules: {
                                                        toolbar: [
                                                            [{
                                                                header: [1, 2, 3, false]
                                                            }],
                                                            ['bold', 'italic', 'underline'],
                                                            ['image', 'code-block'],
                                                        ],
                                                    },
                                                    placeholder: 'Write About us page descriptions...',
                                                    theme: 'snow'
                                                });
                                            </script>
                                            {{-- <script>
                                                let deliveryInfoData;
                                                ClassicEditor.create(document.querySelector('#delivery-info'))
                                                    .then(editor => {
                                                        // console.log('delivery Editor was initialized', editor);
                                                        deliveryInfoData = editor;
                                                    })
                                                    .catch(error => {
                                                        console.error(error);
                                                    });
                                            </script> --}}
                                        </div>

                                        {{-- Refund Policy --}}
                                        <div class="row">
                                            <div class="col-12">
                                                <div id="element_refund-policy">
                                                    <div class="form-group">
                                                        <label for="refund-policy">Refund Policy</label>
                                                        <div name="refund-policy" id="refund-policy"
                                                            class="form-control quill-editor w-full" style="width: 100%;">
                                                        </div>
                                                        <small id="refund-policyHelp" class="form-text text-muted">Insert
                                                            refund policy</small>
                                                    </div>
                                                </div>
                                                <div class="mb-4">
                                                    <!-- Refund Policy visible -->
                                                    @include('components.input-field.checkbox-2', [
                                                        'id' => 'refund_visible',
                                                        'text' => 'Refund Policy visibility',
                                                        'initvalue' => 'false',
                                                    ])
                                                </div>
                                            </div>
                                            <script>
                                                const refundPolicyData = new Quill('#refund-policy', {
                                                    modules: {
                                                        toolbar: [
                                                            [{
                                                                header: [1, 2, 3, false]
                                                            }],
                                                            ['bold', 'italic', 'underline'],
                                                            ['image', 'code-block'],
                                                        ],
                                                    },
                                                    placeholder: 'Write About us page descriptions...',
                                                    theme: 'snow'
                                                });
                                            </script>
                                            {{-- <script>
                                                let refundPolicyData;
                                                ClassicEditor.create(document.querySelector('#refund-policy'))
                                                    .then(editor => {
                                                        // console.log('refund Editor was initialized', editor);
                                                        refundPolicyData = editor;
                                                    })
                                                    .catch(error => {
                                                        console.error(error);
                                                    });
                                            </script> --}}
                                        </div>
                                    </div>


                                    <div class="col-12">
                                        <h4 class="">Social Media Links</h4>

                                        <div>
                                            @include('components.input-field.text', [
                                                'label' => 'Facebook Link',
                                                'placeholder' => '',
                                                'desc' => 'Insert facebook link',
                                                'id' => 'facebook_link',
                                            ])
                                            <div class="mb-4">
                                                <!-- Refund Policy visible -->
                                                @include('components.input-field.checkbox-2', [
                                                    'id' => 'facebook_visible',
                                                    'text' => 'Facebook Link visibility',
                                                    'initvalue' => 'false',
                                                ])
                                            </div>
                                        </div>

                                        <div>
                                            @include('components.input-field.text', [
                                                'label' => 'YouTube Link',
                                                'placeholder' => '',
                                                'desc' => 'Insert youtube link',
                                                'id' => 'youtube_link',
                                            ])
                                            <div class="mb-4">
                                                <!-- Refund Policy visible -->
                                                @include('components.input-field.checkbox-2', [
                                                    'id' => 'youtube_visible',
                                                    'text' => 'YouTube Link visibility',
                                                    'initvalue' => 'false',
                                                ])
                                            </div>
                                        </div>

                                        <div>
                                            @include('components.input-field.text', [
                                                'label' => 'Instagram Link',
                                                'placeholder' => '',
                                                'desc' => 'Insert instagram link',
                                                'id' => 'instagram_link',
                                            ])
                                            <div class="mb-4">
                                                <!-- Refund Policy visible -->
                                                @include('components.input-field.checkbox-2', [
                                                    'id' => 'instagram_visible',
                                                    'text' => 'Instagram Link visibility',
                                                    'initvalue' => 'false',
                                                ])
                                            </div>
                                        </div>

                                        <div>
                                            @include('components.input-field.text', [
                                                'label' => 'Tiktok Link',
                                                'placeholder' => '',
                                                'desc' => 'Insert tiktok link',
                                                'id' => 'tiktok_link',
                                            ])
                                            <div class="mb-4">
                                                <!-- Refund Policy visible -->
                                                @include('components.input-field.checkbox-2', [
                                                    'id' => 'tiktok_visible',
                                                    'text' => 'Tiktok Link visibility',
                                                    'initvalue' => 'false',
                                                ])
                                            </div>
                                        </div>

                                        <div>
                                            @include('components.input-field.text', [
                                                'label' => 'Telegram Link',
                                                'placeholder' => '',
                                                'desc' => 'Insert telegram link',
                                                'id' => 'telegram_link',
                                            ])
                                            <div class="mb-4">
                                                <!-- Refund Policy visible -->
                                                @include('components.input-field.checkbox-2', [
                                                    'id' => 'telegram_visible',
                                                    'text' => 'Telegram Link visibility',
                                                    'initvalue' => 'false',
                                                ])
                                            </div>
                                        </div>
                                    </div>

                                    {{-- <div class="col-12">
                                        <div class="main-container">
                                            {{-- <textarea id="editor"></textarea>
                                        </div>
                                    </div> --}}
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        @include('components.buttons.button', [
                                            'label' => 'Save',
                                            'id' => 'on-save',
                                            'onclick' => 'onSave();',
                                            'block' => '1',
                                        ])
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Tab View - Listings -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script -->
    @include('pages.admin.setting.script')
@endsection
