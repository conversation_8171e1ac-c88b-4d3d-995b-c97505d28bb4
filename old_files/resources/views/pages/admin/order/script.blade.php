{{-- @foreach ($iorderstatus as $key => $idata)
    if ({{ $idata['id'] }} != 4) {
        if ('{{ $idata['name'] }}' == item.order_status)
            text3 +=
            `<option id="role${item.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" selected style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
        else
            text3 +=
            `<option id="role${item.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
    }
@endforeach --}}
{{-- @foreach ($iorderstatus as $key => $idata)
            if ({{ $idata['id'] }} != 4) {
                if ('{{ $idata['name'] }}' == data.order.order_status)
                    text3 +=
                    `<option id="role${data.order.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" selected style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
                else
                    text3 +=
                    `<option id="role${data.order.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
            }
        @endforeach --}}
        {{-- let text2 = '';
        @foreach ($iorderstatus as $key => $idata)
            if ('{{ $idata['name'] }}' == item.order_status)
                text2 = '{{ $idata['name'] }}';
        @endforeach --}}

        {{-- if (item.curbside_pickup == "true") {
            @foreach ($iorderstatus as $key => $idata)
                if ({{ $idata['id'] }} != 4) {
                    if ('{{ $idata['name'] }}' == item.order_status)
                        text3 +=
                        `<option id="role${item.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" selected style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
                    else
                        text3 +=
                        `<option id="role${item.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
                }
            @endforeach
        } else {
            @foreach ($iorderstatus as $key => $idata)
                console.log("text3: ", '{{ $idata['name'] }}', item.order_status, '{{ $idata['name'] }}' == item
                    .order_status);
                if ('{{ $idata['name'] }}' == item.order_status)
                    text3 +=
                    `<option id="role${item.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" selected style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
                else
                    text3 +=
                    `<option id="role${item.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
            @endforeach
        } --}}
<script>
    // Product listsings
    let pages = 1;
    let currentPage = 1;
    let sortCat = 0;
    let searchText = "";
    let sort = "updated_at";
    let sortBy = "desc";
    let data;
    let datas;

    paginationGoPage(1);
    initPaginationLine2(pages, currentPage, 'pagination-list', paginationGoPage);
    // initPaginationLine(pages, currentPage);
    initTableHeader();
    initTableFooter();

    function paginationGoPage(page) {
        data = {
            page: page,
            sortAscDesc: sortBy,
            sortBy: sort,
            cat: sortCat,
            search: searchText,
        };

        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: '{{ route('admin.orders-get') }}',
            data: data,
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('orders get', data);
                if (data.error != 0 || data.data == null) {
                    return showNotification('danger', 'Something went wrong', 'top', 'right');
                }
                currentPage = data.page;
                pages = data.pages;
                datas = data.data;
                initDataTable(datas);
                initPaginationLine2(pages, currentPage, 'pagination-list', paginationGoPage);
                // initPaginationLine(pages, currentPage);
                initTableHeader();
                initTableFooter();
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                dataLoading = false;
                console.log(e);
            }
        });
    }

    function initDataTable(data) {
        html = "";
        if (data.length == 0) {
            html = `
            <tr>
                <td colspan="7">
                    <div class="d-flex justify-content-center align-items-center">
                        <p class="m-0">There are no records</p>
                    </div>
                </td>
            </tr>
            `;
            document.getElementById("table_body").innerHTML = html;
        } else {
            data.forEach(function(item, i, arr) {
                html += buildOneItem(item);
            });
            document.getElementById("table_body").innerHTML = html;
        }
    }

    function buildOneItem(item) {
        let text = `<div>`;
        if (item.curbside_pickup == true) {
            text = `<div><span class="badge badge-pill badge-warning">Curbside Pickup</span>`;
            if (item.arrived == "true")
                text += `<span class="badge badge-pill badge-warning">Customer arrived</span>`;
            text += "<br>";
        }
        text += `<span class="badge badge-pill badge-primary">${item.payment_method ? item.payment_method.name : '-'}</span>`;
        text += `</div>`;
        let text3 = "";
        text3 += `<option id="role0_0" value="0" selected>-</option>`;
        @foreach ($ipaymentstatus as $key => $idata)
            if ('{{ $idata['id'] }}' == (item.is_complete + item.view)) {
                text3 +=
                    `<option id="role${item.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" selected style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
            } else {
                text3 +=
                    `<option id="role${item.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
            }
        @endforeach
        let priceView = `${item.order_total1}`;
        if (item.order_total2 != 0) {
            priceView =
                `<div><span style="text-decoration: line-through;">${item.order_total1}</span><br /><span style="font-weight: 600;font-size: medium;">${item.order_total22}</span></div>`;
        }
        // console.log('order item: ', item);

        // status
        // if (item.order_status == '5') {
        //     let button = `<button type="button" class="btn btn-success btn-round isDisabled">
        //         Done
        //     </button>`;
        // } else if (item.order_status == '6') {
        //     let button = `<button type="button" class="btn btn-warning btn-round isDisabled">
        //         Cancelled
        //     </button>`;
        // } else {
        //     button = `<button type="button" class="btn btn-success btn-round" onclick="showDoneMessage('${item.id}', '{{ url('orderdone') }}')">
        //         Done
        //     </button>`;
        // }
        // ${button}       {{-- Done --}}

        return `
            <tr>
                <td>${item.id}</td>
                <td>${priceView}</td>
                <td>${item.user.name}</td>
                <td>
                    <select name="role" id="role" class="form-control show-tick" onchange="updatePaymentStatus(event, ${item.id})" >
                        ${text3}
                    </select>
                </td>
                <td>${text}</td>
                <td>${item.order_comment}</td>
                <td>
                    <div class="title text-info">${item.timeago}</div>
                    ${item.updated_at}
                </td>
                <td>
                    <button type="button" class="btn btn-info btn-round" onclick="viewItem('${item.id}')">
                        View
                    </button>
                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '{{ route('admin.order-delete') }}')">
                        Delete
                    </button>
                </td>
            </tr>
        `;
    }

    function initPaginationLine(pages, page) {
        allPages = pages;
        currentPage = page;
        let pageNumbers = '';
        pageNumbers = buildPagination(currentPage, allPages);
        let paginationList = document.getElementById('pagination-list');
        paginationList.innerHTML = pageNumbers;
        initializePaginations(paginationGoPage);
    }

    function initPaginationLine2(pages, page, id, callback) {
        allPages = pages;
        currentPage = page;
        let pageNumbers = '';
        pageNumbers = buildPagination(currentPage, allPages);
        let paginationList = document.getElementById(id);
        if (paginationList) {
            paginationList.innerHTML = pageNumbers;
            initializePaginations(callback, id);
        }
    }

    function tableHeaderSort(newsort) {
        if (newsort == sort) {
            if (sortBy == "asc")
                sortBy = "desc";
            else
                sortBy = "asc";
        } else {
            sort = newsort
            sortBy = "asc";
        }
        paginationGoPage(currentPage);
    }

    function utilGetImg(value) {
        let img = "{{ asset('img/arrow_noactive.png') }}";
        if (sort == value && sortBy == "asc") img = "{{ asset('img/asc_arrow.png') }}";
        if (sort == value && sortBy == "desc") img = "{{ asset('img/desc_arrow.png') }}";
        return img;
    }

    function initTableHeader() {
        // <th>Order status</th>
        let header = `
            <th>Id <img onclick="tableHeaderSort('orders.id');" src="${utilGetImg('orders.id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Total <img onclick="tableHeaderSort('orders.order_total');" src="${utilGetImg('orders.order_total')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>User <img onclick="tableHeaderSort('user.name');" src="${utilGetImg('user.name')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Payment status</th>
            <th>Payment</th>
            <th>Comment</th>
            <th>Updated At <img onclick="tableHeaderSort('orders.updated_at');" src="${utilGetImg('orders.updated_at')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Action</th>
        `;
        document.getElementById("table_header").innerHTML = header;
    }

    function initTableFooter() {
        // <th>Order status</th>
        let footer = `
            <th>Id <img onclick="tableHeaderSort('id');" src="${utilGetImg('orders.id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Total <img onclick="tableHeaderSort('order_total');" src="${utilGetImg('orders.order_total')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>User <img onclick="tableHeaderSort('user.name');" src="${utilGetImg('user.name')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Payment status</th>
            <th>Payment</th>
            <th>Comment</th>
            <th>Updated At <img onclick="tableHeaderSort('orders.updated_at');" src="${utilGetImg('orders.updated_at')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Action</th>
        `;
        document.getElementById("table_footer").innerHTML = footer;
    }

    function onCatSearchSelect(object) {
        sortCat = object.value;
        currentPage = 1;
        paginationGoPage(currentPage);
    }

    function onRestSearchSelect(object) {
        sortRest = object.value;
        currentPage = 1;
        paginationGoPage(currentPage);
    }

    $(document).on('input', '#element_search', function() {
        searchText = document.getElementById("element_search").value;
        currentPage = 1;
        paginationGoPage(1);
    });

    function onVisibleSearchSelect(e) {
        // console.log('status change: ', e);
        currentPage = 1;
        paginationGoPage(1);
    }

    $(document).ready(function() {
        @if ($edit != '')
            viewItem({{ $edit }});
        @endif
    });

    // console.log("edit data: ", "{{ $edit }}");

    // View order
    document.getElementById('order_detail_pagination-list').innerHTML = "";

    let order;

    function viewItem(id) {
        // open view edit
        document.getElementById("tabView").style.display = "block";
        $('.nav-tabs a[href="#view"]').tab('show');
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.order-view') }}',
            data: {
                id: id
            },
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log("order detail res: ", data);
                if (!data.error) {
                    order = data;
                    initOrderDetailDataTable(data);
                    fillForm(data.order);
                    initOrderDetailDataProductListTable(data.ordersdetails);
                    // document.getElementById("viewOrderID").innerHTML = data.order.id;
                    // document.getElementById("viewClient").innerHTML = data.user ? data.user.name : 'none';
                }
                return;
                let dataOrder = data;
                // console.log("dataOrder " + dataOrder);
                //
                //
                // console.log(data);

                document.getElementById("viewClientPhone").innerHTML = data.order.phone;
                if (data.order.curbsidePickup == "true") {
                    var xtext = "<span class=\"badge bg-red\">Curbside Pickup</span>";
                    if (data.order.arrived == "true")
                        xtext = xtext + "<span class=\"badge bg-red\">Customer arrived</span>";
                    document.getElementById("viewClientAddress").innerHTML = xtext;
                } else
                    document.getElementById("viewClientAddress").innerHTML = data.order.address;

                document.getElementById("viewCreatedAt").innerHTML = data.order.created_at;
                if (data.company != null) {
                    document.getElementById("viewCompany").innerHTML = data.company.name;
                }
                // if ($userinfo->getUserRoleId() == '2')
                //     // Drivers
                //     if (data.order.curbsidePickup == "true") {
                //         document.getElementById("viewDriverText").hidden = true;
                //         document.getElementById("viewDriver").innerHTML = "";
                //     } else {
                //         document.getElementById("viewDriverText").hidden = false;
                //         document.getElementById("viewDriver").innerHTML = "";
                //         var selectList = document.createElement("select");
                //         selectList.options.liveSearch = true;
                //         // console.log("selectList.options.liveSearch " + selectList.options.liveSearch);
                //         selectList.className = "show-tick";
                //         selectList.onchange = function(event) {
                //             // console.log(event);
                //             checkDriver(event, data.order.id);
                //         };
                //         // $('.show-tick').selectpicker('refresh')
                //     }
                // endif

                document.getElementById("viewUpdatedAt").innerHTML = data.order.updated_at;
                document.getElementById("viewCompanyAddress").innerHTML = data.company.address;
                if (data.driver != null) {
                    document.getElementById("viewDriverFee").innerHTML = data.driver.fee;
                }
                document.getElementById("viewCompanyPhone").innerHTML = data.company.phone;
                document.getElementById("viewCompanyMobilePhone").innerHTML = data.company.mobilephone;


                if (data.order.curbsidePickup == 'false' && data.order.movet_response !=
                    'Request Completed') {
                    var button =
                        `<button type="button" onclick="deliverProduct(${data.order.id})" class="q-btn-all btn-primary waves-effect">Movet Delivery</button> {{-- Delivery Info --}} `;
                } else if (data.order.status == '4' && data.order.movet_response === 'Request Completed') {
                    button =
                        `<button type="button" class="q-btn-all btn-warning waves-effect">Delivery on the way!</button> {{-- Delivery Info --}}`;
                } else if (data.order.status == '5') {
                    button =
                        `<button type="button" class="q-btn-all btn-success waves-effect">Delivery Completed!</button> {{-- Delivery Info --}}`;
                } else if (data.order.status == '6') {
                    button =
                        `<button type="button" class="q-btn-all btn-danger waves-effect">Delivery Cancelled!</button> {{-- Delivery Info --}}`;
                } else if (data.order == 'false') {
                    button =
                        `<button type="button" class="isDisabled q-btn-all btn-primary waves-effect">Deliver Product</button> {{-- Delivery Info --}}`;
                } else {
                    button = `None`;
                }

                document.getElementById("viewMethod").innerHTML = data.order.method;
                document.getElementById("deliveryInfo").innerHTML = button;
                document.getElementById("viewHint").innerHTML = data.order.hint;


                //
                // details
                //
                // showDetails(data.ordertimes, data.orderstatuses, data.drivers);

                //
                // table
                //
                addTableWithDishes();
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
            }
        });
        initOrderDetailTableHeader();
        // initOrderDetailDataTable();

        initOrderDetailProductListTableHeader();
        // initOrderDetailDataProductListTable();
    }

    $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
        var target = $(e.target).attr("href")
        if (target != "#view")
            document.getElementById("tabView").style.display = "none";
    });
</script>

<script>
    function initOrderDetailTableHeader() {
        // <th>Client Address</th>
        let header = `
            <th>Order Id</th>
            <th>Client</th>
            <th>Client Phone</th>
            <th>Status</th>
            <th>Payment</th>
            <th>Comment</th>
            <th>Created At</th>
        `;
        document.getElementById("order_detail_table_header").innerHTML = header;
    }

    function initOrderDetailDataTable(data) {
        let text = `<div>`;
        if (data.order.curbside_pickup == true) {
            text = `<div><span class="badge badge-pill badge-warning">Curbside Pickup</span>`;
            if (data.order.arrived == "true")
                text += `<span class="badge badge-pill badge-warning">Customer arrived</span>`;
            text += "<br>";
        }
        text += `<span class="badge badge-pill badge-primary">${data.order.payment_method.name}</span>`;
        text += `</div>`;
        html = "";
        // <td>${data.order.dropoff_address_id}</td>
        let text3 = "";
        text3 += `<option id="role0_0" value="0" selected>-</option>`;
        @foreach ($ipaymentstatus as $key => $idata)
            if ('{{ $idata['id'] }}' == data.order.is_complete) {
                text3 +=
                    `<option id="role${data.order.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" selected style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
            } else {
                text3 +=
                    `<option id="role${data.order.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
            }
        @endforeach

        // <td>${data.order.order_status ?? '-'}</td>
        html += `
            <tr>
                <td>${data.order.id}</td>
                <td>${data.user.name}</td>
                <td>${data.user.phone ? data.user.phone : '-'}</td>
                <td>
                    <select name="role" id="role" class="form-control show-tick" onchange="updatePaymentStatus(event, ${data.order.id})" >
                        ${text3}
                    </select>
                </td>
                <td>${text}</td>
                <td>${data.order.order_comment}</td>
                <td>
                    <div class="title text-info">${data.order.created_at2}</div>
                    ${data.order.created_at}
                </td>
            </tr>
        `;
        document.getElementById("order_detail_table_body").innerHTML = html;

        if (data.order.coupon_name) {
            document.getElementById("coupon-view").hidden = false;
            document.getElementById("couponName").innerHTML = data.order.coupon_name;
        } else {
            document.getElementById("coupon-view").hidden = true;
        }
        if (data.order.coupon_discount_amount && data.order.coupon_discount_amount != 0) {
            console.log('data.order.coupon_discount_amount: ', data.order.coupon_discount_amount);
            document.getElementById("coupon-discount-view").hidden = false;
            document.getElementById("couponDiscountVal").innerHTML = data.order.coupon_discount_amount2;
        } else {
            document.getElementById("coupon-discount-view").hidden = true;
        }
        let subtotalPriceView = `${data.order.order_subtotal1}`;
        if (data.order.order_subtotal2 != 0) {
            subtotalPriceView =
                `<div><span style="text-decoration: line-through;">${data.order.order_subtotal1}</span><br /><span style="font-weight: 600;font-size: medium;">${data.order.order_subtotal22}</span></div>`;
        }
        document.getElementById("viewSubtotal").innerHTML = `${subtotalPriceView}`;
        document.getElementById("viewTax").innerHTML = data.order.order_tax2 ? data.order.order_tax22 : data.order
            .order_tax1;
        if (data.order.discount_amount) {
            loadingIcon('discount-view', 1);
            // document.getElementById("discount-view").hidden = false;
            document.getElementById("viewDiscount").innerHTML = data.order.discount_amount2;
        } else {
            loadingIcon('discount-view', 0);
            // document.getElementById("discount-view").hidden = true;
        }
        if (data.order.total_investment_discount_amount) {
            loadingIcon('investment-discount-view', 1);
            // document.getElementById("investment-discount-view").hidden = false;
            document.getElementById("viewInvestmentDiscount").innerHTML = data.order.total_investment_discount_amount1;
        } else {
            loadingIcon('investment-discount-view', 0);
            // document.getElementById("investment-discount-view").hidden = true;
        }
        if (data.order.order_fee) {
            document.getElementById("delivery-fee-view").hidden = false;
            document.getElementById("viewDeliveryFee").innerHTML = data.order.order_fee_amount2;
        } else {
            document.getElementById("delivery-fee-view").hidden = true;
        }
        let priceView = `${data.order.order_total1}`;
        if (data.order.order_total2 != 0) {
            priceView =
                `<div><span style="text-decoration: line-through;">${data.order.order_total1}</span><br /><span style="font-weight: 600;font-size: medium;">${data.order.order_total22}</span></div>`;
        }
        document.getElementById("viewTotal").innerHTML = priceView;
    }

    function buildOneDetailItem(item) {
        let text = `<div>`;
        if (item.curbside_pickup == true) {
            text = `<div><span class="badge badge-pill badge-warning">Curbside Pickup</span>`;
            if (item.arrived == "true")
                text += `<span class="badge badge-pill badge-warning">Customer arrived</span>`;
            text += "<br>";
        }
        text += `<span class="badge badge-pill badge-primary">${item.payment_method ? item.payment_method.name : '-'}</span>`;
        text += `</div>`;
        //
        //
        let text3 = "";
        text3 += `<option id="role0_0" value="0" selected>-</option>`;
        @foreach ($ipaymentstatus as $key => $idata)
            if ('{{ $idata['id'] }}' == (item.is_complete + item.view)) {
                text3 +=
                    `<option id="role${item.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" selected style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
            } else {
                text3 +=
                    `<option id="role${item.id}_{{ $idata['id'] }}" value="{{ $idata['id'] }}" style="font-size: 16px  !important;">{{ $idata['name'] }}</option>`;
            }
        @endforeach

        // status
        if (item.order_status == '5') {
            let button = `<button type="button" class="btn btn-success btn-round isDisabled">
                Done
            </button>`;
        } else if (item.order_status == '6') {
            let button = `<button type="button" class="btn btn-warning btn-round isDisabled">
                Cancelled
            </button>`;
        } else {
            button = `<button type="button" class="btn btn-success btn-round" onclick="showDoneMessage('${item.id}', '{{ url('orderdone') }}')">
                Done
            </button>`;
        }

        return `
            <tr>
                <td>${item.id}</td>
                <td>${item.order_total2}</td>
                <td>${item.user.name}</td>
                <td>
                    <select name="role" id="role" class="form-control show-tick" onchange="updatePaymentStatus(event, ${item.id})" >
                        ${text3}
                    </select>
                </td>
                <td>${text}</td>
                <td>
                    <div class="title text-info">${item.timeago}</div>
                    ${item.updated_at}
                </td>
                <td>
                    <button type="button" class="btn btn-info btn-round" onclick="viewItem('${item.id}')">
                        View
                    </button>
                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '{{ route('admin.order-delete') }}')">
                        Delete
                    </button>
                    ${button}       {{-- Done --}}
                </td>
            </tr>
        `;

        let visible = '';
        if (item.visible == 1)
            visible = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;
        else
            visible = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;

        let image = item.images.length > 0 ? item.images[0].image_path : 'storage/no-image.jpg';
        return `
            <tr>
                <td>${item.id}</td>
                <td>${item.name}
                </td>
                <td style="width:90px;position:relative;">
                    ${featuredProduct}
                    <div style="width:80px;height:80px;margin: 0px auto;">
                        <img src="{{ asset('${image}') }}" style="width: 100%;height: 100%;object-fit: contain;" >
                    </div>
                </td>
                <td style="text-align: left;">
                    <ul ${style}>
                        ${branchNames}
                    </ul>
                </td>
                <td>${item.price}</td>
                <td>${item.discount_price}</td>
                <td>
                    <div style="display: flex;">${visible}</div>
                </td>
                <td class="text-center">
                    <div class="">
                        <p class="text-info m-0"><strong>${item.timeago}</strong></p>
                    </div>
                    <p class="h5">${item.updated_at2}</p>
                </td>
                <td class="text-center" style="white-space:nowrap;">
                    <button type="button" class="btn btn-info btn-round" onclick="editItem(${item.id})">
                        Edit
                    </button>

                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '{{ route('admin.delete-product') }}')">
                        <div>Delete</div>
                    </button>
                </td>
            </tr>
        `;
    }

    function initOrderDetailProductListTableHeader() {
        // <th>Action</th>
        let header = `
            <th>Address</th>
            <th>Product</th>
            <th>Price</th>
            <th>Quantity</th>
            <th>Image</th>
            <th>Investment Discount</th>
            <th>Investment Incentives</th>
            <th>Investment Points</th>
            <th>Total</th>
        `;
        document.getElementById("product_list_table_header").innerHTML = header;
    }

    initPaginationLine2(0, 1, 'product_list_pagination-list', null);

    function initOrderDetailDataProductListTable(data) {
        html = "";

        if (data.length == 0) {
            html = `
            <tr>
                <td colspan="7">
                    <div class="d-flex justify-content-center align-items-center">
                        <p class="m-0">There are no records</p>
                    </div>
                </td>
            </tr>
            `;
        } else {
            // <td>
            //                 <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '')">
            //                     Delete
            //                 </button>
            //             </td>
            // TODO: Add role for delete
            data.forEach(function(item, i, arr) {
                let incentives = '-';
                if (item.investment_incentive) {
                    incentives = [];
                    item.investment_incentive.forEach(data => {
                        let name = `<li style="color: #000">${data}</li>`
                        incentives.push(name);
                    });
                    incentives = incentives.join("");
                }
                html += `
                    <tr>
                        <td>${item.branch.name}</td>
                        <td>${item.name}</td>
                        <td>${item.price2}</td>
                        <td>${item.count}</td>
                        <td>
                            <img src="{{ asset('${item.image}') }}" width="70px"/>
                        </td>
                        <td>
                            ${item.investment_discount_amount ? item.investment_discount_amount2 : '-'}
                        </td>
                        <td>
                            <ul class="scrollable-wrap">
                                ${incentives}
                            </ul>
                        </td>
                        <td>${item.investment_point ? item.investment_point : '-'}</td>
                        <td>${item.total2}</td>
                    </tr>
                `;
            });
        }
        document.getElementById("product_list_table_body").innerHTML = html;
    }

    // function selectProduct() {
    //     var text = `<div id="div1" style="height: 400px;position:relative;">
    //                     <div id="div2" style="max-height:100%;overflow:auto;border:1px solid grey; border-radius: 10px; height: 97%;">
    //                         <div id="productslist" class="row" style="position: relative; top: 10px; left: 20px; right: 10px; bottom: 20px;width: 97%; ">
    //                             <table class="table table-bordered">
    //                                 <tbody>
    //                                     <thead class="q-color-bkg-label1">
    //                                         <tr>
    //                                             <th>Name</th>
    //                                             <th>Price</th>
    //                                             <th>Image</th>
    //                                             <th>Count</th>
    //                                             <th>Add</th>
    //                                         </tr>
    //                                     </thead>
    //                                 <tbody id="products">`;

    //     order.products.forEach(function(entry) {
    //         let price = entry.price;
    //         if (entry.discount_price != 0)
    //             price = entry.discount_price;

    //         text += `<tr>
    //             <td>${entry.name}</td>
    //             <td>${price}</td>
    //             <td><img src="${entry.image}" width="70px"/></td>
    //             <td>
    //                 <div onclick="increment(${entry.id})" class="q-inline btn btn-success btn-round">+1</div>
    //                 <h5 class="q-inline" id="count${entry.id}">1</h5>
    //                 <div onclick="decrement(${entry.id})" class="q-inline btn btn-danger btn-round">-1</div>
    //             </td>
    //             <td>
    //                 <div onclick="addProduct(${entry.id})" class="btn btn-info btn-round">Add to Order</div>
    //             </td>
    //         </tr>`;
    //     });

    //     text = `${text}</tbody>
    //         </tbody>
    //     </table>
    //     </div></div></div>`;

    //     console.log('test: ', text);
    //     swal.fire({
    //         title: "Add Products to Order",
    //         // text: text,
    //         confirmButtonColor: "#51cbce",
    //         customClass: 'swal-wide',
    //         html: text
    //     }, function(isConfirm) {
    //         if (isConfirm) {

    //         } else {

    //         }
    //     })
    // }

    // function checkStatus(event, id) {
    //     $.ajax({
    //         headers: {
    //             'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
    //         },
    //         type: 'POST',
    //         url: '{{ route('admin.order-change-status') }}',
    //         data: {
    //             id: id,
    //             status: event.target.value
    //         },
    //         success: function(data) {
    //             console.log('res: change order status = ', data);
    //             if (data.error) {
    //                 return errorNotify(data);
    //             }

    //             order = data.data;
    //             fillForm(data.data.order);
    //             initOrderDetailDataTable(data.data);
    //             initOrderDetailDataProductListTable(data.data.ordersdetails);
    //             return Toast.fire({
    //                 icon: "success",
    //                 title: data.msg
    //             });
    //         },
    //         error: function(e) {
    //             console.log(e);
    //             errorNotify(e.responseJSON);

    //         }
    //     });
    // }

    function updatePaymentStatus(event, id) {
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.update-order-payment-status') }}',
            data: {
                id: id,
                status: event.target.value
            },
            success: function(data) {
                // console.log('res: change order payment status = ', data);
                if (data.error) {
                    return errorNotify(data);
                }

                order = data.data;
                fillForm(data.data.order);
                initOrderDetailDataTable(data.data);
                initOrderDetailDataProductListTable(data.data.ordersdetails);
                paginationGoPage(1);
                return Toast.fire({
                    icon: "success",
                    title: data.msg
                });
            },
            error: function(e) {
                console.log(e);
                errorNotify(e.responseJSON);

            }
        });
    }

    function addComment() {
        let comment = document.getElementById("comment").value;
        if (
            messageStatus('error', 'Error', {msg: "The Comment field is required"}, !comment) ||
            !order ||
            !order.order.id
        ) {
            return;
        }
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.add-comment') }}',
            data: {
                id: order.order.id,
                comment: comment
            },
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('res add comment: ', data);
                if (data.error) {
                    return errorNotify(data);
                }

                order = data.data;
                fillForm(data.data.order);
                initOrderDetailDataTable(data.data);
                initOrderDetailDataProductListTable(data.data.ordersdetails);
                return Toast.fire({
                    icon: "success",
                    title: data.msg
                });
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
                errorNotify(e.responseJSON);
            }
        });
    }

    // fill forms
    function fillForm(data) {
        document.getElementById("comment").value = data.order_comment;
    }
</script>
