@inject('util', 'App\Models\Util')

@extends('layouts.dashboard-layout')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card card-user">
                <!-- Tabs -->
                <div class="card-header card-header-danger">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <ul class="nav nav-tabs" data-tabs="tabs">
                                <li role="presentation" class="nav-item" id="tabHome">
                                    <a href="#home" class="nav-link active" data-toggle="tab">List</a>
                                </li>
                                <li role="presentation" class="nav-item" id="tabView" style="display: none;">
                                    <a href="#view" class="nav-link" data-toggle="tab">View Order</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- End Tabs -->

                <!-- Tab List -->
                <div id="myTabContent" class="card-body">
                    <div class="tab-content text-left">

                        <!-- Tab View - Listings -->
                        <div role="tabpanel" class="tab-pane active" id="home">
                            <div class="card-header">
                                <h5 class="card-title">Order Lists</h5>
                                <div class="row">
                                    <!-- Order status -->
                                    <div class="col-12 col-md-4">
                                        @include('components.input-field.dropdown2', [
                                            'desc' => 'Filter by payment status',
                                            'label' => 'Filter',
                                            'id' => 'rest_search',
                                            'datas' => $util->getPaymentStatus(),
                                            'onchange' => 'onCatSearchSelect(this)',
                                        ])
                                    </div>
                                    <!-- Search -->
                                    <div class="col-12 col-md-4 ml-auto">
                                        @include('components.input-field.text', [
                                            'text' => 'Search',
                                            'label' => 'Search',
                                            'type' => 'search',
                                            'id' => 'element_search',
                                        ])
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Table -->
                                @include('components.table.light-table')
                                <!-- End Table -->
                            </div>
                        </div>
                        <!-- End Tab View - Listings -->

                        <!-- Tab View - order view -->
                        <div role="tabpanel" class="tab-pane" id="view">
                            <div class="card-header">
                                <h5 class="card-title">View Order</h5>
                            </div>
                            <div class="card-body">
                                <!-- Table -->
                                @include('components.table.light-table', [
                                    'id' => 'order_detail',
                                ])
                                <!-- End Table -->

                                <div class="mt-4">
                                    <h5>Properties List</h5>
                                    <!-- Table -->
                                    @include('components.table.light-table', [
                                        'id' => 'product_list',
                                    ])
                                    <!-- End Table -->
                                </div>

                                <div class="mt-4 col-md-8">
                                    @include('components.input-field.text-area', [
                                        'label' => 'Comment',
                                        'placeholder' => 'Insert comment',
                                        'id' => 'comment',
                                        'desc' => 'Comment for customer',
                                    ])
                                    @include('components.buttons.button', [
                                        'label' => 'Add',
                                        'onclick' => 'addComment()',
                                    ])
                                </div>

                                {{-- <button type="button" class="ml-auto d-block btn btn-info btn-round" onclick="selectProduct()">
                                    Add product
                                </button> --}}

                                <div class="col-md-4 ml-auto">
                                    <table class="table">
                                        <tbody>
                                            <tr id="coupon-view" class="hidden">
                                                <td class="text-right">
                                                    <div class="font-bold font-16">Coupon:
                                                    </div>
                                                </td>
                                                <td class="text-left">
                                                    <div id="couponName"></div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="text-right">
                                                    <div class="font-bold font-16">Subtotal:
                                                    </div>
                                                </td>
                                                <td class="text-left">
                                                    <div id="viewSubtotal"></div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="text-right">
                                                    <div id="viewTaxText" class="font-bold font-16">
                                                        Tax:</div>
                                                </td>
                                                <td class="text-left">
                                                    <div id="viewTax"></div>
                                                </td>
                                            </tr>
                                            <tr id="coupon-discount-view" class="hidden">
                                                <td class="text-right">
                                                    <div id="viewCouponDiscountText" class="font-bold font-16">
                                                        Coupon Discount:</div>
                                                </td>
                                                <td class="text-left">
                                                    <div id="couponDiscountVal"></div>
                                                </td>
                                            </tr>
                                            <tr id="discount-view" class="hidden">
                                                <td class="text-right">
                                                    <div id="viewDiscountText" class="font-bold font-16">
                                                        Discount:</div>
                                                </td>
                                                <td class="text-left">
                                                    <div id="viewDiscount"></div>
                                                </td>
                                            </tr>
                                            <tr id="investment-discount-view" class="hidden">
                                                <td class="text-right">
                                                    <div id="viewInvestmentDiscountText" class="font-bold font-16">
                                                        Investment Discount:</div>
                                                </td>
                                                <td class="text-left">
                                                    <div id="viewInvestmentDiscount"></div>
                                                </td>
                                            </tr>
                                            <tr id="delivery-fee-view" class="hidden">
                                                <td class="text-right">
                                                    <div class="font-bold font-16">
                                                        Delivery fee:</div>
                                                </td>
                                                <td class="text-left">
                                                    <div id="viewDeliveryFee"></div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="text-right">
                                                    <div class="font-bold font-16">Total:</div>
                                                </td>
                                                <td class="text-left">
                                                    <div id="viewTotal" class="m-0"></div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <!-- End Tab View - order view -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script -->
    @include('pages.admin.order.script')
@endsection
