<script type="text/javascript">
    // Address Data Listsings
    let pages = 1;
    let currentPage = 1;
    let searchText = "";
    let sort = "updated_at";
    let sortBy = "desc";
    let data;
    let products;

    paginationGoPage(1);
    initPaginationLine(pages, currentPage);
    initTableHeader();

    function paginationGoPage(page) {
        data = {
            page: page,
            sortBy: sort,
        };

        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: '{{ route('admin.transactions-get') }}',
            data: data,
            success: function(data) {
                loadingIcon('loading-bar', 0);
                if (data.error || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                currentPage = data.page;
                pages = data.pages;
                products = data.data;
                initDataTable(products);
                initPaginationLine(pages, currentPage);
                initTableHeader();
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                dataLoading = false;
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function initDataTable(data) {
        html = "";
        if (data.length == 0) {
            html = `
            <tr>
                <td colspan="3">
                    <div class="d-flex justify-content-center align-items-center">
                        <p class="m-0">There are no records</p>
                    </div>
                </td>
            </tr>
            `;
            document.getElementById("table_body").innerHTML = html;
        } else {
            data.forEach(function(item, i, arr) {
                html += buildOneItem(item);
            });
            document.getElementById("table_body").innerHTML = html;
        }
    }

    function buildOneItem(item) {
        if (item.visible == 1)
            var visible = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;
        else
            var visible = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;

        return `
            <tr>
                <td><a href="orders?edit=${item.id}">${item.id}</a></td>
                <td>${item.order_total2}</td>
                <td class="">
                    <div class="">
                        <p class="text-info m-0"><strong>${item.timeago}</strong></p>
                    </div>
                    <p class="h5">${item.updated_at2}</p>
                </td>
            </tr>
        `;
    }

    function initPaginationLine(pages, page) {
        allPages = pages;
        currentPage = page;
        let pageNumbers = '';
        pageNumbers = buildPagination(currentPage, allPages);
        let paginationList = document.getElementById('pagination-list');
        paginationList.innerHTML = pageNumbers;
        initializePaginations(paginationGoPage);
    }

    function tableHeaderSort(newsort) {
        if (newsort == sort) {
            if (sortBy == "asc")
                sortBy = "desc";
            else
                sortBy = "asc";
        } else {
            sort = newsort
            sortBy = "asc";
        }
        paginationGoPage(currentPage);
    }

    function utilGetImg(value) {
        var img = "{{ asset('img/arrow_noactive.png') }}";
        if (sort == value && sortBy == "asc") img = "{{ asset('img/asc_arrow.png') }}";
        if (sort == value && sortBy == "desc") img = "{{ asset('img/desc_arrow.png') }}";
        return img;
    }

    function initTableHeader() {
        let header = `
            <th>ID <img onclick="tableHeaderSort('id');" src="${utilGetImg('id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Order Total <img onclick="tableHeaderSort('order_total');" src="${utilGetImg('name')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Date/Time <img onclick="tableHeaderSort('updated_at');" src="${utilGetImg('ad_type')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
        `;
        document.getElementById("table_header").innerHTML = header;
        document.getElementById("table_footer").innerHTML = header;
    }
</script>
