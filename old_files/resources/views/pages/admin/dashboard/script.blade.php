<script>
    // Sketch chart graph
    var speedCanvas = document.getElementById("speedChart");

    var dataFirst = {
        data: [{{ $e1 }}, {{ $e2 }}, {{ $e3 }},
            {{ $e4 }}, {{ $e5 }}, {{ $e6 }},
            {{ $e7 }}, {{ $e8 }}, {{ $e9 }},
            {{ $e10 }}, {{ $e11 }}, {{ $e12 }}
        ],
        fill: false,
        borderColor: '#41CE8E',
        backgroundColor: 'transparent',
        pointBorderColor: '#41CE8E',
        pointRadius: 4,
        pointHoverRadius: 4,
        pointBorderWidth: 8,
    };

    var speedData = {
        labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
        datasets: [dataFirst]
    };

    var chartOptions = {
        legend: {
            display: false,
            position: 'top'
        }
    };

    $(document).ready(function() {
        var lineChart = new Chart(speedCanvas, {
            type: 'line',
            hover: false,
            data: speedData,
            options: chartOptions
        });
    });
</script>
