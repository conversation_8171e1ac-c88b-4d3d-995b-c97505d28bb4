@inject('util', 'App\Models\Util')
@extends('layouts.dashboard-layout')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card card-user">
                <!-- Tab List -->
                <div id="myTabContent" class="card-body">
                    <div class="tab-content text-left">

                        <!-- Tab View - Listings -->
                        <div role="tabpanel" class="tab-pane active" id="home">
                            <div class="card-header">
                                <h5 class="card-title">SMS API</h5>
                            </div>
                            <div class="card-body">
                                <div class="col-md-6 col-12">
                                    <div class="row">
                                        <div class="col-12">
                                            @include('components.input-field.text', [
                                                'label' => 'API Key',
                                                'placeholder' => 'Insert api key',
                                                'desc' => 'Insert api key',
                                                'id' => 'api',
                                            ])
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <!-- Visible -->
                                            @include('components.input-field.checkbox-2', [
                                                'id' => 'api_visible',
                                                'text' => 'Visible',
                                                'initvalue' => 'true',
                                            ])
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="" style="max-width: 10rem;width: 100%;padding: 0 1rem;">
                                            @include('components.buttons.button', [
                                                'label' => 'Save',
                                                'id' => 'on-save',
                                                'onclick' => 'onSave();',
                                                'block' => '1',
                                            ])
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Tab View - Listings -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script -->
    @include('pages.admin.sms-api.script')
@endsection
