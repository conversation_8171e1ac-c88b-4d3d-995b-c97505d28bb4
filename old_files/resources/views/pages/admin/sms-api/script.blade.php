<script type="text/javascript">
    // Data Listsings
    let data;

    function editItem() {
        let url = `{{ route('admin.sms-api-get') }}`;
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: url,
            data: {},
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('get sms api res: ', data);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: 'Something went wrong'
                    });
                }
                fillForm(data.data);
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function onSave() {
        let api = document.getElementById("api").value;

        // Check invalid fields
        if (
            messageStatus('error', 'Error', {
                msg: "The API key field is required"
            }, !api)
        ) {
            return;
        }

        let data = {
            api: api,
            status: (api_visible) ? 1 : 0,
        }
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.sms-api-add') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('sms api save res: ', data);
                if (data.error) {
                    return messageStatus('error', 'Error', data);
                }
                fillForm(data.data);
                return messageStatus('success', 'Success', data);
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log('err res: ', e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    // Clear forms
    function clearForm() {
        document.getElementById("api").value = "";
        onSetCheck_api_visible(true);
    }

    function fillForm(data) {
        document.getElementById("api").value = data.api;
        onSetCheck_api_visible(data.api_status);
    }

    // Get data
    editItem();
</script>
