<script>
    // Product listsings
    let pages = 1;
    let currentPage = 1;
    let sortCat = 0;
    let sortRest = 0;
    let sortPublished = '1';
    let sortUnPublished = '1';
    let sortFeatured = '0';
    let searchText = "";
    let sort = "updated_at";
    let sortBy = "desc";
    let data;
    let products;

    function convertToEmbedLink(youtubeLink) {
        // Check if the link is already an embedded link
        const embedPattern = /^https?:\/\/(www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})$/;
        if (embedPattern.test(youtubeLink)) {
            return youtubeLink; // Return the original link if it's already embedded
        }

        const videoIdMatch = youtubeLink.match(
            /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/
        );
        if (videoIdMatch) {
            const videoId = videoIdMatch[1];
            return `https://www.youtube.com/embed/${videoId}`;
        } else {
            return youtubeLink; // Invalid YouTube link
        }
    }

    paginationGoPage(1);
    initPaginationLine(pages, currentPage, 'pagination-list', paginationGoPage);
    initTableHeader();

    function paginationGoPage(page) {
        data = {
            page: page,
            per_page: 20,
            sort: sortBy,
            order_by: sort,
            query: searchText,
            visible: sortPublished,
            invisible: sortUnPublished,
            sortFeatured: sortFeatured,
        };

        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: '{{ route('admin.products-get') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('product get', data);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', data);
                }
                currentPage = data.data.current_page;
                pages = data.data.last_page;
                products = data.data.data;
                initDataTable(products);
                initPaginationLine(pages, currentPage, 'pagination-list', paginationGoPage);
                initTableHeader();
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                dataLoading = false;
                console.log(e);
                return messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function initDataTable(data) {
        html = "";
        if (data.length == 0) {
            html = `
            <tr>
                <td colspan="9">
                    <div class="d-flex justify-content-center align-items-center">
                        <p class="m-0">There are no records</p>
                    </div>
                </td>
            </tr>
            `;
            document.getElementById("table_body").innerHTML = html;
        } else {
            data.forEach(function(item, i, arr) {
                html += buildOneItem(item);
            });
            document.getElementById("table_body").innerHTML = html;
        }
    }

    function buildOneItem(item) {
        let visible = '-';
        if (item.visible == 1)
            visible = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;
        else
            visible = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;

        let rating = "";
        if (item.drating ?? '') {
            rating = createRatings(item.drating ?? '');
        }
        let quantity = item.branch_products.quantity;
        let minquantity = item.branch_products.minquantity;
        let maxquantity = item.branch_products.maxquantity;
        let outofstock =
            `<span style="font-weight:700;font-size: 9px;color:#fff;background-color:#f00;padding: 2px 5px;margin: 0px;border-radius: 14px;text-transform: uppercase;white-space: nowrap;margin-left: 5px;">Out of stock</span>`;
        let featuredProduct = "";
        product_branches = item.branch_products;
        let branchNames = [];
        product_branches.forEach(data => {
            if (Number(data.quantity) <= Number(data.minquantity)) {
                name = `<li style="color: #f00">${data.branch.name}${outofstock}</li>`
            } else {
                name = `<li style="color: #000">${data.branch.name}</li>`
            };
            branchNames.push(name);
        });
        branchNames = branchNames.join("");
        let style = ``;
        let one = 1;
        if (item.featured == one) {
            featuredProduct = `<span class="featured">Featured</span>` // Featured Products
        }
        let image = item.images.length > 0 ? item.images[0].image_path : 'storage/no-image.jpg';
        // <br> ${rating}
        //             ${item.rating}
        return `
            <tr>
                <td>${item.id}</td>
                <td>${item.name}
                </td>
                <td style="width:90px;position:relative;">
                    ${featuredProduct}
                    <div style="width:80px;height:80px;margin: 0px auto;">
                        <img src="{{ asset('${image}') }}" style="width: 100%;height: 100%;object-fit: contain;" >
                    </div>
                </td>
                <td style="text-align: left;">
                    <ul ${style} class="scrollable-wrap">
                        ${branchNames}
                    </ul>
                </td>
                <td>${item.price2}</td>
                <td>${item.discount_price2}</td>
                <td>
                    <div style="display: flex;">${visible}</div>
                </td>
                <td class="text-center">
                    <div class="">
                        <p class="text-info m-0"><strong>${item.timeago}</strong></p>
                    </div>
                    <p class="h5">${item.updated_at2}</p>
                </td>
                <td class="text-center" style="white-space:nowrap;">
                    <button type="button" class="btn btn-info btn-round" onclick="editItem(${item.id})">
                        Edit
                    </button>

                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '{{ route('admin.delete-product') }}')">
                        <div>Delete</div>
                    </button>
                </td>
            </tr>
        `;
    }

    function initPaginationLine(pages, page, id, callback) {
        allPages = pages;
        currentPage = page;
        let pageNumbers = '';
        pageNumbers = buildPagination(currentPage, allPages);
        let paginationList = document.getElementById(id);
        if (paginationList) {
            paginationList.innerHTML = pageNumbers;
            initializePaginations(callback, id);
        }
    }

    function tableHeaderSort(newsort) {
        if (newsort == sort) {
            if (sortBy == "asc")
                sortBy = "desc";
            else
                sortBy = "asc";
        } else {
            sort = newsort
            sortBy = "asc";
        }
        paginationGoPage(currentPage);
    }

    function utilGetImg(value) {
        let img = "{{ asset('img/arrow_noactive.png') }}";
        if (sort == value && sortBy == "asc") img = "{{ asset('img/asc_arrow.png') }}";
        if (sort == value && sortBy == "desc") img = "{{ asset('img/desc_arrow.png') }}";
        return img;
    }

    function initTableHeader() {
        let header = `
            <th>ID <img onclick="tableHeaderSort('id');" src="${utilGetImg('id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Name <img onclick="tableHeaderSort('name');" src="${utilGetImg('name')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Image</th>
            <th>Address <img onclick="tableHeaderSort('branch');" src="${utilGetImg('branch')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Price <img <img onclick="tableHeaderSort('price');" src="${utilGetImg('price')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Discount Price <img <img onclick="tableHeaderSort('discountprice');" src="${utilGetImg('discountprice')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Published <img onclick="tableHeaderSort('published');" src="${utilGetImg('published')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Updated At <img onclick="tableHeaderSort('updated_at');" src="${utilGetImg('updated_at')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Action</th>
        `;
        document.getElementById("table_header").innerHTML = header;
        document.getElementById("table_footer").innerHTML = header;
    }

    function onCatSearchSelect(object) {
        sortCat = object.value;
        currentPage = 1;
        paginationGoPage(currentPage);
    }

    function onRestSearchSelect(object) {
        sortRest = object.value;
        currentPage = 1;
        paginationGoPage(currentPage);
    }

    $(document).on('input', '#element_search', function() {
        searchText = document.getElementById("element_search").value;
        currentPage = 1;
        paginationGoPage(1);
    });

    function onVisibleSearchSelect() {
        if (visible_search) sortPublished = "1";
        else sortPublished = "0";
        if (unvisible_search) sortUnPublished = "1";
        else sortUnPublished = "0";
        if (featured_search) sortFeatured = "1";
        else sortFeatured = "0";
        currentPage = 1;
        paginationGoPage(1);
    }
</script>

{{-- product address --}}
<script>
    function initAddressDataTable(data) {
        // console.log('init address: ', data);
        let html = "";
        if (data.length == 0) {
            html = `
            <tr>
                <td colspan="7">
                    <div class="d-flex justify-content-center align-items-center">
                        <p class="m-0">There are no records</p>
                    </div>
                </td>
            </tr>
            `;
            document.getElementById("address_table_body").innerHTML = html;
        } else {
            data.forEach(function(item, i, arr) {
                html += buildAddressOneItem(item);
            });
            document.getElementById("address_table_body").innerHTML = html;
        }
    }

    function buildAddressOneItem(item) {
        let time = '-';
        if (item.timeago) {
            time = `
                <div class="">
                    <p class="text-info m-0"><strong>${item.timeago}</strong></p>
                </div>
                <p class="h5">${item.updated_at2}</p>
            `;
        }
        return `
            <tr>
                <td>${item.branch?.name}</td>
                <td class="text-center">${item.quantity}</td>
                <td class="text-center">${item.minquantity}</td>
                <td class="text-center">${item.maxquantity}</td>
                <td class="text-center">
                    ${time}
                </td>
                <td class="text-center" style="white-space:nowrap;">
                    <button type="button" class="btn btn-info btn-round" onclick="editProductAddress(${item.id}, '${item.branch.id}', ${item.quantity}, ${item.minquantity}, ${item.maxquantity})">
                        Edit
                    </button>

                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '{{ route('admin.delete-branch-product', ['branchProduct' => ':id']) }}', productAddressPaginationGoPage, ${addressCurrentPage})">
                        <div>Delete</div>
                    </button>
                </td>
            </tr>
        `;
    }

    function tableHeaderSort(newsort) {
        if (newsort == sort) {
            if (sortBy == "asc")
                sortBy = "desc";
            else
                sortBy = "asc";
        } else {
            sort = newsort
            sortBy = "asc";
        }
        paginationGoPage(currentPage);
    }

    function utilGetImg(value) {
        let img = "{{ asset('img/arrow_noactive.png') }}";
        if (sort == value && sortBy == "asc") img = "{{ asset('img/asc_arrow.png') }}";
        if (sort == value && sortBy == "desc") img = "{{ asset('img/desc_arrow.png') }}";
        return img;
    }

    function initTableHeader() {
        let header = `
            <th>ID <img onclick="tableHeaderSort('id');" src="${utilGetImg('id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Name <img onclick="tableHeaderSort('name');" src="${utilGetImg('name')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Image</th>
            <th>Address <img onclick="tableHeaderSort('branch');" src="${utilGetImg('branch')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Price <img <img onclick="tableHeaderSort('price');" src="${utilGetImg('price')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Discount Price <img <img onclick="tableHeaderSort('discountprice');" src="${utilGetImg('discountprice')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Published <img onclick="tableHeaderSort('published');" src="${utilGetImg('published')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Updated At <img onclick="tableHeaderSort('updated_at');" src="${utilGetImg('updated_at')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Action</th>
        `;
        document.getElementById("table_header").innerHTML = header;
        document.getElementById("table_footer").innerHTML = header;
    }
</script>

<script type="text/javascript">
    let productType = 0;
    {{-- let is_emptyBranch = {!! json_encode($util->getBranches()) !!}.length == 0 ? true : false;  --}}
    let is_emptyBranch = false;

    // Tabs
    $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
        let target = $(e.target).attr("href");
        if (target != "#edit") {
            document.getElementById("tabEdit").style.display = "none";
        }
        // if (target == "#edit") {
        //     document.getElementById("tabEdit").style.display = "block";
        //     $('.nav-tabs a[href="#edit"]').tab('show');
        //     let target = document.getElementById("form");
        //     document.getElementById('editForm').appendChild(target);
        // }
        if (target == "#create") {
            // Clear form
            clearForm();
            document.getElementById('createForm').appendChild(document.getElementById("form"));
        }
        if (target == "#home") {
            // clearForm();
        }
    });

    let cacheRProducts = [];
    // $('.show-tick').selectpicker('refresh'); OLD

    function addRProduct() {
        // $('select[id=productForList]').val()
        let rpId = $('select[id=productForList] :selected').val();
        let rpPrice = $('select[id=productForList] :selected').attr('data-price');
        let rpDPrice = $('select[id=productForList] :selected').attr('data-dprice');
        let rpPrice2 = $('select[id=productForList] :selected').attr('data-price2');
        let rpDPrice2 = $('select[id=productForList] :selected').attr('data-dprice2');
        let rpName = $('select[id=productForList] :selected').attr('data-name');
        if (messageStatus('error', 'Error', {
                text: "Please select a product"
            }, rpId == 0))
            return;
        let newEl = {
            id: 0,
            rproduct: {
                id: rpId,
                name: rpName,
                image: $('select[id=productForList] :selected').attr('data-image'),
                price: rpPrice,
                discount_price: rpDPrice,
                price2: rpPrice2,
                discount_price2: rpDPrice2,
            }
        };
        if (editId != 0) {
            loadingIcon('loading-bar', 1);
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                },
                type: 'POST',
                url: '{{ route('admin.rproduct-add') }}',
                data: {
                    id: editId,
                    rp: rpId,
                },
                success: function(data) {
                    // console.log('rp response: ', data);
                    loadingIcon('loading-bar', 0);
                    if (data.error || data.data == null) {
                        return messageStatus('error', 'Error', {
                            text: 'Something went wrong'
                        });
                    }
                    if (editId == 0) {
                        if (data.data.length != 0) {
                            // Checking whether the new item exists
                            const newItemCheck = (element) => element.id == rp;
                            if (!cacheRProducts.some(newItemCheck)) {
                                cacheRProducts.push({
                                    id: rp,
                                    name: data.data[0].name,
                                    image: data.data[0].image,
                                    price: data.data[0].price,
                                });
                            } else {
                                return messageStatus('success', 'Success', {
                                    text: 'Already added'
                                });
                            }
                            return loadAllRProducts(cacheRProducts);
                        }
                    }
                    loadAllRProducts(data.data);
                    if (data.msg) return messageStatus('success', 'Success', data);
                },
                error: function(e) {
                    console.log(e);
                    loadingIcon('loading-bar', 0);
                    return messageStatus('error', 'Error', {
                        text: 'Something went wrong'
                    });
                }
            });
        } else {
            // Checking whether the new item exists
            const newItemCheck = (element) => element.rproduct.id == rpId;
            if (!cacheRProducts.some(newItemCheck)) {
                cacheRProducts.push(newEl);
            } else {
                return messageStatus('success', 'Success', {
                    text: 'Already added'
                });
            }
            // console.log('cacheRProducts: ', cacheRProducts);
            return loadAllRProducts(cacheRProducts);
        }
    }

    function deleteRProducts(id) {
        let url = `{{ route('admin.delete-rproduct', ':id') }}`;
        url = url.replace(':id', id);
        swal.fire({
            icon: "warning",
            title: "Are you sure?",
            text: "You will not be able to recover this item!",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: 'Yes',
            cancelButtonText: 'No'
        }).then((result) => {
            if (result.isConfirmed) {
                url = url.replace(':id', id);
                $.ajax({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                    },
                    type: 'DELETE',
                    url: url,
                    data: {
                        id: id
                    },
                    success: function(data) {
                        // console.log('del rproduct res: ', data);
                        if (data.error || data.data == null) {
                            if (data.error == "1") {
                                return messageStatus('error', 'Error', data);
                            }
                            return messageStatus('error', 'Error', {
                                msg: 'Something went wrong'
                            });
                        }
                        // Removing duplicate id
                        if (editId == 0) {
                            for (let i = cacheRProducts.length; i--;) {
                                if (cacheRProducts[i].id == id) {
                                    cacheRProducts.splice(i, 1);
                                }
                            }
                            return loadAllRProducts(data.data);
                        }
                        loadAllRProducts(data.data);
                        if (data.msg) return messageStatus('success', 'Success', data);
                    },
                    error: function(e) {
                        console.log(e);
                        messageStatus('error', 'Error', e.responseJSON);
                    }
                });
            } else {

            }
        });
    }

    function loadAllRProducts(data) {
        let text = "";
        data.forEach(function(item, i, arr) {
            text += `
                <tr>
                    <td>${item.rproduct.name}</td>
                    <td><img src="${item.rproduct.images_link ? item.rproduct.images_link[0] : ''}" height="100px" ></td>
                    <td>${item.rproduct.price2}</td>
                    <td>${item.rproduct.discount_price2}</td>
                    <td>
                        <button type="button" class="btn btn-danger btn-round" onclick="deleteRProducts('${item.id}')">
                            <div>Delete</div>
                        </button>
                    </td>
                </tr>
            `
        });
        document.getElementById("rp_table_body").innerHTML = text;
    }

    let productVariants = false;

    function onProductVariants() {
        productVariants = !productVariants;
        if (productVariants)
            document.getElementById("productVariantsItems").hidden = false;
        else
            document.getElementById("productVariantsItems").hidden = true;
    }

    let recommendedProducts = false;

    function onRecommendedProducts() {
        recommendedProducts = !recommendedProducts;
        if (recommendedProducts)
            document.getElementById("recommendedProductsItems").hidden = false;
        else
            document.getElementById("recommendedProductsItems").hidden = true;
    }

    let cacheVariant = [];

    function addVariant() {
        let pv_name = document.getElementById("pv_name").value;
        let pv_price = document.getElementById("pv_price").value;
        let pv_dprice = document.getElementById("pv_discountprice").value;
        let pv_desc = document.getElementById("pv_desc").value;
        let pv_videoLink = document.getElementById("pv_video_link").value;
        // Check invalid fields
        if (
            messageStatus('error', 'Error', {
                text: "The name field is required"
            }, !pv_name) ||
            messageStatus('error', 'Error', {
                text: "The price field is required"
            }, !pv_price) ||
            messageStatus('error', 'Error', {
                text: "The description field is required"
            }, !pv_desc)
        ) {
            return;
        }
        checkDublicateBranch(1, 'variant_branch');
        if (dublicateBranch) {
            return;
        }
        // product variant branch
        let variantBranchData = [];

        // check product variant branch field error state
        let checkVariant = 1;

        // Stock product variant type
        $('.branch_element_variant_branch').each(function() {
            let branch_id = $(this).find('.branchNames :selected').attr("value");
            let branch_name = $(this).find('.branchNames :selected').text();
            let quantity = $(this).find('.quantity').val() ?? 0;
            let minquantity = $(this).find('.minquantity').val() ?? 0;
            let maxquantity = $(this).find('.maxquantity').val() ?? 0;
            if (branch_id != 0 && quantity && minquantity && maxquantity) {
                variantBranchData.push({
                    "branch": {
                        id: parseInt(branch_id),
                        name: branch_name
                    },
                    "quantity": parseInt(quantity),
                    "minquantity": parseInt(minquantity),
                    "maxquantity": parseInt(maxquantity),
                });
                $(this).css("outline", "0px solid #fff");
                $(this).css("border-radius", "0px");
            } else {
                checkVariant = 0;
                $(this).css("outline", "medium solid #ff674c");
                $(this).css("border-radius", "4px");
                if (!branch_id) {
                    messageStatus('error', 'Error', {
                        msg: "Select branch name"
                    });
                }
                if (!quantity) {
                    messageStatus('error', 'Error', {
                        msg: `Add quantity to ${$(this).find('.branchNames :selected').text()} address`
                    });
                }
                if (!minquantity) {
                    messageStatus('error', 'Error', {
                        msg: `Add minimum quantity to ${$(this).find('.branchNames :selected').text()} address`
                    });
                }
                if (!maxquantity) {
                    messageStatus('error', 'Error', {
                        msg: `Add maximum quantity to ${$(this).find('.branchNames :selected').text()} address`
                    });
                }
                return;
            }
        });

        if (messageStatus('error', 'Error', {
                msg: "The Address field is required"
            }, variantBranchData.length == 0)) {
            return;
        }
        if (!checkVariant)
            return;
        let varData = {
            id: variantId,
            product_id: editId,
            name: pv_name,
            desc: pv_desc,
            price: pv_price,
            dprice: pv_dprice,
            imageid: imageid3,
            branch_products: variantBranchData,
            video_link: pv_videoLink,
            timeago: '-',
        };
        if (editId != 0) {
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                },
                type: 'POST',
                url: '{{ route('admin.product-variant-add') }}',
                data: removeEmptyData(varData),
                success: function(data) {
                    if (data.error || data.data == null) {
                        return messageStatus('error', 'Error', {
                            msg: "Something went wrong"
                        }, (data.error || data.data == null));
                    }
                    variantId = 0;
                    // if (editId == 0) {
                    //     if (data.data.length != 0) {
                    //         cacheVariant.push({
                    //             name: data.data.name,
                    //             desc: data.data.desc,
                    //             price: data.data.price,
                    //             cprice: data.data.cprice,
                    //             dprice: data.data.dprice,
                    //             cdprice: data.data.cdprice,
                    //             video_link: data.data.video_link,
                    //             image: data.data.image,
                    //             imageid: data.data.imageid,
                    //             timeago: data.data.timeago,
                    //             variantType: data.data.variantType,
                    //             branch_products: data.data.branchData
                    //         });
                    //         // console.log("cacheVariant");
                    //         // console.log(cacheVariant);
                    //         loadAllVariants(cacheVariant);
                    //         return;
                    //     }
                    // }
                    loadAllVariants(data.data);
                    // Clearing variant form
                    clearDropZone3();
                    clearVariantProductBranchForm();
                    messageStatus('success', 'Success', {
                        msg: "Data saved successfully"
                    });
                },
                error: function(e) {
                    console.log(e);
                    messageStatus('error', 'Error', e.responseJSON);
                }
            });
        } else {
            if (cacheVariant && cacheVariant.length == 0) {
                cacheVariant.push(varData);
            } else {
                cacheVariant = cacheVariant.map((data) => {
                    if (data.name == varData.name) {
                        return varData;
                    }
                    return data;
                });
            }
            // console.log("cacheVariant");
            // console.log(cacheVariant);
            loadAllVariants(cacheVariant);
            clearVariantProductBranchForm();
        }
    }

    function delVariant(name) {
        cacheVariant = cacheVariant.map((data) => {
            if (data.name != name) {
                return data;
            }
            return;
        });
        loadAllVariants(cacheVariant);
    }

    let variantId = 0;
    let editBranches_variant_branch;
    // All branches
    let allBranches = {!! json_encode($util->getBranches()) !!};

    function editVariableItem(id, name) {
        if (id == 0) {
            let val = cacheVariant.find((item, index) => {
                if (item.name == name) {
                    return item;
                }
            });
            // if none is found it will return undefined
            // console.log('val: ', val);
            return loadVariantBranchProducts(val);
        }
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.product-variant-get') }}',
            data: {
                id: id,
            },
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log("edit variant response: ", data);
                if (data.error || data.data == null) {
                    return messageStatus('error', 'Error', data);
                }
                variantId = data.data.id;
                // console.log("load var response: ", data);
                loadVariantBranchProducts(data.data);
                // branch reset button
                resetButtonView('variant_branch', 1);
                // document.getElementById("pv_name").value = data.data.name;
                // document.getElementById("pv_price").value = data.data.price;
                // if (data.data.dprice != '0.00')
                //     document.getElementById("pv_discountprice").value = data.data.dprice;
                // if (data.data.images.length > 0) addEditImage3(data.data.images_files.id, data.data
                //     .images_files.filename);
                // let branches = data.data.branch_products;
                // All branches
                // {{-- let allBranches = {!! json_encode($util->getBranches()) !!}; --}}
                // let allBranches = [];
                // let options = [];
                // allBranches.forEach(data => {
                //     options.push(
                //         `<option id="${data.id}" value="${data.id}">${data.name}</option>`
                //     );
                // });
                // $('tbody .branch_element_variant_branch').remove();
                // let ptr = '';
                // if (branches.length != 0) {
                //     editBranches_variant_branch = branches;
                //     branches.forEach(data => {
                //         let option = '';
                //         allBranches.forEach(data1 => {
                //             if (data1.id != data.id) {
                //                 option +=
                //                     `<option id="${data1.id}" value="${data1.id}">${data1.name}</option>`;
                //             } else {
                //                 option +=
                //                     `<option id="${data.id}" value="${data.id}" selected="selected">${data.name}</option>`;
                //             }
                //         });
                //         ptr = `
                //         <tr class="branch branch_element_variant_branch">` +
                //             `<td>` +
                //             `<select name="branchName" id="branchName" class="branchNames branchNames_variant_branch q-form">` +
                //             `<option value="0">--Please choose a branch--</option>` +
                //             `${option}` +
                //             `</select>` +
                //             `</td>` +
                //             `<td><input type="number" name="quantity" id="quantity" value="${data.quantity}" class="quantity q-form"></td>` +
                //             `<td><input type="number" name="minquantity" id="minquantity" value="${data.minquantity}" class="minquantity q-form"></td>` +
                //             `<td><button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button></td>` +
                //             `</tr>`;
                //         $('tbody#branchBody_variant_branch').append(ptr);
                //     });
                // } else {
                //     ptr = `
                //     <tr class="branch branch_element_variant_branch">` +
                //         `<td>` +
                //         `<select name="branchName" id="branchName" class="branchNames branchNames_variant_branch q-form">` +
                //         `<option value="0">--Please choose a branch--</option>` +
                //         `${options}` +
                //         `</select>` +
                //         `</td>` +
                //         `<td><input type="number" name="quantity" id="quantity" class="quantity q-form"></td>` +
                //         `<td><input type="number" name="minquantity" id="minquantity" class="minquantity q-form"></td>` +
                //         `<td><button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button></td>` +
                //         `</tr>`;
                //     $('tbody#branchBody_variant_branch').append(ptr);
                // }
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function loadVariantBranchProducts(data) {
        variantId = data.id;
        // branch reset button
        resetButtonView('variant_branch', 1);
        document.getElementById("pv_name").value = data.name;
        document.getElementById("pv_desc").value = data.desc;
        document.getElementById("pv_video_link").value = data.video_link;
        document.getElementById("pv_price").value = data.price;
        if (data.dprice != '0.00')
            document.getElementById("pv_discountprice").value = data.dprice;
        if (data.images_files) {
            addEditImage3(data.images_files.id, data.images_files.filename);
        }
        let branches = data.branch_products;
        // console.log('loading...: ', data.branch_products);
        // let allBranches = [];
        let options = [];
        allBranches.forEach(data => {
            options.push(
                `<option id="${data.id}" value="${data.id}">${data.name}</option>`
            );
        });
        $('tbody .branch_element_variant_branch').remove();
        let ptr = '';
        if (branches.length != 0) {
            editBranches_variant_branch = branches;
            branches.forEach(data => {
                // console.log('branches data: ', data);
                let option = '';
                allBranches.forEach(data1 => {
                    if (data1.id != data.branch.id) {
                        option +=
                            `<option id="${data1.id}" value="${data1.id}">${data1.name}</option>`;
                    } else {
                        option +=
                            `<option id="${data.branch.id}" value="${data.branch.id}" selected="selected">${data.branch.name}</option>`;
                    }
                });
                ptr = `
                    <tr class="branch branch_element_variant_branch">` +
                    `<td>` +
                    `<div id="">` +
                    `<div class="form-group">` +
                    `<select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_variant_branch">` +
                    `<option value="0">--Please choose a branch--</option>` +
                    `${option}` +
                    `</select>` +
                    `</div>` +
                    `</div>` +
                    `</td>` +
                    `<td>
                        <div id="element">
                            <div class="form-group">
                                <input type="number" name="quantity" id="quantity" value="${data.quantity}" class="quantity form-control">
                            </div>
                        </div>
                    </td>` +
                    `<td>
                        <div id="element">
                            <div class="form-group">
                                <input type="number" name="minquantity" id="minquantity" value="${data.minquantity}" class="minquantity form-control">
                            </div>
                        </div>
                    </td>` +
                    `<td>
                        <div id="element">
                            <div class="form-group">
                                <input type="number" name="maxquantity" id="maxquantity" value="${data.maxquantity}" class="maxquantity form-control">
                            </div>
                        </div>
                    </td>` +
                    `<td>
                        <div class="col-5 ml-auto mr-auto">
                            <div class="form-group">
                                <button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button>
                            </div>
                        </div>
                    </td>` +
                    `</tr>
                `;
                $('tbody#branchBody_variant_branch').append(ptr);
            });
        } else {
            ptr = `
        <tr class="branch branch_element_variant_branch">` +
                `<td>` +
                `<div id="">` +
                `<div class="form-group">` +
                `<select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_variant_branch">` +
                `<option value="0">--Please choose a branch--</option>` +
                `${options}` +
                `</select>` +
                `</div>` +
                `</div>` +
                `</td>` +
                `<td>
                    <div id="element">
                        <div class="form-group">
                            <input type="number" value="0" name="quantity" id="quantity" class="quantity form-control">
                        </div>
                    </div>
                </td>` +
                `<td>
                <div id="element">
                    <div class="form-group">
                        <input type="number" value="0" name="minquantity" id="minquantity" class="minquantity form-control">
                    </div>
                </div>
            </td>` +
                `<td>
                <div class="col-5 ml-auto mr-auto">
                    <div class="form-group">
                        <button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button>
                    </div>
                </div>
            </td>` +
                `</tr>
            `;
            $('tbody#branchBody_variant_branch').append(ptr);
        }
    }


    function clearVariantProductBranchForm() {
        variantId = 0;
        resetButtonView('variant_branch', 0);
        editBranches_variant_branch = [];
        document.getElementById("pv_name").value = "";
        document.getElementById("pv_desc").value = "";
        document.getElementById("pv_price").value = "";
        document.getElementById("pv_discountprice").value = "";
        document.getElementById("pv_video_link").value = "";
        let options = [];
        allBranches.forEach(data => {
            options.push(
                `<option id="${data.id}" value="${data.id}">${data.name}</option>`
            );
        });
        $('tbody .branch_element_variant_branch').remove();
        let ptr = '';
        ptr = `
        <tr class="branch branch_element_variant_branch">` +
            `<td>` +
            `<div id="">` +
            `<div class="form-group">` +
            `<select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_variant_branch">` +
            `<option value="0">--Please choose a branch--</option>` +
            `${options}` +
            `</select>` +
            `</div>` +
            `</div>` +
            `</td>` +
            `<td>
                    <div id="element">
                        <div class="form-group">
                            <input type="number" value="0" name="quantity" id="quantity" class="quantity form-control">
                        </div>
                    </div>
                </td>` +
            `<td>
                <div id="element">
                    <div class="form-group">
                        <input type="number" value="0" name="minquantity" id="minquantity" class="minquantity form-control">
                    </div>
                </div>
            </td>` +
            `<td>
                <div id="element">
                    <div class="form-group">
                        <input type="number" value="0" name="maxquantity" id="maxquantity" class="maxquantity form-control">
                    </div>
                </div>
            </td>` +
            `<td>
                <div class="col-5 ml-auto mr-auto">
                    <div class="form-group">
                        <button class="btn btn-danger btn-round deleteRow" style="color: #fff">-</button>
                    </div>
                </div>
            </td>` +
            `</tr>
            `;
        $('tbody#branchBody_variant_branch').append(ptr);
    }

    function loadAllVariants(data) {
        let text = "";
        let outofstock =
            `<span style="font-weight:700;font-size: 9px;color:#fff;background-color:#f00;padding: 2px 5px;margin: 0px;border-radius: 14px;text-transform: uppercase;white-space: nowrap;margin-left: 5px;">Out of stock</span>`;
        data.forEach(function(item, i, arr) {
            if (item) {
                let branchNames = [];
                let delBtn = `
                    <button type="button" class="btn btn-danger btn-round" onclick="delVariant(${item.name});">
                        <div>Delete</div>
                    </button>
                `;
                if (item.product_id != 0) {
                    delBtn = `
                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(item.id, '{{ route('admin.product-variant-delete', ['productVariant' => ':id']) }}', loadAllVariants);">
                        <div>Delete</div>
                    </button>
                `;
                }
                // {{-- showDeleteMessage(${item.id}, '{{ route('admin.product-variant-delete', ['productVariant' => ':id']) }}', loadAllVariants) --}}
                item.branch_products.forEach(data => {
                    if (Number(data.quantity) <= Number(data.minquantity)) {
                        name = `<li style="color: #f00">${data.branch.name}${outofstock}</li>`
                    } else {
                        name = `<li style="color: #000">${data.branch.name}</li>`
                    };
                    branchNames.push(name);
                });
                branchNames = branchNames.join("");
                text += `
                <tr>
                    <td>${item.id}</td>
                    <td>${item.name}</td>
                    <td><img src="${item.image}" alt="${item.name} image" height="100px"></td>
                    <td style="text-align: left;">
                        <ul class="scrollable-wrap">
                            ${branchNames}
                        </ul>
                    </td>
                    <td class="text-center">${item.price}</td>
                    <td class="text-center">${item.dprice ? item.dprice : '-'}</td>
                    <td class="text-center">
                        <div class="">
                            <p class="text-info m-0"><strong>${item.timeago}</strong></p>
                        </div>
                        <p class="h5">${item.updated_at2}</p>
                    </td>
                    <td class="text-center" style="white-space:nowrap;">
                        <button type="button" class="btn btn-info btn-round" onclick="editVariableItem(${item.id}, '${item.name}')">
                            Edit
                        </button>
                        ${delBtn}
                    </td>
                </tr>
                `
            }
            {{--
                // onclick="deleteVariableItem(${item.id}, '${item.name}')"
                // onclick="showDeleteMessage(${item.id}, '{{ route('admin.payment-method-delete', ':id') }}')"
                 --}}
        });
        document.getElementById("pv_table_body").innerHTML = text;
    }

    function deleteVariableItem(id) {
        // if (id == 0) {
        //     let
        //         cacheVariant.forEach(function(item, i, arr) {

        //         });
        // }
        swal({
            title: "Are you sure?",
            text: "You will not be able to recover this item!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, delete it!",
            cancelButtonText: "No, cancel please!",
            closeOnConfirm: true,
            closeOnCancel: true
        }, function(isConfirm) {
            if (isConfirm) {
                $.ajax({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                    },
                    type: 'POST',
                    url: '{{ url('productVariantsDelete') }}',
                    data: {
                        id: id,
                        parent: editId,
                    },
                    success: function(data) {
                        // console.log(data);
                        if (data.error != "0" || data.data == null) {
                            if (data.error == "1") {
                                return messageStatus('error', 'Error', data);
                            }
                            return messageStatus('error', 'Error', {
                                text: 'Something went wrong'
                            });
                        }
                        loadAllVariants(data.data);
                    },
                    error: function(e) {
                        console.log(e);
                        messageStatus('error', 'Error', e.responseJSON);
                    }
                });
            } else {}
        });
    }

    let editId = 0;
    let editBranches_product_branch;

    function editItem(id) {
        let url = `{{ route('admin.product-get', ':id') }}`;
        url = url.replace(':id', id);
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: url,
            data: {},
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log("Product edit res: ", data);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        text: 'Something went wrong'
                    });
                }
                document.getElementById("tabEdit").style.display = "block";
                $('.nav-tabs a[href="#edit"]').tab('show');
                let target = document.getElementById("form");
                document.getElementById('editForm').appendChild(target);

                // Clear previous data
                clearForm();

                document.getElementById("name").value = data.data.name;
                editId = data.data.id;
                $('#parent').val(data.data.category_id).change();
                document.getElementById("price").value = data.data.price;
                document.getElementById("discount_price").value = data.data.discount_price;
                document.getElementById("desc").value = data.data.desc;
                document.getElementById("video_link").value = data.data.video_link;
                clearDropZone();
                // console.log("images: ", data.data);
                addEditImages(data.data.image_files);
                // branch reset button show = true
                resetButtonView('product_branch', 1);
                onSetCheck_visible(data.data.visible);
                onSetCheck_featured(data.data.featured);
                onSetCheck_exited(data.data.soon_instock);
                initAddressDataTable(data.data.branch_products);
                productAddressTableData = data.data.branch_products;
                // console.log('edit productAddressTableData: ', productAddressTableData);
                // productAddressPaginationGoPage(1);
                // initAddressDataTable(data.data.branch_products);
                // branch
                let branchProducts = data.data.branch_products;
                let branches = data.data.branches;
                let lastSelected = '';
                let options = [];
                allBranches.forEach(data => {
                    options.push(
                        `<option id="${data.id}" value="${data.id}">${data.name}</option>`
                    );
                });
                $('tbody .branch_element_product_branch').remove();
                let ptr = '';
                if (branchProducts.length != 0) {
                    editBranches_product_branch = branchProducts;
                    branchProducts.forEach(data => {
                        let options = '';
                        allBranches.forEach(data1 => {
                            if (data1.id != data.branch.id) {
                                options +=
                                    `<option id="${data1.id}" value="${data1.id}">${data1.name}</option>`;
                            } else {
                                options +=
                                    `<option id="${data.branch.id}" value="${data.branch.id}" selected="selected">${data.branch.name}</option>`;
                            }
                        });
                        ptr = `
                        <tr class="branch branch_element_product_branch">` +
                            `<td>
                                <div id="element_branchName">
                                    <div class="form-group">
                                        <select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_product_branch">
                                            <option value="0" selected>--Please choose a branch--</option>
                                            ${options}
                                        </select>
                                    </div>
                                </div>
                            </td>` +
                            `<td>
                                <div id="element_quantity">
                                    <div class="form-group">
                                        <input type="number" name="quantity" id="quantity" class="form-control quantity" value="${data.quantity}">
                                    </div>
                                </div>
                            </td>` +
                            `<td>
                                <div id="element_minQuantity">
                                    <div class="form-group">
                                        <input type="number" name="minquantity" id="minquantity" class="form-control minquantity" value="${data.minquantity}">
                                    </div>
                                </div>
                            </td>` +
                            `<td>
                                <div id="element_maxQuantity">
                                    <div class="form-group">
                                        <input type="number" name="maxquantity" id="maxquantity" class="form-control maxquantity" value="${data.maxquantity}">
                                    </div>
                                </div>
                            </td>` +
                            `<td>
                                <div class="col-5 ml-auto mr-auto">
                                    <div class="form-group">
                                        <button class="btn btn-danger btn-round btn-block deleteRow">-</button>
                                    </div>
                                </div>
                            </td>` +
                            `</tr>`;
                        $('tbody#branchBody_product_branch').append(ptr);
                    });

                    // For Object Type
                    // for (const key in notAddedBranches) {
                    //     if (notAddedBranches.hasOwnProperty(key)) {
                    //         let trNotAdded =
                    //             `<option id="${notAddedBranches[key].id}" value="${notAddedBranches[key].id}">${notAddedBranches[key].name}</option>`;
                    //         $('.branchNames_product_branch').append(trNotAdded);
                    //         // $('#branchName').append($('<option>', {
                    //         //     id: notAddedBranches[key].id,
                    //         //     value: notAddedBranches[key].id,
                    //         //     text : notAddedBranches[key].name
                    //         // }));
                    //     }
                    // }

                    // For Array Type
                    // notAddedBranches.forEach(data => {
                    //     let trNotAdded = `<option id="${data.id}" value="${data.id}">${data.name}</option>`;
                    //     $('.branchNames').append(trNotAdded);
                    // });
                } else {
                    ptr = `
                    <tr class="branch branch_element_product_branch">` +
                        `<td>
                            <div id="element_branchName">
                                <div class="form-group">
                                    <select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_product_branch">
                                        <option value="0" selected>--Please choose a branch--</option>
                                        ${options}
                                    </select>
                                </div>
                            </div>
                        </td>` +
                        `<td>
                            <div id="element_quantity">
                                <div class="form-group">
                                    <input type="number" name="quantity" id="quantity" class="form-control quantity" value="0">
                                </div>
                            </div>
                        </td>` +
                        `<td>
                            <div id="element_minQuantity">
                                <div class="form-group">
                                    <input type="number" name="minquantity" id="minquantity" class="form-control minquantity" value="0">
                                </div>
                            </div>
                        </td>` +
                        `<td>
                            <div id="element_maxQuantity">
                                <div class="form-group">
                                    <input type="number" name="maxquantity" id="maxquantity" class="form-control maxquantity" value="0">
                                </div>
                            </div>
                        </td>` +
                        `<td>
                            <div class="col-5 ml-auto mr-auto">
                                <div class="form-group">
                                    <button class="btn btn-danger btn-round btn-block deleteRow">-</button>
                                </div>
                            </div>
                        </td>` +
                        `</tr>`;
                    $('tbody#branchBody_product_branch').append(ptr);
                }
                //
                loadAllVariants(data.data.variants);
                if (data.data.variants.length != 0) {
                    productVariants = true;
                    document.getElementById("productVariantsItems").hidden = false;
                }
                loadAllRProducts(data.data.recommended);
                if (data.data.recommended.length != 0) {
                    recommendedProducts = true;
                    document.getElementById("recommendedProductsItems").hidden = false;
                }
                // Load product business
                paginationPrBisGoPage(prBisCurrentPage);
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
                return messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    let selectedProductAddressId = 0;

    function editProductAddress(id, branchId, quantity, minquantity, maxquantity) {
        newDataUpdate = true;
        if (!id) {
            $('#product_adderss').val(branchId).change();
            document.getElementById("product_quantity").value = quantity;
            document.getElementById("product_minquantity").value = minquantity;
            document.getElementById("product_maxquantity").value = maxquantity;
            document.getElementById("product_add_btn").innerHTML = "Update";
            return;
        }
        let url = `{{ route('admin.branch-product-get', ':id') }}`;
        url = url.replace(':id', id);
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: url,
            data: {},
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log("product address get res: ", data);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        text: 'Something went wrong'
                    });
                }
                selectedProductAddressId = data.data.id;
                $('#product_adderss').val(data.data.branch_id).change();
                document.getElementById("product_quantity").value = data.data.quantity;
                document.getElementById("product_minquantity").value = data.data.minquantity;
                document.getElementById("product_maxquantity").value = data.data.maxquantity;
                document.getElementById("product_add_btn").innerHTML = "Update";
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
                return messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    let addressCurrentPage = 1;
    let productAddressDatas = [];

    let productAddressTableData = [];
    let newProductAddressData = [];
    let newDataUpdate = false;

    function productAddressPaginationGoPage(page) {
        data = {
            id: editId,
            page: page,
            sortAscDesc: sortBy,
            sortBy: sort,
        };

        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: '{{ route('admin.product-address-get') }}',
            data: removeEmptyData(data),
            success: function(data) {
                // console.log('get product address res: ', data);
                if (data.error || data.data == null) {
                    return messageStatus('error', 'Error', {
                        text: 'Something went wrong'
                    });
                }
                addressCurrentPage = data.data.current_page;
                let pages_ = Math.ceil(data.data.total / data.data.per_page);
                productAddressDatas = data.data.data;
                productAddressTableData = [
                    ...newProductAddressData,
                    ...productAddressDatas
                ];
                initAddressDataTable(productAddressTableData);
                initPaginationLine(pages_, addressCurrentPage, 'address-pagination-list',
                    productAddressPaginationGoPage);
            },
            error: function(e) {
                dataLoading = false;
                console.log(e);
            }
        });
    }

    function addProductAddress() {
        let address = document.getElementById("product_adderss").value;
        let quant = document.getElementById("product_quantity").value;
        let minQuant = document.getElementById("product_minquantity").value;
        let maxQuant = document.getElementById("product_maxquantity").value;
        if (
            messageStatus('error', 'Error', {
                text: 'The address field is required'
            }, !address) ||
            messageStatus('error', 'Error', {
                text: 'The quantity field is required'
            }, quant == 0) ||
            messageStatus('error', 'Error', {
                text: 'The minimum quantity field is required'
            }, minQuant == 0) ||
            messageStatus('error', 'Error', {
                text: 'The maximum quantity field is required'
            }, maxQuant == 0)
        ) {
            return;
        }

        // console.log('branch check: ', productAddressTableData, parseInt(address))

        if (!newDataUpdate) {
            for (const data of productAddressDatas) {
                if (data.branch.id == address) {
                    return messageStatus('error', 'Error', {
                        msg: "Already added"
                    });
                }
            }

            for (const data of productAddressTableData) {
                if (data.branch.id == address) {
                    return messageStatus('error', 'Error', {
                        msg: "Already added"
                    });
                }
            }
        }

        if (selectedProductAddressId == 0) {
            newProductAddressData = [{
                    branch: {
                        id: parseInt(address),
                        name: $('#product_adderss option:selected').text()
                    },
                    quantity: parseInt(quant),
                    minquantity: parseInt(minQuant),
                    maxquantity: parseInt(maxQuant),
                },
                ...newProductAddressData.filter((data) => {
                    return data.branch.id !== parseInt(address)
                })
            ];
        } else {
            loadingIcon('loading-bar', 1);
            return $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                },
                type: 'POST',
                url: '{{ route('admin.update-branch-product') }}',
                data: removeEmptyData({
                    id: selectedProductAddressId,
                    quantity: parseInt(quant),
                    minquantity: parseInt(minQuant),
                    maxquantity: parseInt(maxQuant),
                }),
                success: function(data) {
                    loadingIcon('loading-bar', 0);
                    // console.log('branch product update res: ', data);
                    if (data.error) {
                        return messageStatus('error', 'Error', data);
                    }

                    productAddressPaginationGoPage(addressCurrentPage);
                    clearProductAddressForm();
                    messageStatus('success', 'Success', {
                        text: "Data saved successfully"
                    });
                },
                error: function(e) {
                    loadingIcon('loading-bar', 0);
                    console.log(e);
                    messageStatus('error', 'Error', e.responseJSON);
                }
            });
        }

        productAddressTableData = [...newProductAddressData, ...productAddressDatas];
        initAddressDataTable(productAddressTableData);
        clearProductAddressForm();
    }

    function clearProductAddressForm() {
        newDataUpdate = false;
        selectedProductAddressId = 0;
        document.getElementById("product_add_btn").innerHTML = "Add";
        document.getElementById("product_adderss").value = '';
        document.getElementById("product_quantity").value = 0;
        document.getElementById("product_minquantity").value = 0;
        document.getElementById("product_maxquantity").value = 0;
    }

    $("#name,#price,#discount_price,#desc").on('keyup', function(event) {
        let el = event.target;
        if (el.id == 'name' || el.id == 'desc') {
            el.classList.remove('is-valid', 'is-invalid');
            if (el.value.length > 0) {
                el.classList.add('is-valid');
            } else {
                el.classList.add('is-invalid');
            }
        }

        if (el.id == 'price') {
            el.classList.remove('is-valid', 'is-invalid');
            if (el.value && el.value != 0) {
                el.classList.add('is-valid');
            } else {
                el.classList.add('is-invalid');
            }
        }

        if (el.id == 'discount_price') {
            el.classList.remove('is-valid');
            if (el.value && el.value != 0) {
                el.classList.add('is-valid');
            }
        }
    });

    $("#parent").on('change', function(event) {
        let el = event.target;
        if (el.id == 'parent') {
            el.classList.remove('is-valid', 'is-invalid');
            if (el.value != 0) {
                el.classList.add('is-valid');
            } else {
                el.classList.add('is-invalid');
            }
        }
    });

    // Adding each branches value
    $('#onSave').click(function() {
        // let name_el = document.getElementById("name");
        // if (!name_el.value) {
        //     return showNotification('danger', 'The Name field is required', 'top', 'right');
        // }
        // if (!document.getElementById("price").value) {
        //     return showNotification('danger', 'The Price field is required', 'top', 'right');
        // }
        // if ($('select[id=parent]').val() == "0") {
        //     return showNotification('danger', 'The Category field is required', 'top', 'right');
        // }
        // checkDublicateBranch(1, 'product_branch');
        // if (dublicateBranch) {
        //     return;
        // }

        // // add branch value to an array
        // let branchData = [];
        // // check product variant branch field error state
        // let checkProduct = 1;
        // $('.branch_element_product_branch').each(function() {
        //     $(this).css("outline", "");
        //     $(this).css("border-radius", "");
        //     // $(this).find('.branchNames').val() ? $(this).find('.branchNames').val() : $(this).find('.branchNames option').val();
        //     let branch_id = $(this).find('.branchNames :selected').attr("value") ?? 0;
        //     let quantity = $(this).find('.quantity').val() ?? 0;
        //     let minquantity = $(this).find('.minquantity').val() ?? 0;
        //     // Stock product type
        //     if (branch_id != 0 && quantity && minquantity) {
        //         branchData.push({
        //             "branch_id": parseInt(branch_id),
        //             "quantity": parseInt(quantity),
        //             "minquantity": parseInt(minquantity)
        //         });
        //     } else {
        //         checkProduct = 0;
        //         $(this).css("outline", "medium solid #ff674c");
        //         $(this).css("border-radius", "4px");
        //         if (branch_id == 0) {
        //             showNotification('danger', 'Select address', 'top', 'right');
        //         }
        //         if (!quantity) {
        //             showNotification('danger',
        //                 `Add quantity to ${$(this).find('.branchNames :selected').text()} address`,
        //                 'top', 'right');
        //         }
        //         if (!minquantity) {
        //             showNotification('danger',
        //                 `Add minimum quantity to ${$(this).find('.branchNames :selected').text()} address`,
        //                 'top', 'right');
        //         }
        //         return;
        //     }
        // });

        // if (branchData.length == 0) {
        //     return showNotification('danger', 'The Address field is required', 'top', 'right');
        // }

        if (newProductAddressData.length == 0 && productAddressTableData.length == 0) {
            return showNotification('danger', 'The Address field is required', 'top', 'right');
        }

        // if (!checkProduct)
        //     return;
        onSave(newProductAddressData);
    });

    function onSave(branchData) {
        let images = [];
        let imageid = 0;
        for (let i = 0; i < imageArray.length; i++) {
            if (i === 0)
                imageid = imageArray[i].id;
            else
                images.push(imageArray[i].id);
        };
        let data = {
            id: editId,
            name: document.getElementById("name").value,
            parent: $('select[id=parent]').val(),
            price: document.getElementById("price").value,
            discPrice: document.getElementById("discount_price").value,
            desc: document.getElementById("desc").value,
            videoLink: convertToEmbedLink(document.getElementById("video_link").value),
            images: imageArray,
            published: (visible) ? 1 : 0,
            featured: (featured) ? 1 : 0,
            exited: (exited) ? 1 : 0,
            cacheRProducts: cacheRProducts,
            cacheVariant: cacheVariant,
            branchData: branchData,
            cachePrBusCalculator: cacheProductBusiness,
        };
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.product-add') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('product add res: ', data);
                if (data.error == 2 || data.error == 3) {
                    return messageStatus('error', 'Error', data);
                } else if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        text: 'Something went wrong'
                    });
                }
                if (editId != 0) {
                    paginationGoPage(currentPage);
                } else {
                    let text = buildOneItem(data.data);
                    let text2 = document.getElementById("table_body").innerHTML;
                    document.getElementById("table_body").innerHTML = text + text2;
                }
                $('.nav-tabs a[href="#home"]').tab('show');
                clearForm();
                return messageStatus('success', 'Success', {
                    text: 'Data saved successfully'
                });
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
                return messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function resetButtonView(id, status) {
        let button = document.getElementById(`resetBranch_${id}`);
        if (button) {
            if (status == 1)
                button.style.display = "inline-block";
            else
                button.style.display = "none";
        }
    }

    let dublicateBranch = false;

    function checkDublicateBranch(noNotify, id) {
        // default
        dublicateBranch = false;
        // let select = $(`#branchTable_${id} .branch`).find(`.branchNames_${id}`);
        let select = document.querySelectorAll(`#branchBody_${id} .branch .branchNames`);
        let values = [];
        // Clear outline
        for (i = 0; i < select.length; i++) {
            let el = select[i].options[select[i].selectedIndex].parentElement.parentElement.parentElement;
            el.style.outline = '';
            el.style.borderRadius = "0px";
        }

        // Checking dublicate branch
        for (i = 0; i < select.length; i++) {
            let el = select[i].options[select[i].selectedIndex].parentElement.parentElement.parentElement;
            // let select = select[i];
            // console.log("testing: ", select[i].options[select[i].selectedIndex], values.indexOf(select[i].options[
                // select[i].selectedIndex].id));
            if (values.indexOf(select[i].options[select[i].selectedIndex].id) > -1) {
                // Dublicate found
                dublicateBranch = true;
                el.style.outline = 'medium solid #ff674c';
                el.style.borderRadius = "4px";
                if (noNotify != 0) {
                    messageStatus('error', 'Error', {
                        text: `Duplicate branch form entry found. Please remove dublicate branch name: <b>${select[i].options[select[i].selectedIndex].text.replace(/^\s+|\s+$/gm, '')}</b>`
                    });
                }
                break;
            } else {
                values.push(select[i].options[select[i].selectedIndex].id);
            }
        };
    }

    // Clear forms
    function clearForm() {
        editId = 0;
        newProductAddressData = [];
        productAddressTableData = [];
        initAddressDataTable(productAddressTableData);
        // name
        document.getElementById("name").value = "";
        // published
        onSetCheck_visible(true);
        // featured
        onSetCheck_featured(false);
        // exited
        onSetCheck_exited(false);
        // price
        document.getElementById("price").value = "";
        // discount_price
        document.getElementById("discount_price").value = "";
        document.getElementById("desc").value = "";
        document.getElementById("video_link").value = "";
        // $('#parent').val(0).change();
        // document.querySelector('#parent').selectedIndex = "0";
        resetButtonView('product_branch', 0);
        editBranches_product_branch = [];
        clearDropZone();
        // Clear product business
        clearPrBusForm();
        clearPrBusTable();

        // product variant
        productVariants = false;
        document.getElementById("pv_name").value = "";
        document.getElementById("pv_price").value = "";
        document.getElementById("pv_discountprice").value = "";
        document.getElementById("pv_video_link").value = "";
        document.getElementById("productVariantsItems").hidden = true;
        document.getElementById("pv_table_body").innerHTML = "";
        cacheVariant = [];
        resetButtonView('variant_branch', 0);
        editBranches_variant_branch = [];
        clearDropZone3();

        // recommended product
        document.getElementById("rp_table_body").innerHTML = "";
        recommendedProducts = false;
        document.getElementById("recommendedProductsItems").hidden = true;
        cacheRProducts = [];

        // clear product and variant product addresses
        clearProductBranchForm();

        //
        $('.branchNames').val('0').change();
        $('#quantity').val("").change();
        $('#minquantity').val("").change();
        $('#maxquantity').val("").change();
        // document.getElementById("quantity").value = "";
        // document.getElementById("minquantity").value = "";
    }

    function clearProductBranchForm() {
        // Remove current product branches
        $('tbody .branch_element_product_branch').remove();
        // Remove current vairant branches
        $('tbody .branch_element_variant_branch').remove();
        // options list
        let options = [];
        // Branches
        allBranches.forEach(data => {
            options.push(
                `<option id="${data.id}" value="${data.id}">${data.name}</option>`
            );
        });
        // Branch lists
        let ptr = `
            <tr class="branch branch_element_product_branch">` +
            `<td>
                    <div id="element_branchName">
                        <div class="form-group">
                            <select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_product_branch">
                                <option value="0" selected>--Please choose a branch--</option>
                                ${options}
                            </select>
                        </div>
                    </div>
                </td>` +
            `<td>
                    <div id="element_quantity">
                        <div class="form-group">
                            <input type="number" value="0" name="quantity" id="quantity" class="form-control quantity" value="0">
                        </div>
                    </div>
                </td>` +
            `<td>
                    <div id="element_minQuantity">
                        <div class="form-group">
                            <input type="number" value="0" name="minquantity" id="minquantity" class="form-control minquantity" value="0">
                        </div>
                    </div>
                </td>` +
            `<td>
                    <div id="element_maxQuantity">
                        <div class="form-group">
                            <input type="number" value="0" name="maxquantity" id="maxquantity" class="form-control maxquantity" value="0">
                        </div>
                    </div>
                </td>` +
            `<td>
                    <div class="col-5 ml-auto mr-auto">
                        <div class="form-group">
                            <button class="btn btn-danger btn-round btn-block deleteRow">-</button>
                        </div>
                    </div>
                </td>` +
            `</tr>`;
        let vtr = `
                <tr class="branch branch_element_variant_branch">` +
            `<td>
                    <div id="pv_element_branchName">
                        <div class="form-group">
                            <select name="branchName" id="branchName" class="form-control col-12 branchNames branchNames_variant_branch">
                                <option value="0" selected>--Please choose a branch--</option>
                                ${options}
                            </select>
                        </div>
                    </div>
                </td>` +
            `<td>
                    <div id="element_quantity">
                        <div class="form-group">
                            <input type="number" value="0" name="quantity" id="quantity" class="form-control quantity" value="0">
                        </div>
                    </div>
                </td>` +
            `<td>
                    <div id="element_minQuantity">
                        <div class="form-group">
                            <input type="number" value="0" name="minquantity" id="minquantity" class="form-control minquantity" value="0">
                        </div>
                    </div>
                </td>` +
            `<td>
                    <div id="element_maxQuantity">
                        <div class="form-group">
                            <input type="number" value="0" name="maxquantity" id="maxquantity" class="form-control maxquantity" value="0">
                        </div>
                    </div>
                </td>` +
            `<td>
                    <div class="col-5 ml-auto mr-auto">
                        <div class="form-group">
                            <button class="btn btn-danger btn-round btn-block deleteRow">-</button>
                        </div>
                    </div>
                </td>` +
            `</tr>`;
        // For product
        $('tbody#branchBody_product_branch').append(ptr);
        // For variant
        $('tbody#branchBody_variant_branch').append(vtr);
    }
</script>

{{-- Product business calculator --}}
<script>
    // Product listsings
    let prBispages = 1;
    let prBisCurrentPage = 1;
    let prBisSortPublished = '1';
    let prBisSortUnPublished = '1';
    let prBisQuery = "";
    let prBisSort = "updated_at";
    let prBisSortBy = "desc";
    let cacheProductBusiness = [];
    let prdBisId = 0;

    // paginationPrBisGoPage(1);
    initPrBisPaginationLine(prBispages, prBisCurrentPage, 'product_business_pagination-list', paginationPrBisGoPage);
    initPrBisTableHeader();

    function paginationPrBisGoPage(page) {
        data = {
            prId: editId,
            page: page,
            per_page: 20,
            sort: prBisSortBy,
            order_by: prBisSort,
            query: prBisQuery,
            visible: prBisSortPublished,
            invisible: prBisSortUnPublished,
        };

        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: '{{ route('admin.product-business-calculators-get') }}',
            data: removeEmptyData(data),
            success: function(data) {
                // console.log('product get', data);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        text: 'Something went wrong'
                    });
                }
                prBisCurrentPage = data.data.current_page;
                prBispages = data.data.last_page;
                // cacheProductBusiness = data.data.data;
                initPrBisDataTable(data.data.data);
                initPrBisPaginationLine(prBispages, prBisCurrentPage,
                    'product_business_pagination-list',
                    paginationPrBisGoPage);
                initPrBisTableHeader();
            },
            error: function(e) {
                dataLoading = false;
                console.log(e);
                return messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function initPrBisDataTable(data) {
        // console.log('init PrBis: ', data);
        let html = "";
        if (data.length == 0) {
            html = `
            <tr>
                <td colspan="13">
                    <div class="d-flex justify-content-center align-items-center">
                        <p class="m-0">There are no records</p>
                    </div>
                </td>
            </tr>
            `;
            document.getElementById("product_business_table_body").innerHTML = html;
        } else {
            data.forEach(function(item, i, arr) {
                html += buildPrBisOneItem(item);
            });
            document.getElementById("product_business_table_body").innerHTML = html;
        }
    }

    function buildPrBisOneItem(item) {
        if (item.percent == 1)
            var percent = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;
        else
            var percent = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;

        if (item.visible == 1)
            var visible = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;
        else
            var visible = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;

        let time = '-';
        if (item.timeago) {
            time = `
                <div class="">
                    <p class="text-info m-0"><strong>${item.timeago}</strong></p>
                </div>
                <p class="h5">${item.updated_at2}</p>
            `;
        }
        return `
            <tr>
                <td>${item.id == 0 ? '-' : item.id}</td>
                <td>${item.name}</td>
                <td>${item.amount_of_items}</td>
                <td>${item.from_amount}</td>
                <td class="text-center">${item.to_amount}</td>
                <td class="text-center">${item.rental_yield}</td>
                <td class="text-center">${item.property_appreciation}</td>
                <td class="text-center">${item.discount_amount ?? '-'}</td>
                <td>${item.incentive ?? '-'}</td>
                <td>${item.point ?? '-'}</td>
                <td>
                    <div style="display: flex;">${percent}</div>
                </td>
                <td>
                    <div style="display: flex;">${visible}</div>
                </td>
                <td class="text-center">
                    ${time}
                </td>
                <td class="text-center" style="white-space:nowrap;">
                    <button type="button" class="btn btn-info btn-round" onclick="editPrBisItem(${item.id})">
                        Edit
                    </button>
                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '{{ route('admin.product-business-calculator-delete', ':id') }}')">
                        <div>Delete</div>
                    </button>
                </td>
            </tr>
        `;
    }

    function initPrBisPaginationLine(pages, page, id, callback) {
        allPages = pages;
        currentPage = page;
        let pageNumbers = '';
        pageNumbers = buildPagination(currentPage, allPages);
        let paginationList = document.getElementById(id);
        if (paginationList) {
            paginationList.innerHTML = pageNumbers;
            initializePaginations(callback, id);
        }
    }

    function tablePrBisHeaderSort(newsort) {
        if (newsort == sort) {
            if (sortBy == "asc")
                sortBy = "desc";
            else
                sortBy = "asc";
        } else {
            sort = newsort
            sortBy = "asc";
        }
        paginationPrBisGoPage(currentPage);
    }

    function initPrBisTableHeader() {
        let header = `
        <tr>
            <th>ID <img onclick="tablePrBisHeaderSort('id');" src="${utilGetImg('id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Name <img onclick="tablePrBisHeaderSort('name');" src="${utilGetImg('name')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Year <img onclick="tablePrBisHeaderSort('amount_of_items');" src="${utilGetImg('amount_of_items')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>From Amount <img onclick="tablePrBisHeaderSort('from_amount');" src="${utilGetImg('from_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>To Amount <img onclick="tablePrBisHeaderSort('to_amount');" src="${utilGetImg('to_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Rental Yield <img onclick="tablePrBisHeaderSort('rental_yield');" src="${utilGetImg('rental_yield')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Property Appreciation <img onclick="tablePrBisHeaderSort('property_appreciation');" src="${utilGetImg('property_appreciation')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Discount <img onclick="tablePrBisHeaderSort('discount_amount');" src="${utilGetImg('discount_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Incentive <img onclick="tablePrBisHeaderSort('incentive');" src="${utilGetImg('discount_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Point <img onclick="tablePrBisHeaderSort('point');" src="${utilGetImg('discount_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">In Percent <img onclick="tablePrBisHeaderSort('percent');" src="${utilGetImg('percent')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Visible <img onclick="tablePrBisHeaderSort('visible');" src="${utilGetImg('visible')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Updated At <img onclick="tablePrBisHeaderSort('updated_at');" src="${utilGetImg('updated_at')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Action</th>
        </tr>
        `;
        document.getElementById("product_business_table_header").innerHTML = header;
        document.getElementById("product_business_table_footer").innerHTML = header;
    }

    function editPrBisItem(id, name) {
        if (id == 0) {
            let val = cacheProductBusiness.find((item, index) => {
                if (item.name == name) {
                    return item;
                }
            });
            // if none is found it will return undefined
            // console.log('val: ', val);
            return fillPrBusForm(val);
        }
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.product-business-calculators-show') }}',
            data: {
                id: id,
            },
            success: function(data) {
                // console.log("edit product busi response: ", data);
                loadingIcon('loading-bar', 0);
                if (data.error || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: 'Something went wrong'
                    });
                }
                prdBisId = data.data.id;
                fillPrBusForm(data.data);
                document.getElementById("add_productbis_add_btn").innerHTML = "Update";
            },
            error: function(e) {
                console.log(e);
                loadingIcon('loading-bar', 0);
                return messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function onProductBisSave() {
        let name = document.getElementById("bc_name").value;
        let year = document.getElementById("bc_year").value;
        let rentalYield = document.getElementById("bc_rental_yield").value;
        let propertyAppreciation = document.getElementById("bc_property_appreciation").value;
        let fromAmount = document.getElementById("minimum-amount").value;
        let toAmount = document.getElementById("minimum-amount_1").value;
        let discountAmount = document.getElementById("discount-amount").value;
        let discountAmountType = document.getElementById("discount-amount_select").value;
        let incentive = document.getElementById("incentive").value;
        let point = document.getElementById("point").value;
        let discountType = '0';

        // Check invalid fields
        if (
            messageStatus('error', 'Error', {
                msg: "The Name field is required"
            }, !name) ||
            messageStatus('error', 'Error', {
                msg: "The Year field is required"
            }, !year) ||
            messageStatus('error', 'Error', {
                msg: "The Rental Yield field is required"
            }, !rentalYield) ||
            messageStatus('error', 'Error', {
                msg: "The Property Appreciation field is required"
            }, !propertyAppreciation) ||
            messageStatus('error', 'Error', {
                msg: "The minimum purchase amount field is required"
            }, !fromAmount) ||
            messageStatus('error', 'Error', {
                msg: "The maximum purchase amount field is required"
            }, !toAmount)
        ) {
            return;
        }

        if (discount_type) {
            incentive = null;
            point = null;
            discountAmountType = '1';
            // if (messageStatus(!discountAmount, "The discount amount field is required", "danger")) return;
        }

        if (incentive_type) {
            discountAmount = null;
            discountAmountType = '2';
            point = null;
            if (messageStatus('error', 'Error', {
                    msg: "The Incentive field is required"
                }, !incentive)) return;
        }
        if (point_type) {
            discountAmount = null;
            discountAmountType = '3';
            incentive = null;
            if (messageStatus('error', 'Error', {
                    msg: "The point field is required"
                }, !point)) return;
        }

        let prBisdata = {
            id: prdBisId,
            name: name,
            amount_of_items: year,
            rental_yield: rentalYield,
            property_appreciation: propertyAppreciation,
            product_id: editId,
            from_amount: fromAmount,
            to_amount: toAmount,
            discount_amount: discountAmount,
            discount_type: discountAmountType,
            incentive: incentive,
            point: point,
            status: 'Pending',
            percent: (discountAmountType == 2) ? 1 : 0,
            visible: (bc_visible) ? 1 : 0
        }

        loadingIcon('loading-bar', 1);
        if (editId != 0) {
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
                },
                type: 'POST',
                url: '{{ route('admin.product-business-calculator-add') }}',
                data: removeEmptyData(prBisdata),
                success: function(data) {
                    loadingIcon('loading-bar', 0);
                    if (data.error || data.data == null) {
                        return messageStatus('error', 'Error', {
                            msg: "Something went wrong"
                        }, (data.error || data.data == null));
                    }
                    let newData = data.data;
                    document.getElementById("add_productbis_add_btn").innerHTML = "Add";
                    let checkData = cacheProductBusiness.find((data) => {
                        if (data.id == newData.id) return data
                    });
                    if (checkData) {
                        cacheProductBusiness = cacheProductBusiness.map((data) => {
                            if (data.id == prBisdata.id) {
                                return newData;
                            }
                            return data;
                        });
                    } else {
                        cacheProductBusiness.push(newData);
                    }
                    initPrBisDataTable(cacheProductBusiness);
                    clearPrBusForm();
                    messageStatus('success', 'Success', {
                        msg: "Data saved successfully"
                    });
                },
                error: function(e) {
                    loadingIcon('loading-bar', 0);
                    messageStatus('error', 'Error', e.responseJSON);
                }
            });
            return;
        } else {
            if (cacheProductBusiness && cacheProductBusiness.length == 0) {
                cacheProductBusiness.push(prBisdata);
            } else {
                let checkData = cacheProductBusiness.find((data) => {
                    if (data.name == prBisdata.name) return data
                });
                if (checkData) {
                    cacheProductBusiness = cacheProductBusiness.map((data) => {
                        if (data.name == prBisdata.name) {
                            return prBisdata;
                        }
                        return data;
                    });
                } else {
                    cacheProductBusiness.push(prBisdata);
                }
            }
            // console.log("cacheProductBusiness");
            // console.log(cacheProductBusiness);
            initPrBisDataTable(cacheProductBusiness);
            clearPrBusForm();
            loadingIcon('loading-bar', 0);
        }
    }

    const discountTypes = document.querySelectorAll('#discount_type, #incentive_type, #point_type');
    discountTypes.forEach(function(type) {
        type.addEventListener('click', function(e) {
            let id = e.target.id;
            discountTypesCheck(id);
        });
    });

    function discountTypesCheck(id) {
        // discount_type, incentive_type, point_type
        let views = document.querySelectorAll(
            '.discount-amount-view, .incentive-view, .point-view');
        let activeTap = '';
        if (id == 'discount_type' && discount_type) {
            activeTap = 'discount-amount-view';
            onSetCheck_point_type(false);
            onSetCheck_incentive_type(false);
            onSetCheck_discount_type(true);
        } else if (id == 'incentive_type' && incentive_type) {
            activeTap = 'incentive-view';
            onSetCheck_point_type(false);
            onSetCheck_discount_type(false);
            onSetCheck_incentive_type(true);
        } else if (id == 'point_type' && point_type) {
            activeTap = 'point-view';
            onSetCheck_incentive_type(false);
            onSetCheck_discount_type(false);
            onSetCheck_point_type(true);
        } else {
            activeTap = 'discount-amount-view';
            onSetCheck_point_type(false);
            onSetCheck_incentive_type(false);
            onSetCheck_discount_type(true);
        }
        views.forEach((view) => {
            if (view.classList.contains(activeTap)) {
                view.hidden = false;
            } else {
                view.hidden = true;
            }
        });
    }

    // fill forms
    function fillPrBusForm(data) {
        editId = data.id;
        document.getElementById("bc_name").value = data.name;
        document.getElementById("bc_year").value = data.amount_of_items;
        document.getElementById("minimum-amount").value = data.from_amount;
        document.getElementById("minimum-amount_1").value = data.to_amount;
        document.getElementById("bc_rental_yield").value = data.rental_yield;
        document.getElementById("bc_property_appreciation").value = data.property_appreciation;
        document.getElementById("discount-amount").value = data.discount_amount;
        document.getElementById("discount-amount_select").value = data.percent == 1 ? 2 : 1;
        document.getElementById("incentive").value = data.incentive;
        document.getElementById("point").value = data.point;
        if (data.discount_amount) {
            document.querySelector('#discount_type').click();
        } else if (data.incentive) {
            document.querySelector('#incentive_type').click();
        } else if (data.point) {
            document.querySelector('#point_type').click();
        };
        onSetCheck_visible(data.visible == 1);
    }

    // clear forms
    function clearPrBusForm() {
        prdBisId = 0;
        document.getElementById("bc_name").value = '';
        document.getElementById("bc_year").value = '';
        document.getElementById("bc_rental_yield").value = '';
        document.getElementById("bc_property_appreciation").value = '';
        document.getElementById("minimum-amount").value = 0;
        document.getElementById("minimum-amount_1").value = 0;
        document.getElementById("discount-amount").value = '';
        document.getElementById("discount-amount_select").value = 1;
        document.getElementById("incentive").value = '';
        document.getElementById("point").value = 0;
        document.querySelector('#discount_type').click();
        onSetCheck_visible(1);
    }

    // clear table
    function clearPrBusTable() {
        cacheProductBusiness = [];
        initPrBisDataTable(cacheProductBusiness);
    }
</script>
