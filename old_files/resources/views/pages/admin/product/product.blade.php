{{-- @extends('layouts.dasbhoard.dashboard-layout') --}}
@extends('layouts.dashboard-layout')
@inject('util', 'App\Models\Util')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card card-user">
                <!-- Tabs -->
                <div class="card-header card-header-danger">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <ul class="nav nav-tabs" data-tabs="tabs">
                                <li role="presentation" class="nav-item" id="tabHome">
                                    <a href="#home" class="nav-link active" data-toggle="tab">List</a>
                                </li>
                                <li role="presentation" class="nav-item">
                                    <a href="#create" class="nav-link" data-toggle="tab">Create</a>
                                </li>
                                <li role="presentation" class="nav-item" id="tabEdit" style="display: none;">
                                    <a href="#edit" class="nav-link" data-toggle="tab">Edit</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- End Tabs -->

                <!-- Tab List -->
                <div id="myTabContent" class="card-body">
                    <div class="tab-content text-left">

                        <!-- Tab View - Listings -->
                        <div role="tabpanel" class="tab-pane active" id="home">
                            <div class="card-header">
                                <h5 class="card-title">Product Listings</h5>
                                <div class="row">
                                    <div class="col-12 col-md-4">
                                        <div class="form-group">
                                            <label for="visible_search">Filter</label>
                                            <div class="row gap-4 col-12">
                                                <!-- Published -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'visible_search',
                                                    'text' => 'Published item',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                                <!-- Unpublished -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'unvisible_search',
                                                    'text' => 'Unpublished item',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                                <!-- Featured -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'featured_search',
                                                    'text' => 'Featured item',
                                                    'initvalue' => 'false',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-4 ml-md-auto">
                                        @include('components.input-field.text', [
                                            'text' => 'Search',
                                            'label' => 'Search',
                                            'type' => 'search',
                                            'id' => 'element_search',
                                        ])
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Table -->
                                @include('components.table.light-table')
                                <!-- End Table -->
                            </div>
                        </div>
                        <!-- End Tab View - Listings -->

                        <!-- Tab View - Create -->
                        <div role="tabpanel" class="tab-pane" id="create">
                            <div class="card-header">
                                <h5 class="card-title">Create Product</h5>
                            </div>
                            <div class="card-body">
                                <div id="createForm">
                                    <div id="form">
                                        <div class="row">
                                            <div class="col-md-6 col-12">
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Name',
                                                            'placeholder' => 'Enter name',
                                                            'desc' => 'Insert Name',
                                                            'id' => 'name',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.price', [
                                                            'label' => 'Price',
                                                            'id' => 'price',
                                                            'desc' => 'Insert price',
                                                            'request' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.price', [
                                                            'label' => 'Discount Price',
                                                            'id' => 'discount_price',
                                                            'desc' => 'Insert discount price',
                                                            'request' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text-area', [
                                                            'label' => 'Description',
                                                            'placeholder' => '...',
                                                            'desc' => 'Insert Product Description',
                                                            'id' => 'desc',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Video link',
                                                            'placeholder' => '',
                                                            'desc' => 'Insert Video link',
                                                            'id' => 'video_link',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="row flex-wrap col-12 gap-4">
                                                        <!-- visible -->
                                                        @include('components.input-field.checkbox-2', [
                                                            'id' => 'visible',
                                                            'text' => 'Published',
                                                            'initvalue' => 'true',
                                                        ])
                                                        <!-- featured -->
                                                        @include('components.input-field.checkbox-2', [
                                                            'id' => 'featured',
                                                            'text' => 'Featured',
                                                            'initvalue' => 'false',
                                                        ])
                                                        <!-- soon to be in stock -->
                                                        @include('components.input-field.checkbox-2', [
                                                            'id' => 'exited',
                                                            'text' => 'Secondary Market',
                                                            'initvalue' => 'false',
                                                        ])
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6 col-12">
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.images', []) {{-- Image dropbox --}}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-12 mt-4">
                                                <div class="col-12"
                                                    style="border-color: #2aabd2; border-style: dashed; border-width: 3px; padding: 15px;">
                                                    <div class="card-header">
                                                        <h5 class="card-title">Product Address Listings</h5>
                                                    </div>
                                                    <div class="col-md-12">
                                                        <!-- Table -->
                                                        {{-- @include('components.table.light-table', [
                                                            'id' => 'address',
                                                        ]) --}}
                                                        <div class="table-responsive">
                                                            <table class="table table-striped">
                                                                <thead class="text-primary">
                                                                    <tr>
                                                                        <th>Address</th>
                                                                        <th class="text-center">Qualtity</th>
                                                                        <th class="text-center">Minimum Qualtity</th>
                                                                        <th class="text-center">Maximum Qualtity</th>
                                                                        <th class="text-center">Updated At</th>
                                                                        <th class="text-center">Action</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="address_table_body">
                                                                </tbody>
                                                            </table>
                                                            <div class="d-flex justify-content-center align-items-center">
                                                                <nav aria-label="Page navigation">
                                                                    <ul class="pagination" id="address-pagination-list">
                                                                        {{-- <li class="page-item disabled">
                                                                            <a class="page-link" href="#"
                                                                                tabindex="-1">Previous</a>
                                                                        </li>
                                                                        <li class="page-item active">
                                                                            <a class="page-link" href="#">1 <span
                                                                                    class="sr-only">(current)</span></a>
                                                                        </li>
                                                                        <li class="page-item">
                                                                            <a class="page-link" href="#">2</a>
                                                                        </li>
                                                                        <li class="page-item"><a class="page-link"
                                                                                href="#">3</a></li>
                                                                        <li class="page-item">
                                                                            <a class="page-link" href="#"
                                                                                onclick="productAddressPaginationGoPage(2)">Next</a>
                                                                        </li> --}}
                                                                    </ul>
                                                                </nav>
                                                            </div>
                                                        </div>
                                                        <div class="row align-items-end mt-4">
                                                            <div class="col-12 col-md-8">
                                                                {{-- @include(
                                                                    'components.input-field.dropdown-with-image',
                                                                    [
                                                                        'label' => 'Product',
                                                                        'onchange' => '',
                                                                        'id' => 'productForList',
                                                                        'request' => 'true',
                                                                        'noitem' => 'false',
                                                                    ]
                                                                ) --}}
                                                            </div>
                                                            <div class="col-12">
                                                                <div class="row">
                                                                    <div class="col-12 col-md-6">
                                                                        @include(
                                                                            'components.input-field.select-custom.select',
                                                                            [
                                                                                'label' => 'Address',
                                                                                'id' => 'product_adderss',
                                                                                'datas' => $util->getBranches(),
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-12 col-md-6">
                                                                        @include(
                                                                            'components.input-field.price',
                                                                            [
                                                                                'label' => 'Quantity',
                                                                                'id' => 'product_quantity',
                                                                                'desc' => 'Insert price',
                                                                                'request' => 'true',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-12 col-md-6">
                                                                        @include(
                                                                            'components.input-field.price',
                                                                            [
                                                                                'label' => 'Minimum Quantity',
                                                                                'id' => 'product_minquantity',
                                                                                'desc' => 'Insert discount price',
                                                                                'request' => 'true',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-12 col-md-6">
                                                                        @include(
                                                                            'components.input-field.price',
                                                                            [
                                                                                'label' => 'Maximum Quantity',
                                                                                'id' => 'product_maxquantity',
                                                                                'desc' => 'Insert discount price',
                                                                                'request' => 'true',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                {{-- @include(
                                                                    'components.input-field.dropdown-with-table',
                                                                    [
                                                                        'onchange' => '',
                                                                        'id' => 'product_branch',
                                                                        'request' => 'true',
                                                                        'noitem' => 'true',
                                                                    ]
                                                                ) --}}
                                                                @include('components.buttons.button', [
                                                                    'label' => 'Add',
                                                                    'id' => 'product_add_btn',
                                                                    'onclick' => 'addProductAddress();',
                                                                ]) {{-- Add product  --}}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Product Variants -->
                                        <div class="row mt-2 mb-2">
                                            <div class="col-md-12">
                                                <!-- Product variants -->
                                                @include('components.buttons.button', [
                                                    'label' => 'Product variants',
                                                    'onclick' => 'onProductVariants();',
                                                ])

                                                <div id="productVariantsItems" class="col-md-12" hidden
                                                    style="border-color: #2aabd2; border-style: dashed; border-width: 3px; padding: 15px;">
                                                    <div class="card-header">
                                                        <h5 class="card-title">Product Variants</h5>
                                                    </div>
                                                    <div class="col-md-12">
                                                        <table class="table table-striped">
                                                            <thead class="text-primary">
                                                                <tr>
                                                                    <th>Id</th>
                                                                    <th>Name</th>
                                                                    <th class="text-center">Image</th>
                                                                    <th>Address</th>
                                                                    <th class="text-center">Price</th>
                                                                    <th class="text-center">Discount Price</th>
                                                                    <th class="text-center">Updated At</th>
                                                                    <th class="text-center">Action</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="pv_table_body">
                                                                {{-- product variant body --}}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <div class="col-md-12">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                @include('components.input-field.text', [
                                                                    'label' => 'Name',
                                                                    'text' => 'Insert Name',
                                                                    'id' => 'pv_name',
                                                                    'request' => 'true',
                                                                    'maxlength' => '40',
                                                                ]) {{-- Name - Insert Name --}}
                                                                @include('components.input-field.price', [
                                                                    'label' => 'Insert Price',
                                                                    'text' => 'Insert Price',
                                                                    'id' => 'pv_price',
                                                                    'request' => 'true',
                                                                ]) {{-- Price - Insert Price --}}
                                                                @include('components.input-field.price', [
                                                                    'label' => 'Discount Price',
                                                                    'text' => 'Insert Discount Price',
                                                                    'id' => 'pv_discountprice',
                                                                    'request' => 'false',
                                                                ]) {{-- Discount Price - Insert Discount Price --}}
                                                                @include(
                                                                    'components.input-field.text-area',
                                                                    [
                                                                        'label' => 'Description',
                                                                        'placeholder' => 'Enter desc',
                                                                        'desc' =>
                                                                            'Insert Product variant Description',
                                                                        'id' => 'pv_desc',
                                                                    ]
                                                                )
                                                                @include('components.input-field.text', [
                                                                    'label' => 'Video link',
                                                                    'placeholder' => '',
                                                                    'desc' => 'Insert Video link',
                                                                    'id' => 'pv_video_link',
                                                                ])
                                                            </div>

                                                            <div class="col-md-6">
                                                                @include(
                                                                    'components.input-field.image',
                                                                    []
                                                                )
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            {{-- Variant Branch Insert Form --}}
                                                            <div class="col-md-12">
                                                                @include(
                                                                    'components.input-field.dropdown-with-table',
                                                                    [
                                                                        'label' => 'Select Branch',
                                                                        'onchange' => '',
                                                                        'id' => 'variant_branch',
                                                                        'request' => 'true',
                                                                        'noitem' => 'true',
                                                                    ]
                                                                )
                                                                {{-- Select branch --}}
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-12">
                                                                @include('components.buttons.button', [
                                                                    'label' => 'Clear Data',
                                                                    'onclick' =>
                                                                        'clearVariantProductBranchForm();',
                                                                ]) {{-- Add new variant  --}}
                                                                @include('components.buttons.button', [
                                                                    'label' => 'Save',
                                                                    'onclick' => 'addVariant();',
                                                                ]) {{-- Add new variant  --}}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Recommended Products -->
                                        <div class="row mt-2 mb-2">
                                            <div class="col-12">
                                                <!-- Recommended products -->
                                                @include('components.buttons.button', [
                                                    'label' => 'Recommended products',
                                                    'onclick' => 'onRecommendedProducts();',
                                                ])

                                                <div id="recommendedProductsItems" class="col-12" hidden
                                                    style="border-color: #2aabd2; border-style: dashed; border-width: 3px; padding: 15px;">
                                                    <div class="card-header">
                                                        <h5 class="card-title">Recommended Products</h5>
                                                    </div>
                                                    <div class="col-md-12">
                                                        <table class="table table-striped">
                                                            <thead class="text-primary">
                                                                <tr>
                                                                    <th>Name</th>
                                                                    <th>Image</th>
                                                                    <th>Price</th>
                                                                    <th>Discount Price</th>
                                                                    <th>Action</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="rp_table_body">
                                                                {{-- recommened products --}}
                                                            </tbody>
                                                        </table>
                                                        <div class="row align-items-end">
                                                            <div class="col-12 col-md-8">
                                                                @include(
                                                                    'components.input-field.dropdown-with-image',
                                                                    [
                                                                        'label' => 'Product',
                                                                        'onchange' => '',
                                                                        'id' => 'productForList',
                                                                        'request' => 'true',
                                                                        'noitem' => 'false',
                                                                        'data' => $products,
                                                                    ]
                                                                ) {{-- Product --}}
                                                            </div>
                                                            <div class="col-12 col-md-4">
                                                                @include('components.buttons.button', [
                                                                    'label' => 'Add product',
                                                                    'onclick' => 'addRProduct();',
                                                                ]) {{-- Add product  --}}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt-2 mb-2">
                                            <div class="col-12">
                                                <div class="col-12"
                                                    style="border-color: #2aabd2; border-style: dashed; border-width: 3px; padding: 15px;">
                                                    <div class="card-header">
                                                        <h5 class="card-title">Business Calculator</h5>
                                                    </div>
                                                    <div class="col-md-12">
                                                        <!-- Table -->
                                                        @include('components.table.light-table', [
                                                            'id' => 'product_business',
                                                        ])
                                                        {{-- <div class="table-responsive">
                                                            <table class="table table-striped">
                                                                <thead class="text-primary" id="product_business_table_header">
                                                                </thead>
                                                                <tbody id="product_business_table_body">
                                                                    <tr>
                                                                        <td colspan="6" class="text-center">No data
                                                                            available</td>
                                                                    </tr>
                                                                </tbody>
                                                                <tfoot class="text-primary" id="product_business_table_footer">
                                                                </tfoot>
                                                            </table>
                                                            <div class="d-flex justify-content-center align-items-center">
                                                                <nav aria-label="Page navigation">
                                                                    <ul class="pagination"
                                                                        id="product_business-pagination-list">
                                                                    </ul>
                                                                </nav>
                                                            </div>
                                                        </div> --}}
                                                        <div class="row align-items-end mt-4">
                                                            <div class="col-md-6 col-12">
                                                                <div class="row">
                                                                    <div class="col-12">
                                                                        @include(
                                                                            'components.input-field.text',
                                                                            [
                                                                                'label' => 'Name',
                                                                                'id' => 'bc_name',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-12">
                                                                        @include(
                                                                            'components.input-field.number',
                                                                            [
                                                                                'label' => 'Year',
                                                                                'id' => 'bc_year',
                                                                                'name' => 'year',
                                                                                'class' => 'quantity',
                                                                                'min' => '1',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-12">
                                                                        @include(
                                                                            'components.input-field.price-range',
                                                                            [
                                                                                'label' =>
                                                                                    'Minimum purchase amount range',
                                                                                'id' => 'minimum-amount',
                                                                                'desc' => '',
                                                                                'request' => 'true',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-12">
                                                                        @include(
                                                                            'components.input-field.price',
                                                                            [
                                                                                'label' => 'Rental Yield',
                                                                                'id' => 'bc_rental_yield',
                                                                                'name' => 'rental yield',
                                                                                'desc' =>
                                                                                    'Rental yield of the year',
                                                                                'request' => 'true',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-12">
                                                                        @include(
                                                                            'components.input-field.price',
                                                                            [
                                                                                'label' =>
                                                                                    'Property Appreciation',
                                                                                'id' =>
                                                                                    'bc_property_appreciation',
                                                                                'name' => 'property-appreciation',
                                                                                'desc' =>
                                                                                    'Property appreciation of the year',
                                                                                'request' => 'true',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="col-12 mt-2">
                                                                    <div class="row gap-4 mb-2">
                                                                        @foreach ($util->discountTypes() as $disType)
                                                                            <div class="">
                                                                                @include(
                                                                                    'components.input-field.checkbox-2',
                                                                                    [
                                                                                        'text' =>
                                                                                            $disType['name'],
                                                                                        'id' => $disType['value'],
                                                                                        'initvalue' =>
                                                                                            $disType['value'] ==
                                                                                            'discount_type'
                                                                                                ? 'true'
                                                                                                : 'false',
                                                                                    ]
                                                                                )
                                                                            </div>
                                                                        @endforeach
                                                                    </div>
                                                                </div>
                                                                <div class="row discount-amount-view">
                                                                    <div class="col-12">
                                                                        @include(
                                                                            'components.input-field.price',
                                                                            [
                                                                                'label' => 'Discount amount',
                                                                                'id' => 'discount-amount',
                                                                                'desc' =>
                                                                                    "Leave this field blank if you'd prefer not to set a value.",
                                                                                'request' => '',
                                                                                'max' => '100',
                                                                                'min' => '0',
                                                                                'datas' => $util->discountAmountTypes(),
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="row incentive-view" hidden>
                                                                    <div class="col-12">
                                                                        @include(
                                                                            'components.input-field.text',
                                                                            [
                                                                                'label' => 'Incentive',
                                                                                'id' => 'incentive',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="row point-view" hidden>
                                                                    <div class="col-12">
                                                                        @include(
                                                                            'components.input-field.number',
                                                                            [
                                                                                'label' => 'Point',
                                                                                'id' => 'point',
                                                                                'name' => 'point',
                                                                                'min' => '1',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-12">
                                                                        <!-- visible -->
                                                                        @include(
                                                                            'components.input-field.checkbox-2',
                                                                            [
                                                                                'id' => 'bc_visible',
                                                                                'text' => 'Visible',
                                                                                'initvalue' => 'true',
                                                                            ]
                                                                        )
                                                                    </div>
                                                                </div>
                                                                @include('components.buttons.button', [
                                                                    'label' => 'Add',
                                                                    'id' => 'add_productbis_add_btn',
                                                                    'onclick' => 'onProductBisSave()',
                                                                ]) {{-- Add product  --}}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <hr>

                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.buttons.button', [
                                                    'label' => 'Save',
                                                    'id' => 'onSave',
                                                    'block' => '1',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <!-- End Tab View - Create -->

                        <!-- Tab View - Edit -->
                        <div role="tabpanel" class="tab-pane" id="edit">
                            <div class="card-header">
                                <h5 class="card-title">Edit Product</h5>
                            </div>
                            <div class="card-body">
                                <div id="editForm">
                                </div>
                            </div>
                        </div>
                        <!-- End Tab View - Edit -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script -->
    @include('pages.admin.product.script')
@endsection
