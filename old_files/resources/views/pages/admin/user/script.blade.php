<!-- Utils -->
<script>
    // Email validation
    function emailIsValid(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
    }

    function fullNameIsValid(fullName) {
        return /^\w+\s\w+$/.test(fullName);
    }

    function phoneIsValid(phone) {
        return /(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/.test(phone);
    }
</script>

<script>
    // Address Data Listsings
    let pages = 1;
    let currentPage = 1;
    let sortCat = 0;
    let sortRest = 0;
    let sortPublished = '1';
    let sortUnPublished = '1';
    let searchText = "";
    let sort = "updated_at";
    let sortBy = "desc";
    let data;
    let fetchedData;

    // selected product
    let selectedProduct;

    paginationGoPage(1);
    initPaginationLine(pages, currentPage);
    initTableHeader();

    function paginationGoPage(page) {
        data = {
            page: page,
            per_page: 20,
            sort: sortBy,
            order_by: sort,
            query: searchText,
            active: sortPublished,
            inactive: sortUnPublished
        };

        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: '{{ route('admin.users-get') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('product get', data);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                currentPage = data.data.current_page;
                pages = Math.ceil(data.data.total / data.data.per_page);
                fetchedData = data.data.data;
                initDataTable(fetchedData);
                initPaginationLine(pages, currentPage);
                initTableHeader();
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                dataLoading = false;
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function initDataTable(data) {
        html = "";
        if (data.length == 0) {
            html = `
            <tr>
                <td colspan="8">
                    <div class="d-flex justify-content-center align-items-center">
                        <p class="m-0">There are no records</p>
                    </div>
                </td>
            </tr>
            `;
            document.getElementById("table_body").innerHTML = html;
        } else {
            data.forEach(function(item, i, arr) {
                html += buildOneItem(item);
            });
            document.getElementById("table_body").innerHTML = html;
        }
    }

    function buildOneItem(item) {
        let active = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;
        if (item.active == 1)
            active = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;

        let twofa = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;
        if (item['2fa'] == 1)
            twofa = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;

        return `
            <tr>
                <td>${item.id}</td>
                <td>${item.name}</td>
                <td>${item.email}</td>
                <td>${item.phone}</td>
                <td>${item.role ? item.role.role.toUpperCase() : '-'}</td>
                <td>
                    <div style="display: flex;">${active}</div>
                </td>
                <td>
                    <div style="display: flex;">${twofa}</div>
                </td>
                <td class="text-center">
                    <div class="">
                        <p class="text-info m-0"><strong>${item.timeago}</strong></p>
                    </div>
                    <p class="h5">${item.updated_at2}</p>
                </td>
                <td class="text-center" style="white-space:nowrap;">
                    <button type="button" class="btn btn-info btn-round" onclick="editItem(${item.id})">
                        Edit
                    </button>
                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '{{ route('admin.user-delete', ':id') }}')">
                        <div>Delete</div>
                    </button>
                </td>
            </tr>
        `;
    }

    function initPaginationLine(pages, page) {
        allPages = pages;
        currentPage = page;
        let pageNumbers = '';
        pageNumbers = buildPagination(currentPage, allPages);
        let paginationList = document.getElementById('pagination-list');
        paginationList.innerHTML = pageNumbers;
        initializePaginations(paginationGoPage);
    }

    function tableHeaderSort(newsort) {
        if (newsort == sort) {
            if (sortBy == "asc")
                sortBy = "desc";
            else
                sortBy = "asc";
        } else {
            sort = newsort
            sortBy = "asc";
        }
        paginationGoPage(currentPage);
    }

    function utilGetImg(value) {
        var img = "{{ asset('img/arrow_noactive.png') }}";
        if (sort == value && sortBy == "asc") img = "{{ asset('img/asc_arrow.png') }}";
        if (sort == value && sortBy == "desc") img = "{{ asset('img/desc_arrow.png') }}";
        return img;
    }

    function initTableHeader() {
        let header = `
            <th>ID <img onclick="tableHeaderSort('id');" src="${utilGetImg('id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Name <img onclick="tableHeaderSort('name');" src="${utilGetImg('name')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Email <img onclick="tableHeaderSort('email');" src="${utilGetImg('email')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Phone Number <img onclick="tableHeaderSort('phone');" src="${utilGetImg('phone')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Type <img onclick="tableHeaderSort('role_id');" src="${utilGetImg('role_id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Active <img onclick="tableHeaderSort('active');" src="${utilGetImg('active')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">2FA(Two-factor authentication) <img onclick="tableHeaderSort('2fa');" src="${utilGetImg('2fa')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Updated At <img onclick="tableHeaderSort('updated_at');" src="${utilGetImg('updated_at')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Action</th>
        `;
        document.getElementById("table_header").innerHTML = header;
        document.getElementById("table_footer").innerHTML = header;
    }

    $(document).on('input', '#element_search', function() {
        searchText = document.getElementById("element_search").value;
        currentPage = 1;
        paginationGoPage(1);
    });

    function onVisibleSearchSelect() {
        if (visible_search) sortPublished = "1";
        else sortPublished = "0";
        if (unvisible_search) sortUnPublished = "1";
        else sortUnPublished = "0";
        currentPage = 1;
        paginationGoPage(1);
    }
</script>

<script type="text/javascript">
    // Tabs
    $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
        var target = $(e.target).attr("href");
        if (target != "#edit") {
            document.getElementById("tabEdit").style.display = "none";
        }
        if (target == "#create") {
            // Clear form
            clearForm();
            document.getElementById('createForm').appendChild(document.getElementById("form"));
        }
        if (target == "#home") {
            // clearForm();
        }
    });

    let editId = 0;

    function editItem(id) {
        let url = `{{ route('admin.user-get', ':id') }}`;
        url = url.replace(':id', id);
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: url,
            data: {},
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log("user edit res: ", data);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', data);
                }
                document.getElementById("tabEdit").style.display = "block";
                $('.nav-tabs a[href="#edit"]').tab('show');
                let target = document.getElementById("form");
                document.getElementById('editForm').appendChild(target);

                editId = data.data.id;
                document.getElementById("name").value = data.data.name;
                document.getElementById("email").value = data.data.email;
                if (data.data.phone) document.getElementById("phone-number").value = data.data.phone
                    .substring(3, 12);
                onSetCheck_active(data.data.active);
                onSetCheck_twofa_active(data.data['2fa']);
                $('select[id=user-role]').val(data.data.role_id);
                if (data.data.image) addEditImage3(data.data.image);
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    $("#name,#address").on('keyup', function(event) {
        let el = event.target;
        if (el.id == 'name' || el.id == 'desc') {
            el.classList.remove('is-valid', 'is-invalid');
            if (el.value.length > 0) {
                el.classList.add('is-valid');
            } else {
                el.classList.add('is-invalid');
            }
        }

        if (el.id == 'price') {
            el.classList.remove('is-valid', 'is-invalid');
            if (el.value && el.value != 0) {
                el.classList.add('is-valid');
            } else {
                el.classList.add('is-invalid');
            }
        }

        if (el.id == 'discount_price') {
            el.classList.remove('is-valid');
            if (el.value && el.value != 0) {
                el.classList.add('is-valid');
            }
        }
    });

    function onSave() {
        let name = document.getElementById("name").value;
        let email = document.getElementById("email").value;
        let phone = document.getElementById("phone-number").value;
        let role = $('select[id=user-role]').val();

        // Check invalid fields
        if (
            messageStatus('error', 'Error', {
                msg: "The Name field is required"
            }, !name) ||
            messageStatus('error', 'Error', {
                msg: "The Name is invalid"
            }, !fullNameIsValid(name)) ||
            messageStatus('error', 'Error', {
                msg: "The Email field is required"
            }, !email) ||
            messageStatus('error', 'Error', {
                msg: "The Email is invalid"
            }, !emailIsValid(email)) ||
            messageStatus('error', 'Error', {
                msg: "The Role field is required"
            }, !role)
        ) {
            return;
        }

        if (phone) {
            phone = '251' + phone;
            if (
                messageStatus('error', 'Error', {
                    msg: "The Phone field is required"
                }, !phone) ||
                messageStatus('error', 'Error', {
                    msg: "The Phone is invalid"
                }, !phoneIsValid(phone))
            ) return;
        }

        password = document.getElementById("password").value;
        password2 = document.getElementById("password2").value;
        if (password || !editId) {
            // Check invalid fields
            if (
                messageStatus('error', 'Error', {
                    msg: "The Password field is required"
                }, !password) ||
                messageStatus('error', 'Error', {
                    msg: "The Confirmation Password field is required"
                }, !password2)) {
                return;
            }
            if (password != password2) {
                return messageStatus('error', 'Error', {
                    msg: "Passwords do not match"
                });
            }
        }
        if (!password && password2) {
            if (messageStatus('error', 'Error', {
                    msg: "The Password field is required"
                }, !password)) {
                return;
            }
        }

        let data = {
            id: editId,
            name: name,
            email: email,
            phone: phone,
            role: role,
            identification_img: imageid3,
            active: (active) ? 1 : 0,
            twofa: (twofa_active) ? 1 : 0,
        }
        if (password) {
            data.password = password;
            data.password_confirmation = password2;
        }
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.user-add') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('res data: ', data);
                if (data.error) {
                    return messageStatus('error', 'Error', data);
                } else if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                if (editId != 0) {
                    paginationGoPage(currentPage);
                } else {
                    var text = buildOneItem(data.data);
                    var text2 = document.getElementById("table_body").innerHTML;
                    document.getElementById("table_body").innerHTML = text + text2;
                }
                $('.nav-tabs a[href="#home"]').tab('show');
                clearForm();
                messageStatus('success', 'Success', {
                    msg: "Data saved successfully"
                });
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log('err res: ', e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    // Clear forms
    function clearForm() {
        editId = 0;
        document.getElementById("name").value = "";
        document.getElementById("email").value = "";
        document.getElementById("phone-number").value = "";
        document.getElementById("password").value = "";
        document.getElementById("password2").value = "";
        onSetCheck_active(true);
        onSetCheck_twofa_active(false);
        $('select[id=user-role]').val("");
        clearDropZone3();
    }
</script>
