@inject('util', 'App\Models\Util')
@extends('layouts.dashboard-layout')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card card-user">
                <!-- Tabs -->
                <div class="card-header card-header-danger">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <ul class="nav nav-tabs" data-tabs="tabs">
                                <li role="presentation" class="nav-item" id="tabHome">
                                    <a href="#home" class="nav-link active" data-toggle="tab">List</a>
                                </li>
                                <li role="presentation" class="nav-item">
                                    <a href="#create" class="nav-link" data-toggle="tab">Create</a>
                                </li>
                                <li role="presentation" class="nav-item" id="tabEdit" style="display: none;">
                                    <a href="#edit" class="nav-link" data-toggle="tab">Edit</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- End Tabs -->

                <!-- Tab List -->
                <div id="myTabContent" class="card-body">
                    <div class="tab-content text-left">

                        <!-- Tab View - Listings -->
                        <div role="tabpanel" class="tab-pane active" id="home">
                            <div class="card-header">
                                <h5 class="card-title">User Listings</h5>
                                <div class="row">
                                    <div class="col-12 col-md-4">
                                        <div class="form-group">
                                            <label for="visible_search">Filter</label>
                                            <div class="row gap-4 col-12">
                                                <!-- Active -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'visible_search',
                                                    'text' => 'Active',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                                <!-- Inactive -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'unvisible_search',
                                                    'text' => 'Inactive',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-4 ml-auto">
                                        @include('components.input-field.text', [
                                            'text' => 'Search',
                                            'label' => 'Search',
                                            'type' => 'search',
                                            'id' => 'element_search',
                                        ])
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Table -->
                                @include('components.table.light-table')
                                <!-- End Table -->
                            </div>
                        </div>
                        <!-- End Tab View - Listings -->

                        <!-- Tab View - Create -->
                        <div role="tabpanel" class="tab-pane" id="create">
                            <div class="card-header">
                                <h5 class="card-title">Create User</h5>
                            </div>
                            <div class="card-body">
                                <div id="createForm">
                                    <div id="form">
                                        <div class="row">
                                            <div class="col-md-6 col-12">
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Full Name',
                                                            'placeholder' => 'Insert full name',
                                                            'id' => 'name',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Email',
                                                            'placeholder' => 'Insert email',
                                                            'id' => 'email',
                                                            'type' => 'email',
                                                            'required' => true,
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Phone Number',
                                                            'placeholder' => 'Insert phone number',
                                                            'id' => 'phone-number',
                                                            'type' => 'tel',
                                                            'max' => '9',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Password',
                                                            'placeholder' => 'Insert password',
                                                            'desc' => 'Insert Password',
                                                            'id' => 'password',
                                                            'type' => 'password',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Confirmation Password',
                                                            'placeholder' => 'Insert confirmation password',
                                                            'desc' => 'Insert Confirmation Password',
                                                            'id' => 'password2',
                                                            'type' => 'password',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <!-- Active -->
                                                        @include('components.input-field.checkbox-2', [
                                                            'id' => 'active',
                                                            'text' => 'Active',
                                                            'initvalue' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <!-- 2FA -->
                                                        @include('components.input-field.checkbox-2', [
                                                            'id' => 'twofa_active',
                                                            'text' => '2FA(Two Factor Authentication)',
                                                            'initvalue' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 col-12">
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include(
                                                            'components.input-field.select-custom.select',
                                                            [
                                                                'label' => 'Type',
                                                                'id' => 'user-role',
                                                                'onchange' => '',
                                                                'datas' => $util->getRoles(),
                                                            ]
                                                        )
                                                    </div>
                                                </div>

                                                @include('components.input-field.image', [])
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.buttons.button', [
                                                    'label' => 'Save',
                                                    'id' => 'on-save',
                                                    'onclick' => 'onSave();',
                                                    'block' => '1',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <!-- End Tab View - Create -->

                        <!-- Tab View - Edit -->
                        <div role="tabpanel" class="tab-pane" id="edit">
                            <div class="card-header">
                                <h5 class="card-title">Edit User</h5>
                            </div>
                            <div class="card-body">
                                <div id="editForm">
                                </div>
                            </div>
                        </div>
                        <!-- End Tab View - Edit -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script -->
    @include('pages.admin.user.script')
@endsection
