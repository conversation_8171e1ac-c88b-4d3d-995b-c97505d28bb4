@inject('util', 'App\Models\Util')
@extends('layouts.dashboard-layout')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card card-user">
                <!-- Tabs -->
                <div class="card-header card-header-danger">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <ul class="nav nav-tabs" data-tabs="tabs">
                                <li role="presentation" class="nav-item" id="tabHome">
                                    <a href="#home" class="nav-link active" data-toggle="tab">List</a>
                                </li>
                                <li role="presentation" class="nav-item" id="tabEdit" style="display: none;">
                                    <a href="#edit" class="nav-link" data-toggle="tab">Update</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- End Tabs -->

                <!-- Tab List -->
                <div id="myTabContent" class="card-body">
                    <div class="tab-content text-left">

                        <!-- Tab View - Listings -->
                        <div role="tabpanel" class="tab-pane active" id="home">
                            <div class="card-header">
                                <h5 class="card-title">Requested Products Listings</h5>
                                <div class="row">
                                    <div class="col-12 col-md-4">
                                        <div class="form-group">
                                            <span class="label-span">Filter</span>
                                            <div class="row gap-4 col-12">
                                                <!-- Published -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'visible_search',
                                                    'text' => 'Published item',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                                <!-- Unpublished -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'unvisible_search',
                                                    'text' => 'Unpublished item',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-4 ml-auto">
                                        @include('components.input-field.text', [
                                            'text' => 'Search',
                                            'label' => 'Search',
                                            'type' => 'search',
                                            'id' => 'element_search',
                                        ])
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Table -->
                                @include('components.table.light-table')
                                <!-- End Table -->
                            </div>
                        </div>
                        <!-- End Tab View - Listings -->

                        <!-- Tab View - Edit -->
                        <div role="tabpanel" class="tab-pane" id="edit">
                            <div class="card-header">
                                <h5 class="card-title">Update Requested Product</h5>
                            </div>
                            <div class="card-body">
                                <div>
                                    <div id="form">
                                        <div class="row">
                                            <div class="col-md-6 col-12">
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text-info', [
                                                            'label' => 'Full Name',
                                                            'id' => 'full_name',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text-info', [
                                                            'label' => 'Email Address',
                                                            'id' => 'email_address',
                                                            'disabled' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text-info', [
                                                            'label' => 'Phone Number',
                                                            'id' => 'phone_number',
                                                            'disabled' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text-info', [
                                                            'label' => 'Product Name',
                                                            'id' => 'name',
                                                            'disabled' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text-info', [
                                                            'label' => 'Product Description',
                                                            'id' => 'product-desc',
                                                            'disabled' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text-info', [
                                                            'label' => 'Tin Number',
                                                            'id' => 'tin-number',
                                                            'disabled' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include(
                                                            'components.input-field.select-custom.select',
                                                            [
                                                                'label' => 'Status',
                                                                'onchange' => 'onChange()',
                                                                'id' => 'rp-status',
                                                                'datas' => $util->requestProductsStatus(),
                                                            ]
                                                        )
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12 mb-4">
                                                        @include('components.image-view.image-view', [
                                                            'label' => 'Product Image',
                                                            'id' => 'rpImage',
                                                        ])
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6 col-12">
                                                <div class="col-12 mb-4">
                                                    @include('components.image-view.image-view', [
                                                        'label' => 'Trade License Image',
                                                        'id' => 'tradeLicenseImage',
                                                    ])
                                                </div>
                                                <div class="col-12">
                                                    @include('components.image-view.image-view', [
                                                        'label' => 'Identification Image',
                                                        'id' => 'identificationImage',
                                                    ])
                                                </div>
                                            </div>

                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.buttons.button', [
                                                    'label' => 'Save',
                                                    'id' => 'on-save',
                                                    'onclick' => 'onSave();',
                                                    'block' => '1',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Tab View - Edit -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script -->
    @include('pages.admin.requested-product.script')
@endsection
