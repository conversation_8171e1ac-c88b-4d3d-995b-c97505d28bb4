<script>
    // Address Data Listsings
    let pages = 1;
    let currentPage = 1;
    let sortCat = 0;
    let sortRest = 0;
    let sortPublished = '1';
    let sortUnPublished = '1';
    let searchText = "";
    let sort = "updated_at";
    let sortBy = "desc";
    let data;
    let products;

    // selected product
    let selectedProduct;

    paginationGoPage(1);
    initPaginationLine(pages, currentPage);
    initTableHeader();

    function paginationGoPage(page) {
        data = {
            page: page,
            per_page: 20,
            sort: sortBy,
            order_by: sort,
            query: searchText,
            // status: sortPublished,
            // instatus: sortUnPublished
        };

        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: '{{ route('admin.requested-products-get') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('product get', data);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                currentPage = data.data.current_page;
                pages = Math.ceil(data.data.total / data.data.per_page);
                products = data.data.data;
                initDataTable(products);
                initPaginationLine(pages, currentPage);
                initTableHeader();
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                dataLoading = false;
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function initDataTable(data) {
        html = "";
        if (data.length == 0) {
            html = `
            <tr>
                <td colspan="9">
                    <div class="d-flex justify-content-center align-items-center">
                        <p class="m-0">There are no records</p>
                    </div>
                </td>
            </tr>
            `;
            document.getElementById("table_body").innerHTML = html;
        } else {
            // console.log('data: ', data);
            data.forEach(function(item, i, arr) {
                html += buildOneItem(item);
            });
            document.getElementById("table_body").innerHTML = html;
        }
    }

    function buildOneItem(item) {
        let status = @json($util->requestProductsStatus());
        let statusOptions = '';
        status.forEach(function(statusItem, i, arr) {
            statusOptions +=
                `<option value="${statusItem['id']}" style="font-size: 16px  !important;" ${item.status == statusItem['id'] ? 'selected' : ''}>${statusItem['id']}</option>`;
        });
        if (item.status == 1)
            var visible = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;
        else
            var visible = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;

        // <div style="display: flex; width: 100%; height: 6rem;">
        //             <img width="100" height="100" class="img-contain" src="${item.image ? item.image.image_path : ''}" alt="${item.name}" />
        //         </div>
        // <div style="display: flex;">${item.status}</div>
        return `
            <tr>
                <td>${item.id}</td>
                <td>${item.user.name}</td>
                <td>${item.name}</td>
                <td>
                    <div style="width:80px;height:80px;margin: 0px auto;">
                        <img src="${item.requested_product_image ? item.requested_product_image.image_path : item.image_path}" style="width: 100%;height: 100%;object-fit: contain;" >
                    </div>
                </td>
                <td>
                    ${item.type == 'new' ? `
                        <span class="badge badge-green badge-success">
                            New Request
                        </span>
                    ` : `
                        <span class="badge badge-dark">
                            Sell Share
                        </span>
                    `}
                </td>
                <td>
                    ${item.trade_license_image ? `
                        <div style="width:80px;height:80px;margin: 0px auto;">
                            <img src="${item.trade_license_image.image_path}" style="width: 100%;height: 100%;object-fit: contain;" />
                        </div>
                    ` : '-'}
                </td>
                <td>
                    ${item.identification_image ? `
                        <div style="width:80px;height:80px;margin: 0px auto;">
                            <img src="${item.identification_image.image_path}" style="width: 100%;height: 100%;object-fit: contain;" />
                        </div>
                    ` : '-'}
                </td>
                <td>
                    <select name="requested_product_${item.id}" id="requested_product_${item.id}" class="form-control show-tick" onchange="onStatusChange(${item.id}, 'requested_product_${item.id}');">
                        ${statusOptions}
                    </select>
                </td>
                <td class="text-center">
                    <div class="">
                        <p class="text-info m-0"><strong>${item.timeago}</strong></p>
                    </div>
                    <p class="h5">${item.updated_at2}</p>
                </td>
                <td class="text-center" style="white-space:nowrap;">
                    <button type="button" class="btn btn-info btn-round" onclick="editItem(${item.id})">
                        View
                    </button>
                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '{{ route('admin.advert-delete', ':id') }}')">
                        <div>Delete</div>
                    </button>
                </td>
            </tr>
        `;
    }

    function initPaginationLine(pages, page) {
        allPages = pages;
        currentPage = page;
        let pageNumbers = '';
        pageNumbers = buildPagination(currentPage, allPages);
        let paginationList = document.getElementById('pagination-list');
        paginationList.innerHTML = pageNumbers;
        initializePaginations(paginationGoPage);
    }

    function tableHeaderSort(newsort) {
        if (newsort == sort) {
            if (sortBy == "asc")
                sortBy = "desc";
            else
                sortBy = "asc";
        } else {
            sort = newsort
            sortBy = "asc";
        }
        paginationGoPage(currentPage);
    }

    function utilGetImg(value) {
        var img = "{{ asset('img/arrow_noactive.png') }}";
        if (sort == value && sortBy == "asc") img = "{{ asset('img/asc_arrow.png') }}";
        if (sort == value && sortBy == "desc") img = "{{ asset('img/desc_arrow.png') }}";
        return img;
    }

    function initTableHeader() {
        let header = `
            <th>ID <img onclick="tableHeaderSort('id');" src="${utilGetImg('id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>User <img onclick="tableHeaderSort('user_id');" src="${utilGetImg('user_id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Product Name <img onclick="tableHeaderSort('name');" src="${utilGetImg('name')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Product Image</th>
            <th class="text-center">Request Type</th>
            <th class="text-center">Trade License Image</th>
            <th class="text-center">Identification Image</th>
            <th class="text-center">Status <img onclick="tableHeaderSort('visible');" src="${utilGetImg('visible')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Updated At <img onclick="tableHeaderSort('updated_at');" src="${utilGetImg('updated_at')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Action</th>
        `;
        document.getElementById("table_header").innerHTML = header;
        document.getElementById("table_footer").innerHTML = header;
    }

    $(document).on('input', '#element_search', function() {
        searchText = document.getElementById("element_search").value;
        currentPage = 1;
        paginationGoPage(1);
    });

    function onVisibleSearchSelect() {
        if (visible_search) sortPublished = "1";
        else sortPublished = "0";
        if (unvisible_search) sortUnPublished = "1";
        else sortUnPublished = "0";
        currentPage = 1;
        paginationGoPage(1);
    }
</script>

<script type="text/javascript">
    // Tabs
    $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
        var target = $(e.target).attr("href");
        if (target != "#edit") {
            document.getElementById("tabEdit").style.display = "none";
        }
        if (target == "#home") {
            // clearForm();
        }
    });

    let editId = 0;
    let editData = null;

    function editItem(id) {
        let url = `{{ route('admin.requested-product-get', ':id') }}`;
        url = url.replace(':id', id);
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: url,
            data: {},
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('res data: ', data);
                if (data.error || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                document.getElementById("tabEdit").style.display = "block";
                $('.nav-tabs a[href="#edit"]').tab('show');

                fillForm(data.data);
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function onSave() {
        let data = {
            id: editId,
            status: editData.status,
        }
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.requested-products-add') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('res data: ', data);
                if (data.error) {
                    return messageStatus('error', 'Error', data);
                } else if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                if (editId != 0) {
                    paginationGoPage(currentPage);
                } else {
                    var text = buildOneItem(data.data);
                    var text2 = document.getElementById("table_body").innerHTML;
                    document.getElementById("table_body").innerHTML = text + text2;
                }
                $('.nav-tabs a[href="#home"]').tab('show');
                clearForm();
                return Toast.fire({
                    icon: "success",
                    title: "Data saved successfully"
                });
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log('err res: ', e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function onStatusChange(dataId, id) {
        editId = dataId;
        editData = {
            id: dataId
        };
        editData.status = $(`select[id=${id}]`).val();
        onSave();
    }

    function onChange() {
        editData.status = $('select[id=rp-status]').val();
    }

    // Fill forms
    function fillForm(data) {
        editData = data;
        editId = data.id;
        document.getElementById("full_name").innerHTML = data.user.name;
        document.getElementById("email_address").innerHTML = data.user.email;
        document.getElementById("phone_number").innerHTML = data.user.phone;

        document.getElementById("name").innerHTML = data.name;
        let desc = data.desc;
        if (data.type != 'new') {
            desc = JSON.parse(desc);
            document.getElementById("product-desc").innerHTML = `<div><b>Number of Shares to Sell: </b>${desc.NOS}</div><br><div><b>Purchase Date of Shares: </b>${desc.PDS}</div><br><div><b>Asking Price Per Share: </b>${desc.APPP}</div><br><div><b>Willing to Accept Market Price: </b>${desc.WTAMP}</div><br><div><b>Preferred Payment Method: </b>${desc.PPM}</div><br><div><b>Reason for Selling: </b>${desc.RFS}</div><br><div><b>Agreement & Confirmation: </b>${desc.AC}</div>`;
        } else {
            document.getElementById("product-desc").innerHTML = desc;
        }
        document.getElementById("tin-number").innerHTML = data.tin_number ?? '-';

        document.getElementById("rpImage").src = data.requested_product_image ? data.requested_product_image
            .image_path : data.image_path;
        document.getElementById("tradeLicenseImage").src = data.trade_license_image ? data.trade_license_image
            .image_path : data.image_path;
        document.getElementById("identificationImage").src = data.identification_image ? data.identification_image
            .image_path : data.image_path;
        $('select[id=rp-status]').val(data.status);
    }

    // Clear forms
    function clearForm() {
        editData = null;
        editId = 0;
        document.getElementById("name").value = '';
        document.getElementById("product-desc").value = '';
        document.getElementById("tin-number").value = '';

        document.getElementById("rpImage").src = '';
        document.getElementById("tradeLicenseImage").src = '';
        document.getElementById("identificationImage").src = '';
    }
</script>
