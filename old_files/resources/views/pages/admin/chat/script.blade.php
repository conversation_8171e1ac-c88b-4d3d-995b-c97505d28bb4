<script>
    let currentId = 0;
    let searchText = "";
    let allPages;
    let pages = 1;
    let currentPage = 1;

    buildChatUsers(1);
    initPaginationLine(pages, currentPage);

    function initPaginationLine(pages, page) {
        if (pages > 1) {
            allPages = pages;
            currentPage = page;
            let pageNumbers = '';
            pageNumbers = buildPagination(currentPage, allPages);
            let paginationList = document.getElementById('pagination-list');
            paginationList.innerHTML = pageNumbers;
            initializePaginations(buildChatUsers);
        }
    }

    $(document).on('input', '#users_search', function() {
        searchText = document.getElementById("users_search").value;
        buildChatUsers(1);
    });

    function selectUser(id) {
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.chats-get') }}',
            data: removeEmptyData({
                user_id: id,
            }),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('sel user res: ', data);
                buildChatUsers(currentPage);
                document.getElementById("sendMsg").style.visibility = "visible";
                currentId = id;
                drawMsg(data);
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
            }
        });
    }

    function myGet() {
        if (!currentId || currentId == 0) {
            return;
        }
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.chats-get') }}',
            data: removeEmptyData({
                user_id: currentId,
            }),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('get chats: ', data);
                if (data.error)
                    return messageStatus('error', 'Error', data);
                if (currentLength != data.messages.length)
                    drawMsg(data);
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
            }
        });
    }

    setInterval(myGet, 10000); // one time in 10 sec

    let el = document.getElementById("messageText");
    document.getElementById("chat-send--loading").style.display = 'none';
    el.addEventListener("keydown", function(event) {
        if (event.key === "Enter") {
            // Enter key was hit
            sendMsg();
        }
    });

    function sendMsg() {
        let text = document.getElementById("messageText").value;
        if (text == "")
            return;
        document.getElementById("chat-send--loading").style.display = 'block';
        document.getElementById("button-addon2").style.display = 'none';
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.send-msg') }}',
            data: removeEmptyData({
                user_id: currentId,
                msg: text,
            }),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log(data);
                document.getElementById("messageText").value = "";
                document.getElementById("chat-send--loading").style.display = 'none';
                document.getElementById("button-addon2").style.display = 'block';
                drawMsg(data);
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
            }
        });
    }

    let currentLength = 0;

    function drawMsg(data, id) {
        let last = "";
        let msg = document.getElementById("messagesWindow");
        let username = document.getElementById("user_name");
        username.innerHTML = `
            <div class="w-100" style="background-color: #deefff;padding: 15px 10px;">
                <h4 class="m-0">${data.user.name}</h4>
                <div class="">
                    <span class="text-muted">${data.user.email ? data.user.email : ''}</span>
                    <span class="text-muted">${data.user.phone ? data.user.phone : ''}</span>
                </div>
            </div>
        `;
        msg.innerHTML = "";

        if (data.messages == null)
            return;
        currentLength = data.messages.length;

        data.messages.forEach(function(entry) {
            // let now = entry.created_at.substr(0, 11);
            let now = entry.created_at3;
            if (now != last) {
                let div = document.createElement("div");
                div.innerHTML = `
                <div class="container-center">
                    <div style="text-align: center;">
                        <div>` + now + `</div>
                    </div>
                </div>
                `;
                last = now;
                msg.appendChild(div);
            }
            let div = document.createElement("div");
            // let date = entry.created_at.substr(11, 5);
            let date = entry.created_at2;
            if (entry.author == "customer") {
                div.innerHTML = `
                <div class="msg-box-container container-left">
                            ` + entry.msg + `
                            <div align="right">` + date + `</div>
                    </div>
                `;
            } else {
                div.innerHTML = `
                    <div class="msg-box-container container-right">
                            ` + entry.msg + `
                            <div align="right">` + date + `</div>
                    </div>
                `;
            }
            msg.appendChild(div);
        });
        // console.log("messagesWindow", messagesWindow.childNodes.length);
        // console.log("data", id);
        if (messagesWindow.childNodes.length == 0) {
            msg.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 100%;" align="center">
                <h1>No messages</h1>
            </div>`;
        }
        msg.scrollTop = msg.scrollHeight;
    }

    $(document).on('input', '#element_search', function() {
        searchText = document.getElementById("element_search").value;
        currentPage = 1;
        buildChatUsers(1);
    });

    function buildChatUsers(page) {
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.get-users') }}',
            data: removeEmptyData({
                page: page,
                query: searchText
            }),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log("build chat res: ", data);
                if (data.error)
                    return showNotification("bg-red", "Something went wrong", "bottom", "center", "",
                        "");

                $text = "";
                data.users.forEach(function(user, i, arr) {
                    // if (!user.name.toUpperCase().includes(searchText.toUpperCase()))
                    //     return;
                    let messages = "";
                    if (user.messages != 0)
                        messages = `
                            <button id="user${user.id}msgCountDotAll" class="btn btn-sm btn-outline-success btn-round btn-icon m-0">
                                <i id="user${user.id}msgCountAll"></i>${user.messages}
                            </button>
                            `;
                    let unread = "";
                    if (user.unread != 0)
                        unread = `
                            <button id="user${user.id}msgCountDot" class="btn btn-sm btn-outline-success btn-round btn-icon m-0"><i id="user${user.id}msgCount"></i>${user.unread}
                            </button>
                            `;
                    let bkg = "#FFFFFF";
                    if (user.id == currentId)
                        bkg = "#cbecff";
                    $text = $text + `
                        <li id="user${user.id}" onclick="selectUser(${user.id})" class="user-list" style="background-color:${bkg}">
                            <div class="d-flex justify-content-between">
                                <div class="d-flex">
                                    <div class="align-self-center mr-2" style="padding: 0;">
                                        <div class="avatar">
                                            <img src="${user.default_image}"
                                                alt="${user.name} Image"
                                                class="img-contain img-circle img-no-padding img-responsive">
                                        </div>
                                    </div>
                                    <div class="align-self-center">
                                        <p class="m-0">
                                            ${user.name}
                                        </p>
                                        {{--  <span class="text-muted">${user.email ? user.email : ''}</span>
                                        <span class="text-muted">${user.phone ? user.phone : ''}</span>
                                        <span class="text-muted"><small>Offline</small></span> --}}
                                    </div>
                                </div>
                                <div class="text-right align-self-center">
                                    ${unread}
                                    ${messages}
                                </div>
                            </div>
                        </li>
                        `;
                });
                document.getElementById("chat-users").innerHTML = $text;
                document.getElementById("messagesWindow").style.backgroundColor = " #ffffff";

                initPaginationLine(data.pages, data.page);
                // allPages = data.pages;
                // currentPage = data.page;
                // let pageNumbers = '';
                // pageNumbers = buildPagination(currentPage, allPages);
                // let paginationList = document.getElementById('paginationList');
                // paginationList.innerHTML = pageNumbers;
                // initializePaginations();
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
            }
        });
    }
</script>
