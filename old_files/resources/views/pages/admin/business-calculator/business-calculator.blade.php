@extends('layouts.dashboard-layout')
@inject('util', 'App\Models\Util')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card card-user">
                <!-- Tabs -->
                <div class="card-header card-header-danger">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <ul class="nav nav-tabs" data-tabs="tabs">
                                <li role="presentation" class="nav-item" id="tabHome">
                                    <a href="#home" class="nav-link active" data-toggle="tab">List</a>
                                </li>
                                <li role="presentation" class="nav-item">
                                    <a href="#create" class="nav-link" data-toggle="tab">Create</a>
                                </li>
                                <li role="presentation" class="nav-item" id="tabEdit" style="display: none;">
                                    <a href="#edit" class="nav-link" data-toggle="tab">Edit</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- End Tabs -->

                <!-- Tab List -->
                <div id="myTabContent" class="card-body">
                    <div class="tab-content text-left">

                        <!-- Tab View - Listings -->
                        <div role="tabpanel" class="tab-pane active" id="home">
                            <div class="card-header">
                                <h5 class="card-title">Business Calculator Listings</h5>
                                <div class="row">
                                    <div class="col-12 col-md-4">
                                        <div class="form-group">
                                            <label for="visible_search">Filter</label>
                                            <div class="row gap-4 col-12">
                                                <!-- Published -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'visible_search',
                                                    'text' => 'Published item',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                                <!-- Unpublished -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'unvisible_search',
                                                    'text' => 'Unpublished item',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-4 ml-auto">
                                        @include('components.input-field.text', [
                                            'text' => 'Search',
                                            'label' => 'Search',
                                            'type' => 'search',
                                            'id' => 'element_search',
                                        ])
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Table -->
                                @include('components.table.light-table')
                                <!-- End Table -->
                            </div>
                        </div>
                        <!-- End Tab View - Listings -->

                        <!-- Tab View - Create -->
                        <div role="tabpanel" class="tab-pane" id="create">
                            <div class="card-header">
                                <h5 class="card-title">Create Business Calculator</h5>
                            </div>
                            <div class="card-body">
                                <div id="createForm">
                                    <div id="form">
                                        <div class="row">
                                            <div class="col-md-6 col-12">
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Name',
                                                            'id' => 'bc_name',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.number', [
                                                            'label' => 'Year',
                                                            'id' => 'bc_year',
                                                            'name' => 'year',
                                                            'class' => 'quantity',
                                                            'min' => '1',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.price-range', [
                                                            'label' => 'Minimum purchase amount range',
                                                            'id' => 'minimum-amount',
                                                            'desc' => '',
                                                            'request' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.price', [
                                                            'label' => 'Rental Yield',
                                                            'id' => 'bc_rental_yield',
                                                            'name' => 'rental yield',
                                                            'desc' => 'Rental yield of the year',
                                                            'request' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.price', [
                                                            'label' => 'Property Appreciation',
                                                            'id' => 'bc_property_appreciation',
                                                            'name' => 'property-appreciation',
                                                            'desc' => 'Property appreciation of the year',
                                                            'request' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="col-12 mt-2">
                                                    <div class="row gap-4 mb-2">
                                                        @foreach ($util->discountTypes() as $disType)
                                                            <div class="">
                                                                @include(
                                                                    'components.input-field.checkbox-2',
                                                                    [
                                                                        'text' => $disType['name'],
                                                                        'id' => $disType['value'],
                                                                        'initvalue' =>
                                                                            $disType['value'] == 'discount_type'
                                                                                ? 'true'
                                                                                : 'false',
                                                                    ]
                                                                )
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                                <div class="row discount-amount-view">
                                                    <div class="col-12">
                                                        @include('components.input-field.price', [
                                                            'label' => 'Discount amount',
                                                            'id' => 'discount-amount',
                                                            'desc' => "Leave this field blank if you'd prefer not to set a value.",
                                                            'request' => '',
                                                            'max' => '100',
                                                            'min' => '0',
                                                            'datas' => $util->discountAmountTypes(),
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row incentive-view" hidden>
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Incentive',
                                                            'id' => 'incentive',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row point-view" hidden>
                                                    <div class="col-12">
                                                        @include('components.input-field.number', [
                                                            'label' => 'Point',
                                                            'id' => 'point',
                                                            'name' => 'point',
                                                            'min' => '1',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <!-- visible -->
                                                        @include('components.input-field.checkbox-2', [
                                                            'id' => 'visible',
                                                            'text' => 'Visible',
                                                            'initvalue' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.buttons.button', [
                                                    'label' => 'Save',
                                                    'id' => 'on-save',
                                                    'onclick' => 'onSave();',
                                                    'block' => '1',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <!-- End Tab View - Create -->

                        <!-- Tab View - Edit -->
                        <div role="tabpanel" class="tab-pane" id="edit">
                            <div class="card-header">
                                <h5 class="card-title">Edit Business Calculator</h5>
                            </div>
                            <div class="card-body">
                                <div id="editForm">
                                </div>
                            </div>
                        </div>
                        <!-- End Tab View - Edit -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script -->
    @include('pages.admin.business-calculator.script')
@endsection
