<script>
    // Address Data Listsings
    let pages = 1;
    let currentPage = 1;
    let sortPublished = '1';
    let sortUnPublished = '1';
    let searchText = "";
    let sort = "updated_at";
    let sortBy = "desc";
    let data;
    let products;

    paginationGoPage(1);
    initPaginationLine(pages, currentPage);
    initTableHeader();

    function paginationGoPage(page) {
        data = {
            page: page,
            per_page: 20,
            // sort: sortBy,
            // order_by: sort,
            query: searchText,
            visible: sortPublished,
            invisible: sortUnPublished,
        };

        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: '{{ route('admin.business-calculators-get') }}',
            data: removeEmptyData(data),
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log('product get', data);
                if (data.error != 0 || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                currentPage = data.data.current_page;
                pages = Math.ceil(data.data.total / data.data.per_page);
                products = data.data.data;
                initDataTable(products);
                initPaginationLine(pages, currentPage);
                initTableHeader();
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                dataLoading = false;
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    function initDataTable(data) {
        html = "";
        if (data.length == 0) {
            html = `
            <tr>
                <td colspan="12">
                    <div class="d-flex justify-content-center align-items-center">
                        <p class="m-0">There are no records</p>
                    </div>
                </td>
            </tr>
            `;
            document.getElementById("table_body").innerHTML = html;
        } else {
            data.forEach(function(item, i, arr) {
                html += buildOneItem(item);
            });
            document.getElementById("table_body").innerHTML = html;
        }
    }

    function buildOneItem(item) {
        if (item.percent == 1)
            var percent = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;
        else
            var percent = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;

        if (item.visible == 1)
            var visible = `<img src="{{ asset('img/iconyes.png') }}" height="20px" style="margin: auto;">`;
        else
            var visible = `<img src="{{ asset('img/iconno.png') }}" height="20px" style="margin: auto;">`;

        return `
            <tr>
                <td>${item.id}</td>
                <td>${item.name}</td>
                <td>${item.amount_of_items}</td>
                <td>${item.from_amount}</td>
                <td>${item.to_amount}</td>
                <td>${item.rental_yield}</td>
                <td>${item.property_appreciation}</td>
                <td>${item.discount_amount ?? '-'}</td>
                <td>${item.incentive ?? '-'}</td>
                <td>${item.point ?? '-'}</td>
                <td>
                    <div style="display: flex;">${percent}</div>
                </td>
                <td>
                    <div style="display: flex;">${visible}</div>
                </td>
                <td class="text-center">
                    <div class="">
                        <p class="text-info m-0"><strong>${item.timeago}</strong></p>
                    </div>
                    <p class="h5">${item.updated_at2}</p>
                </td>
                <td class="text-center" style="white-space:nowrap;">
                    <button type="button" class="btn btn-info btn-round" onclick="editItem(${item.id})">
                        Edit
                    </button>
                    <button type="button" class="btn btn-danger btn-round" onclick="showDeleteMessage(${item.id}, '{{ route('admin.business-calculator-delete', ':id') }}')">
                        <div>Delete</div>
                    </button>
                </td>
            </tr>
        `;
    }

    function initPaginationLine(pages, page) {
        allPages = pages;
        currentPage = page;
        let pageNumbers = '';
        pageNumbers = buildPagination(currentPage, allPages);
        let paginationList = document.getElementById('pagination-list');
        paginationList.innerHTML = pageNumbers;
        initializePaginations(paginationGoPage);
    }

    function tableHeaderSort(newsort) {
        if (newsort == sort) {
            if (sortBy == "asc")
                sortBy = "desc";
            else
                sortBy = "asc";
        } else {
            sort = newsort
            sortBy = "asc";
        }
        paginationGoPage(currentPage);
    }

    function utilGetImg(value) {
        var img = "{{ asset('img/arrow_noactive.png') }}";
        if (sort == value && sortBy == "asc") img = "{{ asset('img/asc_arrow.png') }}";
        if (sort == value && sortBy == "desc") img = "{{ asset('img/desc_arrow.png') }}";
        return img;
    }

    function initTableHeader() {
        let header = `
            <th>ID <img onclick="tableHeaderSort('id');" src="${utilGetImg('id')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Name <img onclick="tableHeaderSort('name');" src="${utilGetImg('name')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Year <img onclick="tableHeaderSort('amount_of_items');" src="${utilGetImg('amount_of_items')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>From Amount <img onclick="tableHeaderSort('from_amount');" src="${utilGetImg('from_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>To Amount <img onclick="tableHeaderSort('to_amount');" src="${utilGetImg('to_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Rental Yield <img onclick="tableHeaderSort('rental_yield');" src="${utilGetImg('discount_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Property Appreciation <img onclick="tableHeaderSort('property_appreciation');" src="${utilGetImg('discount_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Discount <img onclick="tableHeaderSort('discount_amount');" src="${utilGetImg('discount_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Incentive <img onclick="tableHeaderSort('incentive');" src="${utilGetImg('discount_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th>Point <img onclick="tableHeaderSort('point');" src="${utilGetImg('discount_amount')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">In Percent <img onclick="tableHeaderSort('percent');" src="${utilGetImg('percent')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Visible <img onclick="tableHeaderSort('visible');" src="${utilGetImg('visible')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Updated At <img onclick="tableHeaderSort('updated_at');" src="${utilGetImg('updated_at')}" class="img-fluid" style="margin-left: 10px; width: 20px;"></th>
            <th class="text-center">Action</th>
        `;
        document.getElementById("table_header").innerHTML = header;
        document.getElementById("table_footer").innerHTML = header;
    }

    $(document).on('input', '#element_search', function() {
        searchText = document.getElementById("element_search").value;
        currentPage = 1;
        paginationGoPage(1);
    });

    function onVisibleSearchSelect() {
        if (visible_search) sortPublished = "1";
        else sortPublished = "0";
        if (unvisible_search) sortUnPublished = "1";
        else sortUnPublished = "0";
        currentPage = 1;
        paginationGoPage(1);
    }
</script>

<script type="text/javascript">
    // Tabs
    $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
        var target = $(e.target).attr("href");
        if (target != "#edit") {
            document.getElementById("tabEdit").style.display = "none";
        }
        if (target == "#create") {
            // Clear form
            clearForm();
            document.getElementById('createForm').appendChild(document.getElementById("form"));
        }
        if (target == "#home") {
            // clearForm();
        }
    });

    let editId = 0;

    function editItem(id) {
        let url = `{{ route('admin.business-calculator-get', ':id') }}`;
        url = url.replace(':id', id);
        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'GET',
            url: url,
            data: {},
            success: function(data) {
                loadingIcon('loading-bar', 0);
                // console.log("Product edit res: ", data);
                if (data.error || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                document.getElementById("tabEdit").style.display = "block";
                $('.nav-tabs a[href="#edit"]').tab('show');
                let target = document.getElementById("form");
                document.getElementById('editForm').appendChild(target);

                fillForm(data.data);
                // document.getElementById("minimum-amount").value = data.data.min_purchase_amount;
                // editId = data.data.id;
                // onSetCheck_visible(data.data.visible);
                // onSetCheck_percent(data.data.percent);
                // document.getElementById("discount-amount").value = data.data.discount_amount;
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                console.log(e);
                messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    // $("#name,#address").on('keyup', function(event) {
    //     let el = event.target;
    //     if (el.id == 'name' || el.id == 'desc') {
    //         el.classList.remove('is-valid', 'is-invalid');
    //         if (el.value.length > 0) {
    //             el.classList.add('is-valid');
    //         } else {
    //             el.classList.add('is-invalid');
    //         }
    //     }

    //     if (el.id == 'price') {
    //         el.classList.remove('is-valid', 'is-invalid');
    //         if (el.value && el.value != 0) {
    //             el.classList.add('is-valid');
    //         } else {
    //             el.classList.add('is-invalid');
    //         }
    //     }

    //     if (el.id == 'discount_price') {
    //         el.classList.remove('is-valid');
    //         if (el.value && el.value != 0) {
    //             el.classList.add('is-valid');
    //         }
    //     }
    // });

    function onSave() {
        let name = document.getElementById("bc_name").value;
        let year = document.getElementById("bc_year").value;
        let rentalYield = parseFloat(document.getElementById("bc_rental_yield").value);
        let propertyAppreciation = parseFloat(document.getElementById("bc_property_appreciation").value);
        let fromAmount = document.getElementById("minimum-amount").value;
        let toAmount = document.getElementById("minimum-amount_1").value;
        let discountAmount = document.getElementById("discount-amount").value;
        let discountAmountType = document.getElementById("discount-amount_select").value;
        let incentive = document.getElementById("incentive").value;
        let point = document.getElementById("point").value;
        let discountType = '0';

        // Check invalid fields
        if (
            messageStatus('error', 'Error', {
                msg: "The Name field is required"
            }, !name) ||
            messageStatus('error', 'Error', {
                msg: "The Year field is required"
            }, !year) ||
            messageStatus('error', 'Error', {
                msg: "The Minimum Purchase Amount field is required"
            }, !fromAmount) ||
            messageStatus('error', 'Error', {
                msg: "The Maximum Purchase Amount field is required"
            }, !toAmount) ||
            messageStatus('error', 'Error', {
                msg: "The Rental Yield field is required"
            }, !rentalYield) ||
            messageStatus('error', 'Error', {
                msg: "The Property Appreciation field is required"
            }, !propertyAppreciation)
        ) {
            return;
        }

        if (discount_type) {
            discountType = '1';
            incentive = null;
            point = null;
            // if (messageStatus(!discountAmount, "The discount amount field is required", "danger")) return;
        }

        if (incentive_type) {
            discountType = '2';
            discountAmount = null;
            discountAmountType = 1;
            point = null;
            if (messageStatus('error', 'Error', {
                    msg: "The Incentive field is required"
                }, !incentive)) return;
        }

        if (point_type) {
            discountType = '3';
            discountAmount = null;
            discountAmountType = 1;
            incentive = null;
            if (messageStatus('error', 'Error', {
                    msg: "The Point field is required"
                }, !point)) return;
        }

        let data = {
            id: editId,
            name: name,
            year: year,
            rentalYield: rentalYield,
            propertyAppreciation: propertyAppreciation,
            fromAmount: fromAmount,
            toAmount: toAmount,
            discountType: discountType,
            discountAmount: discountAmount,
            incentive: incentive,
            point: point,
            percent: (discountAmountType == 2) ? 1 : 0,
            visible: (visible) ? 1 : 0
        }

        loadingIcon('loading-bar', 1);
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="_token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.business-calculator-add') }}',
            data: removeEmptyData(data),
            success: function(data) {
                // console.log('response data: ', data);
                loadingIcon('loading-bar', 0);
                if (data.error == 2 || data.error == 3) {
                    return messageStatus('error', 'Error', data);
                } else if (data.error || data.data == null) {
                    return messageStatus('error', 'Error', {
                        msg: "Something went wrong"
                    });
                }
                if (editId != 0) {
                    paginationGoPage(currentPage);
                } else {
                    var text = buildOneItem(data.data);
                    var text2 = document.getElementById("table_body").innerHTML;
                    document.getElementById("table_body").innerHTML = text + text2;
                }
                $('.nav-tabs a[href="#home"]').tab('show');
                clearForm();
                return messageStatus('success', 'Success', {
                    msg: "Data saved successfully"
                });
            },
            error: function(e) {
                loadingIcon('loading-bar', 0);
                return messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }

    const discountTypes = document.querySelectorAll('#discount_type, #incentive_type, #point_type');
    discountTypes.forEach(function(type) {
        type.addEventListener('click', function(e) {
            let id = e.target.id;
            discountTypesCheck(id);
        });
    });

    function discountTypesCheck(id) {
        // discount_type, incentive_type, point_type
        let views = document.querySelectorAll(
            '.discount-amount-view, .incentive-view, .point-view');
        let activeTap = '';
        if (id == 'discount_type' && discount_type) {
            activeTap = 'discount-amount-view';
            onSetCheck_point_type(false);
            onSetCheck_incentive_type(false);
            onSetCheck_discount_type(true);
        } else if (id == 'incentive_type' && incentive_type) {
            activeTap = 'incentive-view';
            onSetCheck_point_type(false);
            onSetCheck_discount_type(false);
            onSetCheck_incentive_type(true);
        } else if (id == 'point_type' && point_type) {
            activeTap = 'point-view';
            onSetCheck_incentive_type(false);
            onSetCheck_discount_type(false);
            onSetCheck_point_type(true);
        } else {
            activeTap = 'discount-amount-view';
            onSetCheck_point_type(false);
            onSetCheck_incentive_type(false);
            onSetCheck_discount_type(true);
        }
        views.forEach((view) => {
            if (view.classList.contains(activeTap)) {
                view.hidden = false;
            } else {
                view.hidden = true;
            }
        });
    }

    // Clear forms
    function fillForm(data) {
        editId = data.id;
        document.getElementById("bc_name").value = data.name;
        document.getElementById("bc_year").value = data.amount_of_items;
        document.getElementById("bc_rental_yield").value = data.rental_yield;
        document.getElementById("bc_property_appreciation").value = data.property_appreciation;
        document.getElementById("minimum-amount").value = data.from_amount;
        document.getElementById("minimum-amount_1").value = data.to_amount;
        document.getElementById("discount-amount").value = data.discount_amount;
        document.getElementById("discount-amount_select").value = data.percent == 1 ? 2 : 1;
        document.getElementById("incentive").value = data.incentive;
        document.getElementById("point").value = data.point;
        if (data.discount_amount) {
            document.querySelector('#discount_type').click();
        } else if (data.incentive) {
            document.querySelector('#incentive_type').click();
        } else if (data.point) {
            document.querySelector('#point_type').click();
        };
        onSetCheck_visible(data.visible == 1);
    }

    // Clear forms
    function clearForm() {
        editId = 0;
        document.getElementById("bc_name").value = '';
        document.getElementById("bc_year").value = '';
        document.getElementById("bc_rental_yield").value = '';
        document.getElementById("bc_property_appreciation").value = '';
        document.getElementById("minimum-amount").value = 0;
        document.getElementById("minimum-amount_1").value = 0;
        document.getElementById("discount-amount").value = 0;
        document.getElementById("discount-amount_select").value = 1;
        document.getElementById("incentive").value = '';
        document.getElementById("point").value = 0;
        // onSetCheck_percent(true);
        onSetCheck_visible(true);
    }
</script>
