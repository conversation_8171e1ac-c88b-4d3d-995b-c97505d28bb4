<!-- Utils -->
<script>
    // Email validation
    function emailIsValid(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
    }

    function fullNameIsValid(fullName) {
        return /^\w+\s\w+$/.test(fullName);
    }

    function phoneIsValid(phone) {
        return /(^(\+?)(2519|09|2517|07|9|7)(\d{8,8})$)/.test(phone);
    }
</script>

<!-- Sign up -->
<script>
    // Signup page script
    $("#email,#password").on('keypress', function(event) {
        if (event.key === "Enter") {
            event.preventDefault();
            doLogin();
        }
    });

    function doLogin(e) {
        if (e) e.preventDefault();
        let phoneemail = document.getElementById("email").value;
        if (!isNaN(parseInt(phoneemail))) {
            is_email = false;
            if (phoneemail == "") {
                return Toast.fire({
                    icon: "error",
                    title: "Please enter phone number or email-address"
                });
            }
            if (!phoneIsValid(phoneemail)) {
                return Toast.fire({
                    icon: "error",
                    title: "Invalid phone number"
                });
            }
        } else {
            is_email = true;
            if (phoneemail == "") {
                return Toast.fire({
                    icon: "error",
                    title: "Please enter phone number or email-address"
                });
            }
            if (!emailIsValid(phoneemail)) {
                return Toast.fire({
                    icon: "error",
                    title: "Invalid email-address"
                });
            }
        }
        let password = document.getElementById("password").value;
        if (password == "") {
            return Toast.fire({
                icon: "error",
                title: "Please enter password"
            });
        }
        let remember = document.getElementById("remember").value;
        doLoginSendToServer(is_email ? '' : phoneemail, is_email ? phoneemail : '', password, remember ? 1 : 0);
    }

    function doLoginSendToServer(phone, email, password, remember) {
        loadingIcon('loading-bar', 1);
        disableBtn('login', 1);
        let userData = {};
        if (phone) {
            userData['phone'] = phone;
        }
        if (email) {
            userData['email'] = email;
        }
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            type: 'POST',
            url: '{{ route('admin.loginRequest') }}',
            data: {
                ...userData,
                password: password,
                remember_me: remember,
            },
            success: function(data) {
                // console.log('reg res data: ', data);
                loadingIcon('loading-bar', 0);
                disableBtn('login', 0);
                if (data.error) {
                    // redirect the user to checkout page
                    if (data.redirect_url) return window.location.href = data.redirect_url;
                    return messageStatus('error', 'Error', data);
                }

                Toast.fire({
                    icon: "success",
                    title: data.msg
                });
                // redirect the user to checkout page
                if (data.redirect_url) return window.location.href = data.redirect_url;
                // redirect the user to home page
                window.location.href = '{{ route('admin.dashboard') }}';
            },
            error: function(e) {
                console.log(e);
                loadingIcon('loading-bar', 0);
                disableBtn('login', 0);
                return messageStatus('error', 'Error', e.responseJSON);
            }
        });
    }
</script>
