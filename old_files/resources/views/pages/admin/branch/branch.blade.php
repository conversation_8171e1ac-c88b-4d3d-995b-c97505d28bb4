@extends('layouts.dashboard-layout')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card card-user">
                <!-- Tabs -->
                <div class="card-header card-header-danger">
                    <div class="nav-tabs-navigation">
                        <div class="nav-tabs-wrapper">
                            <ul class="nav nav-tabs" data-tabs="tabs">
                                <li role="presentation" class="nav-item" id="tabHome">
                                    <a href="#home" class="nav-link active" data-toggle="tab">List</a>
                                </li>
                                <li role="presentation" class="nav-item">
                                    <a href="#create" class="nav-link" data-toggle="tab">Create</a>
                                </li>
                                <li role="presentation" class="nav-item" id="tabEdit" style="display: none;">
                                    <a href="#edit" class="nav-link" data-toggle="tab">Edit</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- End Tabs -->

                <!-- Tab List -->
                <div id="myTabContent" class="card-body">
                    <div class="tab-content text-left">

                        <!-- Tab View - Listings -->
                        <div role="tabpanel" class="tab-pane active" id="home">
                            <div class="card-header">
                                <h5 class="card-title">Address Listings</h5>
                                <div class="row">
                                    <div class="col-12 col-md-4">
                                        <div class="form-group">
                                            <label for="visible_search">Filter</label>
                                            <div class="row gap-4 col-12">
                                                <!-- Published -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'visible_search',
                                                    'text' => 'Published item',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                                <!-- Unpublished -->
                                                @include('components.input-field.checkbox', [
                                                    'id' => 'unvisible_search',
                                                    'text' => 'Unpublished item',
                                                    'initvalue' => 'true',
                                                    'callback' => 'onVisibleSearchSelect()',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-4 ml-auto">
                                        @include('components.input-field.text', [
                                            'text' => 'Search',
                                            'label' => 'Search',
                                            'type' => 'search',
                                            'id' => 'element_search',
                                        ])
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Table -->
                                @include('components.table.light-table')
                                <!-- End Table -->
                            </div>
                        </div>
                        <!-- End Tab View - Listings -->

                        <!-- Tab View - Create -->
                        <div role="tabpanel" class="tab-pane" id="create">
                            <div class="card-header">
                                <h5 class="card-title">Create Address</h5>
                            </div>
                            <div class="card-body">
                                <div id="createForm">
                                    <div id="form">
                                        <div class="row">
                                            <div class="col-md-6 col-12">
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.text', [
                                                            'label' => 'Name',
                                                            'placeholder' => 'Insert name',
                                                            'desc' => 'Insert Name',
                                                            'id' => 'name',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        @include('components.input-field.address', [
                                                            'label' => 'Address',
                                                            'text' => 'Insert address',
                                                            'id' => 'address',
                                                            'request' => 'false',
                                                            'maxlength' => '100',
                                                        ])
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <!-- visible -->
                                                        @include('components.input-field.checkbox-2', [
                                                            'id' => 'visible',
                                                            'text' => 'Published',
                                                            'initvalue' => 'true',
                                                        ])
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                @include('components.buttons.button', [
                                                    'label' => 'Save',
                                                    'id' => 'on-save',
                                                    'onclick' => 'onSave();',
                                                    'block' => '1',
                                                ])
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <!-- End Tab View - Create -->

                        <!-- Tab View - Edit -->
                        <div role="tabpanel" class="tab-pane" id="edit">
                            <div class="card-header">
                                <h5 class="card-title">Edit Address</h5>
                            </div>
                            <div class="card-body">
                                <div id="editForm">
                                </div>
                            </div>
                        </div>
                        <!-- End Tab View - Edit -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script -->
    @include('pages.admin.branch.script')
@endsection
