@extends('layouts.dashboard-layout')

{{-- 03.02.2021 --}}

@section('content')
    <!-- ChartJs -->
    <script src="plugins/chartjs/Chart.bundle.js"></script>

    <div class="row">
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-body ">
                    <div class="row">
                        <div class="col-5 col-md-4">
                            <div class="icon-big text-center icon-success">
                                <i class="nc-icon nc-money-coins text-success"></i>
                            </div>
                        </div>
                        <div class="col-7 col-md-8">
                            <div class="numbers">
                                <p class="card-category">Total Earnings</p>
                                <p class="card-title" style="font-size: 18px;">
                                    {{ $earning }}
                                    {{ $currency }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer ">
                    <hr>
                    <div class="stats">
                        <i class="fa fa-refresh"></i>
                        Update Now
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-body ">
                    <div class="row">
                        <div class="col-5 col-md-4">
                            <div class="icon-big text-center icon-warning">
                                <i class="nc-icon nc-cart-simple text-warning"></i>
                            </div>
                        </div>
                        <div class="col-7 col-md-8">
                            <div class="numbers">
                                <p class="card-category">Total Orders</p>
                                <p class="card-title">{{ $orderscount }}
                                <p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer ">
                    <hr>
                    <div class="stats">
                        <i class="fa fa-calendar-o"></i>
                        Last day
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-body ">
                    <div class="row">
                        <div class="col-5 col-md-4">
                            <div class="icon-big text-center icon-warning">
                                <i class="nc-icon nc-single-02 text-danger"></i>
                            </div>
                        </div>
                        <div class="col-7 col-md-8">
                            <div class="numbers">
                                <p class="card-category">Total Users</p>
                                <p class="card-title">{{ $userscount }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer ">
                    <hr>
                    <div class="stats">
                        <i class="fa fa-clock-o"></i>
                        In the last hour
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card card-chart">
                <div class="card-header">
                    <h5 class="card-title">Order Earnings</h5>
                    <p class="card-category">Line Chart with Points</p>
                </div>
                <div class="card-body">
                    <div class="col-md-12">
                        <canvas id="speedChart" width="400" height="100"></canvas>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="chart-legend">
                        <i class="fa fa-circle text-info"></i> Earnings
                    </div>
                    <hr />
                    <div class="card-stats">
                        <i class="fa fa-check"></i> Data information certified
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{{ $lang->get(42) }}</h5> {{-- Last 10 Orders --}}
                    <p class="card-category">Transactions</p>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead class="text-primary">
                                <tr>
                                    <th>{{ $lang->get(43) }}</th>
                                    <th>{{ $lang->get(44) }}</th>
                                    <th>{{ $lang->get(45) }}</th>
                                    <th>{{ $lang->get(46) }}</th>
                                    <th>{{ $lang->get(48) }}</th>
                                    <th>{{ $lang->get(49) }}</th>
                                </tr>
                            </thead>
                            <tfoot class="text-primary">
                                <tr>
                                    <th>{{ $lang->get(43) }}</th>
                                    <th>{{ $lang->get(44) }}</th>
                                    <th>{{ $lang->get(45) }}</th>
                                    <th>{{ $lang->get(46) }}</th>
                                    <th>{{ $lang->get(48) }}</th>
                                    <th>{{ $lang->get(49) }}</th>
                                </tr>
                            </tfoot>
                            <tbody>
                                @foreach ($iorders as $key => $data)
                                    @if ($data->send == 1)
                                        <tr id="tr{{ $data->id }}">
                                            <td>{{ $data->id }}</td>
                                            <td id="total{{ $data->id }}">{{ $currency }}{{ $data->total }}
                                            </td>
                                            <td>
                                                @foreach ($iusers as $key => $idata)
                                                    @if ($idata->id == $data->user)
                                                        {{ $idata->name }}
                                                    @endif
                                                @endforeach
                                            </td>

                                            <td>
                                                @foreach ($iorderstatus as $key => $idata)
                                                    @if ($idata->id == $data->status)
                                                        {{ $idata->status }}
                                                    @endif
                                                @endforeach
                                            </td>
                                            <td>
                                                @if ($data->curbsidePickup == 'true')
                                                    <span
                                                        class="q-label q-color-label2 q-color-bkg-label2 q-radius">{{ $lang->get(213) }}</span>
                                                    {{-- curbsidePickup --}}
                                                @endif
                                                @if ($data->arrived == 'true')
                                                    <span
                                                        class="q-label q-color-label2 q-color-bkg-label2 q-radius">{{ $lang->get(214) }}</span><br>
                                                    {{-- customer arrived --}}
                                                @else
                                                    <br>
                                                @endif
                                                <span
                                                    class="q-label q-color-label1 q-color-bkg-label1 q-radius">{{ $data->method }}</span>
                                            </td>
                                            <td>
                                                <div class="q-font-bold q-color-second">{{ $data->timeago }}</div>
                                                {{ $data->updated_at }}
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .simple-text-link,
        .simple-text-link:hover,
        .simple-text-link:focus,
        .simple-text-link:active {
            text-decoration: none;
        }
    </style>

    <script>
        var speedCanvas = document.getElementById("speedChart");

        var dataFirst = {
            label: "{{ $lang->get(62) }}",
            data: [{{ $e1 }}, {{ $e2 }}, {{ $e3 }},
                {{ $e4 }}, {{ $e5 }}, {{ $e6 }},
                {{ $e7 }}, {{ $e8 }}, {{ $e9 }},
                {{ $e10 }}, {{ $e11 }}, {{ $e12 }}
            ],
            fill: false,
            borderColor: '#51CACF',
            backgroundColor: 'transparent',
            pointBorderColor: '#51CACF',
            pointRadius: 4,
            pointHoverRadius: 4,
            pointBorderWidth: 8
        };

        var speedData = {
            labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
            datasets: [dataFirst]
        };

        var chartOptions = {
            legend: {
                display: false,
                position: 'top'
            }
        };

        var lineChart = new Chart(speedCanvas, {
            type: 'line',
            hover: false,
            data: speedData,
            options: chartOptions
        });
    </script>
@endsection
